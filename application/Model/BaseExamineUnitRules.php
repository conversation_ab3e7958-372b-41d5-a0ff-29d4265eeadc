<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseExamineUnitRules extends BaseModel
{
	private $id;
	private $year;
	private $no;
	private $subject;
	private $type;
	private $level;
	private $grade;
	private $score;
	private $mark;
	private $mark_project;
	private $dyzz;
	private $dyzz2;
	private $dyzz3;
	private $txzz;
	private $txzz2;
	private $txzz3;
	private $dezz;
	private $dszz;
	private $dsizz;
	private $dwzz;
	private $qtzz;
	private $dz;
	private $zb;
	private $fzb;
	private $bw;
	private $updated_at;
	public $table = "examine_unit_rules";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getYear()
	{
		return $this->year;
	}

	public function getNo()
	{
		return $this->no;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function getType()
	{
		return $this->type;
	}

	public function getLevel()
	{
		return $this->level;
	}

	public function getGrade()
	{
		return $this->grade;
	}

	public function getScore()
	{
		return $this->score;
	}

	public function getMark()
	{
		return $this->mark;
	}

	public function getMarkProject()
	{
		return $this->mark_project;
	}

	public function getDyzz()
	{
		return $this->dyzz;
	}

	public function getDyzz2()
	{
		return $this->dyzz2;
	}

	public function getDyzz3()
	{
		return $this->dyzz3;
	}

	public function getTxzz()
	{
		return $this->txzz;
	}

	public function getTxzz2()
	{
		return $this->txzz2;
	}

	public function getTxzz3()
	{
		return $this->txzz3;
	}

	public function getDezz()
	{
		return $this->dezz;
	}

	public function getDszz()
	{
		return $this->dszz;
	}

	public function getDsizz()
	{
		return $this->dsizz;
	}

	public function getDwzz()
	{
		return $this->dwzz;
	}

	public function getQtzz()
	{
		return $this->qtzz;
	}

	public function getDz()
	{
		return $this->dz;
	}

	public function getZb()
	{
		return $this->zb;
	}

	public function getFzb()
	{
		return $this->fzb;
	}

	public function getBw()
	{
		return $this->bw;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->year !== $v)
		{
			$this->year = $v;
			$this->fieldData["year"] = $v;
		}
		return $this;

	}

	public function setNo($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->no !== $v)
		{
			$this->no = $v;
			$this->fieldData["no"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type !== $v)
		{
			$this->type = $v;
			$this->fieldData["type"] = $v;
		}
		return $this;

	}

	public function setLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->level !== $v)
		{
			$this->level = $v;
			$this->fieldData["level"] = $v;
		}
		return $this;

	}

	public function setGrade($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->grade !== $v)
		{
			$this->grade = $v;
			$this->fieldData["grade"] = $v;
		}
		return $this;

	}

	public function setScore($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->score !== $v)
		{
			$this->score = $v;
			$this->fieldData["score"] = $v;
		}
		return $this;

	}

	public function setMark($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mark !== $v)
		{
			$this->mark = $v;
			$this->fieldData["mark"] = $v;
		}
		return $this;

	}

	public function setMarkProject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->mark_project !== $v)
		{
			$this->mark_project = $v;
			$this->fieldData["mark_project"] = $v;
		}
		return $this;

	}

	public function setDyzz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dyzz !== $v)
		{
			$this->dyzz = $v;
			$this->fieldData["dyzz"] = $v;
		}
		return $this;

	}

	public function setDyzz2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dyzz2 !== $v)
		{
			$this->dyzz2 = $v;
			$this->fieldData["dyzz2"] = $v;
		}
		return $this;

	}

	public function setDyzz3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dyzz3 !== $v)
		{
			$this->dyzz3 = $v;
			$this->fieldData["dyzz3"] = $v;
		}
		return $this;

	}

	public function setTxzz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->txzz !== $v)
		{
			$this->txzz = $v;
			$this->fieldData["txzz"] = $v;
		}
		return $this;

	}

	public function setTxzz2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->txzz2 !== $v)
		{
			$this->txzz2 = $v;
			$this->fieldData["txzz2"] = $v;
		}
		return $this;

	}

	public function setTxzz3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->txzz3 !== $v)
		{
			$this->txzz3 = $v;
			$this->fieldData["txzz3"] = $v;
		}
		return $this;

	}

	public function setDezz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dezz !== $v)
		{
			$this->dezz = $v;
			$this->fieldData["dezz"] = $v;
		}
		return $this;

	}

	public function setDszz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dszz !== $v)
		{
			$this->dszz = $v;
			$this->fieldData["dszz"] = $v;
		}
		return $this;

	}

	public function setDsizz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dsizz !== $v)
		{
			$this->dsizz = $v;
			$this->fieldData["dsizz"] = $v;
		}
		return $this;

	}

	public function setDwzz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dwzz !== $v)
		{
			$this->dwzz = $v;
			$this->fieldData["dwzz"] = $v;
		}
		return $this;

	}

	public function setQtzz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->qtzz !== $v)
		{
			$this->qtzz = $v;
			$this->fieldData["qtzz"] = $v;
		}
		return $this;

	}

	public function setDz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->dz !== $v)
		{
			$this->dz = $v;
			$this->fieldData["dz"] = $v;
		}
		return $this;

	}

	public function setZb($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zb !== $v)
		{
			$this->zb = $v;
			$this->fieldData["zb"] = $v;
		}
		return $this;

	}

	public function setFzb($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->fzb !== $v)
		{
			$this->fzb = $v;
			$this->fieldData["fzb"] = $v;
		}
		return $this;

	}

	public function setBw($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bw !== $v)
		{
			$this->bw = $v;
			$this->fieldData["bw"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `examine_unit_rules` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"year" => $this->getYear(),
			"no" => $this->getNo(),
			"subject" => $this->getSubject(),
			"type" => $this->getType(),
			"level" => $this->getLevel(),
			"grade" => $this->getGrade(),
			"score" => $this->getScore(),
			"mark" => $this->getMark(),
			"mark_project" => $this->getMarkProject(),
			"dyzz" => $this->getDyzz(),
			"dyzz2" => $this->getDyzz2(),
			"dyzz3" => $this->getDyzz3(),
			"txzz" => $this->getTxzz(),
			"txzz2" => $this->getTxzz2(),
			"txzz3" => $this->getTxzz3(),
			"dezz" => $this->getDezz(),
			"dszz" => $this->getDszz(),
			"dsizz" => $this->getDsizz(),
			"dwzz" => $this->getDwzz(),
			"qtzz" => $this->getQtzz(),
			"dz" => $this->getDz(),
			"zb" => $this->getZb(),
			"fzb" => $this->getFzb(),
			"bw" => $this->getBw(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->year = '';
		$this->no = '';
		$this->subject = '';
		$this->type = '';
		$this->level = '';
		$this->grade = '';
		$this->score = '';
		$this->mark = '';
		$this->mark_project = '';
		$this->dyzz = '';
		$this->dyzz2 = '';
		$this->dyzz3 = '';
		$this->txzz = '';
		$this->txzz2 = '';
		$this->txzz3 = '';
		$this->dezz = '';
		$this->dszz = '';
		$this->dsizz = '';
		$this->dwzz = '';
		$this->qtzz = '';
		$this->dz = '';
		$this->zb = '';
		$this->fzb = '';
		$this->bw = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["year"]) && $this->year = $data["year"];
		isset($data["no"]) && $this->no = $data["no"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["type"]) && $this->type = $data["type"];
		isset($data["level"]) && $this->level = $data["level"];
		isset($data["grade"]) && $this->grade = $data["grade"];
		isset($data["score"]) && $this->score = $data["score"];
		isset($data["mark"]) && $this->mark = $data["mark"];
		isset($data["mark_project"]) && $this->mark_project = $data["mark_project"];
		isset($data["dyzz"]) && $this->dyzz = $data["dyzz"];
		isset($data["dyzz2"]) && $this->dyzz2 = $data["dyzz2"];
		isset($data["dyzz3"]) && $this->dyzz3 = $data["dyzz3"];
		isset($data["txzz"]) && $this->txzz = $data["txzz"];
		isset($data["txzz2"]) && $this->txzz2 = $data["txzz2"];
		isset($data["txzz3"]) && $this->txzz3 = $data["txzz3"];
		isset($data["dezz"]) && $this->dezz = $data["dezz"];
		isset($data["dszz"]) && $this->dszz = $data["dszz"];
		isset($data["dsizz"]) && $this->dsizz = $data["dsizz"];
		isset($data["dwzz"]) && $this->dwzz = $data["dwzz"];
		isset($data["qtzz"]) && $this->qtzz = $data["qtzz"];
		isset($data["dz"]) && $this->dz = $data["dz"];
		isset($data["zb"]) && $this->zb = $data["zb"];
		isset($data["fzb"]) && $this->fzb = $data["fzb"];
		isset($data["bw"]) && $this->bw = $data["bw"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}