<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseBonusPaperAssigns extends BaseModel
{
  private $id;
  private $bonus_id;
  private $money;
  private $end_at;
 private $statement  = '0';
  private $created_at;
 private $updated_at  = 'CURRENT_TIMESTAMP';
 public $table = "bonus_paper_assigns";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getBonusId()
 {
   return $this->bonus_id;
 }

 public function getMoney()
 {
   return $this->money;
 }

 public function getEndAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->end_at));
   else return $this->end_at;
 }

 public function getStatement()
 {
   return $this->statement;
 }

 public function getCreatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
   else return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setBonusId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->bonus_id !== $v)
    {
      $this->bonus_id = $v;
      $this->fieldData["bonus_id"] = $v;
    }
   return $this;

 }

 public function setMoney($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->money !== $v)
    {
      $this->money = $v;
      $this->fieldData["money"] = $v;
    }
   return $this;

 }

 public function setEndAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->end_at !== $v)
    {
      $this->end_at = $v;
      $this->fieldData["end_at"] = $v;
    }
   return $this;

 }

 public function setStatement($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->statement !== $v)
    {
      $this->statement = $v;
      $this->fieldData["statement"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `bonus_paper_assigns` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "bonus_id" => $this->getBonusId(),
     "money" => $this->getMoney(),
     "end_at" => $this->getEndAt(),
     "statement" => $this->getStatement(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->bonus_id = '';
   $this->money = '';
   $this->end_at = '';
   $this->statement = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["bonus_id"]) && $this->bonus_id = $data["bonus_id"];
   isset($data["money"]) && $this->money = $data["money"];
   isset($data["end_at"]) && $this->end_at = $data["end_at"];
   isset($data["statement"]) && $this->statement = $data["statement"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}