<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseCmsTopics;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class CmsTopics extends BaseCmsTopics
{
	function selectAll($addWhere='',$orderBy='',$showMax=0,$fields='')
	{
		!$orderBy && $orderBy = 'ORDER BY `left` ASC,displayorder asc';
		return parent::selectAll($addWhere,$orderBy,$showMax,$fields);
	} 

	function getPager($addWhere='',$orderBy='',$showMax=20,$fields='')
	{
		!$orderBy && $orderBy = 'ORDER BY `left` ASC,displayorder ASC';
		return parent::getPager($addWhere,$orderBy,$showMax,$fields);
	}

	function getModel(){
		return sf::getModel('CmsModels',parent::getMid());
	}

	/**
	 * <AUTHOR>
	 * @DateTime  2018-04-24
	 * @copyright 根据目录获取对象（目录需唯一）
	 * @license   [license]
	 * @version   [version]
	 * @param     string      $mark [description]
	 * @return    [type]            [description]
	 */
	function selectByMark($mark=''){
		$db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `dirname` = '" . $mark . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        return $this;
	}

	function setSetting($v){
		if(!is_array($v)) $v=[];
		parent::setSetting(serialize($v));
	}

	function getSetting($key=''){
		$v = (array)unserialize(parent::getSetting());
		if($v[$key]) return $v[$key];
		else return $v;
	}

	function rebuildTree()
	 {
		$data = $this->getFormatList();
		$db = sf::getLib("db");
		for ($i = 0, $n = count($data); $i < $n; $i++ )
		{
		  	$result = array("left"=>$data[$i]['left'],
						 	"right"=>$data[$i]['right'],
						 	"head_str"=>$data[$i]['HeadStr'],
						 	"level"=>$data[$i]['level']);
		  	$db->update($result,"id = '".$data[$i]['id']."'",$this->table);
		}
	 }
	 
	 function save()
	 {
		 $result = parent::save();
		 $this->rebuildTree();
		 $this->updateChilds();
		 return $result;
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-02-26
	  * @copyright 更新子类
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 private function updateChilds(){
	 	$db = sf::getLib('Db');
	 	$query = $db->query(" select * from ".$this->table);
	 	$result = $db->result_array($query);
	 	$result = array_column($result, 'pid','id');

	 	$data=[];
	 	foreach($result as $id=>$pid){
	 		$data[$pid][] = $id;
	 	}
	 	
	 	unset($data[0]);
	 	$data = array_map(function($v){
	 		return implode(',', $v);
	 	}, $data);

	 	foreach($data as $id=>$childs){
	 		$db->query(" update ".$this->table." set childids='".$childs."' where id=".$id);
	 	}
	 }
	 
	 function getFormatList($pid = 0)
	 {
		$result = parent::selectAll("",'ORDER BY pid ASC,displayorder ASC')->toArray();
		foreach((array)$result as $row){
			$num = count($tmpData[$row['pid']]);
			$tmpData[$row['pid']][$num] = $row;
		}
		if (count($tmpData) == 0) return;
		$showData = array();
		$this->getFormatList_c($tmpData, $showData, $pid, '',1);
		return $showData;
	 }
	 
	 function getFormatList_c(&$tmpData, &$showData, $pid, $headstr = '', $level = 1, $left = 1)
	 {
		$num = count($tmpData[$pid]);
		$i = 0;
		if(!is_array($tmpData[$pid])) return false;
		foreach ($tmpData[$pid] as $key => $val)
		{
		  	$id     = $val['id'];
		  	$tmplen = count($showData);
		  	$showData[$tmplen] = $val;
		  	$showData[$tmplen]['left'] = $left;
		  	$right = $left + 1;
	
		  	if (!empty($headstr)) $showData[$tmplen]['HeadStr'] = $headstr;
	
		  	if ($i == $num-1){
		  		if($pid==0) $showData[$tmplen]['HeadStr'] = '';
				else{
					$nbsp='';
					for($j=1;$j<$level;$j++)
						$nbsp .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';
					$showData[$tmplen]['HeadStr'] = $nbsp.'└─ ';
				}
				$headstr_1 =  "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;".$headstr;
				$level_1   =  $level + 1;
		  	}else{
				$showData[$tmplen]['HeadStr'] .= '';
				$nbsp='';
				for($k=1;$k<=$level;$k++)
					$nbsp .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;';

				$headstr_1 = $nbsp.'├─ ';
				$level_1   =  $level + 1;
		  	}
		  	$showData[$tmplen]['level'] = $level;
		  	$i++;
	
		  	if ((count($tmpData[$id]) > 0)) $right = $this->getFormatList_c($tmpData, $showData, $id, $headstr_1, $level_1, $right);
				
		  	$showData[$tmplen]['right'] = $right;
		  	$left = $right + 1;
		}
		return $right + 1;
	 }
	 
	 function remove()
	 {
		 $result = $this->selectSonTree($this);
		 while($object = $result->getObject()){
			 $object->delete();
		 }
		 $this->delete();
		 $this->rebuildTree();
		 $this->updateChilds();
		 return true;
	 }
	 
	 function selectSonTree($object)
	 {
		 if(!is_object($object)){
			$this->selectByPk((int)$object);
			$object = & $this;
		}
		 return $this->selectAll('`left` > '.$object->getLeft().' AND `left` <'.$object->getRight(),'ORDER BY `left` ASC');
	 }

	 function getSonTree()
	 {
		 return parent::selectAll('`parent_id` = '.$this->getId().' and `left` > '.$this->getLeft().' AND `right` < '.$this->getRight(),'ORDER BY `left` ASC');
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-06-26
	  * @copyright 获取根结点id
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getRootId($pid=''){
	 	$pid || $pid = parent::getPid();
	 	if($pid==0) return parent::getId();
	 	$cat = sf::getModel('CmsCategorys',$pid);
	 	if($cat->getPid()==0) return $cat->getId();
	 	else return $this->getRootId($cat->getPid());
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-01-23
	  * @copyright 获取分类相关
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getCats(){
	 	$cats=[];
	 	$cats['id'] = parent::getId();
	 	$cats['pid'] = parent::getPid();
	 	$cats['rid'] = $this->getRootId();

	 	$cats['categorys']=[];

	 	$all = $this->selectAll(" isshow=1 ");
	 	while($c = $all->getObject()){
	 		$category['id'] = $c->getId();
		 	$category['pid'] = $c->getPid();
		 	$category['tid'] = $c->getTid();
		 	$category['rid'] = $c->getRootId();
		 	$category['subject'] = $c->getName();
		 	$category['level'] = $c->getLevel();
		 	$category['dirname'] = $c->getDirname();
		 	$category['thumb'] = $c->getThumb();

		 	$pc = sf::getModel('CmsCategorys',$category['pid']);
		 	if(!$pc->getIsshow()) continue;
		 	
	 		$setting = $c->getSetting();
	 		
	 		switch ($category['tid']) {
	 			case 2:
		 			if(strpos($setting['linkurl'],'http://')!==false)
		 				$category['url'] = $setting['linkurl'];
		 			else $category['url'] = site_url($setting['linkurl']);
	 				break;
	 			case 3:
	 				$category['url'] = site_url('cms/front/content_category/mark/'.$setting['target_dirname']);
	 				break;
	 			default:
	 				$category['url'] = site_url('cms/front/content_category/mark/'.$category['dirname']);
	 				break;
	 		}

	 		if($category['pid']!=0){
	 			//只找出二级列表
	 			if($category['level']<=2)
	 				$cats['categorys']['c_'.$category['pid']]['childs'][] = $category;
	 		}else{
	 			$cats['categorys']['c_'.$category['id']] = array_only($category,['id','pid','tid','dirname','subject','domain','thumb','url']);
	 		}
	 	}

	 	//重置key
	 	$cats['categorys'] = array_values($cats['categorys']);

	 	$cats['categorys'] = array_map(function($v){
		 		//if($v['tid']!=1) $v['childs']=[];
				return $v;
			}, $cats['categorys']);

		if($cats['id']){
			$cats['childs'] = parent::selectAll(" isshow=1 AND pid=".$cats['id'])->toArray();
		 	$cats['childs'] = array_map(function($v){
		 		if($v['tid']==2){
		 			$setting = unserialize($v['setting']);
		 			if(strpos($setting['linkurl'],'http://')!==false)
		 				$v['url'] = $setting['linkurl'];
		 			else $v['url'] = site_url($setting['linkurl']);
		 		}else $v['url'] = site_url('cms/front/content_category/mark/'.$v['dirname']);

		 		$v['subject'] = $v['name'];
				$v = array_only($v,['id','dirname','subject','domain','thumb','url']);
				return $v;
			}, $cats['childs']);
		}

	 	return $cats;
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-02-23
	  * @copyright 获取内容模型
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getModels(){
	 	$models = sf::getModel('CmsModels')->selectAll(" isshow=1 ");
	 	return array_column($models->toArray()?:[],'subject','id');
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-02-26
	  * @copyright 是否包含子类
	  * @license   [license]
	  * @version   [version]
	  * @return    boolean     [description]
	  */
	 function hasChild(){
	 	if((parent::getTid()==0 || parent::getTid()==2)) return true;

	 	if(parent::getChildids()) return true;
	 	else return false;
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-03-02
	  * @copyright 根据栏目id获取子栏目id
	  * @license   [license]
	  * @version   [version]
	  * @param     string      $catid [description]
	  * @return    [type]             [description]
	  */
	 function getChildidByCatid($catid='',$merge=true){
	 	$catid = is_array($catid)?$catid:[$catid];
	 	if(count(array_filter($catid))==0) return [];

	 	$db = sf::getLib('Db');
	 	$query = $db->query(" SELECT id FROM ".$this->table." where pid in(".implode(',',$catid).")");
	 	$result = array_column($db->result_array($query), 'id');

	 	if($merge && count($result)>0) return array_merge($catid,$result);

	 	return $catid;
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-03-09
	  * @copyright 获取指定id下所有子id
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getAllChildCatid(){
	 	$all = $this->selectAll('`left` > '.parent::getLeft().' AND `left` <'.parent::getRight(),'ORDER BY `left` ASC');

	 	$ids=[];
	 	$ids[] = parent::getId();
	 	while($cat = $all->getObject()){
	 		$ids[] = $cat->getId();
	 		$ids[] = $cat->getChildids();
	 	}
	 	$ids = array_unique(array_filter($ids));
	 	$ids = implode(',',$ids);
	 	return explode(',',$ids);
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-03-02
	  * @copyright 获取内容发布地址
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getPublishUrl(){
	 	$url = $this->getSetting('url')['publish'];
	 	if($url) return site_url("cms/".$url."/catid/".parent::getId());
	 	else return site_url("cms/content/edit/catid/".parent::getId());
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-03-12
	  * @copyright 通过权限获取列表
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function selectByAuth($addWhere ,$addSql){
	 	$addWhere = '1';
	 	$catids=[];

	 	if($_SESSION['cms_auth_subject'] && is_array($_SESSION['cms_auth_subject'])){
	 		$_addWhere = " AND subject IN('".implode("','", $_SESSION['cms_auth_subject'])."')";
	 	}
	 	$auths = sf::getModel("CmsCategorysAuth")->selectAll(" roleid like '%|".$_SESSION['userlevel']."%' ".$_addWhere);
	 	if($auths->getTotal()>0){
	 		while($auth = $auths->getObject()){
		 		$ids = $auth->getCatids(false);
		 		//if(!in_array($_SESSION['userlevel'], $ids)) continue;
		 		if($ids && $catids) $catids = array_merge($catids,$ids);
		 		else $catids = $ids;
		 		//$ids && $catids += $ids;
		 	}
		 	
	 		$catids = array_unique($catids);
	 		$catids = array_filter($catids);

	 		if(count($catids) > 0){
	 			$catids = implode(',', $catids);
	 			$addWhere .= " AND id IN(".$catids.")"; 	
	 		}
	 	}

	 	return $this->selectAll($addWhere ,$addSql);
	 }

	 /**
	  * <AUTHOR>
	  * @DateTime  2018-07-24
	  * @copyright 计算栏目点击数，栏目下文章点击数之和
	  * @license   [license]
	  * @version   [version]
	  * @return    [type]      [description]
	  */
	 function getHits(){
	 	$catid = $this->getAllChildCatid();
        if(count($catid)==0) return 0;
        
        $addWhere .= " catid IN(".implode(',', $catid).")";

	 	$db = sf::getLib('Db');
	 	$query = $db->query(" SELECT sum(hits) as sum FROM cms_contents where ".$addWhere);

	 	$result = $db->result_array($query);

	 	return $result[0]['sum']?:0;
	 }

	 function getIsshow($f=true){
		if($f) return parent::getIsShow();
		$is_show = parent::getIsShow();
		if($pid = parent::getPid()){
			$parent = new static($pid);
			$is_show = $parent->getIsShow();
		}
		return $is_show==1?'<i class="fa fa-check text-success" title="点击隐藏"></i>':'<i class="fa fa-times text-danger" title="点击显示"></i>';
	}

	function setContent1($v){
		if(is_array($v)) parent::setContent1(serialize($v));
		else parent::setContent1($v);
	}

	function getContent1(){
		$v = unserialize(parent::getContent1());
		if(is_array($v)) return (array)$v;
		else return parent::getContent1();
	}

	function setContent2($v){
		if(is_array($v)) parent::setContent2(serialize($v));
		else parent::setContent2($v);
	}

	function getContent2(){
		if($this->isNew()) return [];
		$arr = sf::getModel('CmsTopicContent')->selectAll(" catid in (3,4) AND tid = ".$this->getId())->toArray();

		return array_column($arr, 'cid','id');

		// $v = unserialize(parent::getContent2());
		// if(is_array($v)) return (array)$v;
		// else return parent::getContent2();
	}

	function setContent3($v){
		if(is_array($v)) parent::setContent3(serialize($v));
		else parent::setContent3($v);
	}

	function getContent3(){
		if($this->isNew()) return [];
		$arr = sf::getModel('CmsTopicContent')->selectAll(" catid = 82 AND tid = ".$this->getId())->toArray();

		return array_column($arr, 'cid','id');
	}

	function updateContent($cids=[]){
		if($this->getId()){
			$all = sf::getModel('CmsTopicContent')->selectAll(" type = 'topic' AND tid = ".$this->getId());

			while($obj = $all->getObject()){
				if(in_array($obj->getCid(), $cids)){
					unset($cids[array_search($obj->getCid(), $cids)]);
				}else{
					$obj->delete();
				}
			}
		}

		$topic_content = sf::getModel('CmsTopicContent');
		foreach($cids as $catid){
			$topic_content->setCid($catid);	
			$topic_content->setCatid(sf::getModel('CmsContents',$catid)->getCatid());	
			$topic_content->setTid($this->getId());
			$topic_content->setType('topic');
			$topic_content->setUserId(input::session('roleuserid'));
			$topic_content->save();
		}
	}
}