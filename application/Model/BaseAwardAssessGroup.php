<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseAwardAssessGroup extends BaseModel
{
  private $id;
  private $project_ids;
  private $expert_ids;
  private $types_id;
  private $project_count;
  private $group_note;
  private $group_subject;
  private $project_type;
  private $group_year;
  private $corporation_id;
 private $office_id  = '0';
  private $created_at;
 private $updated_at  = 'CURRENT_TIMESTAMP';
 private $is_lock  = '0';
  private $assess_map;
  private $subject_ids;
  private $configs;
  private $hy_codes;
  private $gb_codes;
 public $table = "award_assess_group";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getProjectIds()
 {
   return $this->project_ids;
 }

 public function getExpertIds()
 {
   return $this->expert_ids;
 }

 public function getTypesId()
 {
   return $this->types_id;
 }

 public function getProjectCount()
 {
   return $this->project_count;
 }

 public function getGroupNote()
 {
   return $this->group_note;
 }

 public function getGroupSubject()
 {
   return $this->group_subject;
 }

 public function getProjectType()
 {
   return $this->project_type;
 }

 public function getGroupYear()
 {
   return $this->group_year;
 }

 public function getCorporationId()
 {
   return $this->corporation_id;
 }

 public function getOfficeId()
 {
   return $this->office_id;
 }

 public function getCreatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
   else return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function getIsLock()
 {
   return $this->is_lock;
 }

 public function getAssessMap()
 {
   return $this->assess_map;
 }

 public function getSubjectIds()
 {
   return $this->subject_ids;
 }

 public function getConfigs()
 {
   return $this->configs;
 }

 public function getHyCodes()
 {
   return $this->hy_codes;
 }

 public function getGbCodes()
 {
   return $this->gb_codes;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setProjectIds($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->project_ids !== $v)
    {
      $this->project_ids = $v;
      $this->fieldData["project_ids"] = $v;
    }
   return $this;

 }

 public function setExpertIds($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->expert_ids !== $v)
    {
      $this->expert_ids = $v;
      $this->fieldData["expert_ids"] = $v;
    }
   return $this;

 }

 public function setTypesId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->types_id !== $v)
    {
      $this->types_id = $v;
      $this->fieldData["types_id"] = $v;
    }
   return $this;

 }

 public function setProjectCount($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->project_count !== $v)
    {
      $this->project_count = $v;
      $this->fieldData["project_count"] = $v;
    }
   return $this;

 }

 public function setGroupNote($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->group_note !== $v)
    {
      $this->group_note = $v;
      $this->fieldData["group_note"] = $v;
    }
   return $this;

 }

 public function setGroupSubject($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->group_subject !== $v)
    {
      $this->group_subject = $v;
      $this->fieldData["group_subject"] = $v;
    }
   return $this;

 }

 public function setProjectType($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->project_type !== $v)
    {
      $this->project_type = $v;
      $this->fieldData["project_type"] = $v;
    }
   return $this;

 }

 public function setGroupYear($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->group_year !== $v)
    {
      $this->group_year = $v;
      $this->fieldData["group_year"] = $v;
    }
   return $this;

 }

 public function setCorporationId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->corporation_id !== $v)
    {
      $this->corporation_id = $v;
      $this->fieldData["corporation_id"] = $v;
    }
   return $this;

 }

 public function setOfficeId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->office_id !== $v)
    {
      $this->office_id = $v;
      $this->fieldData["office_id"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

 public function setIsLock($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->is_lock !== $v)
    {
      $this->is_lock = $v;
      $this->fieldData["is_lock"] = $v;
    }
   return $this;

 }

 public function setAssessMap($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->assess_map !== $v)
    {
      $this->assess_map = $v;
      $this->fieldData["assess_map"] = $v;
    }
   return $this;

 }

 public function setSubjectIds($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->subject_ids !== $v)
    {
      $this->subject_ids = $v;
      $this->fieldData["subject_ids"] = $v;
    }
   return $this;

 }

 public function setConfigs($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->configs !== $v)
    {
      $this->configs = $v;
      $this->fieldData["configs"] = $v;
    }
   return $this;

 }

 public function setHyCodes($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->hy_codes !== $v)
    {
      $this->hy_codes = $v;
      $this->fieldData["hy_codes"] = $v;
    }
   return $this;

 }

 public function setGbCodes($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->gb_codes !== $v)
    {
      $this->gb_codes = $v;
      $this->fieldData["gb_codes"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `award_assess_group` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "project_ids" => $this->getProjectIds(),
     "expert_ids" => $this->getExpertIds(),
     "types_id" => $this->getTypesId(),
     "project_count" => $this->getProjectCount(),
     "group_note" => $this->getGroupNote(),
     "group_subject" => $this->getGroupSubject(),
     "project_type" => $this->getProjectType(),
     "group_year" => $this->getGroupYear(),
     "corporation_id" => $this->getCorporationId(),
     "office_id" => $this->getOfficeId(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     "is_lock" => $this->getIsLock(),
     "assess_map" => $this->getAssessMap(),
     "subject_ids" => $this->getSubjectIds(),
     "configs" => $this->getConfigs(),
     "hy_codes" => $this->getHyCodes(),
     "gb_codes" => $this->getGbCodes(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->project_ids = '';
   $this->expert_ids = '';
   $this->types_id = '';
   $this->project_count = '';
   $this->group_note = '';
   $this->group_subject = '';
   $this->project_type = '';
   $this->group_year = '';
   $this->corporation_id = '';
   $this->office_id = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->is_lock = '';
   $this->assess_map = '';
   $this->subject_ids = '';
   $this->configs = '';
   $this->hy_codes = '';
   $this->gb_codes = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["project_ids"]) && $this->project_ids = $data["project_ids"];
   isset($data["expert_ids"]) && $this->expert_ids = $data["expert_ids"];
   isset($data["types_id"]) && $this->types_id = $data["types_id"];
   isset($data["project_count"]) && $this->project_count = $data["project_count"];
   isset($data["group_note"]) && $this->group_note = $data["group_note"];
   isset($data["group_subject"]) && $this->group_subject = $data["group_subject"];
   isset($data["project_type"]) && $this->project_type = $data["project_type"];
   isset($data["group_year"]) && $this->group_year = $data["group_year"];
   isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
   isset($data["office_id"]) && $this->office_id = $data["office_id"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
   isset($data["is_lock"]) && $this->is_lock = $data["is_lock"];
   isset($data["assess_map"]) && $this->assess_map = $data["assess_map"];
   isset($data["subject_ids"]) && $this->subject_ids = $data["subject_ids"];
   isset($data["configs"]) && $this->configs = $data["configs"];
   isset($data["hy_codes"]) && $this->hy_codes = $data["hy_codes"];
   isset($data["gb_codes"]) && $this->gb_codes = $data["gb_codes"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}