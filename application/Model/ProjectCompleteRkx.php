<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: ProjectCompleteRkx.model.php 2 2012-05-11 07:12:52Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseProjectCompleteRkx;
class ProjectCompleteRkx extends BaseProjectCompleteRkx
{
	function selectByProjectId($project_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setProjectId($project_id);
		return $this;
	}
	
	function getTeam()
	{
		return unserialize(parent::getTeam());
	}
	
	function setTeam($v)
	{
		parent::setTeam(serialize($v));
	}
	
	function getUnit()
	{
		return unserialize(parent::getUnit());
	}
	
	function setUnit($v)
	{
		parent::setUnit(serialize($v));
	}		
}