<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseAwardGroupExpert extends BaseModel
{
  private $id;
  private $group_id;
  private $expert_id;
 private $role  = 'JS';
 private $type  = 'project';
 private $state  = '0';
 public $table = "award_group_expert";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getGroupId()
 {
   return $this->group_id;
 }

 public function getExpertId()
 {
   return $this->expert_id;
 }

 public function getRole()
 {
   return $this->role;
 }

 public function getType()
 {
   return $this->type;
 }

 public function getState()
 {
   return $this->state;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setGroupId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->group_id !== $v)
    {
      $this->group_id = $v;
      $this->fieldData["group_id"] = $v;
    }
   return $this;

 }

 public function setExpertId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->expert_id !== $v)
    {
      $this->expert_id = $v;
      $this->fieldData["expert_id"] = $v;
    }
   return $this;

 }

 public function setRole($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->role !== $v)
    {
      $this->role = $v;
      $this->fieldData["role"] = $v;
    }
   return $this;

 }

 public function setType($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->type !== $v)
    {
      $this->type = $v;
      $this->fieldData["type"] = $v;
    }
   return $this;

 }

 public function setState($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->state !== $v)
    {
      $this->state = $v;
      $this->fieldData["state"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `award_group_expert` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "group_id" => $this->getGroupId(),
     "expert_id" => $this->getExpertId(),
     "role" => $this->getRole(),
     "type" => $this->getType(),
     "state" => $this->getState(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->group_id = '';
   $this->expert_id = '';
   $this->role = '';
   $this->type = '';
   $this->state = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["group_id"]) && $this->group_id = $data["group_id"];
   isset($data["expert_id"]) && $this->expert_id = $data["expert_id"];
   isset($data["role"]) && $this->role = $data["role"];
   isset($data["type"]) && $this->type = $data["type"];
   isset($data["state"]) && $this->state = $data["state"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}