<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectAppraiseeDetail;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class ProjectAppraiseeDetail extends BaseProjectAppraiseeDetail
{
    function selectByBookId($book_id='')
    {
        if($book_id == '') return;
        $db = sf::getLib("db");
        $addWhere ="`book_id` = '".$book_id."' ";
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE ".$addWhere."");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setBookId($book_id);
        return $this;
    }
}