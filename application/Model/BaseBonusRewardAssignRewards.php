<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseBonusRewardAssignRewards extends BaseModel
{
  private $id;
  private $assign_company_id;
  private $bonus_id;
  private $reward_id;
  private $company_id;
  private $company_name;
  private $manager_id;
  private $manager_name;
  private $money;
  private $own_money;
  private $note;
 private $statement  = '0';
  private $created_at;
 private $updated_at  = 'CURRENT_TIMESTAMP';
 public $table = "bonus_reward_assign_rewards";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getAssignCompanyId()
 {
   return $this->assign_company_id;
 }

 public function getBonusId()
 {
   return $this->bonus_id;
 }

 public function getRewardId()
 {
   return $this->reward_id;
 }

 public function getCompanyId()
 {
   return $this->company_id;
 }

 public function getCompanyName()
 {
   return $this->company_name;
 }

 public function getManagerId()
 {
   return $this->manager_id;
 }

 public function getManagerName()
 {
   return $this->manager_name;
 }

 public function getMoney()
 {
   return $this->money;
 }

 public function getOwnMoney()
 {
   return $this->own_money;
 }

 public function getNote()
 {
   return $this->note;
 }

 public function getStatement()
 {
   return $this->statement;
 }

 public function getCreatedAt()
 {
   return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setAssignCompanyId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->assign_company_id !== $v)
    {
      $this->assign_company_id = $v;
      $this->fieldData["assign_company_id"] = $v;
    }
   return $this;

 }

 public function setBonusId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->bonus_id !== $v)
    {
      $this->bonus_id = $v;
      $this->fieldData["bonus_id"] = $v;
    }
   return $this;

 }

 public function setRewardId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->reward_id !== $v)
    {
      $this->reward_id = $v;
      $this->fieldData["reward_id"] = $v;
    }
   return $this;

 }

 public function setCompanyId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->company_id !== $v)
    {
      $this->company_id = $v;
      $this->fieldData["company_id"] = $v;
    }
   return $this;

 }

 public function setCompanyName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->company_name !== $v)
    {
      $this->company_name = $v;
      $this->fieldData["company_name"] = $v;
    }
   return $this;

 }

 public function setManagerId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->manager_id !== $v)
    {
      $this->manager_id = $v;
      $this->fieldData["manager_id"] = $v;
    }
   return $this;

 }

 public function setManagerName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->manager_name !== $v)
    {
      $this->manager_name = $v;
      $this->fieldData["manager_name"] = $v;
    }
   return $this;

 }

 public function setMoney($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->money !== $v)
    {
      $this->money = $v;
      $this->fieldData["money"] = $v;
    }
   return $this;

 }

 public function setOwnMoney($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->own_money !== $v)
    {
      $this->own_money = $v;
      $this->fieldData["own_money"] = $v;
    }
   return $this;

 }

 public function setNote($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->note !== $v)
    {
      $this->note = $v;
      $this->fieldData["note"] = $v;
    }
   return $this;

 }

 public function setStatement($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->statement !== $v)
    {
      $this->statement = $v;
      $this->fieldData["statement"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `bonus_reward_assign_rewards` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "assign_company_id" => $this->getAssignCompanyId(),
     "bonus_id" => $this->getBonusId(),
     "reward_id" => $this->getRewardId(),
     "company_id" => $this->getCompanyId(),
     "company_name" => $this->getCompanyName(),
     "manager_id" => $this->getManagerId(),
     "manager_name" => $this->getManagerName(),
     "money" => $this->getMoney(),
     "own_money" => $this->getOwnMoney(),
     "note" => $this->getNote(),
     "statement" => $this->getStatement(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->assign_company_id = '';
   $this->bonus_id = '';
   $this->reward_id = '';
   $this->company_id = '';
   $this->company_name = '';
   $this->manager_id = '';
   $this->manager_name = '';
   $this->money = '';
   $this->own_money = '';
   $this->note = '';
   $this->statement = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["assign_company_id"]) && $this->assign_company_id = $data["assign_company_id"];
   isset($data["bonus_id"]) && $this->bonus_id = $data["bonus_id"];
   isset($data["reward_id"]) && $this->reward_id = $data["reward_id"];
   isset($data["company_id"]) && $this->company_id = $data["company_id"];
   isset($data["company_name"]) && $this->company_name = $data["company_name"];
   isset($data["manager_id"]) && $this->manager_id = $data["manager_id"];
   isset($data["manager_name"]) && $this->manager_name = $data["manager_name"];
   isset($data["money"]) && $this->money = $data["money"];
   isset($data["own_money"]) && $this->own_money = $data["own_money"];
   isset($data["note"]) && $this->note = $data["note"];
   isset($data["statement"]) && $this->statement = $data["statement"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}