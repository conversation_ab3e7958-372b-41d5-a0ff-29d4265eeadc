<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;

class BaseSeries extends BaseModel
{
	private $id  = '';
	private $subject  = '';
	private $series_type  = '';
	private $series_sql  = '';
	private $series_data  = '';
	private $series_key  = '';
	private $updated_at  = '';
	public $table = "series";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getSeriesType($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->series_type,0,$len,"utf-8");
			else return substr($this->series_type,0,$len);
		}
		return $this->series_type;
	}

	public function getSeriesSql($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->series_sql,0,$len,"utf-8");
			else return substr($this->series_sql,0,$len);
		}
		return $this->series_sql;
	}

	public function getSeriesData($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->series_data,0,$len,"utf-8");
			else return substr($this->series_data,0,$len);
		}
		return $this->series_data;
	}

	public function getSeriesKey($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->series_key,0,$len,"utf-8");
			else return substr($this->series_key,0,$len);
		}
		return $this->series_key;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setSeriesType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->series_type !== $v)
		{
			$this->series_type = $v;
			$this->fieldData["series_type"] = $v;
		}
		return $this;

	}

	public function setSeriesSql($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->series_sql !== $v)
		{
			$this->series_sql = $v;
			$this->fieldData["series_sql"] = $v;
		}
		return $this;

	}

	public function setSeriesData($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->series_data !== $v)
		{
			$this->series_data = $v;
			$this->fieldData["series_data"] = $v;
		}
		return $this;

	}

	public function setSeriesKey($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->series_key !== $v)
		{
			$this->series_key = $v;
			$this->fieldData["series_key"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `series` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"subject" => $this->getSubject(),
			"series_type" => $this->getSeriesType(),
			"series_sql" => $this->getSeriesSql(),
			"series_data" => $this->getSeriesData(),
			"series_key" => $this->getSeriesKey(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->subject = '';
		$this->series_type = '';
		$this->series_sql = '';
		$this->series_data = '';
		$this->series_key = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["series_type"]) && $this->series_type = $data["series_type"];
		isset($data["series_sql"]) && $this->series_sql = $data["series_sql"];
		isset($data["series_data"]) && $this->series_data = $data["series_data"];
		isset($data["series_key"]) && $this->series_key = $data["series_key"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}