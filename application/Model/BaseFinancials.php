<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;

class BaseFinancials extends BaseModel
{
	private $id  = '';
	private $user_id  = '';
	private $user_name  = '';
	private $user_name_en  = '';
	private $user_sex  = '';
	private $user_birthday  = '';
	private $user_level  = '';
	private $user_honor  = '';
	private $honor_file  = '';
	private $card_name  = '';
	private $card_id  = '';
	private $card_file  = '';
	private $user_home_phone  = '';
	private $user_mobile  = '';
	private $user_email  = '';
	private $user_fax  = '';
	private $graduate_school  = '';
	private $subject  = '';
	private $graduate_date  = '';
	private $subject_name  = '';
	private $work_unit  = '';
	private $unit_area  = '';
	private $user_duty  = '';
	private $unit_phone  = '';
	private $unit_address  = '';
	private $unit_postal_code  = '';
	private $user_work  = '';
	private $department  = '';
	private $unit_property  = '';
	private $other_language  = '';
	private $language_level  = '';
	private $note  = '';
	private $is_lock  = '';
	private $created_at  = '';
	private $updated_at  = '';
	public $table = "financials";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getUserNameEn($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name_en,0,$len,"utf-8");
			else return substr($this->user_name_en,0,$len);
		}
		return $this->user_name_en;
	}

	public function getUserSex($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_sex,0,$len,"utf-8");
			else return substr($this->user_sex,0,$len);
		}
		return $this->user_sex;
	}

	public function getUserBirthday($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_birthday,0,$len,"utf-8");
			else return substr($this->user_birthday,0,$len);
		}
		return $this->user_birthday;
	}

	public function getUserLevel($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_level,0,$len,"utf-8");
			else return substr($this->user_level,0,$len);
		}
		return $this->user_level;
	}

	public function getUserHonor($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_honor,0,$len,"utf-8");
			else return substr($this->user_honor,0,$len);
		}
		return $this->user_honor;
	}

	public function getHonorFile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->honor_file,0,$len,"utf-8");
			else return substr($this->honor_file,0,$len);
		}
		return $this->honor_file;
	}

	public function getCardName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->card_name,0,$len,"utf-8");
			else return substr($this->card_name,0,$len);
		}
		return $this->card_name;
	}

	public function getCardId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->card_id,0,$len,"utf-8");
			else return substr($this->card_id,0,$len);
		}
		return $this->card_id;
	}

	public function getCardFile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->card_file,0,$len,"utf-8");
			else return substr($this->card_file,0,$len);
		}
		return $this->card_file;
	}

	public function getUserHomePhone($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_home_phone,0,$len,"utf-8");
			else return substr($this->user_home_phone,0,$len);
		}
		return $this->user_home_phone;
	}

	public function getUserMobile($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_mobile,0,$len,"utf-8");
			else return substr($this->user_mobile,0,$len);
		}
		return $this->user_mobile;
	}

	public function getUserEmail($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_email,0,$len,"utf-8");
			else return substr($this->user_email,0,$len);
		}
		return $this->user_email;
	}

	public function getUserFax($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_fax,0,$len,"utf-8");
			else return substr($this->user_fax,0,$len);
		}
		return $this->user_fax;
	}

	public function getGraduateSchool($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->graduate_school,0,$len,"utf-8");
			else return substr($this->graduate_school,0,$len);
		}
		return $this->graduate_school;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getGraduateDate($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->graduate_date));
		else return $this->graduate_date;
	}

	public function getSubjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name,0,$len,"utf-8");
			else return substr($this->subject_name,0,$len);
		}
		return $this->subject_name;
	}

	public function getWorkUnit($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->work_unit,0,$len,"utf-8");
			else return substr($this->work_unit,0,$len);
		}
		return $this->work_unit;
	}

	public function getUnitArea($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->unit_area,0,$len,"utf-8");
			else return substr($this->unit_area,0,$len);
		}
		return $this->unit_area;
	}

	public function getUserDuty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_duty,0,$len,"utf-8");
			else return substr($this->user_duty,0,$len);
		}
		return $this->user_duty;
	}

	public function getUnitPhone($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->unit_phone,0,$len,"utf-8");
			else return substr($this->unit_phone,0,$len);
		}
		return $this->unit_phone;
	}

	public function getUnitAddress($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->unit_address,0,$len,"utf-8");
			else return substr($this->unit_address,0,$len);
		}
		return $this->unit_address;
	}

	public function getUnitPostalCode()
	{
		return $this->unit_postal_code;
	}

	public function getUserWork($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_work,0,$len,"utf-8");
			else return substr($this->user_work,0,$len);
		}
		return $this->user_work;
	}

	public function getDepartment($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department,0,$len,"utf-8");
			else return substr($this->department,0,$len);
		}
		return $this->department;
	}

	public function getUnitProperty($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->unit_property,0,$len,"utf-8");
			else return substr($this->unit_property,0,$len);
		}
		return $this->unit_property;
	}

	public function getOtherLanguage($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->other_language,0,$len,"utf-8");
			else return substr($this->other_language,0,$len);
		}
		return $this->other_language;
	}

	public function getLanguageLevel($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->language_level,0,$len,"utf-8");
			else return substr($this->language_level,0,$len);
		}
		return $this->language_level;
	}

	public function getNote($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->note,0,$len,"utf-8");
			else return substr($this->note,0,$len);
		}
		return $this->note;
	}

	public function getIsLock()
	{
		return $this->is_lock;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setUserNameEn($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name_en !== $v)
		{
			$this->user_name_en = $v;
			$this->fieldData["user_name_en"] = $v;
		}
		return $this;

	}

	public function setUserSex($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_sex !== $v)
		{
			$this->user_sex = $v;
			$this->fieldData["user_sex"] = $v;
		}
		return $this;

	}

	public function setUserBirthday($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_birthday !== $v)
		{
			$this->user_birthday = $v;
			$this->fieldData["user_birthday"] = $v;
		}
		return $this;

	}

	public function setUserLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_level !== $v)
		{
			$this->user_level = $v;
			$this->fieldData["user_level"] = $v;
		}
		return $this;

	}

	public function setUserHonor($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_honor !== $v)
		{
			$this->user_honor = $v;
			$this->fieldData["user_honor"] = $v;
		}
		return $this;

	}

	public function setHonorFile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->honor_file !== $v)
		{
			$this->honor_file = $v;
			$this->fieldData["honor_file"] = $v;
		}
		return $this;

	}

	public function setCardName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->card_name !== $v)
		{
			$this->card_name = $v;
			$this->fieldData["card_name"] = $v;
		}
		return $this;

	}

	public function setCardId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->card_id !== $v)
		{
			$this->card_id = $v;
			$this->fieldData["card_id"] = $v;
		}
		return $this;

	}

	public function setCardFile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->card_file !== $v)
		{
			$this->card_file = $v;
			$this->fieldData["card_file"] = $v;
		}
		return $this;

	}

	public function setUserHomePhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_home_phone !== $v)
		{
			$this->user_home_phone = $v;
			$this->fieldData["user_home_phone"] = $v;
		}
		return $this;

	}

	public function setUserMobile($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_mobile !== $v)
		{
			$this->user_mobile = $v;
			$this->fieldData["user_mobile"] = $v;
		}
		return $this;

	}

	public function setUserEmail($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_email !== $v)
		{
			$this->user_email = $v;
			$this->fieldData["user_email"] = $v;
		}
		return $this;

	}

	public function setUserFax($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_fax !== $v)
		{
			$this->user_fax = $v;
			$this->fieldData["user_fax"] = $v;
		}
		return $this;

	}

	public function setGraduateSchool($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->graduate_school !== $v)
		{
			$this->graduate_school = $v;
			$this->fieldData["graduate_school"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setGraduateDate($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->graduate_date !== $v)
		{
			$this->graduate_date = $v;
			$this->fieldData["graduate_date"] = $v;
		}
		return $this;

	}

	public function setSubjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name !== $v)
		{
			$this->subject_name = $v;
			$this->fieldData["subject_name"] = $v;
		}
		return $this;

	}

	public function setWorkUnit($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->work_unit !== $v)
		{
			$this->work_unit = $v;
			$this->fieldData["work_unit"] = $v;
		}
		return $this;

	}

	public function setUnitArea($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->unit_area !== $v)
		{
			$this->unit_area = $v;
			$this->fieldData["unit_area"] = $v;
		}
		return $this;

	}

	public function setUserDuty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_duty !== $v)
		{
			$this->user_duty = $v;
			$this->fieldData["user_duty"] = $v;
		}
		return $this;

	}

	public function setUnitPhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->unit_phone !== $v)
		{
			$this->unit_phone = $v;
			$this->fieldData["unit_phone"] = $v;
		}
		return $this;

	}

	public function setUnitAddress($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->unit_address !== $v)
		{
			$this->unit_address = $v;
			$this->fieldData["unit_address"] = $v;
		}
		return $this;

	}

	public function setUnitPostalCode($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->unit_postal_code !== $v)
		{
			$this->unit_postal_code = $v;
			$this->fieldData["unit_postal_code"] = $v;
		}
		return $this;

	}

	public function setUserWork($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_work !== $v)
		{
			$this->user_work = $v;
			$this->fieldData["user_work"] = $v;
		}
		return $this;

	}

	public function setDepartment($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department !== $v)
		{
			$this->department = $v;
			$this->fieldData["department"] = $v;
		}
		return $this;

	}

	public function setUnitProperty($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->unit_property !== $v)
		{
			$this->unit_property = $v;
			$this->fieldData["unit_property"] = $v;
		}
		return $this;

	}

	public function setOtherLanguage($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->other_language !== $v)
		{
			$this->other_language = $v;
			$this->fieldData["other_language"] = $v;
		}
		return $this;

	}

	public function setLanguageLevel($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->language_level !== $v)
		{
			$this->language_level = $v;
			$this->fieldData["language_level"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setIsLock($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_lock !== $v)
		{
			$this->is_lock = $v;
			$this->fieldData["is_lock"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `financials` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"user_name_en" => $this->getUserNameEn(),
			"user_sex" => $this->getUserSex(),
			"user_birthday" => $this->getUserBirthday(),
			"user_level" => $this->getUserLevel(),
			"user_honor" => $this->getUserHonor(),
			"honor_file" => $this->getHonorFile(),
			"card_name" => $this->getCardName(),
			"card_id" => $this->getCardId(),
			"card_file" => $this->getCardFile(),
			"user_home_phone" => $this->getUserHomePhone(),
			"user_mobile" => $this->getUserMobile(),
			"user_email" => $this->getUserEmail(),
			"user_fax" => $this->getUserFax(),
			"graduate_school" => $this->getGraduateSchool(),
			"subject" => $this->getSubject(),
			"graduate_date" => $this->getGraduateDate(),
			"subject_name" => $this->getSubjectName(),
			"work_unit" => $this->getWorkUnit(),
			"unit_area" => $this->getUnitArea(),
			"user_duty" => $this->getUserDuty(),
			"unit_phone" => $this->getUnitPhone(),
			"unit_address" => $this->getUnitAddress(),
			"unit_postal_code" => $this->getUnitPostalCode(),
			"user_work" => $this->getUserWork(),
			"department" => $this->getDepartment(),
			"unit_property" => $this->getUnitProperty(),
			"other_language" => $this->getOtherLanguage(),
			"language_level" => $this->getLanguageLevel(),
			"note" => $this->getNote(),
			"is_lock" => $this->getIsLock(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->user_name_en = '';
		$this->user_sex = '';
		$this->user_birthday = '';
		$this->user_level = '';
		$this->user_honor = '';
		$this->honor_file = '';
		$this->card_name = '';
		$this->card_id = '';
		$this->card_file = '';
		$this->user_home_phone = '';
		$this->user_mobile = '';
		$this->user_email = '';
		$this->user_fax = '';
		$this->graduate_school = '';
		$this->subject = '';
		$this->graduate_date = '';
		$this->subject_name = '';
		$this->work_unit = '';
		$this->unit_area = '';
		$this->user_duty = '';
		$this->unit_phone = '';
		$this->unit_address = '';
		$this->unit_postal_code = '';
		$this->user_work = '';
		$this->department = '';
		$this->unit_property = '';
		$this->other_language = '';
		$this->language_level = '';
		$this->note = '';
		$this->is_lock = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["user_name_en"]) && $this->user_name_en = $data["user_name_en"];
		isset($data["user_sex"]) && $this->user_sex = $data["user_sex"];
		isset($data["user_birthday"]) && $this->user_birthday = $data["user_birthday"];
		isset($data["user_level"]) && $this->user_level = $data["user_level"];
		isset($data["user_honor"]) && $this->user_honor = $data["user_honor"];
		isset($data["honor_file"]) && $this->honor_file = $data["honor_file"];
		isset($data["card_name"]) && $this->card_name = $data["card_name"];
		isset($data["card_id"]) && $this->card_id = $data["card_id"];
		isset($data["card_file"]) && $this->card_file = $data["card_file"];
		isset($data["user_home_phone"]) && $this->user_home_phone = $data["user_home_phone"];
		isset($data["user_mobile"]) && $this->user_mobile = $data["user_mobile"];
		isset($data["user_email"]) && $this->user_email = $data["user_email"];
		isset($data["user_fax"]) && $this->user_fax = $data["user_fax"];
		isset($data["graduate_school"]) && $this->graduate_school = $data["graduate_school"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["graduate_date"]) && $this->graduate_date = $data["graduate_date"];
		isset($data["subject_name"]) && $this->subject_name = $data["subject_name"];
		isset($data["work_unit"]) && $this->work_unit = $data["work_unit"];
		isset($data["unit_area"]) && $this->unit_area = $data["unit_area"];
		isset($data["user_duty"]) && $this->user_duty = $data["user_duty"];
		isset($data["unit_phone"]) && $this->unit_phone = $data["unit_phone"];
		isset($data["unit_address"]) && $this->unit_address = $data["unit_address"];
		isset($data["unit_postal_code"]) && $this->unit_postal_code = $data["unit_postal_code"];
		isset($data["user_work"]) && $this->user_work = $data["user_work"];
		isset($data["department"]) && $this->department = $data["department"];
		isset($data["unit_property"]) && $this->unit_property = $data["unit_property"];
		isset($data["other_language"]) && $this->other_language = $data["other_language"];
		isset($data["language_level"]) && $this->language_level = $data["language_level"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["is_lock"]) && $this->is_lock = $data["is_lock"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}