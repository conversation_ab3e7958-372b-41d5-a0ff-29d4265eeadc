<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseRewardMoneys;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class RewardMoneys extends BaseRewardMoneys
{
    function selectByRewardIdAndCompanyId($rewardId,$companyId)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `reward_id` = '{$rewardId}' and company_id = '{$companyId}'");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else{
            $this->setRewardId($rewardId);
            $this->setCompanyId($companyId);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }
    function selectByRewardIdAndCompanyType($rewardId,$companyType='research')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `reward_id` = '{$rewardId}' and company_type = '{$companyType}'");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else{
            $this->setRewardId($rewardId);
            $this->setCompanyType($companyType);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }
    function selectManagementsByRewardId($rewardId)
    {
       return sf::getModel('RewardMoneys')->selectAll("reward_id = '{$rewardId}' and company_type = 'management'","order by id asc");
    }

    public function getMoney($unit='元')
    {
        return $unit=='万元' ? round(parent::getMoney()/10000,2) : parent::getMoney();
    }

    public function getOwnMoney($unit='元')
    {
        return $unit=='万元' ? round(parent::getOwnMoney()/10000,2) : parent::getOwnMoney();
    }
}