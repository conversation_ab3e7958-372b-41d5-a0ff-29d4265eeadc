<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseTaskLogs;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class TaskLogs extends BaseTaskLogs
{
    public function setExecutorName($v)
    {
        $personnel = sf::getModel('Personnels')->selectByUserId($v);
        parent::setExecutorName($personnel->getName());
    }
    public function setOfficeName($v)
    {
        $category = sf::getModel('Categorys',$v,'office');
        parent::setOfficeName($category->getSubject());
    }

    public function getStatement()
    {
        switch (parent::getStatus()){
            case 0:
                return '待处理';
                break;
            case 1:
                return '正在处理';
                break;
            case 2:
                return '已转交';
                break;
            case 3:
                return '<s style="color:red">拒绝接受</s>';
                break;
            case 10:
                return '已完成';
                break;
        }
    }

    public function getTaskNo()
    {
        return sf::getModel('Tasks',parent::getTaskId())->getTaskNo();
    }

    public function getUserName()
    {
        return sf::getModel('Tasks',parent::getTaskId())->getUserName();
    }

    public function getSubject()
    {
        return sf::getModel('Tasks',parent::getTaskId())->getSubject();
    }

    public function getExecutor()
    {
        if(parent::getType()==2){
            //部门
            return parent::getOfficeName();
        }else{
            return parent::getExecutorName();
        }
//        return sf::getModel('Tasks',parent::getTaskId())->getExecutor();
    }

    public function getExpectedAt($format='Y-m-d')
    {
        return sf::getModel('Tasks',parent::getTaskId())->getExpectedAt($format);
    }

    public function getAttachment()
    {
        $task_id = parent::getTaskId();
        $user_id = input::session('userid');
        $TaskAttachments = M('TaskAttachments');
        return $TaskAttachments->selectAll("`task_id` = '{$task_id}' and `user_id` = '{$user_id}'");
    }

    /**
     * 处理附件
     * @param $attachment_id 附件id
     */
    public function doAttachment($attachment_id = '')
    {
        if (empty($attachment_id)) return;
        $task_id = parent::getTaskId();
        $TaskAttachments = M('TaskAttachments');
        $attachment_ids = implode(',', $attachment_id);
        $userid = input::getInput('session.userid');
        foreach ($attachment_id as $v) {
            $attachment = sf::getModel('TaskAttachments', $v);
            $attachment->setTaskId(parent::getTaskId());
            $attachment->setUpdatedAt(date('Y-m-d H:i:s'));
            $attachment->save();
        }
        //删除多余的附件
        $TaskAttachments->remove("`task_id` is null and `user_id` = '{$userid}'");
    }

    /**
     * 判断是否有附件
     */
    public function hasAttachment()
    {
        $task_id = parent::getTaskId();
        $executor_id = parent::getExecutorId();     //执行人
        $count = sf::getModel('TaskAttachments')->selectAll("`task_id` = {$task_id} and `user_id` = '{$executor_id}'")->getTotal();
        return $count>0 ? true : false;
    }



    /**
     * 判断是否是我未完成的任务
     */
    public function isMyTask()
    {
        $userid = input::session('userid');
        $officeid = sf::getModel('Personnels')->selectByUserId($userid)->getOfficeId();
        if(($this->getExecutorId()==$userid || (!empty($officeid) && $this->getOfficeId()==$officeid)) && $this->getStatus()<2){
            return true;
        }
        return false;
    }

    /**
     * 获取当前用户及部门待处理的任务
     * @return int
     */
    public function getNewTasks()
    {
        $userid = input::session('userid');
        $where = "`executor_id` = '{$userid}'";
        $officeid = sf::getModel('Personnels')->selectByUserId($userid)->getOfficeId();
        if(!empty($officeid)) $where = "(".$where." or `office_id` = '{$officeid}')";
        $where.=" and status < 2";
        return $this->selectAll($where);
    }

    /**
     * 获取当前用户未完成任务数量
     * @return int
     */
    public function getNewTaskCount()
    {
        $userid = input::session('userid');
        $where = "`executor_id` = '{$userid}'";
        $where.=" and status < 2";
        return $this->selectAll($where)->getTotal();
    }

    /**
     * 获取当前用户所在部门未完成任务数量
     * @return int
     */
    public function getDeptTaskCount()
    {
        $userid = input::session('userid');
        $officeid = sf::getModel('Personnels')->selectByUserId($userid)->getOfficeId();
        if(!empty($officeid)){
            $where = "`office_id` = '{$officeid}' and status < 2";
            return $this->selectAll($where)->getTotal();
        }else{
            return 0;
        }
    }

    /**
     * 分配任务的人所在的部门id
     * @param $userid
     */
    public function setAssignerOfficeId($userid)
    {
        $officeid = sf::getModel('Personnels')->selectByUserId($userid)->getOfficeId();
        parent::setAssignerOfficeId($officeid);
    }

    /**
     * 执行任务的人所在的部门id
     * @param $userid
     */
    public function setExecutorOfficeId($userid)
    {
        $officeid = sf::getModel('Personnels')->selectByUserId($userid)->getOfficeId();
        parent::setExecutorOfficeId($officeid);
    }
}