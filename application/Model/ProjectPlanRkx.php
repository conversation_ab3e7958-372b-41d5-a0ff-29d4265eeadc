<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: ProjectPlanRkx.model.php 2 2012-05-11 07:12:52Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseProjectPlanRkx;
class ProjectPlanRkx extends BaseProjectPlanRkx
{
	function selectByProjectId($project_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setProjectId($project_id);
		return $this;
	}
	
	function setAnticipateFruits($v)
	{
		if(is_array($v)) parent::setAnticipateFruits(implode("|",$v));
		else parent::setAnticipateFruits($v);
	}
	
	function getAnticipateFruits($num=0)
	{
		$result = explode("|",parent::getAnticipateFruits());
		if($num) return $result[$num - 1];
		else return $result;
	}
	
	function setAnticipatePatent($v)
	{
		if(is_array($v)) parent::setAnticipatePatent(implode("|",$v));
		else parent::setAnticipatePatent($v);
	}
	
	function getAnticipatePatent($num=0)
	{
		$result = explode("|",parent::getAnticipatePatent());
		if($num) return $result[$num - 1];
		else return $result;
	}
	
	function getOutlay()
	{
		return unserialize(parent::getOutlay());
	}
	
	function setOutlay($v)
	{
		parent::setOutlay(serialize($v));
	}		
}