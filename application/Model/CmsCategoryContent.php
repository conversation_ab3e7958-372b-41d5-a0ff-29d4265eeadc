<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseCmsCategoryContent;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class CmsCategoryContent extends BaseCmsCategoryContent
{
	function getCidsByCatid($catid=[]){
		if(!$catid) return false;
		if(is_array($catid))
			$addWhere = "catid IN(".implode(',', $catid).")";
		else $addWhere = "catid = ".$catid;

		$arr = parent::selectAll($addWhere)->toArray();
		$arr = array_column($arr, 'cid');
		$arr = array_unique($arr);
		$arr = array_filter($arr);
		return $arr;
	}
}