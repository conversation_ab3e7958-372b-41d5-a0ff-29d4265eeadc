<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseBids extends BaseModel
{
	private $id;
	private $item_id;
	private $project_id;
	private $project_no;
	private $subject;
	private $corporation_id;
	private $corporation_name;
	private $bid_company;
	private $department_id;
	private $department_name;
	private $user_id;
	private $user_name;
	private $bid_mode;
	private $agency;
	private $amount  = '0.00';
	private $cash  = '0.00';
	private $is_outbid  = '0';
	private $note;
	private $post_at;
	private $apply_at;
	private $outbid_at;
	private $submit_at;
	private $statement  = '0';
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "bids";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getItemId()
	{
		return $this->item_id;
	}

	public function getProjectId()
	{
		return $this->project_id;
	}

	public function getProjectNo()
	{
		return $this->project_no;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function getCorporationId()
	{
		return $this->corporation_id;
	}

	public function getCorporationName()
	{
		return $this->corporation_name;
	}

	public function getBidCompany()
	{
		return $this->bid_company;
	}

	public function getDepartmentId()
	{
		return $this->department_id;
	}

	public function getDepartmentName()
	{
		return $this->department_name;
	}

	public function getUserId()
	{
		return $this->user_id;
	}

	public function getUserName()
	{
		return $this->user_name;
	}

	public function getBidMode()
	{
		return $this->bid_mode;
	}

	public function getAgency()
	{
		return $this->agency;
	}

	public function getAmount()
	{
		return $this->amount;
	}

	public function getCash()
	{
		return $this->cash;
	}

	public function getIsOutbid()
	{
		return $this->is_outbid;
	}

	public function getNote()
	{
		return $this->note;
	}

	public function getPostAt()
	{
		return $this->post_at;
	}

	public function getApplyAt()
	{
		return $this->apply_at;
	}

	public function getOutbidAt()
	{
		return $this->outbid_at;
	}

	public function getSubmitAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->submit_at));
		else return $this->submit_at;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setItemId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->item_id !== $v)
		{
			$this->item_id = $v;
			$this->fieldData["item_id"] = $v;
		}
		return $this;

	}

	public function setProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_id !== $v)
		{
			$this->project_id = $v;
			$this->fieldData["project_id"] = $v;
		}
		return $this;

	}

	public function setProjectNo($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_no !== $v)
		{
			$this->project_no = $v;
			$this->fieldData["project_no"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setCorporationId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_id !== $v)
		{
			$this->corporation_id = $v;
			$this->fieldData["corporation_id"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setBidCompany($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bid_company !== $v)
		{
			$this->bid_company = $v;
			$this->fieldData["bid_company"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setDepartmentName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_name !== $v)
		{
			$this->department_name = $v;
			$this->fieldData["department_name"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setBidMode($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bid_mode !== $v)
		{
			$this->bid_mode = $v;
			$this->fieldData["bid_mode"] = $v;
		}
		return $this;

	}

	public function setAgency($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->agency !== $v)
		{
			$this->agency = $v;
			$this->fieldData["agency"] = $v;
		}
		return $this;

	}

	public function setAmount($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->amount !== $v)
		{
			$this->amount = $v;
			$this->fieldData["amount"] = $v;
		}
		return $this;

	}

	public function setCash($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->cash !== $v)
		{
			$this->cash = $v;
			$this->fieldData["cash"] = $v;
		}
		return $this;

	}

	public function setIsOutbid($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_outbid !== $v)
		{
			$this->is_outbid = $v;
			$this->fieldData["is_outbid"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setPostAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->post_at !== $v)
		{
			$this->post_at = $v;
			$this->fieldData["post_at"] = $v;
		}
		return $this;

	}

	public function setApplyAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->apply_at !== $v)
		{
			$this->apply_at = $v;
			$this->fieldData["apply_at"] = $v;
		}
		return $this;

	}

	public function setOutbidAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->outbid_at !== $v)
		{
			$this->outbid_at = $v;
			$this->fieldData["outbid_at"] = $v;
		}
		return $this;

	}

	public function setSubmitAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->submit_at !== $v)
		{
			$this->submit_at = $v;
			$this->fieldData["submit_at"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `bids` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"item_id" => $this->getItemId(),
			"project_id" => $this->getProjectId(),
			"project_no" => $this->getProjectNo(),
			"subject" => $this->getSubject(),
			"corporation_id" => $this->getCorporationId(),
			"corporation_name" => $this->getCorporationName(),
			"bid_company" => $this->getBidCompany(),
			"department_id" => $this->getDepartmentId(),
			"department_name" => $this->getDepartmentName(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"bid_mode" => $this->getBidMode(),
			"agency" => $this->getAgency(),
			"amount" => $this->getAmount(),
			"cash" => $this->getCash(),
			"is_outbid" => $this->getIsOutbid(),
			"note" => $this->getNote(),
			"post_at" => $this->getPostAt(),
			"apply_at" => $this->getApplyAt(),
			"outbid_at" => $this->getOutbidAt(),
			"submit_at" => $this->getSubmitAt(),
			"statement" => $this->getStatement(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->item_id = '';
		$this->project_id = '';
		$this->project_no = '';
		$this->subject = '';
		$this->corporation_id = '';
		$this->corporation_name = '';
		$this->bid_company = '';
		$this->department_id = '';
		$this->department_name = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->bid_mode = '';
		$this->agency = '';
		$this->amount = '';
		$this->cash = '';
		$this->is_outbid = '';
		$this->note = '';
		$this->post_at = '';
		$this->apply_at = '';
		$this->outbid_at = '';
		$this->submit_at = '';
		$this->statement = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["item_id"]) && $this->item_id = $data["item_id"];
		isset($data["project_id"]) && $this->project_id = $data["project_id"];
		isset($data["project_no"]) && $this->project_no = $data["project_no"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["bid_company"]) && $this->bid_company = $data["bid_company"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["department_name"]) && $this->department_name = $data["department_name"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["bid_mode"]) && $this->bid_mode = $data["bid_mode"];
		isset($data["agency"]) && $this->agency = $data["agency"];
		isset($data["amount"]) && $this->amount = $data["amount"];
		isset($data["cash"]) && $this->cash = $data["cash"];
		isset($data["is_outbid"]) && $this->is_outbid = $data["is_outbid"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["post_at"]) && $this->post_at = $data["post_at"];
		isset($data["apply_at"]) && $this->apply_at = $data["apply_at"];
		isset($data["outbid_at"]) && $this->outbid_at = $data["outbid_at"];
		isset($data["submit_at"]) && $this->submit_at = $data["submit_at"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}