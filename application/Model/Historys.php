<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Historys.model.php 41 2012-05-25 03:01:13Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Model\BaseHistorys;
class Historys extends BaseHistorys
{
	/**
	 * 选取指定ID的历史记录
	 */ 
	function selectByProjectId($project_id='',$showMax=20)
	{
		if(in_array(input::getInput("session.userlevel"),array('1','5','6','7','8')))
			$addWhere = "project_id = '".$project_id."' ";
		elseif(in_array(input::getInput("session.userlevel"),array('40','41','42','43','44')))
			$addWhere = "project_id = '".$project_id."' AND (is_hiden = 0 OR is_hiden = 9) ";
		else $addWhere = "project_id = '".$project_id."' AND is_hiden = 0";
		return $this->getPager($addWhere,'ORDER BY `updated_at` DESC,`id` DESC',$showMax);
	}
	/**
	 * 选取指定ID的历史记录
	 */
	function selectByProjectIdAndType($project_id='',$type='project',$showMax=20)
	{
		$addWhere = "project_id = '{$project_id}' and type = '{$type}'";
		return $this->getPager($addWhere,'ORDER BY `updated_at` DESC,`id` DESC',$showMax);
	}

	function selectByContent($project_id='',$content='',$showMax=20)
	{
		$addWhere = "project_id = '".$project_id."' "."AND content = '".$content."'";
		return $this->getPager($addWhere,'ORDER BY `updated_at` DESC,`id` DESC',$showMax);
	}

	function selectByDay($project_id='',$type='project',$start=0,$length=3){
		$addWhere = "project_id = '{$project_id}' and type = '{$type}'";

		$result=[];
		$db = sf::getLib('DB');
		$query = $db->query("SELECT DATE_FORMAT(updated_at,'%Y-%m-%d') as d,any_value(updated_at) updated_at from ".$this->table." where project_id = '".$project_id."' and `type` = '".$type."' group by d  ORDER BY updated_at DESC ");

		$count = $db->num_rows($query);
		for($i=0;$i<$count;$i++){
			$temp = $db->fetch_array($query);
			$result[] = $temp['d'];
		}

		$date = array_slice($result,$start,$length);
		
		$addWhere .= " and DATE_FORMAT(updated_at,'%Y-%m-%d') IN('".implode("','",$date)."') ";

		$data['days'] = $date;
		$data['pager'] = $this->getPager($addWhere,'ORDER BY `updated_at` DESC,`id` DESC',0);
		return $data;
	}
	
	function addHistory($project_id='',$content='',$type='project',$is_hiden=0)
	{
		$this->setUserId(input::getInput("session.userid"));
		$this->setUserGroupId(input::getInput("session.userlevel"));
        $this->setUserName(input::getInput("session.nickname"));
		$this->setType($type);
		$this->setProjectId($project_id);
		$this->setContent($content);
		$this->setUserIp(input::getIp());
		$this->setIsHiden($is_hiden);
		$this->setUpdatedAt(date("Y-m-d H:i:s"));
		$this->save();
	}
	
	/**
	*读取指定ID的项目名称
	*/
	function getProjectSubject()
	{
		$id = parent::getProjectId();
		if($id == 'system') return lang::get("System!!");
		else return sf::getModel("projects")->selectByProjectId($id)->getSubject();
	}
	/**
	*读取操作用户组的名称
	*/
	function getUserGroupName()
	{
	    $groupName = sf::getModel("UserGroups",parent::getUserGroupId())->getUserGroupName();
        if(parent::getUserGroupId()==2 && parent::getType()=='project'){
            $project = sf::getModel('Projects')->selectByProjectId(parent::getProjectId());
            if($project->getUserId()==parent::getUserId()) $groupName = '项目负责人';
        }
	    if(in_array(parent::getUserGroupId(),[3,4])){
	        $user = sf::getModel('Users')->selectByUserId(parent::getUserId());
	        $company = sf::getModel('Corporations')->selectByUserId($user->getRoleUserId(3));
	        if(!$company->isNew()){
	            return $groupName.'（'.$company->getSubject().'）';
            }
        }
		return $groupName;
	}
	/**
	*读取操作用户组的名称
	*/
	function getUserGroupIcon()
	{
		return sf::getModel("UserGroups",parent::getUserGroupId())->getIcon();
	}
	
	/**
	*读取用户名
	*/
	function getUserNameById()
	{
		$corporation = sf::getModel("Corporations")->selectByUserId(parent::getUserId());
		return $corporation->getSubject()?:'';
	}
}
?>