<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseChartSeries extends BaseModel
{
	private $id  = '';
	private $chart_id  = '';
	private $serie_id  = '';
	private $y_axis  = '';
	private $setting  = '';
	public $table = "chart_series";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getChartId()
	{
		return $this->chart_id;
	}

	public function getSerieId()
	{
		return $this->serie_id;
	}

	public function getYAxis($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->y_axis,0,$len,"utf-8");
			else return substr($this->y_axis,0,$len);
		}
		return $this->y_axis;
	}

	public function getSetting($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->setting,0,$len,"utf-8");
			else return substr($this->setting,0,$len);
		}
		return $this->setting;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setChartId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->chart_id !== $v)
		{
			$this->chart_id = $v;
			$this->fieldData["chart_id"] = $v;
		}
		return $this;

	}

	public function setSerieId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->serie_id !== $v)
		{
			$this->serie_id = $v;
			$this->fieldData["serie_id"] = $v;
		}
		return $this;

	}

	public function setYAxis($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->y_axis !== $v)
		{
			$this->y_axis = $v;
			$this->fieldData["y_axis"] = $v;
		}
		return $this;

	}

	public function setSetting($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->setting !== $v)
		{
			$this->setting = $v;
			$this->fieldData["setting"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `chart_series` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"chart_id" => $this->getChartId(),
			"serie_id" => $this->getSerieId(),
			"y_axis" => $this->getYAxis(),
			"setting" => $this->getSetting(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->chart_id = '';
		$this->serie_id = '';
		$this->y_axis = '';
		$this->setting = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["chart_id"]) && $this->chart_id = $data["chart_id"];
		isset($data["serie_id"]) && $this->serie_id = $data["serie_id"];
		isset($data["y_axis"]) && $this->y_axis = $data["y_axis"];
		isset($data["setting"]) && $this->setting = $data["setting"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}