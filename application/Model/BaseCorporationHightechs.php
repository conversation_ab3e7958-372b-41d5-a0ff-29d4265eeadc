<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseCorporationHightechs extends BaseModel
{
	private $id;
	private $user_id;
	private $subject;
	private $year;
	private $register_at;
	private $level;
	private $department;
	private $main;
	private $zcze1;
	private $zcze2;
	private $zcze3;
	private $zcze4;
	private $jzc1;
	private $jzc2;
	private $jzc3;
	private $jzc4;
	private $yysr1;
	private $yysr2;
	private $yysr3;
	private $yysr4;
	private $lrze1;
	private $lrze2;
	private $lrze3;
	private $lrze4;
	private $yftr1;
	private $yftr2;
	private $yftr3;
	private $yftr4;
	private $fmzl;
	private $syxx;
	private $wgsj;
	private $rjzzq;
	private $pass;
	private $confirm_at;
	private $linkman;
	private $phone;
	private $created_at;
	private $updated_at;
	public $table = "corporation_hightechs";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId()
	{
		return $this->user_id;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function getYear()
	{
		return $this->year;
	}

	public function getRegisterAt()
	{
		return $this->register_at;
	}

	public function getLevel()
	{
		return $this->level;
	}

	public function getDepartment()
	{
		return $this->department;
	}

	public function getMain()
	{
		return $this->main;
	}

	public function getZcze1()
	{
		return $this->zcze1;
	}

	public function getZcze2()
	{
		return $this->zcze2;
	}

	public function getZcze3()
	{
		return $this->zcze3;
	}

	public function getZcze4()
	{
		return $this->zcze4;
	}

	public function getJzc1()
	{
		return $this->jzc1;
	}

	public function getJzc2()
	{
		return $this->jzc2;
	}

	public function getJzc3()
	{
		return $this->jzc3;
	}

	public function getJzc4()
	{
		return $this->jzc4;
	}

	public function getYysr1()
	{
		return $this->yysr1;
	}

	public function getYysr2()
	{
		return $this->yysr2;
	}

	public function getYysr3()
	{
		return $this->yysr3;
	}

	public function getYysr4()
	{
		return $this->yysr4;
	}

	public function getLrze1()
	{
		return $this->lrze1;
	}

	public function getLrze2()
	{
		return $this->lrze2;
	}

	public function getLrze3()
	{
		return $this->lrze3;
	}

	public function getLrze4()
	{
		return $this->lrze4;
	}

	public function getYftr1()
	{
		return $this->yftr1;
	}

	public function getYftr2()
	{
		return $this->yftr2;
	}

	public function getYftr3()
	{
		return $this->yftr3;
	}

	public function getYftr4()
	{
		return $this->yftr4;
	}

	public function getFmzl()
	{
		return $this->fmzl;
	}

	public function getSyxx()
	{
		return $this->syxx;
	}

	public function getWgsj()
	{
		return $this->wgsj;
	}

	public function getRjzzq()
	{
		return $this->rjzzq;
	}

	public function getPass()
	{
		return $this->pass;
	}

	public function getConfirmAt()
	{
		return $this->confirm_at;
	}

	public function getLinkman()
	{
		return $this->linkman;
	}

	public function getPhone()
	{
		return $this->phone;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setYear($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->year !== $v)
		{
			$this->year = $v;
			$this->fieldData["year"] = $v;
		}
		return $this;

	}

	public function setRegisterAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->register_at !== $v)
		{
			$this->register_at = $v;
			$this->fieldData["register_at"] = $v;
		}
		return $this;

	}

	public function setLevel($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->level !== $v)
		{
			$this->level = $v;
			$this->fieldData["level"] = $v;
		}
		return $this;

	}

	public function setDepartment($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department !== $v)
		{
			$this->department = $v;
			$this->fieldData["department"] = $v;
		}
		return $this;

	}

	public function setMain($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->main !== $v)
		{
			$this->main = $v;
			$this->fieldData["main"] = $v;
		}
		return $this;

	}

	public function setZcze1($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zcze1 !== $v)
		{
			$this->zcze1 = $v;
			$this->fieldData["zcze1"] = $v;
		}
		return $this;

	}

	public function setZcze2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zcze2 !== $v)
		{
			$this->zcze2 = $v;
			$this->fieldData["zcze2"] = $v;
		}
		return $this;

	}

	public function setZcze3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zcze3 !== $v)
		{
			$this->zcze3 = $v;
			$this->fieldData["zcze3"] = $v;
		}
		return $this;

	}

	public function setZcze4($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->zcze4 !== $v)
		{
			$this->zcze4 = $v;
			$this->fieldData["zcze4"] = $v;
		}
		return $this;

	}

	public function setJzc1($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jzc1 !== $v)
		{
			$this->jzc1 = $v;
			$this->fieldData["jzc1"] = $v;
		}
		return $this;

	}

	public function setJzc2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jzc2 !== $v)
		{
			$this->jzc2 = $v;
			$this->fieldData["jzc2"] = $v;
		}
		return $this;

	}

	public function setJzc3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jzc3 !== $v)
		{
			$this->jzc3 = $v;
			$this->fieldData["jzc3"] = $v;
		}
		return $this;

	}

	public function setJzc4($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jzc4 !== $v)
		{
			$this->jzc4 = $v;
			$this->fieldData["jzc4"] = $v;
		}
		return $this;

	}

	public function setYysr1($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yysr1 !== $v)
		{
			$this->yysr1 = $v;
			$this->fieldData["yysr1"] = $v;
		}
		return $this;

	}

	public function setYysr2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yysr2 !== $v)
		{
			$this->yysr2 = $v;
			$this->fieldData["yysr2"] = $v;
		}
		return $this;

	}

	public function setYysr3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yysr3 !== $v)
		{
			$this->yysr3 = $v;
			$this->fieldData["yysr3"] = $v;
		}
		return $this;

	}

	public function setYysr4($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yysr4 !== $v)
		{
			$this->yysr4 = $v;
			$this->fieldData["yysr4"] = $v;
		}
		return $this;

	}

	public function setLrze1($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lrze1 !== $v)
		{
			$this->lrze1 = $v;
			$this->fieldData["lrze1"] = $v;
		}
		return $this;

	}

	public function setLrze2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lrze2 !== $v)
		{
			$this->lrze2 = $v;
			$this->fieldData["lrze2"] = $v;
		}
		return $this;

	}

	public function setLrze3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lrze3 !== $v)
		{
			$this->lrze3 = $v;
			$this->fieldData["lrze3"] = $v;
		}
		return $this;

	}

	public function setLrze4($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lrze4 !== $v)
		{
			$this->lrze4 = $v;
			$this->fieldData["lrze4"] = $v;
		}
		return $this;

	}

	public function setYftr1($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yftr1 !== $v)
		{
			$this->yftr1 = $v;
			$this->fieldData["yftr1"] = $v;
		}
		return $this;

	}

	public function setYftr2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yftr2 !== $v)
		{
			$this->yftr2 = $v;
			$this->fieldData["yftr2"] = $v;
		}
		return $this;

	}

	public function setYftr3($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yftr3 !== $v)
		{
			$this->yftr3 = $v;
			$this->fieldData["yftr3"] = $v;
		}
		return $this;

	}

	public function setYftr4($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yftr4 !== $v)
		{
			$this->yftr4 = $v;
			$this->fieldData["yftr4"] = $v;
		}
		return $this;

	}

	public function setFmzl($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->fmzl !== $v)
		{
			$this->fmzl = $v;
			$this->fieldData["fmzl"] = $v;
		}
		return $this;

	}

	public function setSyxx($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->syxx !== $v)
		{
			$this->syxx = $v;
			$this->fieldData["syxx"] = $v;
		}
		return $this;

	}

	public function setWgsj($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->wgsj !== $v)
		{
			$this->wgsj = $v;
			$this->fieldData["wgsj"] = $v;
		}
		return $this;

	}

	public function setRjzzq($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->rjzzq !== $v)
		{
			$this->rjzzq = $v;
			$this->fieldData["rjzzq"] = $v;
		}
		return $this;

	}

	public function setPass($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->pass !== $v)
		{
			$this->pass = $v;
			$this->fieldData["pass"] = $v;
		}
		return $this;

	}

	public function setConfirmAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->confirm_at !== $v)
		{
			$this->confirm_at = $v;
			$this->fieldData["confirm_at"] = $v;
		}
		return $this;

	}

	public function setLinkman($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->linkman !== $v)
		{
			$this->linkman = $v;
			$this->fieldData["linkman"] = $v;
		}
		return $this;

	}

	public function setPhone($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->phone !== $v)
		{
			$this->phone = $v;
			$this->fieldData["phone"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `corporation_hightechs` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"subject" => $this->getSubject(),
			"year" => $this->getYear(),
			"register_at" => $this->getRegisterAt(),
			"level" => $this->getLevel(),
			"department" => $this->getDepartment(),
			"main" => $this->getMain(),
			"zcze1" => $this->getZcze1(),
			"zcze2" => $this->getZcze2(),
			"zcze3" => $this->getZcze3(),
			"zcze4" => $this->getZcze4(),
			"jzc1" => $this->getJzc1(),
			"jzc2" => $this->getJzc2(),
			"jzc3" => $this->getJzc3(),
			"jzc4" => $this->getJzc4(),
			"yysr1" => $this->getYysr1(),
			"yysr2" => $this->getYysr2(),
			"yysr3" => $this->getYysr3(),
			"yysr4" => $this->getYysr4(),
			"lrze1" => $this->getLrze1(),
			"lrze2" => $this->getLrze2(),
			"lrze3" => $this->getLrze3(),
			"lrze4" => $this->getLrze4(),
			"yftr1" => $this->getYftr1(),
			"yftr2" => $this->getYftr2(),
			"yftr3" => $this->getYftr3(),
			"yftr4" => $this->getYftr4(),
			"fmzl" => $this->getFmzl(),
			"syxx" => $this->getSyxx(),
			"wgsj" => $this->getWgsj(),
			"rjzzq" => $this->getRjzzq(),
			"pass" => $this->getPass(),
			"confirm_at" => $this->getConfirmAt(),
			"linkman" => $this->getLinkman(),
			"phone" => $this->getPhone(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->subject = '';
		$this->year = '';
		$this->register_at = '';
		$this->level = '';
		$this->department = '';
		$this->main = '';
		$this->zcze1 = '';
		$this->zcze2 = '';
		$this->zcze3 = '';
		$this->zcze4 = '';
		$this->jzc1 = '';
		$this->jzc2 = '';
		$this->jzc3 = '';
		$this->jzc4 = '';
		$this->yysr1 = '';
		$this->yysr2 = '';
		$this->yysr3 = '';
		$this->yysr4 = '';
		$this->lrze1 = '';
		$this->lrze2 = '';
		$this->lrze3 = '';
		$this->lrze4 = '';
		$this->yftr1 = '';
		$this->yftr2 = '';
		$this->yftr3 = '';
		$this->yftr4 = '';
		$this->fmzl = '';
		$this->syxx = '';
		$this->wgsj = '';
		$this->rjzzq = '';
		$this->pass = '';
		$this->confirm_at = '';
		$this->linkman = '';
		$this->phone = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["year"]) && $this->year = $data["year"];
		isset($data["register_at"]) && $this->register_at = $data["register_at"];
		isset($data["level"]) && $this->level = $data["level"];
		isset($data["department"]) && $this->department = $data["department"];
		isset($data["main"]) && $this->main = $data["main"];
		isset($data["zcze1"]) && $this->zcze1 = $data["zcze1"];
		isset($data["zcze2"]) && $this->zcze2 = $data["zcze2"];
		isset($data["zcze3"]) && $this->zcze3 = $data["zcze3"];
		isset($data["zcze4"]) && $this->zcze4 = $data["zcze4"];
		isset($data["jzc1"]) && $this->jzc1 = $data["jzc1"];
		isset($data["jzc2"]) && $this->jzc2 = $data["jzc2"];
		isset($data["jzc3"]) && $this->jzc3 = $data["jzc3"];
		isset($data["jzc4"]) && $this->jzc4 = $data["jzc4"];
		isset($data["yysr1"]) && $this->yysr1 = $data["yysr1"];
		isset($data["yysr2"]) && $this->yysr2 = $data["yysr2"];
		isset($data["yysr3"]) && $this->yysr3 = $data["yysr3"];
		isset($data["yysr4"]) && $this->yysr4 = $data["yysr4"];
		isset($data["lrze1"]) && $this->lrze1 = $data["lrze1"];
		isset($data["lrze2"]) && $this->lrze2 = $data["lrze2"];
		isset($data["lrze3"]) && $this->lrze3 = $data["lrze3"];
		isset($data["lrze4"]) && $this->lrze4 = $data["lrze4"];
		isset($data["yftr1"]) && $this->yftr1 = $data["yftr1"];
		isset($data["yftr2"]) && $this->yftr2 = $data["yftr2"];
		isset($data["yftr3"]) && $this->yftr3 = $data["yftr3"];
		isset($data["yftr4"]) && $this->yftr4 = $data["yftr4"];
		isset($data["fmzl"]) && $this->fmzl = $data["fmzl"];
		isset($data["syxx"]) && $this->syxx = $data["syxx"];
		isset($data["wgsj"]) && $this->wgsj = $data["wgsj"];
		isset($data["rjzzq"]) && $this->rjzzq = $data["rjzzq"];
		isset($data["pass"]) && $this->pass = $data["pass"];
		isset($data["confirm_at"]) && $this->confirm_at = $data["confirm_at"];
		isset($data["linkman"]) && $this->linkman = $data["linkman"];
		isset($data["phone"]) && $this->phone = $data["phone"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}