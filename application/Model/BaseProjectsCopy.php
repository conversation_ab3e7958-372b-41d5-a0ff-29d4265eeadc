<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseProjectsCopy extends BaseModel
{
	private $id  = '';
	private $type_id  = '';
	private $cat_id  = '';
	private $guide_id  = '';
	private $project_id  = '';
	private $main_project_id  = '';
	private $corporation_id  = '';
	private $user_id  = '';
	private $department_id  = '';
	private $type_current_group  = '';
	private $accept_id  = '';
	private $radicate_id  = '';
	private $subject  = '';
	private $declare_year  = '';
	private $declare_at  = '';
	private $declare_money  = '';
	private $total_money  = '';
	private $subtitle  = '';
	private $end_at  = '';
	private $start_at  = '';
	private $real_start_at  = '';
	private $real_end_at  = '';
	private $subject_name  = '';
	private $subject_id  = '';
	private $subject_id2  = '';
	private $subject_name2  = '';
	private $industry_id  = '';
	private $industry_id2  = '';
	private $industry_name  = '';
	private $industry_name2  = '';
	private $skill_domain  = '';
	private $radicate_year  = '';
	private $radicate_at  = '';
	private $radicate_money  = '';
	private $payfor_num  = '';
	private $statement  = '';
	private $office_id  = '';
	private $group_id  = '';
	private $budget_group_id  = '';
	private $type_group  = '';
	private $user_name  = '';
	private $corporation_name  = '';
	private $department_name  = '';
	private $cooperation  = '';
	private $office_subject  = '';
	private $type_subject  = '';
	private $score  = '';
	private $score_complete  = '';
	private $summary  = '';
	private $budget_score  = '';
	private $has_expert  = '';
	private $state_for_budget_book  = '';
	private $state_for_plan_book  = '';
	private $state_for_complete_book  = '';
	private $state_for_audit  = '';
	private $state_for_interimreport  = '';
	private $state_for_datum  = '';
	private $updated_at  = '';
	private $budget_open  = '';
	private $task_open  = '';
	private $is_wait  = '';
	private $is_shift  = '';
	private $is_package  = '';
	private $is_audit  = '';
	private $is_error  = '';
	private $configs  = '';
	private $file_path  = '';
	private $created_at  = '';
	private $category_id  = '';
	private $category_name  = '';
	private $brief  = '';
	public $table = "projects_copy";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getTypeId()
	{
		return $this->type_id;
	}

	public function getCatId()
	{
		return $this->cat_id;
	}

	public function getGuideId()
	{
		return $this->guide_id;
	}

	public function getProjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->project_id,0,$len,"utf-8");
			else return substr($this->project_id,0,$len);
		}
		return $this->project_id;
	}

	public function getMainProjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->main_project_id,0,$len,"utf-8");
			else return substr($this->main_project_id,0,$len);
		}
		return $this->main_project_id;
	}

	public function getCorporationId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_id,0,$len,"utf-8");
			else return substr($this->corporation_id,0,$len);
		}
		return $this->corporation_id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getDepartmentId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_id,0,$len,"utf-8");
			else return substr($this->department_id,0,$len);
		}
		return $this->department_id;
	}

	public function getTypeCurrentGroup()
	{
		return $this->type_current_group;
	}

	public function getAcceptId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->accept_id,0,$len,"utf-8");
			else return substr($this->accept_id,0,$len);
		}
		return $this->accept_id;
	}

	public function getRadicateId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->radicate_id,0,$len,"utf-8");
			else return substr($this->radicate_id,0,$len);
		}
		return $this->radicate_id;
	}

	public function getSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject,0,$len,"utf-8");
			else return substr($this->subject,0,$len);
		}
		return $this->subject;
	}

	public function getDeclareYear($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->declare_year,0,$len,"utf-8");
			else return substr($this->declare_year,0,$len);
		}
		return $this->declare_year;
	}

	public function getDeclareAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->declare_at));
		else return $this->declare_at;
	}

	public function getDeclareMoney($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->declare_money,0,$len,"utf-8");
			else return substr($this->declare_money,0,$len);
		}
		return $this->declare_money;
	}

	public function getTotalMoney($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->total_money,0,$len,"utf-8");
			else return substr($this->total_money,0,$len);
		}
		return $this->total_money;
	}

	public function getSubtitle($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subtitle,0,$len,"utf-8");
			else return substr($this->subtitle,0,$len);
		}
		return $this->subtitle;
	}

	public function getEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->end_at,0,$len,"utf-8");
			else return substr($this->end_at,0,$len);
		}
		return $this->end_at;
	}

	public function getStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->start_at,0,$len,"utf-8");
			else return substr($this->start_at,0,$len);
		}
		return $this->start_at;
	}

	public function getRealStartAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->real_start_at,0,$len,"utf-8");
			else return substr($this->real_start_at,0,$len);
		}
		return $this->real_start_at;
	}

	public function getRealEndAt($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->real_end_at,0,$len,"utf-8");
			else return substr($this->real_end_at,0,$len);
		}
		return $this->real_end_at;
	}

	public function getSubjectName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name,0,$len,"utf-8");
			else return substr($this->subject_name,0,$len);
		}
		return $this->subject_name;
	}

	public function getSubjectId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_id,0,$len,"utf-8");
			else return substr($this->subject_id,0,$len);
		}
		return $this->subject_id;
	}

	public function getSubjectId2($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_id2,0,$len,"utf-8");
			else return substr($this->subject_id2,0,$len);
		}
		return $this->subject_id2;
	}

	public function getSubjectName2($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->subject_name2,0,$len,"utf-8");
			else return substr($this->subject_name2,0,$len);
		}
		return $this->subject_name2;
	}

	public function getIndustryId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry_id,0,$len,"utf-8");
			else return substr($this->industry_id,0,$len);
		}
		return $this->industry_id;
	}

	public function getIndustryId2($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry_id2,0,$len,"utf-8");
			else return substr($this->industry_id2,0,$len);
		}
		return $this->industry_id2;
	}

	public function getIndustryName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry_name,0,$len,"utf-8");
			else return substr($this->industry_name,0,$len);
		}
		return $this->industry_name;
	}

	public function getIndustryName2($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->industry_name2,0,$len,"utf-8");
			else return substr($this->industry_name2,0,$len);
		}
		return $this->industry_name2;
	}

	public function getSkillDomain($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->skill_domain,0,$len,"utf-8");
			else return substr($this->skill_domain,0,$len);
		}
		return $this->skill_domain;
	}

	public function getRadicateYear()
	{
		return $this->radicate_year;
	}

	public function getRadicateAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->radicate_at));
		else return $this->radicate_at;
	}

	public function getRadicateMoney($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->radicate_money,0,$len,"utf-8");
			else return substr($this->radicate_money,0,$len);
		}
		return $this->radicate_money;
	}

	public function getPayforNum()
	{
		return $this->payfor_num;
	}

	public function getStatement()
	{
		return $this->statement;
	}

	public function getOfficeId()
	{
		return $this->office_id;
	}

	public function getGroupId()
	{
		return $this->group_id;
	}

	public function getBudgetGroupId()
	{
		return $this->budget_group_id;
	}

	public function getTypeGroup($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type_group,0,$len,"utf-8");
			else return substr($this->type_group,0,$len);
		}
		return $this->type_group;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getCorporationName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->corporation_name,0,$len,"utf-8");
			else return substr($this->corporation_name,0,$len);
		}
		return $this->corporation_name;
	}

	public function getDepartmentName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->department_name,0,$len,"utf-8");
			else return substr($this->department_name,0,$len);
		}
		return $this->department_name;
	}

	public function getCooperation($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->cooperation,0,$len,"utf-8");
			else return substr($this->cooperation,0,$len);
		}
		return $this->cooperation;
	}

	public function getOfficeSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->office_subject,0,$len,"utf-8");
			else return substr($this->office_subject,0,$len);
		}
		return $this->office_subject;
	}

	public function getTypeSubject($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->type_subject,0,$len,"utf-8");
			else return substr($this->type_subject,0,$len);
		}
		return $this->type_subject;
	}

	public function getScore($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->score,0,$len,"utf-8");
			else return substr($this->score,0,$len);
		}
		return $this->score;
	}

	public function getScoreComplete($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->score_complete,0,$len,"utf-8");
			else return substr($this->score_complete,0,$len);
		}
		return $this->score_complete;
	}

	public function getSummary($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->summary,0,$len,"utf-8");
			else return substr($this->summary,0,$len);
		}
		return $this->summary;
	}

	public function getBudgetScore()
	{
		return $this->budget_score;
	}

	public function getHasExpert()
	{
		return $this->has_expert;
	}

	public function getStateForBudgetBook()
	{
		return $this->state_for_budget_book;
	}

	public function getStateForPlanBook()
	{
		return $this->state_for_plan_book;
	}

	public function getStateForCompleteBook()
	{
		return $this->state_for_complete_book;
	}

	public function getStateForAudit()
	{
		return $this->state_for_audit;
	}

	public function getStateForInterimreport()
	{
		return $this->state_for_interimreport;
	}

	public function getStateForDatum()
	{
		return $this->state_for_datum;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function getBudgetOpen()
	{
		return $this->budget_open;
	}

	public function getTaskOpen()
	{
		return $this->task_open;
	}

	public function getIsWait()
	{
		return $this->is_wait;
	}

	public function getIsShift()
	{
		return $this->is_shift;
	}

	public function getIsPackage()
	{
		return $this->is_package;
	}

	public function getIsAudit()
	{
		return $this->is_audit;
	}

	public function getIsError()
	{
		return $this->is_error;
	}

	public function getConfigs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->configs,0,$len,"utf-8");
			else return substr($this->configs,0,$len);
		}
		return $this->configs;
	}

	public function getFilePath($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->file_path,0,$len,"utf-8");
			else return substr($this->file_path,0,$len);
		}
		return $this->file_path;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getCategoryId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->category_id,0,$len,"utf-8");
			else return substr($this->category_id,0,$len);
		}
		return $this->category_id;
	}

	public function getCategoryName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->category_name,0,$len,"utf-8");
			else return substr($this->category_name,0,$len);
		}
		return $this->category_name;
	}

	public function getBrief($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->brief,0,$len,"utf-8");
			else return substr($this->brief,0,$len);
		}
		return $this->brief;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setTypeId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->type_id !== $v)
		{
			$this->type_id = $v;
			$this->fieldData["type_id"] = $v;
		}
		return $this;

	}

	public function setCatId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->cat_id !== $v)
		{
			$this->cat_id = $v;
			$this->fieldData["cat_id"] = $v;
		}
		return $this;

	}

	public function setGuideId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->guide_id !== $v)
		{
			$this->guide_id = $v;
			$this->fieldData["guide_id"] = $v;
		}
		return $this;

	}

	public function setProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_id !== $v)
		{
			$this->project_id = $v;
			$this->fieldData["project_id"] = $v;
		}
		return $this;

	}

	public function setMainProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->main_project_id !== $v)
		{
			$this->main_project_id = $v;
			$this->fieldData["main_project_id"] = $v;
		}
		return $this;

	}

	public function setCorporationId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_id !== $v)
		{
			$this->corporation_id = $v;
			$this->fieldData["corporation_id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setDepartmentId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_id !== $v)
		{
			$this->department_id = $v;
			$this->fieldData["department_id"] = $v;
		}
		return $this;

	}

	public function setTypeCurrentGroup($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->type_current_group !== $v)
		{
			$this->type_current_group = $v;
			$this->fieldData["type_current_group"] = $v;
		}
		return $this;

	}

	public function setAcceptId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->accept_id !== $v)
		{
			$this->accept_id = $v;
			$this->fieldData["accept_id"] = $v;
		}
		return $this;

	}

	public function setRadicateId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->radicate_id !== $v)
		{
			$this->radicate_id = $v;
			$this->fieldData["radicate_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setDeclareYear($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->declare_year !== $v)
		{
			$this->declare_year = $v;
			$this->fieldData["declare_year"] = $v;
		}
		return $this;

	}

	public function setDeclareAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->declare_at !== $v)
		{
			$this->declare_at = $v;
			$this->fieldData["declare_at"] = $v;
		}
		return $this;

	}

	public function setDeclareMoney($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->declare_money !== $v)
		{
			$this->declare_money = $v;
			$this->fieldData["declare_money"] = $v;
		}
		return $this;

	}

	public function setTotalMoney($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->total_money !== $v)
		{
			$this->total_money = $v;
			$this->fieldData["total_money"] = $v;
		}
		return $this;

	}

	public function setSubtitle($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subtitle !== $v)
		{
			$this->subtitle = $v;
			$this->fieldData["subtitle"] = $v;
		}
		return $this;

	}

	public function setEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->end_at !== $v)
		{
			$this->end_at = $v;
			$this->fieldData["end_at"] = $v;
		}
		return $this;

	}

	public function setStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->start_at !== $v)
		{
			$this->start_at = $v;
			$this->fieldData["start_at"] = $v;
		}
		return $this;

	}

	public function setRealStartAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->real_start_at !== $v)
		{
			$this->real_start_at = $v;
			$this->fieldData["real_start_at"] = $v;
		}
		return $this;

	}

	public function setRealEndAt($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->real_end_at !== $v)
		{
			$this->real_end_at = $v;
			$this->fieldData["real_end_at"] = $v;
		}
		return $this;

	}

	public function setSubjectName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name !== $v)
		{
			$this->subject_name = $v;
			$this->fieldData["subject_name"] = $v;
		}
		return $this;

	}

	public function setSubjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_id !== $v)
		{
			$this->subject_id = $v;
			$this->fieldData["subject_id"] = $v;
		}
		return $this;

	}

	public function setSubjectId2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_id2 !== $v)
		{
			$this->subject_id2 = $v;
			$this->fieldData["subject_id2"] = $v;
		}
		return $this;

	}

	public function setSubjectName2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject_name2 !== $v)
		{
			$this->subject_name2 = $v;
			$this->fieldData["subject_name2"] = $v;
		}
		return $this;

	}

	public function setIndustryId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry_id !== $v)
		{
			$this->industry_id = $v;
			$this->fieldData["industry_id"] = $v;
		}
		return $this;

	}

	public function setIndustryId2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry_id2 !== $v)
		{
			$this->industry_id2 = $v;
			$this->fieldData["industry_id2"] = $v;
		}
		return $this;

	}

	public function setIndustryName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry_name !== $v)
		{
			$this->industry_name = $v;
			$this->fieldData["industry_name"] = $v;
		}
		return $this;

	}

	public function setIndustryName2($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->industry_name2 !== $v)
		{
			$this->industry_name2 = $v;
			$this->fieldData["industry_name2"] = $v;
		}
		return $this;

	}

	public function setSkillDomain($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->skill_domain !== $v)
		{
			$this->skill_domain = $v;
			$this->fieldData["skill_domain"] = $v;
		}
		return $this;

	}

	public function setRadicateYear($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->radicate_year !== $v)
		{
			$this->radicate_year = $v;
			$this->fieldData["radicate_year"] = $v;
		}
		return $this;

	}

	public function setRadicateAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->radicate_at !== $v)
		{
			$this->radicate_at = $v;
			$this->fieldData["radicate_at"] = $v;
		}
		return $this;

	}

	public function setRadicateMoney($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->radicate_money !== $v)
		{
			$this->radicate_money = $v;
			$this->fieldData["radicate_money"] = $v;
		}
		return $this;

	}

	public function setPayforNum($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->payfor_num !== $v)
		{
			$this->payfor_num = $v;
			$this->fieldData["payfor_num"] = $v;
		}
		return $this;

	}

	public function setStatement($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->statement !== $v)
		{
			$this->statement = $v;
			$this->fieldData["statement"] = $v;
		}
		return $this;

	}

	public function setOfficeId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->office_id !== $v)
		{
			$this->office_id = $v;
			$this->fieldData["office_id"] = $v;
		}
		return $this;

	}

	public function setGroupId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->group_id !== $v)
		{
			$this->group_id = $v;
			$this->fieldData["group_id"] = $v;
		}
		return $this;

	}

	public function setBudgetGroupId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->budget_group_id !== $v)
		{
			$this->budget_group_id = $v;
			$this->fieldData["budget_group_id"] = $v;
		}
		return $this;

	}

	public function setTypeGroup($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type_group !== $v)
		{
			$this->type_group = $v;
			$this->fieldData["type_group"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setCorporationName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->corporation_name !== $v)
		{
			$this->corporation_name = $v;
			$this->fieldData["corporation_name"] = $v;
		}
		return $this;

	}

	public function setDepartmentName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->department_name !== $v)
		{
			$this->department_name = $v;
			$this->fieldData["department_name"] = $v;
		}
		return $this;

	}

	public function setCooperation($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->cooperation !== $v)
		{
			$this->cooperation = $v;
			$this->fieldData["cooperation"] = $v;
		}
		return $this;

	}

	public function setOfficeSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->office_subject !== $v)
		{
			$this->office_subject = $v;
			$this->fieldData["office_subject"] = $v;
		}
		return $this;

	}

	public function setTypeSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type_subject !== $v)
		{
			$this->type_subject = $v;
			$this->fieldData["type_subject"] = $v;
		}
		return $this;

	}

	public function setScore($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->score !== $v)
		{
			$this->score = $v;
			$this->fieldData["score"] = $v;
		}
		return $this;

	}

	public function setScoreComplete($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->score_complete !== $v)
		{
			$this->score_complete = $v;
			$this->fieldData["score_complete"] = $v;
		}
		return $this;

	}

	public function setSummary($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->summary !== $v)
		{
			$this->summary = $v;
			$this->fieldData["summary"] = $v;
		}
		return $this;

	}

	public function setBudgetScore($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->budget_score !== $v)
		{
			$this->budget_score = $v;
			$this->fieldData["budget_score"] = $v;
		}
		return $this;

	}

	public function setHasExpert($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->has_expert !== $v)
		{
			$this->has_expert = $v;
			$this->fieldData["has_expert"] = $v;
		}
		return $this;

	}

	public function setStateForBudgetBook($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_budget_book !== $v)
		{
			$this->state_for_budget_book = $v;
			$this->fieldData["state_for_budget_book"] = $v;
		}
		return $this;

	}

	public function setStateForPlanBook($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_plan_book !== $v)
		{
			$this->state_for_plan_book = $v;
			$this->fieldData["state_for_plan_book"] = $v;
		}
		return $this;

	}

	public function setStateForCompleteBook($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_complete_book !== $v)
		{
			$this->state_for_complete_book = $v;
			$this->fieldData["state_for_complete_book"] = $v;
		}
		return $this;

	}

	public function setStateForAudit($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_audit !== $v)
		{
			$this->state_for_audit = $v;
			$this->fieldData["state_for_audit"] = $v;
		}
		return $this;

	}

	public function setStateForInterimreport($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_interimreport !== $v)
		{
			$this->state_for_interimreport = $v;
			$this->fieldData["state_for_interimreport"] = $v;
		}
		return $this;

	}

	public function setStateForDatum($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->state_for_datum !== $v)
		{
			$this->state_for_datum = $v;
			$this->fieldData["state_for_datum"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function setBudgetOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->budget_open !== $v)
		{
			$this->budget_open = $v;
			$this->fieldData["budget_open"] = $v;
		}
		return $this;

	}

	public function setTaskOpen($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->task_open !== $v)
		{
			$this->task_open = $v;
			$this->fieldData["task_open"] = $v;
		}
		return $this;

	}

	public function setIsWait($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_wait !== $v)
		{
			$this->is_wait = $v;
			$this->fieldData["is_wait"] = $v;
		}
		return $this;

	}

	public function setIsShift($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_shift !== $v)
		{
			$this->is_shift = $v;
			$this->fieldData["is_shift"] = $v;
		}
		return $this;

	}

	public function setIsPackage($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_package !== $v)
		{
			$this->is_package = $v;
			$this->fieldData["is_package"] = $v;
		}
		return $this;

	}

	public function setIsAudit($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_audit !== $v)
		{
			$this->is_audit = $v;
			$this->fieldData["is_audit"] = $v;
		}
		return $this;

	}

	public function setIsError($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->is_error !== $v)
		{
			$this->is_error = $v;
			$this->fieldData["is_error"] = $v;
		}
		return $this;

	}

	public function setConfigs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->configs !== $v)
		{
			$this->configs = $v;
			$this->fieldData["configs"] = $v;
		}
		return $this;

	}

	public function setFilePath($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->file_path !== $v)
		{
			$this->file_path = $v;
			$this->fieldData["file_path"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setCategoryId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->category_id !== $v)
		{
			$this->category_id = $v;
			$this->fieldData["category_id"] = $v;
		}
		return $this;

	}

	public function setCategoryName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->category_name !== $v)
		{
			$this->category_name = $v;
			$this->fieldData["category_name"] = $v;
		}
		return $this;

	}

	public function setBrief($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->brief !== $v)
		{
			$this->brief = $v;
			$this->fieldData["brief"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `projects_copy` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"type_id" => $this->getTypeId(),
			"cat_id" => $this->getCatId(),
			"guide_id" => $this->getGuideId(),
			"project_id" => $this->getProjectId(),
			"main_project_id" => $this->getMainProjectId(),
			"corporation_id" => $this->getCorporationId(),
			"user_id" => $this->getUserId(),
			"department_id" => $this->getDepartmentId(),
			"type_current_group" => $this->getTypeCurrentGroup(),
			"accept_id" => $this->getAcceptId(),
			"radicate_id" => $this->getRadicateId(),
			"subject" => $this->getSubject(),
			"declare_year" => $this->getDeclareYear(),
			"declare_at" => $this->getDeclareAt(),
			"declare_money" => $this->getDeclareMoney(),
			"total_money" => $this->getTotalMoney(),
			"subtitle" => $this->getSubtitle(),
			"end_at" => $this->getEndAt(),
			"start_at" => $this->getStartAt(),
			"real_start_at" => $this->getRealStartAt(),
			"real_end_at" => $this->getRealEndAt(),
			"subject_name" => $this->getSubjectName(),
			"subject_id" => $this->getSubjectId(),
			"subject_id2" => $this->getSubjectId2(),
			"subject_name2" => $this->getSubjectName2(),
			"industry_id" => $this->getIndustryId(),
			"industry_id2" => $this->getIndustryId2(),
			"industry_name" => $this->getIndustryName(),
			"industry_name2" => $this->getIndustryName2(),
			"skill_domain" => $this->getSkillDomain(),
			"radicate_year" => $this->getRadicateYear(),
			"radicate_at" => $this->getRadicateAt(),
			"radicate_money" => $this->getRadicateMoney(),
			"payfor_num" => $this->getPayforNum(),
			"statement" => $this->getStatement(),
			"office_id" => $this->getOfficeId(),
			"group_id" => $this->getGroupId(),
			"budget_group_id" => $this->getBudgetGroupId(),
			"type_group" => $this->getTypeGroup(),
			"user_name" => $this->getUserName(),
			"corporation_name" => $this->getCorporationName(),
			"department_name" => $this->getDepartmentName(),
			"cooperation" => $this->getCooperation(),
			"office_subject" => $this->getOfficeSubject(),
			"type_subject" => $this->getTypeSubject(),
			"score" => $this->getScore(),
			"score_complete" => $this->getScoreComplete(),
			"summary" => $this->getSummary(),
			"budget_score" => $this->getBudgetScore(),
			"has_expert" => $this->getHasExpert(),
			"state_for_budget_book" => $this->getStateForBudgetBook(),
			"state_for_plan_book" => $this->getStateForPlanBook(),
			"state_for_complete_book" => $this->getStateForCompleteBook(),
			"state_for_audit" => $this->getStateForAudit(),
			"state_for_interimreport" => $this->getStateForInterimreport(),
			"state_for_datum" => $this->getStateForDatum(),
			"updated_at" => $this->getUpdatedAt(),
			"budget_open" => $this->getBudgetOpen(),
			"task_open" => $this->getTaskOpen(),
			"is_wait" => $this->getIsWait(),
			"is_shift" => $this->getIsShift(),
			"is_package" => $this->getIsPackage(),
			"is_audit" => $this->getIsAudit(),
			"is_error" => $this->getIsError(),
			"configs" => $this->getConfigs(),
			"file_path" => $this->getFilePath(),
			"created_at" => $this->getCreatedAt(),
			"category_id" => $this->getCategoryId(),
			"category_name" => $this->getCategoryName(),
			"brief" => $this->getBrief(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->type_id = '';
		$this->cat_id = '';
		$this->guide_id = '';
		$this->project_id = '';
		$this->main_project_id = '';
		$this->corporation_id = '';
		$this->user_id = '';
		$this->department_id = '';
		$this->type_current_group = '';
		$this->accept_id = '';
		$this->radicate_id = '';
		$this->subject = '';
		$this->declare_year = '';
		$this->declare_at = '';
		$this->declare_money = '';
		$this->total_money = '';
		$this->subtitle = '';
		$this->end_at = '';
		$this->start_at = '';
		$this->real_start_at = '';
		$this->real_end_at = '';
		$this->subject_name = '';
		$this->subject_id = '';
		$this->subject_id2 = '';
		$this->subject_name2 = '';
		$this->industry_id = '';
		$this->industry_id2 = '';
		$this->industry_name = '';
		$this->industry_name2 = '';
		$this->skill_domain = '';
		$this->radicate_year = '';
		$this->radicate_at = '';
		$this->radicate_money = '';
		$this->payfor_num = '';
		$this->statement = '';
		$this->office_id = '';
		$this->group_id = '';
		$this->budget_group_id = '';
		$this->type_group = '';
		$this->user_name = '';
		$this->corporation_name = '';
		$this->department_name = '';
		$this->cooperation = '';
		$this->office_subject = '';
		$this->type_subject = '';
		$this->score = '';
		$this->score_complete = '';
		$this->summary = '';
		$this->budget_score = '';
		$this->has_expert = '';
		$this->state_for_budget_book = '';
		$this->state_for_plan_book = '';
		$this->state_for_complete_book = '';
		$this->state_for_audit = '';
		$this->state_for_interimreport = '';
		$this->state_for_datum = '';
		$this->updated_at = '';
		$this->budget_open = '';
		$this->task_open = '';
		$this->is_wait = '';
		$this->is_shift = '';
		$this->is_package = '';
		$this->is_audit = '';
		$this->is_error = '';
		$this->configs = '';
		$this->file_path = '';
		$this->created_at = '';
		$this->category_id = '';
		$this->category_name = '';
		$this->brief = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["type_id"]) && $this->type_id = $data["type_id"];
		isset($data["cat_id"]) && $this->cat_id = $data["cat_id"];
		isset($data["guide_id"]) && $this->guide_id = $data["guide_id"];
		isset($data["project_id"]) && $this->project_id = $data["project_id"];
		isset($data["main_project_id"]) && $this->main_project_id = $data["main_project_id"];
		isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["department_id"]) && $this->department_id = $data["department_id"];
		isset($data["type_current_group"]) && $this->type_current_group = $data["type_current_group"];
		isset($data["accept_id"]) && $this->accept_id = $data["accept_id"];
		isset($data["radicate_id"]) && $this->radicate_id = $data["radicate_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["declare_year"]) && $this->declare_year = $data["declare_year"];
		isset($data["declare_at"]) && $this->declare_at = $data["declare_at"];
		isset($data["declare_money"]) && $this->declare_money = $data["declare_money"];
		isset($data["total_money"]) && $this->total_money = $data["total_money"];
		isset($data["subtitle"]) && $this->subtitle = $data["subtitle"];
		isset($data["end_at"]) && $this->end_at = $data["end_at"];
		isset($data["start_at"]) && $this->start_at = $data["start_at"];
		isset($data["real_start_at"]) && $this->real_start_at = $data["real_start_at"];
		isset($data["real_end_at"]) && $this->real_end_at = $data["real_end_at"];
		isset($data["subject_name"]) && $this->subject_name = $data["subject_name"];
		isset($data["subject_id"]) && $this->subject_id = $data["subject_id"];
		isset($data["subject_id2"]) && $this->subject_id2 = $data["subject_id2"];
		isset($data["subject_name2"]) && $this->subject_name2 = $data["subject_name2"];
		isset($data["industry_id"]) && $this->industry_id = $data["industry_id"];
		isset($data["industry_id2"]) && $this->industry_id2 = $data["industry_id2"];
		isset($data["industry_name"]) && $this->industry_name = $data["industry_name"];
		isset($data["industry_name2"]) && $this->industry_name2 = $data["industry_name2"];
		isset($data["skill_domain"]) && $this->skill_domain = $data["skill_domain"];
		isset($data["radicate_year"]) && $this->radicate_year = $data["radicate_year"];
		isset($data["radicate_at"]) && $this->radicate_at = $data["radicate_at"];
		isset($data["radicate_money"]) && $this->radicate_money = $data["radicate_money"];
		isset($data["payfor_num"]) && $this->payfor_num = $data["payfor_num"];
		isset($data["statement"]) && $this->statement = $data["statement"];
		isset($data["office_id"]) && $this->office_id = $data["office_id"];
		isset($data["group_id"]) && $this->group_id = $data["group_id"];
		isset($data["budget_group_id"]) && $this->budget_group_id = $data["budget_group_id"];
		isset($data["type_group"]) && $this->type_group = $data["type_group"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
		isset($data["department_name"]) && $this->department_name = $data["department_name"];
		isset($data["cooperation"]) && $this->cooperation = $data["cooperation"];
		isset($data["office_subject"]) && $this->office_subject = $data["office_subject"];
		isset($data["type_subject"]) && $this->type_subject = $data["type_subject"];
		isset($data["score"]) && $this->score = $data["score"];
		isset($data["score_complete"]) && $this->score_complete = $data["score_complete"];
		isset($data["summary"]) && $this->summary = $data["summary"];
		isset($data["budget_score"]) && $this->budget_score = $data["budget_score"];
		isset($data["has_expert"]) && $this->has_expert = $data["has_expert"];
		isset($data["state_for_budget_book"]) && $this->state_for_budget_book = $data["state_for_budget_book"];
		isset($data["state_for_plan_book"]) && $this->state_for_plan_book = $data["state_for_plan_book"];
		isset($data["state_for_complete_book"]) && $this->state_for_complete_book = $data["state_for_complete_book"];
		isset($data["state_for_audit"]) && $this->state_for_audit = $data["state_for_audit"];
		isset($data["state_for_interimreport"]) && $this->state_for_interimreport = $data["state_for_interimreport"];
		isset($data["state_for_datum"]) && $this->state_for_datum = $data["state_for_datum"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		isset($data["budget_open"]) && $this->budget_open = $data["budget_open"];
		isset($data["task_open"]) && $this->task_open = $data["task_open"];
		isset($data["is_wait"]) && $this->is_wait = $data["is_wait"];
		isset($data["is_shift"]) && $this->is_shift = $data["is_shift"];
		isset($data["is_package"]) && $this->is_package = $data["is_package"];
		isset($data["is_audit"]) && $this->is_audit = $data["is_audit"];
		isset($data["is_error"]) && $this->is_error = $data["is_error"];
		isset($data["configs"]) && $this->configs = $data["configs"];
		isset($data["file_path"]) && $this->file_path = $data["file_path"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["category_id"]) && $this->category_id = $data["category_id"];
		isset($data["category_name"]) && $this->category_name = $data["category_name"];
		isset($data["brief"]) && $this->brief = $data["brief"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}