<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseIdeas extends BaseModel
{
	private $id  = '';
	private $user_id  = '';
	private $user_name  = '';
	private $content  = '';
	private $reply_id  = '';
	private $reply_name  = '';
	private $reply  = '';
	private $created_at  = '';
	private $updated_at  = '';
	public $table = "ideas";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getUserId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_id,0,$len,"utf-8");
			else return substr($this->user_id,0,$len);
		}
		return $this->user_id;
	}

	public function getUserName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->user_name,0,$len,"utf-8");
			else return substr($this->user_name,0,$len);
		}
		return $this->user_name;
	}

	public function getContent($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->content,0,$len,"utf-8");
			else return substr($this->content,0,$len);
		}
		return $this->content;
	}

	public function getReplyId($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->reply_id,0,$len,"utf-8");
			else return substr($this->reply_id,0,$len);
		}
		return $this->reply_id;
	}

	public function getReplyName($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->reply_name,0,$len,"utf-8");
			else return substr($this->reply_name,0,$len);
		}
		return $this->reply_name;
	}

	public function getReply($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->reply,0,$len,"utf-8");
			else return substr($this->reply,0,$len);
		}
		return $this->reply;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setReplyId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->reply_id !== $v)
		{
			$this->reply_id = $v;
			$this->fieldData["reply_id"] = $v;
		}
		return $this;

	}

	public function setReplyName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->reply_name !== $v)
		{
			$this->reply_name = $v;
			$this->fieldData["reply_name"] = $v;
		}
		return $this;

	}

	public function setReply($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->reply !== $v)
		{
			$this->reply = $v;
			$this->fieldData["reply"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `ideas` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"content" => $this->getContent(),
			"reply_id" => $this->getReplyId(),
			"reply_name" => $this->getReplyName(),
			"reply" => $this->getReply(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->content = '';
		$this->reply_id = '';
		$this->reply_name = '';
		$this->reply = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["reply_id"]) && $this->reply_id = $data["reply_id"];
		isset($data["reply_name"]) && $this->reply_name = $data["reply_name"];
		isset($data["reply"]) && $this->reply = $data["reply"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}