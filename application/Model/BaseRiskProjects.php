<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseRiskProjects extends BaseModel
{
	private $id;
	private $project_id;
	private $subject;
	private $risk_no;
	private $risk_type;
	private $risk_subject;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "risk_projects";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getProjectId()
	{
		return $this->project_id;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function getRiskNo()
	{
		return $this->risk_no;
	}

	public function getRiskType()
	{
		return $this->risk_type;
	}

	public function getRiskSubject()
	{
		return $this->risk_subject;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setProjectId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->project_id !== $v)
		{
			$this->project_id = $v;
			$this->fieldData["project_id"] = $v;
		}
		return $this;

	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setRiskNo($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->risk_no !== $v)
		{
			$this->risk_no = $v;
			$this->fieldData["risk_no"] = $v;
		}
		return $this;

	}

	public function setRiskType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->risk_type !== $v)
		{
			$this->risk_type = $v;
			$this->fieldData["risk_type"] = $v;
		}
		return $this;

	}

	public function setRiskSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->risk_subject !== $v)
		{
			$this->risk_subject = $v;
			$this->fieldData["risk_subject"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `risk_projects` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"project_id" => $this->getProjectId(),
			"subject" => $this->getSubject(),
			"risk_no" => $this->getRiskNo(),
			"risk_type" => $this->getRiskType(),
			"risk_subject" => $this->getRiskSubject(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->project_id = '';
		$this->subject = '';
		$this->risk_no = '';
		$this->risk_type = '';
		$this->risk_subject = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["project_id"]) && $this->project_id = $data["project_id"];
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["risk_no"]) && $this->risk_no = $data["risk_no"];
		isset($data["risk_type"]) && $this->risk_type = $data["risk_type"];
		isset($data["risk_subject"]) && $this->risk_subject = $data["risk_subject"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}