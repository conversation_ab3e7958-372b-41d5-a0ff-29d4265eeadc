<?php
namespace App\Model;
use App\Facades\Button;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseStandards;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Standards extends BaseStandards
{

    private $corporation = NULL;
    private $user=NULL;
    private $company = NULL;
    function getUser($f = false)
    {
        if ($this->user === NULL || $f) {
            $this->user = sf::getModel("Users")->selectByUserId(parent::getUserId());
        }
        return $this->user;
    }
    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
        return $this->corporation;
    }
    function getCompany($f=false)
    {
        if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->company;
    }

    function getProjectSubject()
    {
        return $this->getProjectId() ? sf::getModel('Projects')->selectByProjectId($this->getProjectId())->getSubject() : '无';
    }

    function selectBySubject($subject)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '{$subject}' ");
        if ($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        } else {
            $this->setStandardId(sf::getLib("MyString")->getRandString());
            $this->setSubject($subject);
        }
        return $this;
    }

    function selectByStandardId($standard_id = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `standard_id` = '" . $standard_id . "' ");
        if ($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        } else {
            $this->setStandardId($standard_id ? $standard_id : sf::getLib("MyString")->getRandString());
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function selectByWfId($wf_id='')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `wf_id` = '{$wf_id}' ");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else{
            $this->setStandardId(sf::getLib("MyString")->getRandString());
            $this->setWfId($wf_id);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    // 取得审核状态
    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '填写中';
            case 4:
                return '待单位/部门助理审核';
            case 5:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getStandardId().'/type/standard').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门助理退回</span>';
            case 6:
                return '待单位/部门管理员审核';
            case 7:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getStandardId().'/type/standard').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门管理员退回</span>';
            case 9:
                return '待科技处助理审核';
            case 12:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getStandardId().'/type/standard').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处助理退回</span>';
            case 15:
                return '待科技处管理员审核';
            case 17:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getStandardId().'/type/standard').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处管理员退回</span>';
            case 20:
                return '已审核';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    function getYear()
    {
        return substr(parent::getDate(), 0, 4);
    }

    function addUserToMember()
    {
        if(input::session('userlevel')==2){
            $user = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
            if(!$user->isNew()){
                $member = sf::getModel("FruitMembers")->selectByUserId($user->getUserId(),$this->getStandardId(),'standard');
                if($member->isNew()){
                    $member->setUserName($user->getUserName());
                    $member->setCompanyId($user->getCorporationId());
                    $member->setCompanyName($user->getCorporationName());
                    $member->save();
                }
            }
        }
    }

    /**
     * 单位
     */
    public function selectCompanys()
    {
        return sf::getModel("FruitCompanys")->selectAll("item_id = '" . parent::getStandardId() . "' and item_type = 'standard'","order by sort asc,id asc");
    }

    /**
     * 人员
     * @return [type] [description]
     */
    public function selectMembers()
    {
        return sf::getModel("FruitMembers")->selectAll("item_id = '" . parent::getStandardId() . "' and item_type = 'standard'","order by sort asc,id asc");
    }

    public function getUsers()
    {
        $members = $this->selectMembers();
        $users = [];
        while($member = $members->getObject()){
            if(empty($member->getUserId())) continue;
            $users[$member->getUserId()] = $member->getUserName();
        }
        return $users;
    }

    public function getCompanyUsers()
    {
        $members = $this->selectMembers();
        $users = [];
        while($member = $members->getObject()){
            if(empty($member->getCompanyId())) continue;
            $users[$member->getCompanyId()] = $member->getCompanyName();
        }
        return $users;
    }

    public function selectProjects()
    {
        return sf::getModel("FruitProjects")->selectAll("item_id = '" . parent::getStandardId() . "' and item_type = 'standard'","order by sort asc,id asc");
    }

    public function getProjects()
    {
        $projects = $this->selectProjects();
        $projectArr = [];
        while($project = $projects->getObject()){
            $_project = $project->getProject();
            $projectArr[$_project->getProjectId()] = $_project->getSubject();
        }
        if(empty($projectArr)){
            $projectArr[] = '无';
        }
        return $projectArr;
    }

    public function selectProducts()
    {
        return sf::getModel("FruitProducts")->selectAll("item_id = '" . parent::getStandardId() . "' and item_type = 'standard'","order by sort asc,id asc");
    }

    function getProjectById($id)
    {
        return sf::getModel("FruitProjects",$id);
    }
    function getProductById($id)
    {
        return sf::getModel("FruitProducts",$id);
    }

    function getCompanyById($id)
    {
        return sf::getModel("FruitCompanys",$id);
    }
    function getCompanyNames()
    {
        $members = $this->selectCompanys();
        $userNames = [];
        while($member = $members->getObject()){
            $userNames[] = $member->getCompanyName();
        }
        return implode('、',$userNames);
    }

    function getMemberById($id)
    {
        return sf::getModel("FruitMembers",$id);
    }

    function getUserNames()
    {
        $members = $this->selectMembers();
        $userNames = [];
        while($member = $members->getObject()){
            $userNames[] = $member->getUserName();
        }
        return implode('、',$userNames);
    }

    function getProjectNames(){
        $db = sf::getLib("db");
        $sql ="SELECT p.subject FROM `projects` p INNER JOIN `fruit_projects` fp ON p.project_id = fp.project_id where fp.item_id = '{$this->getStandardId()}' and item_type='standard'";
        $query = $db->query($sql);
        $result=$db->result_array($query);
        $projectNamesArr=array_column($result,"subject");
        return implode('，',$projectNamesArr);
    }

    function getProductNames(){
        $db = sf::getLib("db");
        $sql ="SELECT p.subject FROM `products` p INNER JOIN `fruit_products` fp ON p.product_id = fp.product_id where fp.item_id = '{$this->getStandardId()}' and item_type='standard'";
        $query = $db->query($sql);
        $result=$db->result_array($query);
        $projectNamesArr=array_column($result,"subject");
        return implode('，',$projectNamesArr);
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'standard', $limit = 0, $orders = 'order by no asc,id asc')
    {
        $addwhere = "`item_id` = '" . $this->getStandardId() . "'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }

    /**
     * 获得附件
     */
    public function getAttachments($limit = 0)
    {
        $addwhere = "`item_id` = '" . $this->getStandardId() . "' and `item_type` = 'standard'";
        return sf::getModel("Filemanager")->selectAll($addwhere, "order by id asc", $limit);
    }

    public function setFruitDomain($arr)
    {
        return parent::setFruitDomain(implode('、',$arr));
    }

    public function getFruitDomainArr()
    {
        return explode('、',parent::getFruitDomain());
    }

    public function validate()
    {
        $message = array();
        if (!$this->getSubject()) $message[] = '标准名称必须填写';
        if (!$this->getSn()) $message[] = '标准号必须填写';
        if (!$this->getType()) $message[] = '标准类别必须填写';
        if (!$this->getWay()) $message[] = '发布形式必须填写';
        if (!$this->getDate()) $message[] = '发布日期必须填写';
        if (!$this->getApplyCompany()) $message[] = '提出单位必须填写';
        if (!$this->getManagerCompany()) $message[] = '归口单位必须填写';
        if (!$this->getFruitDomain()) $message[] = '技术领域必须填写';
        if($this->getAttachments()->getTotal()==0){
            $message[] = '附件必须上传';
        }
        return $message;
    }

    /**
     * 是否有编辑权限
     * @return bool
     */
    public function hasEditAuth()
    {
        if($this->isNew()) return true;
        if ($this->getUserId() != input::session("roleuserid"))  return false;
        if(!in_array($this->getStatement(),[0,1,5,7,12,17,20])) return false;
        return true;
    }

    /**
     * 是否有查看权限
     * @return bool
     */
    public function hasShowAuth()
    {
        if (input::session('userlevel')==2 && $this->getUserId() != input::session("roleuserid") && $this->isInMembers(input::session("roleuserid"))===false) {
            return false;
        }
        return true;
    }

    public function isInMembers($userId)
    {
        $users = $this->getUsers();
        return $users[$userId] ? true : false;
    }

    public function isInCompanyMembers($companyId)
    {
        $users = $this->getCompanyUsers();
        return $users[$companyId] ? true : false;
    }

    function sendMessage()
    {
        $message = sf::getLib('Message');
        $userIds = [];
        $roleIds = [];
        $msgTypes = [];
        $itemId = $this->getStandardId();
        $itemType = 'standard';
        $taskId = uniqid($itemType, true);
        $url = 'manager/index';
        $oaMsgType = 1; //1:审批类待办  2:通知类待办
        switch (parent::getStatement()){
            case 4:
                $title = '【成果】你有一个待审核的标准';
                $content = "标准《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentAssistantIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(3);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 6:
                $title = '【成果】你有一个待审核的标准';
                $content = "标准《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentManagerIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(4);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 9:
                //待科技处助理审核
                $title = '【成果】你有一个待审核的标准';
                $content = "标准《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeAssistantIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(5);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 15:
                //待科技处管理员审核
                $title = '【成果】你有一个待审核的标准';
                $content = "标准《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeManagerIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(6);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 20:
                //已审核
                $oaMsgType = 2;
                $title = '【成果】你登记的标准已审核通过';
                $content = "你登记的标准《".$this->getSubject()."》已审核通过。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 5:
                //部门助理退回
            case 7:
                //部门管理员退回
            case 12:
                //科技处助理退回
            case 17:
                //科技处管理员退回
                $roleName = $this->getRoleName(parent::getStatement());
                $title = "【成果】你登记的标准被{$roleName}退回";
                $content = "标准《{$this->getSubject()}》被{$roleName}退回，请登录园区科研综合管理平台查看。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
        }
        if($userIds){
            foreach ($userIds as $userId){
                $message->setUserId($userId)->setRoleId($roleIds[$userId])->setTitle($title)->setContent($content)->setItemId($itemId)->setItemType($itemType)->setTaskId($taskId)->setMsgType($msgTypes[$userId])->setOaMsgType($oaMsgType)->setUrl($url)->add();
            }
        }
    }

    private function getRoleName($statement)
    {
        $roleName = '';
        switch ($statement){
            case 5:
                $roleName = '部门助理';
                break;
            case 7:
                $roleName = '部门管理员';
                break;
            case 12:
                $roleName = '科技处助理';
                break;
            case 17:
                $roleName = '科技处管理员';
                break;
            default:
                $roleName = '';
                break;
        }
        return $roleName;
    }

    /**
     * 获取部门助理id
     * @return void
     */
    public function getDepartmentAssistantIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyAssistants = $company->selectAssistants();
        if ($companyAssistants->getTotal() == 0) return [];
        $userIds = [];
        while ($companyAssistant = $companyAssistants->getObject()) {
            if ($companyAssistant->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyAssistant->getUserId();
        }
        return $userIds;
    }

    /**
     * 获取部门管理员id
     * @return void
     */
    public function getDepartmentManagerIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyManagers = $company->selectManagers();
        if ($companyManagers->getTotal() == 0) return [];
        $userIds = [];
        while ($companyManager = $companyManagers->getObject()) {
            if ($companyManager->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyManager->getUserId();
        }
        return $userIds;
    }

    function getCompanyMoney($companyId)
    {
        return sf::getModel("StandardMoneys")->selectByStandardIdAndCompanyId(parent::getStandardId(),$companyId);
    }

    function getResearchCompanyMoney()
    {
        return sf::getModel("StandardMoneys")->selectByStandardIdAndCompanyType(parent::getStandardId());
    }

    function selectManagementsCompanyMoney()
    {
        return sf::getModel("StandardMoneys")->selectManagementsByStandardId(parent::getStandardId());
    }

    function selectManagements()
    {
        $managements = $this->selectManagementsCompanyMoney();
        return $managements->toArray();
    }

    public function getReseachMoneyTable()
    {
        $researchCompany = $this->getResearchCompanyMoney();
        if($researchCompany->isNew()) return;
        $html = <<<EOT
        <table class="table table-bordered table-sm">
                  <tr>
                      <td class="text-center">单位名称</td>
                      <td style="35%" class="text-center">奖金</td>
                  </tr>
                  <tr>
                      <td>
                          {$researchCompany->getCompanyName()}
                      </td>
                      <td>
                          {$researchCompany->getMoney()}
                      </td>
                  </tr>
              </table>
EOT;
        return $html;
    }

    public function getDepartmentMoneyTable()
    {
        $managementCompanys = $this->selectManagementsCompanyMoney();
        if($managementCompanys->getTotal()==0) return;
        $html = <<<EOT
        <table class="table table-bordered table-sm">
                  <tr>
                      <td class="text-center">单位名称</td>
                      <td style="35%" class="text-center">奖金</td>
                  </tr>
EOT;
        while($managementCompany = $managementCompanys->getObject()):
            $html .= <<<EOT
                  <tr>
                      <td>
                          {$managementCompany->getCompanyName()}
                      </td>
                      <td>
                          {$managementCompany->getMoney()}
                      </td>
                  </tr>
EOT;
        endwhile;
        $html.="</table>";
        return $html;
    }

    public function getIncomeMoney($unit='元')
    {
        return $unit=='万元' ? round(parent::getIncomeMoney()/10000,2) : parent::getIncomeMoney();
    }

    public function labels()
    {
        $tags = sf::getModel("Tags")->getTagSubjectsByTypeAndItemId("standard",$this->getStandardId());
        $tagArr = explode('、',$tags);
        $tagArr = array_filter($tagArr);
        if(empty($tagArr)) return '';
        $html = '<br>';
        foreach ($tagArr as $tag){
            $url = strtolower(router::getController().'/'.router::getMethod());
            $html .= '<a href="'.site_url($url.'?search='.$tag.'&field=tag').'"><span class="badge badge-info"><i class="fa fa-tag"></i> '.$tag.'</span></a> ';
        }
        return $html;
    }

    /**
     * 根据权限定义操作菜单
     */
    function getCustomButton()
    {
        $htmlStr = '';
        $htmlStr.=Button::back('返回');

        switch(input::getInput("session.userlevel"))
        {
            case 3:
                if($this->getStatement()==4){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("unit/standard/doSubmitOne/id/".$this->getStandardId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("unit/standard/doRejectedOnlyOne/id/".$this->getStandardId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 4:
                if($this->getStatement()==6){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("unit/standard/doSubmitOne/id/".$this->getStandardId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("unit/standard/doRejectedOnlyOne/id/".$this->getStandardId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 5:
                if($this->getStatement()==9){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("office/standard/doSubmitOne/id/".$this->getStandardId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("office/standard/doRejectedOnlyOne/id/".$this->getStandardId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 6:
                if($this->getStatement()==15){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("office/standard/doSubmitOne/id/".$this->getStandardId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("office/standard/doRejectedOnlyOne/id/".$this->getStandardId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            default:
                break;
        }
        return $htmlStr;
    }

    public function getMark()
    {
        if(input::session('userlevel')==2 && $this->getUserId()!==input::session('roleuserid')){
            return '<mark>[参与]</mark>';
        }
        if(in_array(input::session('userlevel'),[3,4]) && $this->getCompanyId()!==input::session('roleuserid') && $this->isInCompanyMembers(input::session('roleuserid'))){
            return '<mark>[参与]</mark>';
        }
        return '';
    }
}