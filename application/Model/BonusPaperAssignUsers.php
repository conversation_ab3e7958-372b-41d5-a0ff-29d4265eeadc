<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseBonusPaperAssignUsers;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class BonusPaperAssignUsers extends BaseBonusPaperAssignUsers
{
    private $company = NULL;
    private $bonus = NULL;
    private $user = NULL;
    private $paper = NULL;
    private $assignPaper = NULL;

    function getCompany($f=false)
    {
        if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->company;
    }

    function getBonus($f=false)
    {
        if($this->bonus === NULL || $f) $this->bonus = sf::getModel("Bonus")->selectByBonusId(parent::getBonusId());
        return $this->bonus;
    }

    function getUser($f=false)
    {
        if($this->user === NULL || $f) $this->user = sf::getModel("Declarers")->selectByUserId(parent::getUserId());
        return $this->user;
    }

    function getPaper($f=false)
    {
        if($this->paper === NULL || $f) $this->paper = sf::getModel("Papers")->selectByPaperId(parent::getPaperId());
        return $this->paper;
    }

    function getAssignPaper($f=false)
    {
        if($this->assignPaper === NULL || $f) $this->assignPaper = sf::getModel("BonusPaperAssignPapers",parent::getAssignPaperId());
        return $this->assignPaper;
    }


    public function getNewSort()
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from `".$this->table."` where bonus_id = '".$this->getBonusId()."' and `assign_paper_id` = '".$this->getAssignPaperId()."'");
        return $count+1;
    }

    function selectByBonusIdAndCompanyIdAndUserId($bonusId,$companyId,$userId)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `bonus_id` = '{$bonusId}' and `company_id` = '{$companyId}' and `user_id` = '{$userId}'");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setBonusId($bonusId);
            $this->setCompanyId($companyId);
            $this->setUserId($userId);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '待确认';
            case 10:
                return '已确认';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    public function isConfirmAll()
    {
        $assignPaper = $this->getAssignPaper();
        $userCount = $assignPaper->selectAssignUsers()->getTotal();
        $confirmCount = $assignPaper->selectAssignConfirmUsers()->getTotal();
        return $userCount==$confirmCount;
    }

    function getData($key='')
    {
        if(parent::getDatas()){
            $arr = $this->dejson(parent::getDatas(),true);
            return $key ? $arr[$key] : $arr;
        }else return $key ? '' : [];
    }

    function setData($arr)
    {
        return parent::setDatas(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    private function dejson($str){
        $str = $this->replaceQuote($str);
        $str = stripslashes($str);
        $str = str_replace("\\", '\\\\', $str);
        $str = str_replace("\t", '\\t', $str);
        $str = str_replace("\r\n", '\n', $str);
        return json_decode($str, 1);
    }

    private function replaceQuote($string){
        return preg_replace('/\\\"([^"]*)\\\"/',"“$1”",$string);
    }
}