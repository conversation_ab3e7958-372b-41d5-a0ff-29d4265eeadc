<?php
namespace App\Model;
use App\Facades\Button;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseCertificates;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Certificates extends BaseCertificates
{
    private $corporation = NULL;
    private $user=NULL;
    private $company = NULL;
    function getUser($f = false)
    {
        if ($this->user === NULL || $f) {
            $this->user = sf::getModel("Users")->selectByUserId(parent::getUserId());
        }
        return $this->user;
    }
    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
        return $this->corporation;
    }
    function getCompany($f=false)
    {
        if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->company;
    }
    function selectByCertId($cert_id = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `cert_id` = '" . $cert_id . "' ");
        if ($db->num_rows($query)) {
            $this->fillObject($db->fetch_array($query));
        } else {
            $this->setCertId($cert_id ? $cert_id : sf::getLib("MyString")->getRandString());
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }
    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachments($limit = 0)
    {
        $addwhere = "`item_id` = '" . $this->getCertId() . "' and `item_type` = 'certificate'";
        return sf::getModel("Filemanager")->selectAll($addwhere, "order by id asc", $limit);
    }
    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'certificate', $limit = 0, $orders = 'order by no asc,id asc')
    {
        $addwhere = "`item_id` = '" . $this->getCertId() . "'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }
    // 取得专利状态
    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '填写中';
            case 4:
                return '待单位/部门助理审核';
            case 5:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getCertId().'/type/certificate').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门助理退回</span>';
            case 6:
                return '待单位/部门管理员审核';
            case 7:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getCertId().'/type/certificate').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门管理员退回</span>';
            case 9:
                return '待科技处助理审核';
            case 12:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getCertId().'/type/certificate').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处助理退回</span>';
            case 15:
                return '待科技处管理员审核';
            case 17:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getCertId().'/type/certificate').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处管理员退回</span>';
            case 20:
                return '已审核';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    function getProductById($id)
    {
        return sf::getModel("FruitProducts",$id);
    }

    public function selectProducts()
    {
        return sf::getModel("FruitProducts")->selectAll("item_id = '" . parent::getCertId() . "' and item_type = 'certificate'","order by sort asc,id asc");
    }

    function getProductNames(){
        $db = sf::getLib("db");
        $sql ="SELECT p.subject FROM `products` p INNER JOIN `fruit_products` fp ON p.product_id = fp.product_id where fp.item_id = '{$this->getCertId()}' and item_type='certificate'";
        $query = $db->query($sql);
        $result=$db->result_array($query);
        $projectNamesArr=array_column($result,"subject");
        return implode('，',$projectNamesArr);
    }

    /**
     * 是否有编辑权限
     * @return bool
     */
    public function hasEditAuth()
    {
        if($this->isNew()) return true;
        if ($this->getUserId() != input::session("roleuserid"))  return false;
        if(!in_array($this->getStatement(),[0,1,5,7,12,17,20])) return false;
        return true;
    }

    /**
     * 是否有查看权限
     * @return bool
     */
    public function hasShowAuth()
    {
        if (input::session('userlevel')==2 && $this->getUserId() != input::session("roleuserid")) {
            return false;
        }
        return true;
    }

    function sendMessage()
    {
        $message = sf::getLib('Message');
        $userIds = [];
        $roleIds = [];
        $msgTypes = [];
        $itemId = $this->getCertId();
        $itemType = 'certificate';
        $taskId = uniqid($itemType, true);
        $url = 'manager/index';
        $oaMsgType = 1; //1:审批类待办  2:通知类待办
        switch (parent::getStatement()){
            case 4:
                $title = '【成果】你有一个待审核的产品认证';
                $content = "产品认证《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentAssistantIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(3);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 6:
                $title = '【成果】你有一个待审核的产品认证';
                $content = "产品认证《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentManagerIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(4);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 9:
                //待科技处助理审核
                $title = '【成果】你有一个待审核的产品认证';
                $content = "产品认证《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeAssistantIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(5);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 15:
                //待科技处管理员审核
                $title = '【成果】你有一个待审核的产品认证';
                $content = "产品认证《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeManagerIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(6);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 20:
                //已审核
                $oaMsgType = 2;
                $title = '【成果】你登记的产品认证已审核通过';
                $content = "你登记的产品认证《".$this->getSubject()."》已审核通过。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 5:
                //部门助理退回
            case 7:
                //部门管理员退回
            case 12:
                //科技处助理退回
            case 17:
                //科技处管理员退回
                $roleName = $this->getRoleName(parent::getStatement());
                $title = "【成果】你登记的产品认证被{$roleName}退回";
                $content = "产品认证《{$this->getSubject()}》被{$roleName}退回，请登录园区科研综合管理平台查看。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
        }
        if($userIds){
            foreach ($userIds as $userId){
                $message->setUserId($userId)->setRoleId($roleIds[$userId])->setTitle($title)->setContent($content)->setItemId($itemId)->setItemType($itemType)->setTaskId($taskId)->setMsgType($msgTypes[$userId])->setOaMsgType($oaMsgType)->setUrl($url)->add();
            }
        }
    }

    private function getRoleName($statement)
    {
        $roleName = '';
        switch ($statement){
            case 5:
                $roleName = '部门助理';
                break;
            case 7:
                $roleName = '部门管理员';
                break;
            case 12:
                $roleName = '科技处助理';
                break;
            case 17:
                $roleName = '科技处管理员';
                break;
            default:
                $roleName = '';
                break;
        }
        return $roleName;
    }

    /**
     * 获取部门助理id
     * @return void
     */
    public function getDepartmentAssistantIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyAssistants = $company->selectAssistants();
        if ($companyAssistants->getTotal() == 0) return [];
        $userIds = [];
        while ($companyAssistant = $companyAssistants->getObject()) {
            if ($companyAssistant->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyAssistant->getUserId();
        }
        return $userIds;
    }

    /**
     * 获取部门管理员id
     * @return void
     */
    public function getDepartmentManagerIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyManagers = $company->selectManagers();
        if ($companyManagers->getTotal() == 0) return [];
        $userIds = [];
        while ($companyManager = $companyManagers->getObject()) {
            if ($companyManager->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyManager->getUserId();
        }
        return $userIds;
    }

    public function validate()
    {
        $message = array();
        if (!$this->getSubject()) $message[] = '证书名称必须填写';
        if (!$this->getDate()) $message[] = '发证日期必须填写';
        if (!$this->getEndAt()) $message[] = '有效期必须填写';
        if($this->getAttachments()->getTotal()==0){
            $message[] = '附件必须上传';
        }
        return $message;
    }

    public function labels()
    {
        $tags = sf::getModel("Tags")->getTagSubjectsByTypeAndItemId("certificate",$this->getCertId());
        $tagArr = explode('、',$tags);
        $tagArr = array_filter($tagArr);
        if(empty($tagArr)) return '';
        $html = '<br>';
        foreach ($tagArr as $tag){
            $url = strtolower(router::getController().'/'.router::getMethod());
            $html .= '<a href="'.site_url($url.'?search='.$tag.'&field=tag').'"><span class="badge badge-info"><i class="fa fa-tag"></i> '.$tag.'</span></a> ';
        }
        return $html;
    }

    /**
     * 根据权限定义操作菜单
     */
    function getCustomButton()
    {
        $htmlStr = '';
        $htmlStr.=Button::back('返回');

        switch(input::getInput("session.userlevel"))
        {
            case 3:
                if($this->getStatement()==4){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("unit/certificate/doSubmitOne/id/".$this->getCertId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("unit/certificate/doRejectedOnlyOne/id/".$this->getCertId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 4:
                if($this->getStatement()==6){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("unit/certificate/doSubmitOne/id/".$this->getCertId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("unit/certificate/doRejectedOnlyOne/id/".$this->getCertId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 5:
                if($this->getStatement()==9){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("office/certificate/doSubmitOne/id/".$this->getCertId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("office/certificate/doRejectedOnlyOne/id/".$this->getCertId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            case 6:
                if($this->getStatement()==15){
                    $htmlStr .= Button::setName('审核')->setUrl(site_url("office/certificate/doSubmitOne/id/".$this->getCertId().'/from/detail'))->setIcon('check')->setWidth('800px')->setHeight('600px')->window();
                    $htmlStr .= Button::setName('退回')->setUrl(site_url("office/certificate/doRejectedOnlyOne/id/".$this->getCertId().'/from/detail'))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window();
                }
                break;
            default:
                break;
        }
        return $htmlStr;
    }
}