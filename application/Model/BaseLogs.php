<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseLogs extends BaseModel
{
	private $id;
	private $type  = 'login';
	private $user_id;
	private $user_name;
	private $user_group_id;
	private $content;
	private $user_ip;
	private $num  = '1';
	private $updated_at;
	public $table = "logs";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getType()
	{
		return $this->type;
	}

	public function getUserId()
	{
		return $this->user_id;
	}

	public function getUserName()
	{
		return $this->user_name;
	}

	public function getUserGroupId()
	{
		return $this->user_group_id;
	}

	public function getContent()
	{
		return $this->content;
	}

	public function getUserIp()
	{
		return $this->user_ip;
	}

	public function getNum()
	{
		return $this->num;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type !== $v)
		{
			$this->type = $v;
			$this->fieldData["type"] = $v;
		}
		return $this;

	}

	public function setUserId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_id !== $v)
		{
			$this->user_id = $v;
			$this->fieldData["user_id"] = $v;
		}
		return $this;

	}

	public function setUserName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_name !== $v)
		{
			$this->user_name = $v;
			$this->fieldData["user_name"] = $v;
		}
		return $this;

	}

	public function setUserGroupId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->user_group_id !== $v)
		{
			$this->user_group_id = $v;
			$this->fieldData["user_group_id"] = $v;
		}
		return $this;

	}

	public function setContent($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->content !== $v)
		{
			$this->content = $v;
			$this->fieldData["content"] = $v;
		}
		return $this;

	}

	public function setUserIp($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->user_ip !== $v)
		{
			$this->user_ip = $v;
			$this->fieldData["user_ip"] = $v;
		}
		return $this;

	}

	public function setNum($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->num !== $v)
		{
			$this->num = $v;
			$this->fieldData["num"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `logs` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"type" => $this->getType(),
			"user_id" => $this->getUserId(),
			"user_name" => $this->getUserName(),
			"user_group_id" => $this->getUserGroupId(),
			"content" => $this->getContent(),
			"user_ip" => $this->getUserIp(),
			"num" => $this->getNum(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->type = '';
		$this->user_id = '';
		$this->user_name = '';
		$this->user_group_id = '';
		$this->content = '';
		$this->user_ip = '';
		$this->num = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["type"]) && $this->type = $data["type"];
		isset($data["user_id"]) && $this->user_id = $data["user_id"];
		isset($data["user_name"]) && $this->user_name = $data["user_name"];
		isset($data["user_group_id"]) && $this->user_group_id = $data["user_group_id"];
		isset($data["content"]) && $this->content = $data["content"];
		isset($data["user_ip"]) && $this->user_ip = $data["user_ip"];
		isset($data["num"]) && $this->num = $data["num"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}