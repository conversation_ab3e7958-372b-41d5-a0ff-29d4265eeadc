<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseOutFruits extends BaseModel
{
	private $subject;
	private $fruit_domain;
	private $transform;
	private $belong;
	public $table = "out_fruits";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getSubject()
	{
		return $this->subject;
	}

	public function getFruitDomain()
	{
		return $this->fruit_domain;
	}

	public function getTransform()
	{
		return $this->transform;
	}

	public function getBelong()
	{
		return $this->belong;
	}

	public function setSubject($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->subject !== $v)
		{
			$this->subject = $v;
			$this->fieldData["subject"] = $v;
		}
		return $this;

	}

	public function setFruitDomain($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->fruit_domain !== $v)
		{
			$this->fruit_domain = $v;
			$this->fieldData["fruit_domain"] = $v;
		}
		return $this;

	}

	public function setTransform($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->transform !== $v)
		{
			$this->transform = $v;
			$this->fieldData["transform"] = $v;
		}
		return $this;

	}

	public function setBelong($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->belong !== $v)
		{
			$this->belong = $v;
			$this->fieldData["belong"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`` = '$this->' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `out_fruits` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"subject" => $this->getSubject(),
			"fruit_domain" => $this->getFruitDomain(),
			"transform" => $this->getTransform(),
			"belong" => $this->getBelong(),
			);
	}

	public function cleanObject()
	{
		$this->subject = '';
		$this->fruit_domain = '';
		$this->transform = '';
		$this->belong = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["subject"]) && $this->subject = $data["subject"];
		isset($data["fruit_domain"]) && $this->fruit_domain = $data["fruit_domain"];
		isset($data["transform"]) && $this->transform = $data["transform"];
		isset($data["belong"]) && $this->belong = $data["belong"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `` = '$this->'");
		return $db->affected_rows();
	}

}