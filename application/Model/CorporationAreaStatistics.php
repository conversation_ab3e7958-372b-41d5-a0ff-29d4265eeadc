<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseCorporationAreaStatistics;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class CorporationAreaStatistics extends BaseCorporationAreaStatistics
{
    function selectByYear($year='',$code)
    {
        if(!$year) $year = date('Y');
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `area_code` = '{$code}' and `year` = {$year}");
        if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else{
            $this->setAreaCode($code);
            $this->setYear($year);
        }
        return $this;
    }

    /**
     * 检查是否已存在某年度的统计信息
     */
    public function isExist($year,$code)
    {
        $count = $this->selectAll("`area_code` = '".$code."' and `year` = '{$year}'")->getTotal();
        if($count>0) return true;
        else return false;
    }

    public function setAreaName($code)
    {
        $city = sf::getModel('Citys')->selectByCode($code);
        if(!$city->isNew()) parent::setAreaName($city->getSubject());
        else parent::setAreaName('');
    }

    /**
     * 是否锁定
     */
    function getState()
    {
        switch (parent::getIsLock()){
            case 0:
                return '已审核';
                break;
            case 1:
                return '填写中';
                break;
            case 2:
                return '审核中';
                break;
            case 3:
                return '<s style="color:red">已驳回</s>';
                break;
        }
    }
}