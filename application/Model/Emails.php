<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseEmails;
class Emails extends BaseEmails
{
	function selectByEmail($emailAddress='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `email_address` = '".$emailAddress."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}
}