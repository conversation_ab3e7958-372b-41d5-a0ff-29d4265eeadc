<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseMoneyLocks;
class MoneyLocks extends BaseMoneyLocks
{
	function selectByProjectId($project_id='')
	{
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `project_id` = '".$project_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setProjectId($project_id);
		return $this;
	}
	
	function checkLock($field='')
	{
		if(!$field) return false;
		$money = $this->getMoney();
		 	
	}
	
	function setMoney($v)
	{
		$v = is_array($v) ? $v : array();
		parent::setMoney(serialize(removeQuotation($v)));
	}
	
	function getMoney($num=0)
	{
		return (array)unserialize(parent::getMoney());
	}
			
}