<?php

/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use App\Model\BaseVersions;
class Versions extends BaseVersions
{
	function selectByItemId($item_id,$type='budget')
	{
		return $this->selectAll("item_id = '".$item_id."' AND type = '".$type."'","ORDER BY updated_at DESC");
	}
	
	/**
	 * 取得文件链接
	 */
	function getLink()
	{
		return '<a href="'.site_path("up_files/doument/".parent::getType().'/'.parent::getFilePath()).'" target="_blank">Ver.'.parent::getNumber().'</a>';	
	}
	
	/**
	 * 设置版本号 
	 */
	function setNumber()
	{
		$db = sf::getLib("db");
		$row = $db->fetch_first("SELECT COUNT(*) AS NUM FROM ".$this->table." WHERE item_id = '".$this->getItemId()."'");
		return parent::setNumber(str_pad(($row['NUM']+1),3,'0',STR_PDA_LEFT));
	}
	
	/**
	 * 保存版本 
	 */
	function save()
	{
		if($this->isNew()) $this->setNumber();
		return parent::save();
	} 
			
}