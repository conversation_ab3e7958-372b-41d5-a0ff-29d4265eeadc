<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseLicenses extends BaseModel
{
  private $id;
  private $license_id;
  private $user_id;
  private $user_name;
  private $corporation_id;
  private $corporation_name;
  private $company_id;
  private $company_name;
 private $company_level  = '0';
  private $role;
  private $sn;
  private $subject;
  private $department;
  private $version;
  private $fruit_domain;
  private $date;
  private $industry;
  private $end_at;
  private $notice_at;
  private $website;
  private $summary;
 private $statement  = '1';
  private $created_at;
 private $updated_at  = 'CURRENT_TIMESTAMP';
 public $table = "licenses";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getLicenseId()
 {
   return $this->license_id;
 }

 public function getUserId()
 {
   return $this->user_id;
 }

 public function getUserName()
 {
   return $this->user_name;
 }

 public function getCorporationId()
 {
   return $this->corporation_id;
 }

 public function getCorporationName()
 {
   return $this->corporation_name;
 }

 public function getCompanyId()
 {
   return $this->company_id;
 }

 public function getCompanyName()
 {
   return $this->company_name;
 }

 public function getCompanyLevel()
 {
   return $this->company_level;
 }

 public function getRole()
 {
   return $this->role;
 }

 public function getSn()
 {
   return $this->sn;
 }

 public function getSubject()
 {
   return $this->subject;
 }

 public function getDepartment()
 {
   return $this->department;
 }

 public function getVersion()
 {
   return $this->version;
 }

 public function getFruitDomain()
 {
   return $this->fruit_domain;
 }

 public function getDate()
 {
   return $this->date;
 }

 public function getIndustry()
 {
   return $this->industry;
 }

 public function getEndAt()
 {
   return $this->end_at;
 }

 public function getNoticeAt()
 {
   return $this->notice_at;
 }

 public function getWebsite()
 {
   return $this->website;
 }

 public function getSummary()
 {
   return $this->summary;
 }

 public function getStatement()
 {
   return $this->statement;
 }

 public function getCreatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
   else return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setLicenseId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->license_id !== $v)
    {
      $this->license_id = $v;
      $this->fieldData["license_id"] = $v;
    }
   return $this;

 }

 public function setUserId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_id !== $v)
    {
      $this->user_id = $v;
      $this->fieldData["user_id"] = $v;
    }
   return $this;

 }

 public function setUserName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_name !== $v)
    {
      $this->user_name = $v;
      $this->fieldData["user_name"] = $v;
    }
   return $this;

 }

 public function setCorporationId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->corporation_id !== $v)
    {
      $this->corporation_id = $v;
      $this->fieldData["corporation_id"] = $v;
    }
   return $this;

 }

 public function setCorporationName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->corporation_name !== $v)
    {
      $this->corporation_name = $v;
      $this->fieldData["corporation_name"] = $v;
    }
   return $this;

 }

 public function setCompanyId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->company_id !== $v)
    {
      $this->company_id = $v;
      $this->fieldData["company_id"] = $v;
    }
   return $this;

 }

 public function setCompanyName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->company_name !== $v)
    {
      $this->company_name = $v;
      $this->fieldData["company_name"] = $v;
    }
   return $this;

 }

 public function setCompanyLevel($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->company_level !== $v)
    {
      $this->company_level = $v;
      $this->fieldData["company_level"] = $v;
    }
   return $this;

 }

 public function setRole($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->role !== $v)
    {
      $this->role = $v;
      $this->fieldData["role"] = $v;
    }
   return $this;

 }

 public function setSn($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->sn !== $v)
    {
      $this->sn = $v;
      $this->fieldData["sn"] = $v;
    }
   return $this;

 }

 public function setSubject($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->subject !== $v)
    {
      $this->subject = $v;
      $this->fieldData["subject"] = $v;
    }
   return $this;

 }

 public function setDepartment($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->department !== $v)
    {
      $this->department = $v;
      $this->fieldData["department"] = $v;
    }
   return $this;

 }

 public function setVersion($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->version !== $v)
    {
      $this->version = $v;
      $this->fieldData["version"] = $v;
    }
   return $this;

 }

 public function setFruitDomain($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruit_domain !== $v)
    {
      $this->fruit_domain = $v;
      $this->fieldData["fruit_domain"] = $v;
    }
   return $this;

 }

 public function setDate($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->date !== $v)
    {
      $this->date = $v;
      $this->fieldData["date"] = $v;
    }
   return $this;

 }

 public function setIndustry($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->industry !== $v)
    {
      $this->industry = $v;
      $this->fieldData["industry"] = $v;
    }
   return $this;

 }

 public function setEndAt($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->end_at !== $v)
    {
      $this->end_at = $v;
      $this->fieldData["end_at"] = $v;
    }
   return $this;

 }

 public function setNoticeAt($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->notice_at !== $v)
    {
      $this->notice_at = $v;
      $this->fieldData["notice_at"] = $v;
    }
   return $this;

 }

 public function setWebsite($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->website !== $v)
    {
      $this->website = $v;
      $this->fieldData["website"] = $v;
    }
   return $this;

 }

 public function setSummary($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->summary !== $v)
    {
      $this->summary = $v;
      $this->fieldData["summary"] = $v;
    }
   return $this;

 }

 public function setStatement($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->statement !== $v)
    {
      $this->statement = $v;
      $this->fieldData["statement"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `licenses` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "license_id" => $this->getLicenseId(),
     "user_id" => $this->getUserId(),
     "user_name" => $this->getUserName(),
     "corporation_id" => $this->getCorporationId(),
     "corporation_name" => $this->getCorporationName(),
     "company_id" => $this->getCompanyId(),
     "company_name" => $this->getCompanyName(),
     "company_level" => $this->getCompanyLevel(),
     "role" => $this->getRole(),
     "sn" => $this->getSn(),
     "subject" => $this->getSubject(),
     "department" => $this->getDepartment(),
     "version" => $this->getVersion(),
     "fruit_domain" => $this->getFruitDomain(),
     "date" => $this->getDate(),
     "industry" => $this->getIndustry(),
     "end_at" => $this->getEndAt(),
     "notice_at" => $this->getNoticeAt(),
     "website" => $this->getWebsite(),
     "summary" => $this->getSummary(),
     "statement" => $this->getStatement(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->license_id = '';
   $this->user_id = '';
   $this->user_name = '';
   $this->corporation_id = '';
   $this->corporation_name = '';
   $this->company_id = '';
   $this->company_name = '';
   $this->company_level = '';
   $this->role = '';
   $this->sn = '';
   $this->subject = '';
   $this->department = '';
   $this->version = '';
   $this->fruit_domain = '';
   $this->date = '';
   $this->industry = '';
   $this->end_at = '';
   $this->notice_at = '';
   $this->website = '';
   $this->summary = '';
   $this->statement = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["license_id"]) && $this->license_id = $data["license_id"];
   isset($data["user_id"]) && $this->user_id = $data["user_id"];
   isset($data["user_name"]) && $this->user_name = $data["user_name"];
   isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
   isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
   isset($data["company_id"]) && $this->company_id = $data["company_id"];
   isset($data["company_name"]) && $this->company_name = $data["company_name"];
   isset($data["company_level"]) && $this->company_level = $data["company_level"];
   isset($data["role"]) && $this->role = $data["role"];
   isset($data["sn"]) && $this->sn = $data["sn"];
   isset($data["subject"]) && $this->subject = $data["subject"];
   isset($data["department"]) && $this->department = $data["department"];
   isset($data["version"]) && $this->version = $data["version"];
   isset($data["fruit_domain"]) && $this->fruit_domain = $data["fruit_domain"];
   isset($data["date"]) && $this->date = $data["date"];
   isset($data["industry"]) && $this->industry = $data["industry"];
   isset($data["end_at"]) && $this->end_at = $data["end_at"];
   isset($data["notice_at"]) && $this->notice_at = $data["notice_at"];
   isset($data["website"]) && $this->website = $data["website"];
   isset($data["summary"]) && $this->summary = $data["summary"];
   isset($data["statement"]) && $this->statement = $data["statement"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}