<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseBonusAssignCompanys;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class BonusAssignCompanys extends BaseBonusAssignCompanys
{
    private $company = NULL;
    private $bonus = NULL;

    function getCompany($f=false)
    {
        if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->company;
    }

    function getBonus($f=false)
    {
        if($this->bonus === NULL || $f) $this->bonus = sf::getModel("Bonus")->selectByBonusId(parent::getBonusId());
        return $this->bonus;
    }

    function selectByBonusIdAndCompanyId($bonusId,$companyId)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `bonus_id` = '{$bonusId}' and company_id = '{$companyId}'");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setBonusId($bonusId);
            $this->setCompanyId($companyId);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    function selectAssignProjects()
    {
        switch (parent::getBonusType()){
            case 'guide':
                return sf::getModel('BonusGuides')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'projectgj':
                return sf::getModel('BonusProjectgjs')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'projectzx':
                return sf::getModel('BonusProjectzxs')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'projecthx':
                return sf::getModel('BonusProjecthxs')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'performance':
                return sf::getModel('BonusPerformances')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."' and state_for_assign > 0","order by id asc");
            case 'reward':
            case 'rewardkxjs':
            case 'rewardrkx':
            case 'rewardtd':
            case 'rewardgr':
                return sf::getModel('BonusAssignRewards')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'standard':
                return sf::getModel('BonusAssignStandards')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."'","order by id asc");
            case 'paper':
                return sf::getModel('BonusPaperDetails')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."' and state_for_assign > 0","order by field(`levels`,'高水平SCI','SCI','SCI会议','EI期刊','EI会议','中文核心')");
            case 'patent':
                return sf::getModel('BonusPatentDetails')->selectAll("bonus_id = '".$this->getBonusId()."' and company_id  = '".$this->getCompanyId()."' and state_for_assign > 0","order by field(`type`,'发明','实用新型','外观设计')");
        }
    }

    public function getAssignProjectCount()
    {
        return $this->selectAssignProjects()->getTotal();
    }

    function getAssignConfirmUsersByGroup()
    {
        $sql = "SELECT id,user_id,user_name,sum(money) as money,sum(own_money) as own_money FROM `bonus_assign_users` where `bonus_id` = '".$this->getBonusId()."' and company_id = '".$this->getCompanyId()."' and state_for_confirm = 10 GROUP BY user_id order by item_id asc,sort asc";
        $db = sf::getLib("db");
        $query = $db->query($sql);
        $users = [];
        while ($data = $db->fetch_array($query)) {
            $users[$data['user_id']] = $data;
        }
        return $users;
    }

    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '未上报';
            case 9:
                return '<span onclick="return showWindow(\'历史记录\',\''.site_url('admin/history/index/id/'.$this->getId().'/type/bonus_'.$this->getBonusType()).'\',{area:[\'550px\',\'80%\']});" role="button" title="查看历史记录">待科技处助理审核</span>';
            case 12:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getId().'/type/bonus_'.$this->getBonusType()).'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处助理退回</span>';
            case 15:
                return '<span onclick="return showWindow(\'历史记录\',\''.site_url('admin/history/index/id/'.$this->getId().'/type/bonus_'.$this->getBonusType()).'\',{area:[\'550px\',\'80%\']});" role="button" title="查看历史记录">待科技处管理员审核</span>';
            case 17:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getId().'/type/bonus_'.$this->getBonusType()).'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处管理员退回</span>';
            case 20:
                return '<span onclick="return showWindow(\'历史记录\',\''.site_url('admin/history/index/id/'.$this->getId().'/type/bonus_'.$this->getBonusType()).'\',{area:[\'550px\',\'80%\']});" role="button" title="查看历史记录">已审核</span>';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    public function allowEdit()
    {
        return in_array($this->getStatement(),[0,1,12,17]);
    }

    public function getMark()
    {
        if($this->getSubmitAt() && strtotime($this->getSubmitAt())>strtotime($this->getBonus(true)->getEndAt())) return '<code>[迟]</code>';
    }

    function getAssignUserById($id)
    {
        return sf::getModel("BonusAssignUsers",$id);
    }

    /**
     * 是否允许修改奖金确认
     * @return void
     */
    public function allowEditConfirm()
    {
        return in_array($this->getStatement(),[0,1,12,17]);
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'bonus_projectgj', $limit = 0, $orders='order by no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getId()."'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }

    public function getAttachementCount($itemType = 'bonus_projectgj')
    {
        $addwhere = "item_id = '".$this->getId()."'";
        if(strstr($itemType,'%')!==false){
            $addwhere.=" and item_type like '{$itemType}'";
        }else{
            $addwhere.=" and item_type = '{$itemType}'";
        }
        return (int)sf::getLib('db')->result_first("select count(*) c from filemanager where {$addwhere}");
    }

    public function validate()
    {
        $itemType = 'bonus_'.$this->getBonusType();
        $message = array();
        if($this->getAttachment($itemType)->getTotal()<2) $message[] = '请上传部门汇总表、奖金分配明细表盖章扫描件';
        return $message;
    }

    function selectAssignUsers()
    {
        return sf::getModel('BonusAssignUsers')->selectAll("`bonus_id` = '".$this->getBonusId()."' and `company_id` = '".$this->getCompanyId()."'","order by `sort` asc,id asc");
    }

    public function getUserCount()
    {
        $userArr = [];
        $users = $this->selectAssignUsers();
        while($user = $users->getObject()){
            $userArr[] = $user->getUserId();
        }
        return count(array_unique($userArr));
    }


    public function changeRewardBonusStatement($statement=1)
    {
        $db = sf::getLib('db');
        return $db->exec("update rewards set bonus_statement = {$statement} where reward_id in (select reward_id from bonus_assign_rewards where assign_company_id = '{$this->getId()}')");
    }

    public function changePaperBonusStatement($statement=1)
    {
        $db = sf::getLib('db');
        return $db->exec("update papers set bonus_statement = {$statement} where paper_id in (select paper_id from bonus_paper_details where bonus_id = '{$this->getBonusId()}' and company_id = '{$this->getCompanyId()}' and state_for_assign > 0)");
    }

    public function changePatentBonusStatement($statement=1)
    {
        $db = sf::getLib('db');
        return $db->exec("update patents set bonus_statement = {$statement} where patent_id in (select patent_id from bonus_patent_details where bonus_id = '{$this->getBonusId()}' and company_id = '{$this->getCompanyId()}' and state_for_assign > 0)");
    }

    public function addPayment()
    {
        $assignUsers = $this->selectAssignUsers();
        $companys = [];
        $companyMoneys = [];
        $items = [];
        while($assignUser = $assignUsers->getObject()){
            $paymentUser = sf::getModel('BonusPaymentUsers')->selectByBonusIdAndItemId($this->getBonusId(),$assignUser->getItemId(),$assignUser->getUserId());
            $companys[$assignUser->getCompanyId()] =  $assignUser->getCompanyName();
            $companyMoneys[$assignUser->getCompanyId()][$assignUser->getItemId()] +=  $assignUser->getMoneyTotal();
            $items[$assignUser->getItemId()] = $assignUser->getItem(true)->getSubject();
            $paymentUser->setBonusType($this->getBonusType());
            $paymentUser->setSubject($assignUser->getItem()->getSubject());
            $paymentUser->setCompanyId($assignUser->getCompanyId());
            $paymentUser->setCompanyName($assignUser->getCompanyName());
            $paymentUser->setUserName($assignUser->getUserName());
            $paymentUser->setMoney($assignUser->getMoneyTotal());
            $paymentUser->setPayAt(date('Y-m-d'));
            $paymentUser->save();
        }
        foreach ($companyMoneys as $companyId=>$moneys){
            foreach ($moneys as $itemId=>$money){
                $bonusPayment = sf::getModel('BonusPayments')->selectByBonusIdAndItemId($this->getBonusId(),$itemId);
                $bonusPayment->setBonusType($this->getBonusType());
                $bonusPayment->setSubject($items[$itemId]);
                $bonusPayment->setCompanyId($companyId);
                $bonusPayment->setCompanyName($companys[$companyId]);
                $bonusPayment->setMoney($money);
                $bonusPayment->setPayAt(date('Y-m-d'));
                $bonusPayment->save();
            }
        }
    }

    public function removePayment()
    {
        $assignUsers = $this->selectAssignUsers();
        $itemIds = [];
        while($assignUser = $assignUsers->getObject()){
            $paymentUser = sf::getModel('BonusPaymentUsers')->selectByBonusIdAndItemId($this->getBonusId(),$assignUser->getItemId(),$assignUser->getUserId());
            if($paymentUser->isNew()) continue;
            $itemIds[] =  $assignUser->getItemId();
            $paymentUser->delete();
        }
        foreach ($itemIds as $itemId){
            $bonusPayment = sf::getModel('BonusPayments')->selectByBonusIdAndItemId($this->getBonusId(),$itemId);
            $bonusPayment->delete();
        }
    }

    public function addExpend()
    {
        $assignProjects = $this->selectAssignProjects();
        while($assignProject = $assignProjects->getObject()){
            $projectPerformance = sf::getModel('ProjectPerformances')->selectByBonusIdAndProjectId($this->getBonusId(),$assignProject->getProjectId());
            $project = sf::getModel('Projects')->selectByProjectId($assignProject->getProjectId());
            if($project->isNew()) continue;
            $projectPerformance->setSubject($project->getSubject());
            $projectPerformance->setCorporationId($project->getCorporationId());
            $projectPerformance->setCorporationName($project->getCorporationName());
            $projectPerformance->setPerformanceMoney($project->getPerformanceMoney());
            $projectPerformance->setBonusAt(date('Y-m-d'));
            $projectPerformance->setMoney(round($assignProject->getMoney()/10000,2));
            $projectPerformance->save();
        }
    }

    public function undoExpend()
    {
        $assignProjects = $this->selectAssignProjects();
        while($assignProject = $assignProjects->getObject()){
            $projectPerformance = sf::getModel('ProjectPerformances')->selectByBonusIdAndProjectId($this->getBonusId(),$assignProject->getProjectId());
            if($projectPerformance->isNew()) continue;
            $projectPerformance->delete();
        }
    }
}