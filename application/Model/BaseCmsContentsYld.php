<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;

class BaseCmsContentsYld extends BaseModel
{
  private $id;
  private $zhiwu;
  private $zhicheng;
 public $table = "cms_contents_yld";
 private $is_new = true;
 private $primary_key = 'id';

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getZhiwu($len=0)
 {
     if($len){
     if(function_exists("mb_substr")) return mb_substr($this->zhiwu,0,$len,"utf-8");
     else return substr($this->zhiwu,0,$len);
   }
   return $this->zhiwu;
 }

 public function getZhicheng($len=0)
 {
     if($len){
     if(function_exists("mb_substr")) return mb_substr($this->zhicheng,0,$len,"utf-8");
     else return substr($this->zhicheng,0,$len);
   }
   return $this->zhicheng;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setZhiwu($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->zhiwu !== $v)
    {
      $this->zhiwu = $v;
      $this->fieldData["zhiwu"] = $v;
    }
   return $this;

 }

 public function setZhicheng($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->zhicheng !== $v)
    {
      $this->zhicheng = $v;
      $this->fieldData["zhicheng"] = $v;
    }
   return $this;

 }

 public function save()
 {
   $db = sf::getLib("db");
   if($this->fieldData){
    if(!$this->is_new)
    {
      return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
    }
    if($this->id = $db->insert($this->fieldData,$this->table)){
      $this->is_new = false;
      return true;
    }else return false;
    }
 }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `cms_contents_yld` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "zhiwu" => $this->getZhiwu(),
     "zhicheng" => $this->getZhicheng(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->zhiwu = '';
   $this->zhicheng = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["zhiwu"]) && $this->zhiwu = $data["zhiwu"];
   isset($data["zhicheng"]) && $this->zhicheng = $data["zhicheng"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}