<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseProjectReviews;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class ProjectReviews extends BaseProjectReviews
{
    private $project = NULL;

    function selectByProjectId($projectId,$groupId){
        $db = sf::getLib("db");
        $addWhere = "`project_id` = '{$projectId}' and group_id = '{$groupId}'";
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE " . $addWhere . "");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setProjectId($projectId);
            $this->setGroupId($groupId);
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

    public function getProject($f=false)
    {
        if($this->project === NULL || $f)
            $this->project = sf::getModel("Projects")->selectByProjectId(parent::getProjectId());
        return $this->project;
    }

    function setConfigs($key, $val = '')
    {
        $configs = (array)json_decode(parent::getConfigs(), true);

        if (!is_array($key)) $key = array($key => $val);
        foreach ($key as $_key => $_val) {
            $_ks = explode('.', $_key);
            $_code = '$configs';
            for ($i = 0, $n = count($_ks); $i < $n; $i++)
                $_code .= '[\'' . $_ks[$i] . '\']';
            $_code .= '= $_val;';
            @eval($_code);//执行语句,不是最佳选择，寻找替代方法
        }

        parent::setConfigs(json_encode($configs));
        return parent::save();
    }

    function getConfigs($key = '')
    {
        $configs = json_decode(parent::getConfigs(), true);
        $is_file = false;

        if ($key) {
            $_key = explode('.', $key);
            foreach ($_key as $k) {
                if ($k == 'file') $is_file = true;
                $configs = $configs[$k];
            }
        }
        //判断文件是否存在
        if ($is_file && !is_file(WEBROOT . "/up_files/" . $configs)) return '';
        return $configs;
    }

    function getProcessEvaluateLink()
    {
        return link_to("apply/review/show/id/" . parent::getId(), parent::getProcessEvaluate(), array('title' => '查看评审详细情况'));
    }

    function getMoneyEvaluateLink()
    {
        return link_to("apply/review/show/id/" . parent::getId(), parent::getMoneyEvaluate(), array('title' => '查看评审详细情况'));
    }


    /**
     * 取得项目评审信息
     */
    function selectAssess()
    {
        return sf::getModel("ReviewGrade")->selectAll("project_id = '" . parent::getProjectId() . "' and group_id = '".$this->getGroupId()."'", "ORDER BY id asc");
    }
}