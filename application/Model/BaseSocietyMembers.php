<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseSocietyMembers extends BaseModel
{
  private $id;
  private $member_id;
  private $corporation_id;
  private $corporation_name;
  private $user_id;
  private $user_name;
  private $user_idcard;
  private $user_sex;
  private $user_birthday;
  private $birthplace;
  private $nation;
  private $party;
  private $school;
  private $major;
  private $education;
  private $department;
  private $user_duty;
  private $user_level_rank;
  private $user_level;
  private $skill;
  private $user_mobile;
  private $address;
  private $postcode;
  private $user_email;
  private $declare_year;
  private $content1;
  private $content2;
  private $is_research_job;
  private $job_name;
  private $is_join_academic;
  private $academic;
  private $statement;
  private $wait_for_company;
  private $source;
  private $configs;
  private $submit_at;
  private $created_at;
  private $updated_at;
 public $table = "society_members";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getMemberId()
 {
   return $this->member_id;
 }

 public function getCorporationId()
 {
   return $this->corporation_id;
 }

 public function getCorporationName()
 {
   return $this->corporation_name;
 }

 public function getUserId()
 {
   return $this->user_id;
 }

 public function getUserName()
 {
   return $this->user_name;
 }

 public function getUserIdcard()
 {
   return $this->user_idcard;
 }

 public function getUserSex()
 {
   return $this->user_sex;
 }

 public function getUserBirthday()
 {
   return $this->user_birthday;
 }

 public function getBirthplace()
 {
   return $this->birthplace;
 }

 public function getNation()
 {
   return $this->nation;
 }

 public function getParty()
 {
   return $this->party;
 }

 public function getSchool()
 {
   return $this->school;
 }

 public function getMajor()
 {
   return $this->major;
 }

 public function getEducation()
 {
   return $this->education;
 }

 public function getDepartment()
 {
   return $this->department;
 }

 public function getUserDuty()
 {
   return $this->user_duty;
 }

 public function getUserLevelRank()
 {
   return $this->user_level_rank;
 }

 public function getUserLevel()
 {
   return $this->user_level;
 }

 public function getSkill()
 {
   return $this->skill;
 }

 public function getUserMobile()
 {
   return $this->user_mobile;
 }

 public function getAddress()
 {
   return $this->address;
 }

 public function getPostcode()
 {
   return $this->postcode;
 }

 public function getUserEmail()
 {
   return $this->user_email;
 }

 public function getDeclareYear()
 {
   return $this->declare_year;
 }

 public function getContent1()
 {
   return $this->content1;
 }

 public function getContent2()
 {
   return $this->content2;
 }

 public function getIsResearchJob()
 {
   return $this->is_research_job;
 }

 public function getJobName()
 {
   return $this->job_name;
 }

 public function getIsJoinAcademic()
 {
   return $this->is_join_academic;
 }

 public function getAcademic()
 {
   return $this->academic;
 }

 public function getStatement()
 {
   return $this->statement;
 }

 public function getWaitForCompany()
 {
   return $this->wait_for_company;
 }

 public function getSource()
 {
   return $this->source;
 }

 public function getConfigs()
 {
   return $this->configs;
 }

 public function getSubmitAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->submit_at));
   else return $this->submit_at;
 }

 public function getCreatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
   else return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setMemberId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->member_id !== $v)
    {
      $this->member_id = $v;
      $this->fieldData["member_id"] = $v;
    }
   return $this;

 }

 public function setCorporationId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->corporation_id !== $v)
    {
      $this->corporation_id = $v;
      $this->fieldData["corporation_id"] = $v;
    }
   return $this;

 }

 public function setCorporationName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->corporation_name !== $v)
    {
      $this->corporation_name = $v;
      $this->fieldData["corporation_name"] = $v;
    }
   return $this;

 }

 public function setUserId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_id !== $v)
    {
      $this->user_id = $v;
      $this->fieldData["user_id"] = $v;
    }
   return $this;

 }

 public function setUserName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_name !== $v)
    {
      $this->user_name = $v;
      $this->fieldData["user_name"] = $v;
    }
   return $this;

 }

 public function setUserIdcard($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_idcard !== $v)
    {
      $this->user_idcard = $v;
      $this->fieldData["user_idcard"] = $v;
    }
   return $this;

 }

 public function setUserSex($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_sex !== $v)
    {
      $this->user_sex = $v;
      $this->fieldData["user_sex"] = $v;
    }
   return $this;

 }

 public function setUserBirthday($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_birthday !== $v)
    {
      $this->user_birthday = $v;
      $this->fieldData["user_birthday"] = $v;
    }
   return $this;

 }

 public function setBirthplace($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->birthplace !== $v)
    {
      $this->birthplace = $v;
      $this->fieldData["birthplace"] = $v;
    }
   return $this;

 }

 public function setNation($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->nation !== $v)
    {
      $this->nation = $v;
      $this->fieldData["nation"] = $v;
    }
   return $this;

 }

 public function setParty($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->party !== $v)
    {
      $this->party = $v;
      $this->fieldData["party"] = $v;
    }
   return $this;

 }

 public function setSchool($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->school !== $v)
    {
      $this->school = $v;
      $this->fieldData["school"] = $v;
    }
   return $this;

 }

 public function setMajor($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->major !== $v)
    {
      $this->major = $v;
      $this->fieldData["major"] = $v;
    }
   return $this;

 }

 public function setEducation($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->education !== $v)
    {
      $this->education = $v;
      $this->fieldData["education"] = $v;
    }
   return $this;

 }

 public function setDepartment($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->department !== $v)
    {
      $this->department = $v;
      $this->fieldData["department"] = $v;
    }
   return $this;

 }

 public function setUserDuty($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_duty !== $v)
    {
      $this->user_duty = $v;
      $this->fieldData["user_duty"] = $v;
    }
   return $this;

 }

 public function setUserLevelRank($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_level_rank !== $v)
    {
      $this->user_level_rank = $v;
      $this->fieldData["user_level_rank"] = $v;
    }
   return $this;

 }

 public function setUserLevel($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_level !== $v)
    {
      $this->user_level = $v;
      $this->fieldData["user_level"] = $v;
    }
   return $this;

 }

 public function setSkill($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill !== $v)
    {
      $this->skill = $v;
      $this->fieldData["skill"] = $v;
    }
   return $this;

 }

 public function setUserMobile($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_mobile !== $v)
    {
      $this->user_mobile = $v;
      $this->fieldData["user_mobile"] = $v;
    }
   return $this;

 }

 public function setAddress($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->address !== $v)
    {
      $this->address = $v;
      $this->fieldData["address"] = $v;
    }
   return $this;

 }

 public function setPostcode($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->postcode !== $v)
    {
      $this->postcode = $v;
      $this->fieldData["postcode"] = $v;
    }
   return $this;

 }

 public function setUserEmail($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->user_email !== $v)
    {
      $this->user_email = $v;
      $this->fieldData["user_email"] = $v;
    }
   return $this;

 }

 public function setDeclareYear($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->declare_year !== $v)
    {
      $this->declare_year = $v;
      $this->fieldData["declare_year"] = $v;
    }
   return $this;

 }

 public function setContent1($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->content1 !== $v)
    {
      $this->content1 = $v;
      $this->fieldData["content1"] = $v;
    }
   return $this;

 }

 public function setContent2($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->content2 !== $v)
    {
      $this->content2 = $v;
      $this->fieldData["content2"] = $v;
    }
   return $this;

 }

 public function setIsResearchJob($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->is_research_job !== $v)
    {
      $this->is_research_job = $v;
      $this->fieldData["is_research_job"] = $v;
    }
   return $this;

 }

 public function setJobName($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->job_name !== $v)
    {
      $this->job_name = $v;
      $this->fieldData["job_name"] = $v;
    }
   return $this;

 }

 public function setIsJoinAcademic($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->is_join_academic !== $v)
    {
      $this->is_join_academic = $v;
      $this->fieldData["is_join_academic"] = $v;
    }
   return $this;

 }

 public function setAcademic($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->academic !== $v)
    {
      $this->academic = $v;
      $this->fieldData["academic"] = $v;
    }
   return $this;

 }

 public function setStatement($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->statement !== $v)
    {
      $this->statement = $v;
      $this->fieldData["statement"] = $v;
    }
   return $this;

 }

 public function setWaitForCompany($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->wait_for_company !== $v)
    {
      $this->wait_for_company = $v;
      $this->fieldData["wait_for_company"] = $v;
    }
   return $this;

 }

 public function setSource($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->source !== $v)
    {
      $this->source = $v;
      $this->fieldData["source"] = $v;
    }
   return $this;

 }

 public function setConfigs($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->configs !== $v)
    {
      $this->configs = $v;
      $this->fieldData["configs"] = $v;
    }
   return $this;

 }

 public function setSubmitAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->submit_at !== $v)
    {
      $this->submit_at = $v;
      $this->fieldData["submit_at"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `society_members` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "member_id" => $this->getMemberId(),
     "corporation_id" => $this->getCorporationId(),
     "corporation_name" => $this->getCorporationName(),
     "user_id" => $this->getUserId(),
     "user_name" => $this->getUserName(),
     "user_idcard" => $this->getUserIdcard(),
     "user_sex" => $this->getUserSex(),
     "user_birthday" => $this->getUserBirthday(),
     "birthplace" => $this->getBirthplace(),
     "nation" => $this->getNation(),
     "party" => $this->getParty(),
     "school" => $this->getSchool(),
     "major" => $this->getMajor(),
     "education" => $this->getEducation(),
     "department" => $this->getDepartment(),
     "user_duty" => $this->getUserDuty(),
     "user_level_rank" => $this->getUserLevelRank(),
     "user_level" => $this->getUserLevel(),
     "skill" => $this->getSkill(),
     "user_mobile" => $this->getUserMobile(),
     "address" => $this->getAddress(),
     "postcode" => $this->getPostcode(),
     "user_email" => $this->getUserEmail(),
     "declare_year" => $this->getDeclareYear(),
     "content1" => $this->getContent1(),
     "content2" => $this->getContent2(),
     "is_research_job" => $this->getIsResearchJob(),
     "job_name" => $this->getJobName(),
     "is_join_academic" => $this->getIsJoinAcademic(),
     "academic" => $this->getAcademic(),
     "statement" => $this->getStatement(),
     "wait_for_company" => $this->getWaitForCompany(),
     "source" => $this->getSource(),
     "configs" => $this->getConfigs(),
     "submit_at" => $this->getSubmitAt(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->member_id = '';
   $this->corporation_id = '';
   $this->corporation_name = '';
   $this->user_id = '';
   $this->user_name = '';
   $this->user_idcard = '';
   $this->user_sex = '';
   $this->user_birthday = '';
   $this->birthplace = '';
   $this->nation = '';
   $this->party = '';
   $this->school = '';
   $this->major = '';
   $this->education = '';
   $this->department = '';
   $this->user_duty = '';
   $this->user_level_rank = '';
   $this->user_level = '';
   $this->skill = '';
   $this->user_mobile = '';
   $this->address = '';
   $this->postcode = '';
   $this->user_email = '';
   $this->declare_year = '';
   $this->content1 = '';
   $this->content2 = '';
   $this->is_research_job = '';
   $this->job_name = '';
   $this->is_join_academic = '';
   $this->academic = '';
   $this->statement = '';
   $this->wait_for_company = '';
   $this->source = '';
   $this->configs = '';
   $this->submit_at = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["member_id"]) && $this->member_id = $data["member_id"];
   isset($data["corporation_id"]) && $this->corporation_id = $data["corporation_id"];
   isset($data["corporation_name"]) && $this->corporation_name = $data["corporation_name"];
   isset($data["user_id"]) && $this->user_id = $data["user_id"];
   isset($data["user_name"]) && $this->user_name = $data["user_name"];
   isset($data["user_idcard"]) && $this->user_idcard = $data["user_idcard"];
   isset($data["user_sex"]) && $this->user_sex = $data["user_sex"];
   isset($data["user_birthday"]) && $this->user_birthday = $data["user_birthday"];
   isset($data["birthplace"]) && $this->birthplace = $data["birthplace"];
   isset($data["nation"]) && $this->nation = $data["nation"];
   isset($data["party"]) && $this->party = $data["party"];
   isset($data["school"]) && $this->school = $data["school"];
   isset($data["major"]) && $this->major = $data["major"];
   isset($data["education"]) && $this->education = $data["education"];
   isset($data["department"]) && $this->department = $data["department"];
   isset($data["user_duty"]) && $this->user_duty = $data["user_duty"];
   isset($data["user_level_rank"]) && $this->user_level_rank = $data["user_level_rank"];
   isset($data["user_level"]) && $this->user_level = $data["user_level"];
   isset($data["skill"]) && $this->skill = $data["skill"];
   isset($data["user_mobile"]) && $this->user_mobile = $data["user_mobile"];
   isset($data["address"]) && $this->address = $data["address"];
   isset($data["postcode"]) && $this->postcode = $data["postcode"];
   isset($data["user_email"]) && $this->user_email = $data["user_email"];
   isset($data["declare_year"]) && $this->declare_year = $data["declare_year"];
   isset($data["content1"]) && $this->content1 = $data["content1"];
   isset($data["content2"]) && $this->content2 = $data["content2"];
   isset($data["is_research_job"]) && $this->is_research_job = $data["is_research_job"];
   isset($data["job_name"]) && $this->job_name = $data["job_name"];
   isset($data["is_join_academic"]) && $this->is_join_academic = $data["is_join_academic"];
   isset($data["academic"]) && $this->academic = $data["academic"];
   isset($data["statement"]) && $this->statement = $data["statement"];
   isset($data["wait_for_company"]) && $this->wait_for_company = $data["wait_for_company"];
   isset($data["source"]) && $this->source = $data["source"];
   isset($data["configs"]) && $this->configs = $data["configs"];
   isset($data["submit_at"]) && $this->submit_at = $data["submit_at"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}