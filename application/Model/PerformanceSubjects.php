<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BasePerformanceSubjects;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class PerformanceSubjects extends BasePerformanceSubjects
{
    function selectByCode($code='')
    {
        $db = sf::getLib("db");
        $result = $db->fetch_first("SELECT * FROM `{$this->table}` WHERE `code` = '{$code}'");
        if($result){
            $this->fillObject($result);
            return $this;
        }else{
            $this->setCode($code);
            return $this;
        }
    }

    function getNewChildCode()
    {
        $db = sf::getLib("db");
        $code = $db->result_first("SELECT `code` FROM `{$this->table}` WHERE `pid` = '".$this->getId()."' order by code desc");
        if($code){
            $newNo = (int)substr($code,-2)+1;
            $newcode = substr($code,0,(strlen($code)-2)).str_pad($newNo,2,'0',STR_PAD_LEFT);
            return $newcode;
        }
        $newcode = $this->getCode().'01';
        return $newcode;
    }

    function hasChild()
    {
        $db = sf::getLib("db");
        $count = $db->result_first("SELECT count(*) c FROM `{$this->table}` WHERE `pid` = '".$this->getId()."'");
        return $count>0;
    }
}