<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseCmsContentsYqsb extends BaseModel
{
	private $id  = '';
	private $gydw  = '';
	private $pp  = '';
	private $xh  = '';
	private $gg  = '';
	private $jscs  = '';
	private $hgzs  = '';
	private $sysms  = '';
	private $ghzl  = '';
	private $szd  = '';
	private $lxr  = '';
	private $lxdh  = '';
	private $yfsm  = '';
	private $bz  = '';
	private $jiage  = '';
	public $table = "cms_contents_yqsb";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getGydw($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->gydw,0,$len,"utf-8");
			else return substr($this->gydw,0,$len);
		}
		return $this->gydw;
	}

	public function getPp($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->pp,0,$len,"utf-8");
			else return substr($this->pp,0,$len);
		}
		return $this->pp;
	}

	public function getXh($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->xh,0,$len,"utf-8");
			else return substr($this->xh,0,$len);
		}
		return $this->xh;
	}

	public function getGg($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->gg,0,$len,"utf-8");
			else return substr($this->gg,0,$len);
		}
		return $this->gg;
	}

	public function getJscs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->jscs,0,$len,"utf-8");
			else return substr($this->jscs,0,$len);
		}
		return $this->jscs;
	}

	public function getHgzs($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->hgzs,0,$len,"utf-8");
			else return substr($this->hgzs,0,$len);
		}
		return $this->hgzs;
	}

	public function getSysms($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->sysms,0,$len,"utf-8");
			else return substr($this->sysms,0,$len);
		}
		return $this->sysms;
	}

	public function getGhzl($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->ghzl,0,$len,"utf-8");
			else return substr($this->ghzl,0,$len);
		}
		return $this->ghzl;
	}

	public function getSzd($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->szd,0,$len,"utf-8");
			else return substr($this->szd,0,$len);
		}
		return $this->szd;
	}

	public function getLxr($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->lxr,0,$len,"utf-8");
			else return substr($this->lxr,0,$len);
		}
		return $this->lxr;
	}

	public function getLxdh($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->lxdh,0,$len,"utf-8");
			else return substr($this->lxdh,0,$len);
		}
		return $this->lxdh;
	}

	public function getYfsm($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->yfsm,0,$len,"utf-8");
			else return substr($this->yfsm,0,$len);
		}
		return $this->yfsm;
	}

	public function getBz($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->bz,0,$len,"utf-8");
			else return substr($this->bz,0,$len);
		}
		return $this->bz;
	}

	public function getJiage($len=0)
	{
 		if($len){
			if(function_exists("mb_substr")) return mb_substr($this->jiage,0,$len,"utf-8");
			else return substr($this->jiage,0,$len);
		}
		return $this->jiage;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setGydw($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->gydw !== $v)
		{
			$this->gydw = $v;
			$this->fieldData["gydw"] = $v;
		}
		return $this;

	}

	public function setPp($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->pp !== $v)
		{
			$this->pp = $v;
			$this->fieldData["pp"] = $v;
		}
		return $this;

	}

	public function setXh($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->xh !== $v)
		{
			$this->xh = $v;
			$this->fieldData["xh"] = $v;
		}
		return $this;

	}

	public function setGg($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->gg !== $v)
		{
			$this->gg = $v;
			$this->fieldData["gg"] = $v;
		}
		return $this;

	}

	public function setJscs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jscs !== $v)
		{
			$this->jscs = $v;
			$this->fieldData["jscs"] = $v;
		}
		return $this;

	}

	public function setHgzs($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->hgzs !== $v)
		{
			$this->hgzs = $v;
			$this->fieldData["hgzs"] = $v;
		}
		return $this;

	}

	public function setSysms($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->sysms !== $v)
		{
			$this->sysms = $v;
			$this->fieldData["sysms"] = $v;
		}
		return $this;

	}

	public function setGhzl($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->ghzl !== $v)
		{
			$this->ghzl = $v;
			$this->fieldData["ghzl"] = $v;
		}
		return $this;

	}

	public function setSzd($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->szd !== $v)
		{
			$this->szd = $v;
			$this->fieldData["szd"] = $v;
		}
		return $this;

	}

	public function setLxr($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lxr !== $v)
		{
			$this->lxr = $v;
			$this->fieldData["lxr"] = $v;
		}
		return $this;

	}

	public function setLxdh($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->lxdh !== $v)
		{
			$this->lxdh = $v;
			$this->fieldData["lxdh"] = $v;
		}
		return $this;

	}

	public function setYfsm($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->yfsm !== $v)
		{
			$this->yfsm = $v;
			$this->fieldData["yfsm"] = $v;
		}
		return $this;

	}

	public function setBz($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->bz !== $v)
		{
			$this->bz = $v;
			$this->fieldData["bz"] = $v;
		}
		return $this;

	}

	public function setJiage($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->jiage !== $v)
		{
			$this->jiage = $v;
			$this->fieldData["jiage"] = $v;
		}
		return $this;

	}

	public function save()
	{
		$db = sf::getLib("db");
		if($this->fieldData){
		if(!$this->is_new)
		{
			return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table); 
		}
		return $this->id = $db->insert($this->fieldData,$this->table);
    }
	}

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `cms_contents_yqsb` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"gydw" => $this->getGydw(),
			"pp" => $this->getPp(),
			"xh" => $this->getXh(),
			"gg" => $this->getGg(),
			"jscs" => $this->getJscs(),
			"hgzs" => $this->getHgzs(),
			"sysms" => $this->getSysms(),
			"ghzl" => $this->getGhzl(),
			"szd" => $this->getSzd(),
			"lxr" => $this->getLxr(),
			"lxdh" => $this->getLxdh(),
			"yfsm" => $this->getYfsm(),
			"bz" => $this->getBz(),
			"jiage" => $this->getJiage(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->gydw = '';
		$this->pp = '';
		$this->xh = '';
		$this->gg = '';
		$this->jscs = '';
		$this->hgzs = '';
		$this->sysms = '';
		$this->ghzl = '';
		$this->szd = '';
		$this->lxr = '';
		$this->lxdh = '';
		$this->yfsm = '';
		$this->bz = '';
		$this->jiage = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["gydw"]) && $this->gydw = $data["gydw"];
		isset($data["pp"]) && $this->pp = $data["pp"];
		isset($data["xh"]) && $this->xh = $data["xh"];
		isset($data["gg"]) && $this->gg = $data["gg"];
		isset($data["jscs"]) && $this->jscs = $data["jscs"];
		isset($data["hgzs"]) && $this->hgzs = $data["hgzs"];
		isset($data["sysms"]) && $this->sysms = $data["sysms"];
		isset($data["ghzl"]) && $this->ghzl = $data["ghzl"];
		isset($data["szd"]) && $this->szd = $data["szd"];
		isset($data["lxr"]) && $this->lxr = $data["lxr"];
		isset($data["lxdh"]) && $this->lxdh = $data["lxdh"];
		isset($data["yfsm"]) && $this->yfsm = $data["yfsm"];
		isset($data["bz"]) && $this->bz = $data["bz"];
		isset($data["jiage"]) && $this->jiage = $data["jiage"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}