<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseSocietyMembers;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class SocietyMembers extends BaseSocietyMembers
{
    private $corporation = NULL;

    function getCorporation($f=false)
    {
        if($this->corporation === NULL || $f) $this->corporation = sf::getModel("Corporations")->selectByUserId(parent::getCorporationId());
        return $this->corporation;
    }

    function selectByMemberId($memberId = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `member_id` = '{$memberId}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setMemberId($memberId ? $memberId : sf::getLib("MyString")->getRandString());
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setUserName(input::getInput("session.nickname"));
        }
        return $this;
    }

    function selectByUserMobile($userMobile)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_mobile` = '{$userMobile}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setMemberId(sf::getLib("MyString")->getRandString());
            $this->setUserMobile($userMobile);
        }
        return $this;
    }

    function selectByUserName($userName)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_name` = '{$userName}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setMemberId(sf::getLib("MyString")->getRandString());
            $this->setUserName($userName);
        }
        return $this;
    }

    function selectByUserIdcard($userIdcard)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_idcard` = '{$userIdcard}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setMemberId(sf::getLib("MyString")->getRandString());
            $this->setUserIdcard($userIdcard);
        }
        return $this;
    }

    public function getMark()
    {
        $htmlStr = '';
        return $htmlStr;
//        if($this->getSource() == 'expert') $htmlStr .= '[专]';
//        if($htmlStr) $htmlStr = '<mark>'.$htmlStr.'</mark>';
//        return $htmlStr;
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'society_member', $limit = 0, $orders='order by no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getMemberId()."'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }


    /**
     * 状态
     */
    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '填写中';
            case 4:
                return '待单位/部门助理审核';
            case 5:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getMemberId().'/type/society_member').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门助理退回</span>';
            case 6:
                return '待单位/部门管理员审核';
            case 7:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getMemberId().'/type/society_member').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门管理员退回</span>';
            case 9:
                return '待科技处助理审核';
            case 12:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getMemberId().'/type/society_member').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处助理退回</span>';
            case 15:
                return '待科技处管理员审核';
            case 17:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getMemberId().'/type/society_member').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处管理员退回</span>';
            case 20:
                return '已审核';
            case 29:
                return '已提名';
            case 30:
                return '已提名（增补）';
            case 31:
                return '已免除';
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    function setConfigs($key,$val='')
    {
        $configs = (array)json_decode(parent::getConfigs(),true);

        if(!is_array($key)) $key = array($key => $val);
        foreach($key as $_key => $_val){
            $_ks = explode('.',$_key);
            $_code = '$configs';
            for($i=0,$n=count($_ks);$i<$n;$i++)
                $_code .= '[\''.$_ks[$i].'\']';
            $_code .= '= $_val;';
            @eval($_code);//执行语句,不是最佳选择，寻找替代方法
        }
        parent::setConfigs(json_encode($configs));
        return parent::save();
    }

    function getConfigs($key = '')
    {
        $configs = json_decode(parent::getConfigs(), true);
        if ($key) {
            $_key = explode('.', $key);
            foreach ($_key as $k) {
                $configs = $configs[$k];
            }
        }
        return $configs;
    }

    public function validate()
    {
        $message = array();

        if (!$this->getDeclareYear()) $message[] = '年度必须选择';
        if (!$this->getUserName()) $message[] = '姓名必须填写';
        if (!$this->getContent1()) $message[] = '工作简历必须填写';
        if (!$this->getParty()) $message[] = '政治面貌必须选择';
//        if (!$this->getUserLevel() && !$this->getSkill()) $message[] = '职称与技能等级需至少填写一项';
        if ($this->getIsResearchJob()=='是' && !$this->getJobName()) $message[] = '工作岗位必须填写';
        $academicWrite = false;
        $academics = $this->getAcademic();
        foreach ($academics as $k=>$v){
            if($academics['level'][0] && $academics['subject'][0]) {
                $academicWrite = true;
                break;
            }
        }
        if($this->getIsJoinAcademic()=='是' && $academicWrite===false) $message[] = '参加学术组织情况必须填写';
//        if($this->getAttachment()->getTotal()==0) $message[] = '请上传附件';
        return $message;
    }

    public function getSubmitAt($format='Y-m-d')
    {
        if(parent::getSubmitAt()){
            return date($format,strtotime(parent::getSubmitAt()));
        }else{
            return '';
        }
    }



    /**
     * 判断是否可以填写
     */
    function enableWrite()
    {
        if (in_array($this->getStatement(), array(0,1,5,7,12,17))) return true;
        else return false;
    }

    /**
     * 判断是否重复填报
     * @return int
     */
    function isRepeat()
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from `".$this->table."` where user_id = '" . $this->getUserId() . "' and member_id!='" . $this->getMemberId() . "'");
        return $count > 0;
    }

    public function getAcademic($key='')
    {
        $data = parent::getAcademic();
        if(empty($data)) $data = '[]';
        $data = $this->dejson($data);
        return $key ? $data[$key] : $data;
    }

    public function setAcademic($arr=[])
    {
        return parent::setAcademic(json_encode($arr,JSON_UNESCAPED_UNICODE));
    }

    private function dejson($str){
        $str = $this->replaceQuote($str);
        $str = stripslashes($str);
        $str = str_replace("\\", '\\\\', $str);
        $str = str_replace("\t", '\\t', $str);
        $str = str_replace("\r\n", '\n', $str);
        return json_decode($str, 1);
    }

    private function replaceQuote($string){
        return preg_replace('/\\\"([^"]*)\\\"/',"“$1”",$string);
    }
}