<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseCorporationTalents extends BaseModel
{
	private $id;
	private $company_id;
	private $company_name;
	private $type;
	private $total  = '0';
	private $qrz  = '0';
	private $fqrz  = '0';
	private $gcl  = '0';
	private $jrgl  = '0';
	private $qt  = '0';
	private $age  = '0';
	private $zcfz  = '0';
	private $gjzg  = '0';
	private $note;
	private $created_at;
	private $updated_at  = 'CURRENT_TIMESTAMP';
	public $table = "corporation_talents";
	private $is_new = true;

	public function setTable($table)
	{
		if($table) $this->table = $table;
		return $this;
	}

	public function isNew()
	{
		return $this->is_new;
	}

	public function getId()
	{
		return $this->id;
	}

	public function getCompanyId()
	{
		return $this->company_id;
	}

	public function getCompanyName()
	{
		return $this->company_name;
	}

	public function getType()
	{
		return $this->type;
	}

	public function getTotal()
	{
		return $this->total;
	}

	public function getQrz()
	{
		return $this->qrz;
	}

	public function getFqrz()
	{
		return $this->fqrz;
	}

	public function getGcl()
	{
		return $this->gcl;
	}

	public function getJrgl()
	{
		return $this->jrgl;
	}

	public function getQt()
	{
		return $this->qt;
	}

	public function getAge()
	{
		return $this->age;
	}

	public function getZcfz()
	{
		return $this->zcfz;
	}

	public function getGjzg()
	{
		return $this->gjzg;
	}

	public function getNote()
	{
		return $this->note;
	}

	public function getCreatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
		else return $this->created_at;
	}

	public function getUpdatedAt($fromat="Y-m-d H:i:s")
	{
		if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
		else return $this->updated_at;
	}

	public function setId($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->id !== $v)
		{
			$this->id = $v;
			$this->fieldData["id"] = $v;
		}
		return $this;

	}

	public function setCompanyId($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_id !== $v)
		{
			$this->company_id = $v;
			$this->fieldData["company_id"] = $v;
		}
		return $this;

	}

	public function setCompanyName($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->company_name !== $v)
		{
			$this->company_name = $v;
			$this->fieldData["company_name"] = $v;
		}
		return $this;

	}

	public function setType($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->type !== $v)
		{
			$this->type = $v;
			$this->fieldData["type"] = $v;
		}
		return $this;

	}

	public function setTotal($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->total !== $v)
		{
			$this->total = $v;
			$this->fieldData["total"] = $v;
		}
		return $this;

	}

	public function setQrz($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->qrz !== $v)
		{
			$this->qrz = $v;
			$this->fieldData["qrz"] = $v;
		}
		return $this;

	}

	public function setFqrz($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->fqrz !== $v)
		{
			$this->fqrz = $v;
			$this->fieldData["fqrz"] = $v;
		}
		return $this;

	}

	public function setGcl($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->gcl !== $v)
		{
			$this->gcl = $v;
			$this->fieldData["gcl"] = $v;
		}
		return $this;

	}

	public function setJrgl($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->jrgl !== $v)
		{
			$this->jrgl = $v;
			$this->fieldData["jrgl"] = $v;
		}
		return $this;

	}

	public function setQt($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->qt !== $v)
		{
			$this->qt = $v;
			$this->fieldData["qt"] = $v;
		}
		return $this;

	}

	public function setAge($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->age !== $v)
		{
			$this->age = $v;
			$this->fieldData["age"] = $v;
		}
		return $this;

	}

	public function setZcfz($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->zcfz !== $v)
		{
			$this->zcfz = $v;
			$this->fieldData["zcfz"] = $v;
		}
		return $this;

	}

	public function setGjzg($v)
	{
		if(!isset($v)) return $this;
		$v = (int)$v;
		if($this->gjzg !== $v)
		{
			$this->gjzg = $v;
			$this->fieldData["gjzg"] = $v;
		}
		return $this;

	}

	public function setNote($v)
	{
	if(!isset($v)) return $this;
		$v = (string)$v;
		if($this->note !== $v)
		{
			$this->note = $v;
			$this->fieldData["note"] = $v;
		}
		return $this;

	}

	public function setCreatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->created_at !== $v)
		{
			$this->created_at = $v;
			$this->fieldData["created_at"] = $v;
		}
		return $this;

	}

	public function setUpdatedAt($v)
	{
		if(!isset($v)) return $this;
		$v = date("Y-m-d H:i:s",strtotime($v));
		if($this->updated_at !== $v)
		{
			$this->updated_at = $v;
			$this->fieldData["updated_at"] = $v;
		}
		return $this;

	}

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

	public function remove($addWhere = '')
	{
		if(!$addWhere) return false;
		$db = sf::getLib("db");
    	$sql = "DELETE FROM `corporation_talents` WHERE $addWhere ";
    	$db->query($sql);
    	return $db->affected_rows();
	}

	public function toArray()
	{
		return array(
			"id" => $this->getId(),
			"company_id" => $this->getCompanyId(),
			"company_name" => $this->getCompanyName(),
			"type" => $this->getType(),
			"total" => $this->getTotal(),
			"qrz" => $this->getQrz(),
			"fqrz" => $this->getFqrz(),
			"gcl" => $this->getGcl(),
			"jrgl" => $this->getJrgl(),
			"qt" => $this->getQt(),
			"age" => $this->getAge(),
			"zcfz" => $this->getZcfz(),
			"gjzg" => $this->getGjzg(),
			"note" => $this->getNote(),
			"created_at" => $this->getCreatedAt(),
			"updated_at" => $this->getUpdatedAt(),
			);
	}

	public function cleanObject()
	{
		$this->id = '';
		$this->company_id = '';
		$this->company_name = '';
		$this->type = '';
		$this->total = '';
		$this->qrz = '';
		$this->fqrz = '';
		$this->gcl = '';
		$this->jrgl = '';
		$this->qt = '';
		$this->age = '';
		$this->zcfz = '';
		$this->gjzg = '';
		$this->note = '';
		$this->created_at = '';
		$this->updated_at = '';
		$this->fieldData = array();
		$this->is_new = true;
		return $this;
	}

	public function fillObject($data=array())
	{
		$this->cleanObject();
		if(!$data) return $this;
		if($data["is_new"]) $this->is_new = true;
		else $this->is_new = false;
		isset($data["id"]) && $this->id = $data["id"];
		isset($data["company_id"]) && $this->company_id = $data["company_id"];
		isset($data["company_name"]) && $this->company_name = $data["company_name"];
		isset($data["type"]) && $this->type = $data["type"];
		isset($data["total"]) && $this->total = $data["total"];
		isset($data["qrz"]) && $this->qrz = $data["qrz"];
		isset($data["fqrz"]) && $this->fqrz = $data["fqrz"];
		isset($data["gcl"]) && $this->gcl = $data["gcl"];
		isset($data["jrgl"]) && $this->jrgl = $data["jrgl"];
		isset($data["qt"]) && $this->qt = $data["qt"];
		isset($data["age"]) && $this->age = $data["age"];
		isset($data["zcfz"]) && $this->zcfz = $data["zcfz"];
		isset($data["gjzg"]) && $this->gjzg = $data["gjzg"];
		isset($data["note"]) && $this->note = $data["note"];
		isset($data["created_at"]) && $this->created_at = $data["created_at"];
		isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
		return $this;
	}

	public function __construct($data='')
	{
		if(!$data) return $this;
		if(is_array($data))
			return $this->fillObject($data);
		else return $this->selectByPk($data);
	}

	public function selectByPk($pk='')
	{
		if(!$pk) return $this;
		$pk = (int)$pk;
    	$db = sf::getLib("db");
		$sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
		$query = $db->query($sql);
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		return $this;
	}

	public function delete()
	{
		if(!$this->id) return false;
		$db = sf::getLib("db");
		$db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
		return $db->affected_rows();
	}

}