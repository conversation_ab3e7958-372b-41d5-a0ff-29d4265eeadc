<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Config;
use App\Model\BaseCorporations;
use App\Facades\Button;

class Corporations extends BaseCorporations
{
    private $user                   = NULL;
    private $department             = NULL;
    private $assets                 = NULL;
    private $staffs                 = NULL;
    private $products               = NULL;
    private $projects               = NULL;
    private $corporation_statistics = NULL;

    function selectByUserId($user_id = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `user_id` = '" . $user_id . "' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setUserId($user_id ? $user_id : sf::getLib("MyString")->getRandString());
        return $this;
    }

    function selectByCode($code = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `code` = '" . $code . "' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setCode($code);
        return $this;
    }

    function selectByOaCode($code)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `oa_code` = '{$code}' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setOaCode($code);
        return $this;
    }

    function selectBySubject($subject)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `subject` = '{$subject}' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setSubject($subject);
        return $this;
    }

    function selectByFullName($fullName)
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `full_name` = '{$fullName}' ");

        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else $this->setFullName($fullName);
        return $this;
    }

    function hasByCode($code = '')
    {
        if ($card_id == '') return true;
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `code` = '" . $code . "' ");
        if ($db->num_rows($query)) return true;
        else return false;
    }

    public function getUser($f = false)
    {
        if ($this->user === NULL || $f)
            $this->user = sf::getModel("Users")->selectByUserId($this->getAccountId());
        return $this->user;
    }

    public function getTalents($type='docter',$f = false)
    {
        if ($this->talent[$type] === NULL || $f)
            $this->talent[$type] = sf::getModel("CorporationTalents")->selectByCompanyId($this->getUserId(),$type);
        return $this->talent[$type];
    }

    function getAccountId()
    {
        $db = sf::getLib("db");
        $sql = "select user_id from user_roles where user_role_id = '" . $this->getUserId() . "' and role_id = '3' ";
        $row = $db->fetch_first($sql);
        return $row['user_id'] ? $row['user_id'] : $this->getUserId();
    }

    /**
     * 是否有项目
     */
    function hasProject()
    {
        if (sf::getModel("Projects")->selectAll("corporation_id = '" . parent::getUserId() . "' ", 'order by id desc', 1)->getTotal()) return true;
        else return false;
    }

    public function cleanObject()
    {
        $this->user = NULL;
        parent::cleanObject();
    }

    public function remove($ids = '')
    {
        return false;//屏蔽该方法
        return parent::remove("user_id in('" . $ids . "')");
    }

    public function delete()
    {
        if ($this->hasProject()) return false;
        sf::getModel("UserRoles")->removeRole($this->getUserId(), 3);//删除角色
        return parent::delete();
    }

    /**
     * 取得快到期的项目列表
     */
    function selectExpires()
    {
        return sf::getModel("Projects")->selectAll("corporation_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at < '".date("Y-m-d",time() + 30*24*60*60)."' and real_end_at > '".date("Y-m-d")."'");
    }

    /**
     * 取得已到期的项目列表
     */
    function selectHasExpires()
    {
        return sf::getModel("Projects")->selectAll("corporation_id = '".$this->getUserId()."' and `statement` = 29 and real_end_at <= '".date("Y-m-d")."' ");
    }

    /**
     * 取得该填写季度报告的项目数量
     */
    function getNeedQuartersCount()
    {
        $db = sf::getLib('db');
        //该写季度报告的项目数量
        $count = $db->result_first("select count(*) c from projects where corporation_id = '".$this->getUserId()."' and `statement` = 29 and (project_type in (3782,3783,3786) or cat_id = 16)");
        //已填写的数量
        $season = (int)ceil((date('n'))/3)-1;//上季度是第几季度
        $year = date('Y', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
        $hasCount = $db->result_first("select count(*) c from project_quarters where company_id = '".$this->getUserId()."' and `year` = '{$year}' and `quarter` = '{$season}'");

        return $count-$hasCount;
    }

    public function decoupling($userid = '')
    {
        if (!$userid) return false;
        return sf::getModel("UserRoles")->remove("user_role_id = '" . $this->getUserId() . "' and role_id = '4' AND user_id = '" . $userid . "' ");
    }

    public function coupling($userid = '')
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, 4);
        if($userrole->isNew()){
            $userrole->setUserRoleId($this->getUserId());
        }else{
            $userrole = sf::getModel("UserRoles");
            $userrole->setUserId($userid);
            $userrole->setRoleId(4);
            $userrole->setUserRoleId($this->getUserId());
        }

        return $userrole->save();
    }

    public function removeAssistant($userid = '')
    {
        if (!$userid) return false;
        return sf::getModel("UserRoles")->remove("user_role_id = '" . $this->getUserId() . "' and role_id = '3' AND user_id = '" . $userid . "' ");
    }

    public function addAssistant($userid = '')
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, 3);
        if($userrole->isNew()){
            $userrole->setUserRoleId($this->getUserId());
        }else{
            $userrole = sf::getModel("UserRoles");
            $userrole->setUserId($userid);
            $userrole->setRoleId(3);
            $userrole->setUserRoleId($this->getUserId());
        }

        return $userrole->save();
    }

    /**
     * 设置默认管理员
     */
    public function setManager($userid = '')
    {
        if (!$userid) return false;
        $user = sf::getModel("Users")->selectByUserId($userid);
        if ($user->isNew()) return false;
        //更新联系人信息
        $this->setLinkman($user->getUserUsername());
        $this->setMobile($user->getUserMobile());
        $this->setLinkmanEmail($user->getUserEmail());
        $this->setPhone($user->getUserPhone());
        $this->setManagerUserId($user->getUserId());
        $this->setUpdatedAt(date('Y-m-d H:i:s'));
        $this->save();//保存结果
        return true;
    }

    public function isManager($userid = '')
    {
        if (!$userid) return false;
        if ($this->getManagerUserId() == $userid) return true;
        else return false;
    }

    public function getManagerUserId()
    {
        if (parent::getManagerUserId()) return parent::getManagerUserId();
        else return $this->getUserId();
    }

    public function getDepartmentName()
    {
        return sf::getModel("departments")->selectByUserId(parent::getDepartmentId())->getSubject();
    }

    public function getParent()
    {
        return sf::getModel("corporations")->selectByUserId(parent::getParentId());
    }

    public function getParentName()
    {
        return sf::getModel("corporations")->selectByUserId(parent::getParentId())->getSubject();
    }

    public function getCorporationPersons()
    {
        return sf::getModel("CorporationPersons")->selectByUserId(parent::getUserId());
    }


    function isHas($code = '')
    {
        if (strlen($code) > 10) {             //社会信用代码
            $code_first = substr($code, 8, 8);//前8位
        } else {                              //组织机构代码
            $code_first = substr($code, 0, 8);//前8位
        }
        if ($this->selectAll("(code = '" . $code . "' OR code LIKE '%" . $code_first . "%') AND user_id <> '" . $this->getUserId() . "'")->getTotal()) return true;
        else return false;
    }

    function isHave($subject = '', $department_id = '')
    {
        $addWhere = "subject = '" . trim($subject) . "' and department_id = '" . $department_id . "'";
        if ($this->selectAll($addWhere)->getTotal()) return true;
        else return false;
    }

    function hasUser($userName = '')
    {
        return $this->getUser(true)->hasUser($userName);
    }

    function getStateByYear($year = '')
    {
        if (!$year) $year = date('Y');
        $statistics = sf::getModel('CorporationStatistics')->getStateByYear(parent::getUserId(), $year);
        if ($statistics->isNew())
            return '未填写';
        else return $statistics->getState();
    }

    function getState()
    {
        switch (parent::getIsLock()) {
            case -1:
            return '<span class="badge badge-danger">已删除</span>';
            case 0:
            return '<span class="badge badge-success">已认证</span>';
            case 1:
            return '<span class="badge badge-warning">等待审核</span>';
            case 2:
            return '<span class="badge badge-danger">审核退回</span>';
            case 3:
            return '<span class="badge badge-danger">认证退回</span>';
            case 4:
            return '<s style="color:#F00;">列入黑名单</s>';
            case 5:
            return '<span class="badge badge-warning">等待认证</span>';
            case 6:
            return '<span class="badge badge-warning">更新证件待核</span>';
            case 7:
            return '<span style="color:#F93;">未认证</span>';
            case 9:
            return '<span class="badge badge-info">单位注册中</span>';
            default:
            return '<s style="color:#F00;">未知状态</s>';
        }
    }

    //基本信息审核状态
    function getStatemen()
    {
        switch (parent::getStatement()) {
            case 1:
            return '<span class="badge badge-warning">完善中</span>';
            break;
            case 11:
            return '<span class="badge badge-info">等待审核</span>';
            break;
            case 7:
            return '<span class="badge badge-danger">审核退回</span>';
            break;
            case 10:
            return '<span class="badge badge-success">审核通过</span>';
            break;
            default:
            return '<s style="color:#F00;">待完善</s>';
            break;
        }
    }

    //经费情况审核状态
    function getStateForMone()
    {
        switch (parent::getStateForMoney()) {
            case 1:
            return '<label class="label label-warning">完善中</label>';
            break;
            case 11:
            return '<label class="label label-info">等待审核</label>';
            break;
            case 7:
            return '<label class="label label-danger">审核退回</label>';
            break;
            case 10:
            return '<label class="label label-success">审核通过</label>';
            break;
            default:
            return '<s style="color:#F00;">待完善</s>';
            break;
        }
    }

    //人才情况审核状态
    function getStateForPerso()
    {
        switch (parent::getStateForPerson()) {
            case 1:
            return '<label class="label label-warning">完善中</label>';
            break;
            case 11:
            return '<label class="label label-info">等待审核</label>';
            break;
            case 7:
            return '<label class="label label-danger">审核退回</label>';
            break;
            case 10:
            return '<label class="label label-success">审核通过</label>';
            break;
            default:
            return '<s style="color:#F00;">待完善</s>';
            break;
        }
    }

    //管理层级
    function getManageLeve()
    {
        switch (parent::getManageLevel()) {
            case 1:
            return '一级企业';
            break;
            case 2:
            return '二级企业';
            break;
            case 3:
            return '三级企业';
            break;
            case 4:
            return '四级企业';
            break;
            case 5:
            return '五级及以上企业';
            break;
            default:
            return '';
            break;
        }
    }

    //是否已通过高企认定
    function getIsHightec()
    {
        switch (parent::getIsHightech()) {
            case 1:
            return '已认定企业';
            break;
            case 2:
            return '培育企业';
            break;
            default:
            return '';
            break;
        }
    }

    /**
     *等待审核的项目数
     */
    function getWaitNum()
    {
        $addWhere = "statement = 6 and `corporation_id` like '".input::session("companyid")."%' and wait_for_company = '".input::session("companylevel")."'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();
    }

    /**
     *等待部门助理审核的项目数
     */
    function getAssistantWaitNum()
    {
        $addWhere = "statement = 4 and `corporation_id` like '".input::session("companyid")."%' and wait_for_company = '".input::session("companylevel")."'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();
    }

    /**
     *已立项的项目数
     */
    function getWaitNum1()
    {
        $addWhere = "statement = 29 and `corporation_id` like '".input::session("companyid")."%'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *已结题的项目数
     */
    function getWaitNum2()
    {
        $addWhere = "statement = 30 and `corporation_id` like '".input::session('companyid')."%'";
        return sf::getModel("projects")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的预算书数
     */
    function getBudgetWaitnum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and state_for_budget_book = 10")->getTotal();

    }

    /**
     *等待审核的计划任务书数
     */
    function getTaskWaitnum()
    {
        $addwhere = "`corporation_id` like '".getParentCompanyId($this->getUserId())."%' and company_level = '".input::session("companylevel")."'";
        if(input::session('userlevel')==4){
            $addwhere.=" and `state_for_plan_book` = 6";
        }else{
            $addwhere.=" and `state_for_plan_book` = 4";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addwhere}");
        return $row['num'];
    }

    /**
     *等待审核的科技奖励数
     */
    function getAwardWaitNum()
    {
        if (isParent(input::getInput("session.roleuserid"))) {
            $addWhere = " (statement = 5 and corporation_id in (select user_id from corporations where parent_id = '" . input::getInput("session.roleuserid") . "')) or (statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "')";
        } else {
            $addWhere = "statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        }
        return sf::getModel("Awards")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的科技奖励数
     */
    function getRewardWaitNum()
    {
        $addWhere = "statement = 2 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Rewards")->selectAll($addWhere)->getTotal();

    }
    /**
     *审核通过的科技奖励数
     */
    function getRewardWaitNum1()
    {
        $addWhere = "statement = 20 and corporation_id = '" . input::getInput("session.roleuserid") . "' ";
        return sf::getModel("Rewards")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的中期报告数
     */
    function getZqbgWaitnum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement >= 0 and state_for_interimreport = 1")->getTotal();

    }

    /**
     *等待审核的验收书数
     */
    function getCompleteWaitnum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `state_for_complete_book` = 4 ";
        }else{
            $addwhere.=" and `state_for_complete_book` = 6 ";
        }
        return sf::getModel("Projects")->selectAll($addwhere)->getTotal();

    }

    /**
     *待审核人员数
     */
    function getDeclareWaitnum()
    {
        return sf::getModel("declarers")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 1")->getTotal();

    }

    /**
     *已注册的人员数
     */
    function getDeclareNum()
    {
        $addWhere = "  corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        return sf::getModel("declarers")->selectAll($addWhere)->getTotal();
    }

    /**
     *已认证人员数
     */
    function getDeclareSubmitNum($includeChilds=false)
    {
        $addWhere = "`is_lock` = 0";
        if($includeChilds){
            $addWhere.=" and corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        }else{
            $addWhere.=" and corporation_id = '".$this->getUserId()."'";
        }
        return sf::getModel("declarers")->selectAll($addWhere)->getTotal();

    }

    /**
     *等待审核的专家数
     */
    function getExpertWaitnum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `is_lock` = 4 ";
        }else{
            $addwhere.=" and `is_lock` = 6 ";
        }
        return sf::getModel("Experts")->selectAll($addwhere)->getTotal();

    }
    /**
     *已注册的专家数
     */
    function getExpertNum()
    {
        $addWhere = "  corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        return sf::getModel("Experts")->selectAll($addWhere)->getTotal();

    }
    /**
     *已认证的专家数
     */
    function getExpertSubmitNum()
    {
        return sf::getModel("Experts")->selectAll("corporation_id = '" . $this->getUserId() . "' and `is_lock` = 0")->getTotal();

    }

    /**
     *驳回的预算申报书数
     */
    function getBudgetRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and `state_for_budget_book` = 2")->getTotal();

    }

    /**
     *驳回的计划任务书数
     */
    function getTaskRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement = 29 and `state_for_plan_book` = 3")->getTotal();

    }

    /**
     *驳回的验收书数
     */
    function getCompleteRejectednum()
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "' and statement = 29 and `state_for_complete_book` = 3")->getTotal();

    }

    /**
     *等待审核的项目外协申请
     */
    function getProjectOutNum()
    {
        return 0;
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `corporation_id`  = '".$this->getUserId()."' and `state_for_out` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的开题报告申请
     */
    function getStartupWaitNum()
    {
        $addWhere = "`corporation_id` like '".input::session("companyid")."%' and company_level = '".input::session("companylevel")."'";
        if($this->isAssistant()){
            $addWhere.=" and `state_for_startup` = 4 ";
        }else{
            $addWhere.=" and `state_for_startup` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addWhere}");
        return $row['num'];
    }

    /**
     *等待审核的中期检查数
     */
    function getInspectWaitNum()
    {
        $addWhere = "`corporation_id` like '".input::session("companyid")."%' and company_level = '".input::session("companylevel")."'";
        if($this->isAssistant()){
            $addWhere.=" and `state_for_inspect` = 4 ";
        }else{
            $addWhere.=" and `state_for_inspect` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addWhere}");
        return $row['num'];
    }

    /**
     *等待审核的年度报告数
     */
    function getStageWaitNum()
    {
        $addWhere = "`corporation_id` like '".input::session("companyid")."%' and company_level = '".input::session("companylevel")."'";
        if($this->isAssistant()){
            $addWhere.=" and `statement` = 4 ";
        }else{
            $addWhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_stages WHERE {$addWhere}");
        return $row['num'];
    }

    /**
     *等待审核的里程碑数
     */
    function getMilestoneWaitNum()
    {
        $addWhere = "`corporation_id` like '".input::session("companyid")."%' and company_level = '".input::session("companylevel")."'";
        if($this->isAssistant()){
            $addWhere.=" and `statement` = 4 ";
        }else{
            $addWhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_milestones WHERE {$addWhere}");
        return $row['num'];
    }

    /**
     *等待审核的经费支出
     */
    function getPaymentNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `is_lock` = 4 ";
        }else{
            $addwhere.=" and `is_lock` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_payments WHERE {$addwhere}");
        return $row['num'];
    }

    /**
     *等待审核的经费到账
     */
    function getIncomeWaitNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `is_lock` = 4 ";
        }else{
            $addwhere.=" and `is_lock` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_incomes WHERE {$addwhere} ");
        return $row['num'];
    }

    function isAssistant()
    {
        return input::session('userlevel')==3;
    }

    /**
     *等待审核的平台数
     */
    function getPlatformNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platforms WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的平台数
     */
    function getPlatformSummaryNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platform_summarys WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的协会组织登记
     */
    function getAssociationMemberNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM association_members WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *等待审核的团队数
     */
    function getTeamWaitNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM teams WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *等待审核的档案数
     */
    function getArchiveWaitNum()
    {
        $addwhere = "`company_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM archives WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *等待审核的档案数
     */
    function getArchiveBorrowWaitNum()
    {
        $addwhere = "`company_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM archive_borrows WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *待审学术会议
     */
    function getConferenceWaitNum()
    {
        $addwhere = "`company_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM conferences WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *待审提名申请
     */
    function getSocietyWaitNum()
    {
        $addwhere = "`corporation_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM society_members WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *等待审核的设备使用情况
     */
    function getDeviceUsageWaitNum()
    {
        $addwhere = "`company_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM device_usages WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *审核通过的科技著作数
     */
    function getAcceptWorkNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    function getAcceptPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

    /**
     *等待审核的工法数
     */
    function getMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *审核通过的工法数
     */
    function getAcceptMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 10 ");
        return $row['num'];
    }

     /**
     * 待上报的联合协议数
     */
    function getJointNum()
    {
        $addwhere = "statement = 4";//单位/部门助理
        if(input::session('userlevel')==4){//单位部门管理员
            $addwhere = "statement = 6";
        }
        $addwhere.=" AND company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_joints WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的新产品数
     */
    function getProductNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE $addwhere ");
        return $row['num'];
    }

    /**
     *等待审核的专利成果数
     */
    function getPatentNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE $addwhere ");
        return $row['num'];
    }

    /**
     *等待审核的著作权数
     */
    function getSoftNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的奖励数
     */
    function getRewardNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科技论文数
     */
    function getPaperNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科技著作数
     */
    function getWorkNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getStandardNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM standards WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的成果转化数
     */
    function getTransformNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM transforms WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getScientificSuiteNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM scientific_suites WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getCertificateNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM certificates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getLicenseNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM licenses WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getQualificateNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM qualificates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的奖励
     */
    function getAwardNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and corporation_id like '".input::session("companyid")."%' and wait_for_company = '".input::session("companylevel")."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM awards WHERE $addwhere");
        return $row['num'];
    }
    /**
     *等待审核的科研考核
     */
    function getEvaluateWaitNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM evaluates WHERE $addwhere");
        return $row['num'];
    }
    /**
     *等待审核的论文奖金申请
     */
    function getBonusPaperWaitNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_papers WHERE $addwhere");
        return $row['num'];
    }
    /**
     *等待审核的专利奖金申请
     */
    function getBonusPatentWaitNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_patents WHERE $addwhere");
        return $row['num'];
    }
    /**
     *等待审核的绩效支出申请
     */
    function getBonusPerformanceWaitNum()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " and company_id = '".input::session('roleuserid')."'";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_performances WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的文章数
     */
    function getArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE `corporation_id`  = '".$this->getUserId()."' and `statement` = 2 ");
        return $row['num'];
    }

    /**
     *等待审核的季度执行报告
     */
    function getQuarterWaitnum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_quarters WHERE company_id = '".$this->getUserId()."' and statement = 2");
        return $row['num'];
    }

    /**
     *等待科研部审核的变更申请
     */
    function getChangeWaitnum()
    {
        $addwhere = "`company_id`  = '".$this->getUserId()."'";
        if($this->isAssistant()){
            $addwhere.=" and `statement` = 4 ";
        }else{
            $addwhere.=" and `statement` = 6 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_changes WHERE {$addwhere}");
        return $row['num'];
    }

    /**
     *审核通过的专利成果数
     */
    function getAcceptPatentNum()
    {
        $addWhere = "  corporation_id like '".getParentCompanyId($this->getUserId())."%' and `statement` = 20";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE {$addWhere} ");
        return $row['num'];
    }

    /**
     * 已审核的新产品数
     */
    function getAcceptProductNum()
    {
        $addWhere = "  corporation_id like '".getParentCompanyId($this->getUserId())."%' and `statement` = 20";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE {$addWhere} ");
        return $row['num'];
    }
    /**
     * 已审核的科普文章数
     */
    function getAcceptArticleNum()
    {
        return 0;
    }

    /**
     * 已审核的软著数
     */
    function getAcceptSoftNum()
    {
        $addWhere = "  corporation_id like '".getParentCompanyId($this->getUserId())."%' and `statement` = 20";
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE {$addWhere} ");
        return $row['num'];
    }



    function getCode($len = 0, $is_verify = false)
    {
        if (!$is_verify) return parent::getCode();

        if (isUnitCode(parent::getCode())) return parent::getCode();
        elseif (isCreditCode(parent::getCode())) return parent::getCode();
        else return '<s style="color:red">' . parent::getCode() . '</s>';
    }

    /**
     * 检查身份证是否正确
     */
    function getVerify()
    {
        if (isUnitCode(parent::getCode())) return true;
        elseif (isCreditCode(parent::getCode())) return true;
        else return false;
    }

    function selectHistorys($showMax = 0)
    {
        return sf::getModel("historys")->selectAll("project_id = '" . $this->getUserId() . "' ", 'ORDER BY `updated_at` DESC', $showMax);
    }

    function getAttachments()
    {
        return sf::getModel("filemanager")->selectAll("item_id = '" . $this->getUserId() . "' AND item_type IN ('company','corporation')", "ORDER BY created_at ASC");
    }

    /**
     * 取得项目列表
     */
    function selectProject($num = 0)
    {
        return sf::getModel("projects")->selectAll("corporation_id = '" . $this->getUserId() . "'", "ORDER BY updated_at DESC", $num);
    }

    /**
     * 验证信息是否完整
     */
    public function validate()
    {
        $message = array();

        if (!$this->getSubject()) $message[] = '【基本信息】中的【单位名称】必须正确填写；';
        // if(!$this->getVerify()) 		$message[] = '申报单位机构代码必须正确填写！';
        if (!$this->getParentId()) $message[] = '【基本信息】中的【上级单位】必须填写；';
        if (!trim($this->getPrincipal())) $message[] = '【基本信息】中的【法人代表 / 单位负责人】必须填写；';
        if (!trim($this->getAddress())) $message[] = '【基本信息】中的【单位地址】必须填写；';
        if (!$this->getAreaCode() || $this->getAreaCode() == '510000') $message[] = '【基本信息】中的【所属地区】必须选择！';
        if (!trim($this->getLinkman())) $message[] = '【基本信息】中的【联系人】必须填写；';
        if (!isMobile($this->getMobile())) $message[] = '【基本信息】中的【联系人手机号码】必须正确填写；';
        if (!trim($this->getPhone())) $message[] = '【基本信息】中的【联系人座机】必须填写；';
        if (!trim($this->getLinkmanEmail())) $message[] = '【基本信息】中的【联系人电子邮箱】必须填写；';
        if ($this->getIsLock() == 4) $message[] = '申报单位被列入黑名单！';
        // if(!$this->getAttachments()->getTotal()) 	$message[] = '请在上传单位认证的必要的附件！';
        $user = $this->getUser();
        if (!trim($user->getUserUsername())) $message[] = '【账号信息】中的【姓名】必须填写；';
        if (!trim($user->getUserIdcard())) $message[] = '【账号信息】中的【身份证号】必须填写；';
        if (!trim($user->getUserMobile())) $message[] = '【账号信息】中的【手机号码】必须填写；';
        if (!trim($user->getUserEmail())) $message[] = '【账号信息】中的【安全邮箱】必须填写；';

        //如果找不到上一年的统计信息或统计信息没有审核通过，则不允许上报项目(明年奶奶3月份)
        return $message;
    }

    /**
     * 发送短消息给用户
     */
    public function sendMessage($message = '', $item_id = '', $item_type = 'projects', $send_at = '')
    {
        if (!isMobile($this->getMobile())) return false;
        return sf::getModel("ShortMessages")->sendSms($this->getMobile(), $message, $item_id, $item_type, $send_at);
    }

    function selectByUserName($userName = '')
    {
        $this->cleanObject();//清空对象
        $db = sf::getLib("db");
        $result = $db->fetch_first("SELECT * FROM " . $this->table . "  WHERE user_id IN (SELECT user_id FROM unit_users WHERE user_name = '" . $userName . "') ");
        if ($result) $this->fillObject($result);
        return $this;
    }

    /**
     * 查找相识的用户
     */
    public function selectSameUser()
    {
        return $this->selectAll("user_id != '" . parent::getUserId() . "' AND (subject LIKE '%" . parent::getSubject() . "%' OR code = '" . parent::getCode() . "')", "ORDER BY id ASC");
    }

    function getRepeatCount()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM " . $this->table . " WHERE trim(code) = '" . $this->getCode() . "' ");
        return $row['num'];
    }

    /**
     * 是否锁定
     */
    function hasLock()
    {
        if (in_array($this->getIsLock(), [1, 4, 5, 6])) return true;
        return false;
    }

    /**
     * 获取集团公司单位
     * @return mixed
     */
    public function getDepartments()
    {
        return sf::getModel('Categorys', '', 'department')->selectAll();
    }

    public function getDepartmentType()
    {
        $obj = M('Departments')->selectAll("user_id = '{$this->getDepartmentId()}'")->getObject();
        if ($obj !== false) return $obj->getType();
        else return 0;
    }

    public function getCompanyProperty()
    {
        return $this->getProperty();
    }

    public function getDepartmentOption()
    {
        $type = $this->getDepartmentType();
        $departments = M('Departments')->selectAll("type = '{$type}'");
        $htmlStr = '';
        while ($item = $departments->getObject()) {
            $selected = ($item->getUserId() == $this->getDepartmentId()) ? 'selected' : '';
            $htmlStr .= "<option value={$item->getUserId()} {$selected}>{$item->getSubject()}</option>";
        }

        return $htmlStr;
    }

    public function getDepartmentModel($f = false)
    {
        if ($this->department === NULL || $f) $this->department = M('Departments')->selectAll("user_id = '{$this->getDepartmentId()}'")->getObject();
        return $this->department;
    }

    /**
     * 根据权限定义操作菜单
     */
    function getCustomButton($type = 'show')
    {
        $htmlStr = Button::back();
        //$htmlStr .= Button::window('查看记录', site_url("admin/history/index/type/corporations/id/" . $this->getUserId()), 'time', 500, 360);
        $htmlStr .= Button::setName('查看记录')->setUrl(site_url("admin/history/index/type/corporations/id/" . $this->getUserId()))->setWidth('550px')->setHeight('80%')->window();
        switch (input::getInput("session.userlevel")) {
            case 1:
            if ($this->getIsLock() != 0)
                $htmlStr .= Button::link("认证", site_url("admin/corporation/dosubmit/userid/" . $this->getUserId()), "doit");
//                $htmlStr .= Button::window("退回", site_url("admin/corporation/doBack/userid/" . $this->getUserId()), "delete", 500, 360, 'btn-sm', 'btn-danger');
            $htmlStr .= Button::setName('退回')->setUrl(site_url("admin/corporation/doBack/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("发送短消息", site_url("message/short/edit/item_type/experts/item_id/" . $this->getUserId() . '/mobile/' . $this->getMobile()), "send", 400, 300);
            $htmlStr .= Button::setName('发送短消息')->setUrl(site_url("message/short/edit/item_type/experts/item_id/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("用户合并", site_url("admin/corporation/more/userid/" . $this->getUserId()), "mark", 600, 460);
//                $htmlStr .= Button::setName('用户合并')->setUrl(site_url("admin/corporation/more/userid/" . $this->getUserId()))->setWidth('550px')->setHeight('80%')->window();
//                $htmlStr .= Button::window("编辑用户", site_url("admin/corporation/edit/userid/" . $this->getUserId()), "edit", 600, 460);
            $htmlStr .= Button::setName('编辑用户')->setUrl(site_url("admin/corporation/edit/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window('打入黑名单', site_url("admin/corporation/doBlack/userid/" . $this->getUserId()), 'lock', 500, 360, 'btn-sm', 'btn-danger');
            $htmlStr .= Button::setName('打入黑名单')->setUrl(site_url("admin/corporation/doBlack/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("重置密码", site_url("admin/corporation/users/userid/" . $this->getUserId()), "list", 600, 460, 'btn-sm', 'btn-danger');
            $htmlStr .= Button::setName('重置密码')->setUrl(site_url("admin/corporation/users/userid/" . $this->getUserId()))->window();
//                $htmlStr .= Button::window("删除", site_url("admin/corporation/delete/userid/" . $this->getUserId()), "list", 500, 360, 'btn-sm', 'btn-danger');
            break;
            case 3:
            if ($this->getUserId() == input::getInput("session.roleuserid")) {
                if (!in_array($this->getIsLock(), [1, 4, 5, 6, 7]))
                    $htmlStr .= Button::link("资料上报", site_url("unit/profile/submit/userid/" . $this->getUserId()), "submit");
            }
            break;
            case 4:
            if ($this->getDepartmentId() == input::getInput("session.roleuserid")) {
                if (in_array($this->getIsLock(), [1, 2]))
                    $htmlStr .= Button::link("推荐", site_url("gather/corporation/dosubmit/userid/" . $this->getUserId()), "doit", 'btn-success');
                $htmlStr .= Button::window("退回", site_url("gather/corporation/doBack/userid/" . $this->getUserId()), "delete", 500, 360, 'btn-danger');
                $htmlStr .= Button::setClass('btn-alt-danger')->window("重置密码", site_url("gather/corporation/users/userid/" . $this->getUserId()));
            }
            break;
            case 6:
            $htmlStr .= Button::setClass('btn-alt-danger')->window("重置密码", site_url("gather/corporation/users/userid/" . $this->getUserId()), "refresh");
            break;
            default:
            break;
        }
        return $htmlStr;
    }

    public function getAreaCode()
    {
        if (!parent::getAreaCode()) return '510000';
        else return parent::getAreaCode();
    }

    /**
     * 获得企业资产信息模型
     * @param bool $f
     * @return null
     */
    public function getAssets($f = false, $year = '')
    {
        $assetModel = $this->getAssetsModel();
        if ($f && $year) {
            $this->assets = $assetModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->assets === NULL || $f) {
            $this->assets = $assetModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->assets->getTotal() == 0) $this->assets = NULL;
        }
        return $this->assets;
    }

    /**
     * 检查企业是否已存在某年度的资产信息
     */
    public function checkAssetYear($year)
    {
        $assets = $this->getAssets(true, $year);
        $count = $assets->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }


    public function getAssetsModel()
    {
        return M('CorporationsAsset');
    }

    public function getStaffsModel()
    {
        return M('CorporationsStaff');
    }

    public function getProductModel()
    {
        return M('CorporationsProduct');
    }

    public function getProjectModel()
    {
        return M('CorporationsProject');
    }

    /**
     * 获得企业员工信息模型
     * @param bool $f
     * @return null
     */
    public function getStaffs($f = false, $year = '')
    {
        $staffModel = $this->getStaffsModel();
        if ($f && $year) {
            $this->staffs = $staffModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->staffs === NULL || $f) {
            $this->staffs = $staffModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->staffs->getTotal() == 0) $this->staffs = NULL;
        }
        return $this->staffs;
    }

    /**
     * 检查企业是否已存在某年度的职工概况
     */
    public function checkStaffYear($year)
    {
        $staffs = $this->getStaffs(true, $year);
        $count = $staffs->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    /**
     * 检查企业是否已存在某年度的产品信息
     */
    public function checkProductYear($year)
    {
        $products = $this->getProducts(true, $year);
        $count = $products->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    /**
     * 获得企业产品模型
     * @param bool $f
     * @return null
     */
    public function getProducts($f = false, $year = '')
    {
        $productModel = $this->getProductModel();
        if ($f && $year) {
            $this->products = $productModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->products === NULL || $f) {
            $this->products = $productModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->products->getTotal() == 0) $this->products = NULL;
        }
        return $this->products;
    }

    /**
     * 获得企业产品模型
     * @param bool $f
     * @return null
     */
    public function getProjects($f = false, $year = '')
    {
        $projectModel = $this->getProjectModel();
        if ($f && $year) {
            $this->projects = $projectModel->selectByUserIdAndYear($this->getUserId(), $year);
        } elseif ($this->projects === NULL || $f) {
            $this->projects = $projectModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `year` DESC");
            if ($this->projects->getTotal() == 0) $this->projects = NULL;
        }
        return $this->projects;
    }

    /**
     * 检查企业是否已存在某年度的项目信息
     */
    public function checkProjectYear($year)
    {
        $projects = $this->getProjects(true, $year);
        $count = $projects->selectAll("user_id = '{$this->getUserId()}' and year = '{$year}'")->getTotal();
        if ($count == 0) return true;
        else return false;
    }

    public function selectPatents()
    {
        return sf::getModel("CorporationsPatent")->selectAll("user_id = '" . $this->getUserId() . "'");
    }

    public function selectUsers()
    {
        return $this->selectManagers();
    }

    public function selectManagers()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getUserId() . "' AND role_id = '4')");
    }

    public function selectAssistants()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getUserId() . "' AND role_id = '3')");
    }

    public function users()
    {
        return $this->selectUsers();
    }

    public function getMark()
    {
        $htmlStr = '[' . $this->getLevel() . ']';
        if(!empty($this->getProperty()) && $this->getProperty()!='O') $htmlStr .= '['.$this->getProperty().']';
        return '<mark>'.$htmlStr.'</mark>';
    }

    function toBlack()
    {
        $this->setIsLock(4);//设置为黑名单
        //设置账号不可登录
        $users = $this->users();
        while ($user = $users->getObject()) {
            $user->setIsLock(4);
            $user->save();
        }
        return $this->save();
    }

    public function getUnitArea()
    {
        if (empty(parent::getUnitArea())) {
            //如果区域为空，默认选中四川省
            return '510000';
        } else {
            return parent::getUnitArea();
        }
    }

    /**
     * 获得企业专利信息
     * @param bool $f
     * @return null
     */
    public function getPatents($f = false)
    {
        $patentModel = $this->getPatentModel();
        if ($this->patents === NULL || $f) {
            $this->patents = $patentModel->selectAll("user_id = '{$this->getUserId()}'", "ORDER BY `created_at` DESC");
            if ($this->patents->getTotal() == 0) $this->patents = NULL;
        }
        return $this->patents;
    }

    public function getPatentModel()
    {
        return M('CorporationsPatent');
    }

    /**
     * 获取企业年度职工状态
     * @param $year
     * @return mixed
     */
    public function getStaffState($year)
    {
        $staffModel = $this->getStaffs(true, $year);
        return $staffModel->getIsLock();
    }

    /**
     * 获取企业年度产品状态
     * @param $year
     * @return mixed
     */
    public function getProductState($year)
    {
        $productModel = $this->getProducts(true, $year);
        return $productModel->getIsLock();
    }

    /**
     * 获取企业年度项目状态
     * @param $year
     * @return mixed
     */
    public function getProjectState($year)
    {
        $projectModel = $this->getProjects(true, $year);
        return $projectModel->getIsLock();
    }

    public function getAreas()
    {
        if (!parent::getAreaCode()) return '/';
        return sf::getModel('Citys')->selectByCode(parent::getAreaCode())->getSubject();
    }

    function getStatistics()
    {
        $statistics = sf::getModel('CorporationStatistics')->getPager(" corporation_id='" . parent::getUserId() . "' ORDER BY year ASC ");
        return $statistics;
    }

    function getUnitManagers()
    {
        $users = [];
        $db = sf::getLib("db");
        $sql = "select c.manager_user_id as manager_user_id from corporations as c,corporation_statistics as cs where c.user_id=cs.corporation_id AND c.property IN('企业','国有企业','外资企业','民营企业','高新企业')";
        $query = $db->query($sql);
        while ($row = $db->fetch_array($query)) {
            if (!array_key_exists($row['manager_user_id'], $users))
                $users[$row['manager_user_id']] = sf::getModel("Users")->selectByUserId($row['manager_user_id']);
        }
        return $users;
    }

    //科研经费
    function getMoneyByYear($year = '')
    {
        return sf::getModel("CorporationMoneys")->selectByUserId(parent::getUserId(), $year ?: date('Y'));
    }

    /**
     * 单位财务信息数据集
     */
    function finances()
    {
        return sf::getModel("CorporationFinances")->selectAll("user_id = '" . $this->getUserId() . "' ", "ORDER BY year DESC");
    }

    /**
     * 取得指定年度的财务数据
     */
    function getFinance($year)
    {
        if ($this->isNew()) return false;
        return sf::getModel("CorporationFinances")->selectByYear($year, $this->getUserId());
    }

    /**
     * 获取法人手机号码
     * @return [type] [description]
     */
    function getLegalPersonMobile()
    {
        return $this->getPrincipalPhone();
    }

    /**
     * 设置法人手机号码
     * @param string $v 手机号码
     */
    function setLegalPersonMobile($v)
    {
        parent::setPrincipalPhone($v);
        return $this;
    }

    /**
     * 获取法人姓名
     * @return [type] [description]
     */
    function getLegalPersonName()
    {
        return $this->getPrincipal();
    }

    /**
     * 设置法人姓名
     * @param string $v 手机号码
     */
    function setLegalPersonName($v)
    {
        parent::setPrincipal($v);
        return $this;
    }

    /**
     * 获取法人身份证号
     * @return [type] [description]
     */
    function getLegalPersonIdcard()
    {
        return $this->getPrincipalCardid();
    }

    /**
     * 设置法人身份证号
     * @param string $v 手机号码
     */
    function setLegalPersonIdcard($v)
    {
        parent::setPrincipalCardid($v);
        return $this;
    }

    /**
     * 是否是叶子节点
     */
    function isLeaf()
    {
        $db = sf::getLib('db');
        $count = $db->result_first("select count(*) c from `" . $this->table . "` where parent_id = '" . $this->getUserId() . "'");
        return $count == 0;
    }

    function getChilds()
    {
        $childs = $this->selectAll("parent_id = '" . $this->getUserId() . "'", "order by orders asc,id asc");
        $childArrs = [];
        $i=0;
//        if($this->getProperty()=='company'){
//            $childArrs[$i]['n'] = '本级';
//            $childArrs[$i]['v'] = $this->getUserId();
//            $i++;
//        }
        while ($child = $childs->getObject()) {

            $childArrs[$i]['n'] = $child->getSubject();
            $childArrs[$i]['v'] = $child->getUserId();
            if ($child->isLeaf() === false) {
                $childArrs[$i]['s'] = $child->getChilds();
            }
            $i++;
        }
        return $childArrs;
    }

    function getChildCompanys($property='')
    {
        $addwhere = "`user_id` like '" . $this->getCompanyId() . "%' and `level` > '".$this->getLevel()."'";
        if($property) $addwhere .= " and `property` = '".$property."'";
        return $this->selectAll($addwhere, "order by `level` asc,`orders` asc");;
    }

    function getCompanyField()
    {
        switch ($this->getLevel()){
            case 1:
            return 'first_id';
            case 2:
            return 'second_id';
            case 3:
            return 'third_id';
            case 4:
            return 'fourth_id';
        }
    }

    function getManagerArr()
    {
        $users = $this->selectUsers();
        $userNames = [];
        $i=0;
        while($user = $users->getObject()){
            if($this->isManager($user->getUserId())){
                $userNames[0]['user_id'] = $user->getUserId();
                $userNames[0]['user_name'] = $user->getUserUsername();
            }else{
                $userNames[$i+1]['user_id'] = $user->getUserId();
                $userNames[$i+1]['user_name'] = $user->getUserUsername();
            }
            $i++;
        }
        ksort($userNames);
        $managers = array_column($userNames, 'user_name', 'user_id');
        return $managers;
    }

    function getManagerNames()
    {
        $users = $this->selectUsers();
        $userNames = [];
        $i=0;
        while($user = $users->getObject()){
            if($this->isManager($user->getUserId())){
                $userNames[0] = $user->getUserUsername();
            }else{
                $userNames[$i+1] = $user->getUserUsername();
            }
            $i++;
        }
        ksort($userNames);
        return implode('、',$userNames);
    }

    function getAssistantNames()
    {
        $users = $this->selectAssistants();
        $userNames = [];
        $i=0;
        while($user = $users->getObject()){
            if($user->getUserType()!='EMPLOYEE') continue;
            $userNames[$i] = $user->getUserUsername();
            $i++;
        }
        ksort($userNames);
        return implode('、',$userNames);
    }

    public function getLevelStr()
    {
        return $this->getBigNumber(parent::getLevel()).'级';
    }

    private function getBigNumber($num)
    {
        $arr = array("零","一","二","三","四","五","六","七","八","九","十");
        return $arr[$num];
    }

    public function getMoneys($year=5)
    {
        $years = [];
        for($year=date('Y',strtotime('-'.($year-1).' year'));$year<=date('Y');$year++){
            $years[] = $year;
        }

        $moneys =  sf::getModel('CorporationMoneys')->selectAll("user_id = '".$this->getUserId()."' and `year` in (".implode(',',$years).")");
        $datas = [];
        while($money = $moneys->getObject()){
            $datas[$money->getYear()] = $money->toArray();
        }
        return $datas;
    }

    public function setProperty($arr)
    {
        if(is_array($arr)) {
            return parent::setProperty(implode('|',$arr));
        }
        return parent::setProperty($arr);
    }

    public function getProperty()
    {
        return str_replace('|','、',parent::getProperty());
    }

    public function getPropertyArr()
    {
        return explode('|',parent::getProperty());
    }

    public function getPropertySubject()
    {
        if($this->isO()){
            return '都不是';
        }
        $categorySubjectArr = [];
        if($this->isG()){
            $categorySubjectArr[] = '高新技术企业（已认定）';
        }
        if($this->isP()){
            $categorySubjectArr[] = '高新技术企业（培育）';
        }
        if($this->isK()){
            $categorySubjectArr[] = '科技型中小企业';
        }
        if($this->isD()){
            $categorySubjectArr[] = '四川省瞪羚企业';
        }
        if($this->isQ()){
            $categorySubjectArr[] = '其他科技型企业';
        }
        return getCheckedStr(['高新技术企业（已认定）','高新技术企业（培育）','科技型中小企业','四川省瞪羚企业','其他科技型企业','都不是'], $categorySubjectArr);
    }

    public function getPropertyStr()
    {
        if($this->isO()){
            return '都不是';
        }
        $categorySubjectArr = [];
        if($this->isG()){
            $categorySubjectArr[] = '高新技术企业';
        }
        if($this->isP()){
            $categorySubjectArr[] = '高新技术企业（培育）';
        }
        if($this->isK()){
            $categorySubjectArr[] = '科技型中小企业';
        }
        if($this->isD()){
            $categorySubjectArr[] = '四川省瞪羚企业';
        }
        if($this->isQ()){
            $categorySubjectArr[] = '其他科技型企业';
        }
        return implode('、',$categorySubjectArr);
    }

    public function getFmzl()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = '发明' and statement = 20") ->getTotal();
    }

    public function getSyxx()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = '实用新型' and statement = 20") ->getTotal();
    }

    public function getWgsj()
    {
        return sf::getModel('Patents')->selectAll("corporation_id = '".$this->getUserId()."' and `type` = '外观设计' and statement = 20") ->getTotal();
    }

    public function getRjzzq()
    {
        return sf::getModel('Softs')->selectAll("corporation_id = '".$this->getUserId()."' and statement = 20") ->getTotal();
    }

    public function getPersoncount()
    {
        return sf::getLib('db')->result_first("select count(*) c from `declarers` where corporation_id like '".getParentCompanyId($this->getCompanyId())."%'");
    }


    /**
     * 是否是高新技术企业
     * @return bool|string
     */
    public function isG()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('G',$categorys);
    }


    /**
     * 是否是高新技术企业(培育)
     * @return bool|string
     */
    public function isP()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('P',$categorys);
    }


    /**
     * 是否是科技型中小企业
     * @return bool|string
     */
    public function isK()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('K',$categorys);
    }

    /**
     * 是否是瞪羚企业
     * @return bool
     */
    public function isD()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('D',$categorys);
    }

    /**
     * 是否是其他科技型企业
     * @return bool
     */
    public function isQ()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('Q',$categorys);
    }

    /**
     * 不属于任何科技型企业
     * @return bool
     */
    public function isO()
    {
        if(empty(parent::getProperty())) return false;
        $categorys = $this->getPropertyArr();
        return in_array('O',$categorys);
    }

    public function getMoneyUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_moneys` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'<i class="text-danger">未填写</i>';
    }

    public function getPersonUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_persons` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'<i class="text-danger">未填写</i>';
    }

    public function getInnovateUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_innovates` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'-';
    }

    public function getPlatformUpdatedAt()
    {
        $updatedAt =  sf::getLib('db')->result_first("SELECT updated_at FROM `corporation_platforms` where user_id = '".$this->getUserId()."' order by `updated_at` desc");
        return $updatedAt?:'-';
    }

    public function getInnovateCount()
    {
        $count =  sf::getLib('db')->result_first("SELECT count(*) c FROM `corporation_innovates` where user_id = '".$this->getUserId()."'");
        return (int)$count;
    }

    public function getPlatformCount()
    {
        $count =  sf::getLib('db')->result_first("SELECT count(*) c FROM `corporation_platforms` where user_id = '".$this->getUserId()."'");
        return (int)$count;
    }

    public function getChildUserId()
    {
        $userId = $this->getUserId();
        $count =  sf::getLib('db')->result_first("SELECT count(*) c FROM `".$this->table."` where parent_id = '".$userId."'");
        $str = str_pad(($count+1),2,'0',STR_PAD_LEFT);
        $newUserId = rtrimZero($userId);
        $newUserId = $newUserId.$str;
        $newUserId = str_pad($newUserId,36,'0',STR_PAD_RIGHT);

		//判断是否重复
		$c =  sf::getLib('db')->result_first("SELECT count(*) c FROM `".$this->table."` where user_id = '".$newUserId."'");
        if($c>0){
	        $str = str_pad(($count+2),2,'0',STR_PAD_LEFT);
	        $newUserId = rtrimZero($userId);
	        $newUserId = $newUserId.$str;
	        $newUserId = str_pad($newUserId,36,'0',STR_PAD_RIGHT);
        }
        return $newUserId;
    }

    /**
     * 返回单位ID
     */
    function getCompanyId()
    {
        return rtrimZero($this->getUserId());
    }


    function getFirstId()
    {
        $firstId = substr($this->getUserId(),0,24);
        return str_pad($firstId,36,'0',STR_PAD_RIGHT);
    }

    function getSecondId()
    {
        $firstId = substr($this->getUserId(),0,26);
        return str_pad($firstId,36,'0',STR_PAD_RIGHT);
    }

    function getThirdId()
    {
        $firstId = substr($this->getUserId(),0,28);
        return str_pad($firstId,36,'0',STR_PAD_RIGHT);
    }

    function getFourthId()
    {
        $firstId = substr($this->getUserId(),0,30);
        return str_pad($firstId,36,'0',STR_PAD_RIGHT);
    }

    function hasChild()
    {
        $childCount = sf::getLib('db')->result_first("select count(*) c from `".$this->table."` where parent_id = '".$this->getUserId()."'");
        return $childCount>0;
    }

    function getChild()
    {
        $datas = [];
        $level = $this->getLevel()+1;
        $companyId = $this->getCompanyId();
        $addwhere = "level = {$level} and user_id like '{$companyId}%'";
        $childs = $this->selectAll($addwhere,"order by user_id asc");
        $i = 0;
        while($child = $childs->getObject()){
            $datas[$i] = [
                'n'=>$child->getSubject(),
                'v'=>$child->getUserId(),
            ];

            if($child->hasChild()){
                $datas[$i]['s'] = $child->getChild();
            }
            $i++;
        }
        return $datas;
    }

    function getChildmap()
    {
        $datas = [];
        $level = $this->getLevel()+1;
        $companyId = $this->getCompanyId();
        $addwhere = "level = {$level} and user_id like '{$companyId}%'";
        $childs = $this->selectAll($addwhere,"order by orders asc,id asc");
        $i = 0;
        while($child = $childs->getObject()){
            $datas[$i] = [
                'name'=>$child->getSubject(),
                'value'=>$child->getUserId(),
            ];

            if($child->hasChild()){
                $datas[$i]['children'] = $child->getChildmap();
            }
            $i++;
        }
        return $datas;
    }

    function getAllSubject()
    {
        $names = [];
        switch ($this->getLevel()){
            case 1:
                $names[] = $this->getSubject();
                break;
            case 2:
                $names[] = $this->getSubject();
                break;
            case 3:
                $parent = $this->getParent();
                $names[] = $parent->getSubject();
                $names[] = $this->getSubject();
                break;
            case 4:
                $parent = $this->getParent();
                $names[] = $parent->getParent()->getSubject();
                $names[] = $parent->getSubject();
                $names[] = $this->getSubject();
                break;
        }
        return implode('/',$names);
    }

    function getAllParentSubject()
    {
        $names = [];
        switch ($this->getLevel()){
            case 1:
                $names[] = $this->getSubject();
                break;
            case 2:
                $parent = $this->getParent();
                $names[] = $parent->getSubject();
                break;
            case 3:
                $parent = $this->getParent();
                $names[] = $parent->getParent()->getSubject();
                $names[] = $parent->getSubject();
                break;
            case 4:
                $parent = $this->getParent();
                $names[] = $parent->getParent()->getParent()->getSubject();
                $names[] = $parent->getParent()->getSubject();
                $names[] = $parent->getSubject();
                break;
        }
        return implode('/',$names);
    }

    public function selectResearchers($includeChilds=false,$limit=10)
    {
        $addWhere = "is_lock = 0";
        if($includeChilds){
            $addWhere.=" and corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        }else{
            $addWhere.=" and corporation_id = '".$this->getUserId()."'";
        }
        return sf::getModel('Declarers')->selectAll($addWhere,"order by orders asc,id asc",$limit);
    }

    public function selectExperts($includeChilds=false,$limit=10)
    {
        $addWhere = "1 ";
        if($includeChilds){
            $addWhere.=" and corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        }else{
            $addWhere.=" and corporation_id = '".$this->getUserId()."'";
        }
        return sf::getModel('Experts')->selectAll($addWhere,"order by id asc",$limit);
    }

    public function selectProjects($includeChilds=false,$limit=10)
    {
        $addWhere = "1 ";
        if($includeChilds){
            $addWhere.=" and corporation_id like '".getParentCompanyId($this->getUserId())."%'";
        }else{
            $addWhere.=" and corporation_id = '".$this->getUserId()."'";
        }
        return sf::getModel('Projects')->selectAll($addWhere,"order by updated_at asc",$limit);
    }

    public function isSpecialCompany()
    {
        return isSpecialCompany($this->getSubject());
    }

    public function getAllowLoginHtml()
    {
        if(parent::getAllowLogin()=='yes'){
            return Button::setUrl(site_url('admin/corporation/allowLogin/id/'.$this->getUserId().'/type/forbidden'))->setClass('btn-alt-success')->setIcon('check')->window();
        }else{
            return Button::setUrl(site_url('admin/corporation/allowLogin/id/'.$this->getUserId().'/type/allow'))->setClass('btn-alt-danger')->setIcon('close')->window();
        }

    }

    public function setLoginAuth($data)
    {
        $childs = $this->getChildCompanys();
        while($child = $childs->getObject()){
            $child->setAllowLogin($data);
            $child->save();
        }
        $this->setAllowLogin($data);
        return true;
    }

    public function getSecondCompany()
    {
        if($this->getLevel()<=2) return $this;
        $companyId = $this->getSecondCompanyId();
        return sf::getModel('Corporations')->selectByUserId($companyId);
    }

    public function getSecondCompanyId()
    {
        $secondCompanys = getSecondCompanys();
        $companyId = getParentCompanyId($this->getUserId(),3,true);
        if(array_key_exists($companyId,$secondCompanys)) return $companyId;
        return getParentCompanyId($this->getUserId(),2,true);
    }

    public function getSecondCompanyName()
    {
        $companyId = $this->getSecondCompanyId();
        $company = sf::getModel('Corporations')->selectByUserId($companyId);
        return $company->getSubject();
    }

    /**
     *等待立项的项目数
     */
    function getRadicateWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE is_own = 1 and statement = 20 and corporation_id = '".$this->getUserId()."'");
        return $row['num'];
    }

    /*
     * 获取公司ID
     * 审定中心代管可持续航空燃料发展研究中心、航油事业部
     * */
    public function getCompanyIdArr()
    {
        $companyIds[] = $this->getUserId();
        if($this->getUserId()=='9B581D83-B2BA-7337-0678-240000000000'){
            //如果是审定中心，需要把可持续航空燃料发展研究中心、航油事业部的成果也算进来
            $companyIds[] = '9B581D83-B2BA-7337-0678-030000000000';
            $companyIds[] = '9B581D83-B2BA-7337-0678-220000000000';
        }
        return $companyIds;
    }
    public function getCompanyIds()
    {
        $companyIds = $this->getCompanyIdArr();
        return "'".implode("','",$companyIds)."'";
    }
}