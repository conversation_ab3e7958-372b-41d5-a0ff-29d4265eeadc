<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Support\Model as BaseModel;
/**
 * 类名：数据模型基本类
 * 说明：提供数据模型公用方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */

class BaseProjectBaseinfos extends BaseModel
{
  private $id;
 private $type  = 'apply';
  private $project_id;
  private $linkman;
  private $linkman_mobile;
  private $linkman_tel;
  private $special_subject;
  private $skill_domain;
  private $support_direction;
  private $skill_source;
  private $skill_source_type;
  private $skill_level;
  private $awards;
  private $apply_domain;
  private $produce_domain;
  private $innovation_type;
  private $level;
  private $fruits;
  private $fruit_source;
  private $fruit_form;
  private $fruit_level;
  private $budget;
  private $patent;
  private $yield_study;
  private $fruit_award;
  private $work_progress;
  private $market_demand;
  private $market_target;
  private $implement_circle;
  private $current_stage;
  private $advantage;
  private $reach_target;
  private $plan_target;
  private $skill_maturity;
  private $core_skill;
  private $patents;
  private $economic_results;
  private $skill_standard;
  private $sign_at;
  private $paper_other;
  private $datas;
  private $team;
  private $research_checkbox1;
  private $cooperative_country;
  private $created_at;
 private $updated_at  = 'CURRENT_TIMESTAMP';
 public $table = "project_baseinfos";
 private $is_new = true;

 public function setTable($table)
  {
    if($table) $this->table = $table;
    return $this;
  }

 public function isNew()
 {
   return $this->is_new;
 }

 public function getId()
 {
   return $this->id;
 }

 public function getType()
 {
   return $this->type;
 }

 public function getProjectId()
 {
   return $this->project_id;
 }

 public function getLinkman()
 {
   return $this->linkman;
 }

 public function getLinkmanMobile()
 {
   return $this->linkman_mobile;
 }

 public function getLinkmanTel()
 {
   return $this->linkman_tel;
 }

 public function getSpecialSubject()
 {
   return $this->special_subject;
 }

 public function getSkillDomain()
 {
   return $this->skill_domain;
 }

 public function getSupportDirection()
 {
   return $this->support_direction;
 }

 public function getSkillSource()
 {
   return $this->skill_source;
 }

 public function getSkillSourceType()
 {
   return $this->skill_source_type;
 }

 public function getSkillLevel()
 {
   return $this->skill_level;
 }

 public function getAwards()
 {
   return $this->awards;
 }

 public function getApplyDomain()
 {
   return $this->apply_domain;
 }

 public function getProduceDomain()
 {
   return $this->produce_domain;
 }

 public function getInnovationType()
 {
   return $this->innovation_type;
 }

 public function getLevel()
 {
   return $this->level;
 }

 public function getFruits()
 {
   return $this->fruits;
 }

 public function getFruitSource()
 {
   return $this->fruit_source;
 }

 public function getFruitForm()
 {
   return $this->fruit_form;
 }

 public function getFruitLevel()
 {
   return $this->fruit_level;
 }

 public function getBudget()
 {
   return $this->budget;
 }

 public function getPatent()
 {
   return $this->patent;
 }

 public function getYieldStudy()
 {
   return $this->yield_study;
 }

 public function getFruitAward()
 {
   return $this->fruit_award;
 }

 public function getWorkProgress()
 {
   return $this->work_progress;
 }

 public function getMarketDemand()
 {
   return $this->market_demand;
 }

 public function getMarketTarget()
 {
   return $this->market_target;
 }

 public function getImplementCircle()
 {
   return $this->implement_circle;
 }

 public function getCurrentStage()
 {
   return $this->current_stage;
 }

 public function getAdvantage()
 {
   return $this->advantage;
 }

 public function getReachTarget()
 {
   return $this->reach_target;
 }

 public function getPlanTarget()
 {
   return $this->plan_target;
 }

 public function getSkillMaturity()
 {
   return $this->skill_maturity;
 }

 public function getCoreSkill()
 {
   return $this->core_skill;
 }

 public function getPatents()
 {
   return $this->patents;
 }

 public function getEconomicResults()
 {
   return $this->economic_results;
 }

 public function getSkillStandard()
 {
   return $this->skill_standard;
 }

 public function getSignAt()
 {
   return $this->sign_at;
 }

 public function getPaperOther()
 {
   return $this->paper_other;
 }

 public function getDatas()
 {
   return $this->datas;
 }

 public function getTeam()
 {
   return $this->team;
 }

 public function getResearchCheckbox1()
 {
   return $this->research_checkbox1;
 }

 public function getCooperativeCountry()
 {
   return $this->cooperative_country;
 }

 public function getCreatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->created_at));
   else return $this->created_at;
 }

 public function getUpdatedAt($fromat="Y-m-d H:i:s")
 {
   if($fromat != "Y-m-d H:i:s") return date($fromat,strtotime($this->updated_at));
   else return $this->updated_at;
 }

 public function setId($v)
 {
   if(!isset($v)) return $this;
    $v = (int)$v;
    if($this->id !== $v)
    {
      $this->id = $v;
      $this->fieldData["id"] = $v;
    }
   return $this;

 }

 public function setType($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->type !== $v)
    {
      $this->type = $v;
      $this->fieldData["type"] = $v;
    }
   return $this;

 }

 public function setProjectId($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->project_id !== $v)
    {
      $this->project_id = $v;
      $this->fieldData["project_id"] = $v;
    }
   return $this;

 }

 public function setLinkman($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->linkman !== $v)
    {
      $this->linkman = $v;
      $this->fieldData["linkman"] = $v;
    }
   return $this;

 }

 public function setLinkmanMobile($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->linkman_mobile !== $v)
    {
      $this->linkman_mobile = $v;
      $this->fieldData["linkman_mobile"] = $v;
    }
   return $this;

 }

 public function setLinkmanTel($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->linkman_tel !== $v)
    {
      $this->linkman_tel = $v;
      $this->fieldData["linkman_tel"] = $v;
    }
   return $this;

 }

 public function setSpecialSubject($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->special_subject !== $v)
    {
      $this->special_subject = $v;
      $this->fieldData["special_subject"] = $v;
    }
   return $this;

 }

 public function setSkillDomain($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_domain !== $v)
    {
      $this->skill_domain = $v;
      $this->fieldData["skill_domain"] = $v;
    }
   return $this;

 }

 public function setSupportDirection($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->support_direction !== $v)
    {
      $this->support_direction = $v;
      $this->fieldData["support_direction"] = $v;
    }
   return $this;

 }

 public function setSkillSource($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_source !== $v)
    {
      $this->skill_source = $v;
      $this->fieldData["skill_source"] = $v;
    }
   return $this;

 }

 public function setSkillSourceType($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_source_type !== $v)
    {
      $this->skill_source_type = $v;
      $this->fieldData["skill_source_type"] = $v;
    }
   return $this;

 }

 public function setSkillLevel($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_level !== $v)
    {
      $this->skill_level = $v;
      $this->fieldData["skill_level"] = $v;
    }
   return $this;

 }

 public function setAwards($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->awards !== $v)
    {
      $this->awards = $v;
      $this->fieldData["awards"] = $v;
    }
   return $this;

 }

 public function setApplyDomain($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->apply_domain !== $v)
    {
      $this->apply_domain = $v;
      $this->fieldData["apply_domain"] = $v;
    }
   return $this;

 }

 public function setProduceDomain($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->produce_domain !== $v)
    {
      $this->produce_domain = $v;
      $this->fieldData["produce_domain"] = $v;
    }
   return $this;

 }

 public function setInnovationType($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->innovation_type !== $v)
    {
      $this->innovation_type = $v;
      $this->fieldData["innovation_type"] = $v;
    }
   return $this;

 }

 public function setLevel($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->level !== $v)
    {
      $this->level = $v;
      $this->fieldData["level"] = $v;
    }
   return $this;

 }

 public function setFruits($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruits !== $v)
    {
      $this->fruits = $v;
      $this->fieldData["fruits"] = $v;
    }
   return $this;

 }

 public function setFruitSource($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruit_source !== $v)
    {
      $this->fruit_source = $v;
      $this->fieldData["fruit_source"] = $v;
    }
   return $this;

 }

 public function setFruitForm($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruit_form !== $v)
    {
      $this->fruit_form = $v;
      $this->fieldData["fruit_form"] = $v;
    }
   return $this;

 }

 public function setFruitLevel($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruit_level !== $v)
    {
      $this->fruit_level = $v;
      $this->fieldData["fruit_level"] = $v;
    }
   return $this;

 }

 public function setBudget($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->budget !== $v)
    {
      $this->budget = $v;
      $this->fieldData["budget"] = $v;
    }
   return $this;

 }

 public function setPatent($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->patent !== $v)
    {
      $this->patent = $v;
      $this->fieldData["patent"] = $v;
    }
   return $this;

 }

 public function setYieldStudy($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->yield_study !== $v)
    {
      $this->yield_study = $v;
      $this->fieldData["yield_study"] = $v;
    }
   return $this;

 }

 public function setFruitAward($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->fruit_award !== $v)
    {
      $this->fruit_award = $v;
      $this->fieldData["fruit_award"] = $v;
    }
   return $this;

 }

 public function setWorkProgress($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->work_progress !== $v)
    {
      $this->work_progress = $v;
      $this->fieldData["work_progress"] = $v;
    }
   return $this;

 }

 public function setMarketDemand($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->market_demand !== $v)
    {
      $this->market_demand = $v;
      $this->fieldData["market_demand"] = $v;
    }
   return $this;

 }

 public function setMarketTarget($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->market_target !== $v)
    {
      $this->market_target = $v;
      $this->fieldData["market_target"] = $v;
    }
   return $this;

 }

 public function setImplementCircle($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->implement_circle !== $v)
    {
      $this->implement_circle = $v;
      $this->fieldData["implement_circle"] = $v;
    }
   return $this;

 }

 public function setCurrentStage($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->current_stage !== $v)
    {
      $this->current_stage = $v;
      $this->fieldData["current_stage"] = $v;
    }
   return $this;

 }

 public function setAdvantage($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->advantage !== $v)
    {
      $this->advantage = $v;
      $this->fieldData["advantage"] = $v;
    }
   return $this;

 }

 public function setReachTarget($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->reach_target !== $v)
    {
      $this->reach_target = $v;
      $this->fieldData["reach_target"] = $v;
    }
   return $this;

 }

 public function setPlanTarget($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->plan_target !== $v)
    {
      $this->plan_target = $v;
      $this->fieldData["plan_target"] = $v;
    }
   return $this;

 }

 public function setSkillMaturity($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_maturity !== $v)
    {
      $this->skill_maturity = $v;
      $this->fieldData["skill_maturity"] = $v;
    }
   return $this;

 }

 public function setCoreSkill($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->core_skill !== $v)
    {
      $this->core_skill = $v;
      $this->fieldData["core_skill"] = $v;
    }
   return $this;

 }

 public function setPatents($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->patents !== $v)
    {
      $this->patents = $v;
      $this->fieldData["patents"] = $v;
    }
   return $this;

 }

 public function setEconomicResults($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->economic_results !== $v)
    {
      $this->economic_results = $v;
      $this->fieldData["economic_results"] = $v;
    }
   return $this;

 }

 public function setSkillStandard($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->skill_standard !== $v)
    {
      $this->skill_standard = $v;
      $this->fieldData["skill_standard"] = $v;
    }
   return $this;

 }

 public function setSignAt($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->sign_at !== $v)
    {
      $this->sign_at = $v;
      $this->fieldData["sign_at"] = $v;
    }
   return $this;

 }

 public function setPaperOther($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->paper_other !== $v)
    {
      $this->paper_other = $v;
      $this->fieldData["paper_other"] = $v;
    }
   return $this;

 }

 public function setDatas($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->datas !== $v)
    {
      $this->datas = $v;
      $this->fieldData["datas"] = $v;
    }
   return $this;

 }

 public function setTeam($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->team !== $v)
    {
      $this->team = $v;
      $this->fieldData["team"] = $v;
    }
   return $this;

 }

 public function setResearchCheckbox1($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->research_checkbox1 !== $v)
    {
      $this->research_checkbox1 = $v;
      $this->fieldData["research_checkbox1"] = $v;
    }
   return $this;

 }

 public function setCooperativeCountry($v)
 {
 if(!isset($v)) return $this;
    $v = (string)$v;
    if($this->cooperative_country !== $v)
    {
      $this->cooperative_country = $v;
      $this->fieldData["cooperative_country"] = $v;
    }
   return $this;

 }

 public function setCreatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->created_at !== $v)
    {
      $this->created_at = $v;
      $this->fieldData["created_at"] = $v;
    }
   return $this;

 }

 public function setUpdatedAt($v)
 {
   if(!isset($v)) return $this;
    $v = date("Y-m-d H:i:s",strtotime($v));
    if($this->updated_at !== $v)
    {
      $this->updated_at = $v;
      $this->fieldData["updated_at"] = $v;
    }
   return $this;

 }

    public function save()
    {
        $db = sf::getLib("db");
        if($this->fieldData){
            if(!$this->is_new)
            {
                return $db->update($this->fieldData,"`id` = '$this->id' ",$this->table);
            }
            if($this->id = $db->insert($this->fieldData,$this->table)){
                $this->is_new = false;
                return true;
            }else return false;

        }
    }

 public function remove($addWhere = '')
 {
   if(!$addWhere) return false;
   $db = sf::getLib("db");
     $sql = "DELETE FROM `project_baseinfos` WHERE $addWhere ";
     $db->query($sql);
     return $db->affected_rows();
 }

 public function toArray()
 {
   return array(
     "id" => $this->getId(),
     "type" => $this->getType(),
     "project_id" => $this->getProjectId(),
     "linkman" => $this->getLinkman(),
     "linkman_mobile" => $this->getLinkmanMobile(),
     "linkman_tel" => $this->getLinkmanTel(),
     "special_subject" => $this->getSpecialSubject(),
     "skill_domain" => $this->getSkillDomain(),
     "support_direction" => $this->getSupportDirection(),
     "skill_source" => $this->getSkillSource(),
     "skill_source_type" => $this->getSkillSourceType(),
     "skill_level" => $this->getSkillLevel(),
     "awards" => $this->getAwards(),
     "apply_domain" => $this->getApplyDomain(),
     "produce_domain" => $this->getProduceDomain(),
     "innovation_type" => $this->getInnovationType(),
     "level" => $this->getLevel(),
     "fruits" => $this->getFruits(),
     "fruit_source" => $this->getFruitSource(),
     "fruit_form" => $this->getFruitForm(),
     "fruit_level" => $this->getFruitLevel(),
     "budget" => $this->getBudget(),
     "patent" => $this->getPatent(),
     "yield_study" => $this->getYieldStudy(),
     "fruit_award" => $this->getFruitAward(),
     "work_progress" => $this->getWorkProgress(),
     "market_demand" => $this->getMarketDemand(),
     "market_target" => $this->getMarketTarget(),
     "implement_circle" => $this->getImplementCircle(),
     "current_stage" => $this->getCurrentStage(),
     "advantage" => $this->getAdvantage(),
     "reach_target" => $this->getReachTarget(),
     "plan_target" => $this->getPlanTarget(),
     "skill_maturity" => $this->getSkillMaturity(),
     "core_skill" => $this->getCoreSkill(),
     "patents" => $this->getPatents(),
     "economic_results" => $this->getEconomicResults(),
     "skill_standard" => $this->getSkillStandard(),
     "sign_at" => $this->getSignAt(),
     "paper_other" => $this->getPaperOther(),
     "datas" => $this->getDatas(),
     "team" => $this->getTeam(),
     "research_checkbox1" => $this->getResearchCheckbox1(),
     "cooperative_country" => $this->getCooperativeCountry(),
     "created_at" => $this->getCreatedAt(),
     "updated_at" => $this->getUpdatedAt(),
     );
 }

 public function cleanObject()
 {
   $this->id = '';
   $this->type = '';
   $this->project_id = '';
   $this->linkman = '';
   $this->linkman_mobile = '';
   $this->linkman_tel = '';
   $this->special_subject = '';
   $this->skill_domain = '';
   $this->support_direction = '';
   $this->skill_source = '';
   $this->skill_source_type = '';
   $this->skill_level = '';
   $this->awards = '';
   $this->apply_domain = '';
   $this->produce_domain = '';
   $this->innovation_type = '';
   $this->level = '';
   $this->fruits = '';
   $this->fruit_source = '';
   $this->fruit_form = '';
   $this->fruit_level = '';
   $this->budget = '';
   $this->patent = '';
   $this->yield_study = '';
   $this->fruit_award = '';
   $this->work_progress = '';
   $this->market_demand = '';
   $this->market_target = '';
   $this->implement_circle = '';
   $this->current_stage = '';
   $this->advantage = '';
   $this->reach_target = '';
   $this->plan_target = '';
   $this->skill_maturity = '';
   $this->core_skill = '';
   $this->patents = '';
   $this->economic_results = '';
   $this->skill_standard = '';
   $this->sign_at = '';
   $this->paper_other = '';
   $this->datas = '';
   $this->team = '';
   $this->research_checkbox1 = '';
   $this->cooperative_country = '';
   $this->created_at = '';
   $this->updated_at = '';
   $this->fieldData = array();
   $this->is_new = true;
   return $this;
 }

 public function fillObject($data=array())
 {
   $this->cleanObject();
   if(!$data) return $this;
   if($data["is_new"]) $this->is_new = true;
   else $this->is_new = false;
   isset($data["id"]) && $this->id = $data["id"];
   isset($data["type"]) && $this->type = $data["type"];
   isset($data["project_id"]) && $this->project_id = $data["project_id"];
   isset($data["linkman"]) && $this->linkman = $data["linkman"];
   isset($data["linkman_mobile"]) && $this->linkman_mobile = $data["linkman_mobile"];
   isset($data["linkman_tel"]) && $this->linkman_tel = $data["linkman_tel"];
   isset($data["special_subject"]) && $this->special_subject = $data["special_subject"];
   isset($data["skill_domain"]) && $this->skill_domain = $data["skill_domain"];
   isset($data["support_direction"]) && $this->support_direction = $data["support_direction"];
   isset($data["skill_source"]) && $this->skill_source = $data["skill_source"];
   isset($data["skill_source_type"]) && $this->skill_source_type = $data["skill_source_type"];
   isset($data["skill_level"]) && $this->skill_level = $data["skill_level"];
   isset($data["awards"]) && $this->awards = $data["awards"];
   isset($data["apply_domain"]) && $this->apply_domain = $data["apply_domain"];
   isset($data["produce_domain"]) && $this->produce_domain = $data["produce_domain"];
   isset($data["innovation_type"]) && $this->innovation_type = $data["innovation_type"];
   isset($data["level"]) && $this->level = $data["level"];
   isset($data["fruits"]) && $this->fruits = $data["fruits"];
   isset($data["fruit_source"]) && $this->fruit_source = $data["fruit_source"];
   isset($data["fruit_form"]) && $this->fruit_form = $data["fruit_form"];
   isset($data["fruit_level"]) && $this->fruit_level = $data["fruit_level"];
   isset($data["budget"]) && $this->budget = $data["budget"];
   isset($data["patent"]) && $this->patent = $data["patent"];
   isset($data["yield_study"]) && $this->yield_study = $data["yield_study"];
   isset($data["fruit_award"]) && $this->fruit_award = $data["fruit_award"];
   isset($data["work_progress"]) && $this->work_progress = $data["work_progress"];
   isset($data["market_demand"]) && $this->market_demand = $data["market_demand"];
   isset($data["market_target"]) && $this->market_target = $data["market_target"];
   isset($data["implement_circle"]) && $this->implement_circle = $data["implement_circle"];
   isset($data["current_stage"]) && $this->current_stage = $data["current_stage"];
   isset($data["advantage"]) && $this->advantage = $data["advantage"];
   isset($data["reach_target"]) && $this->reach_target = $data["reach_target"];
   isset($data["plan_target"]) && $this->plan_target = $data["plan_target"];
   isset($data["skill_maturity"]) && $this->skill_maturity = $data["skill_maturity"];
   isset($data["core_skill"]) && $this->core_skill = $data["core_skill"];
   isset($data["patents"]) && $this->patents = $data["patents"];
   isset($data["economic_results"]) && $this->economic_results = $data["economic_results"];
   isset($data["skill_standard"]) && $this->skill_standard = $data["skill_standard"];
   isset($data["sign_at"]) && $this->sign_at = $data["sign_at"];
   isset($data["paper_other"]) && $this->paper_other = $data["paper_other"];
   isset($data["datas"]) && $this->datas = $data["datas"];
   isset($data["team"]) && $this->team = $data["team"];
   isset($data["research_checkbox1"]) && $this->research_checkbox1 = $data["research_checkbox1"];
   isset($data["cooperative_country"]) && $this->cooperative_country = $data["cooperative_country"];
   isset($data["created_at"]) && $this->created_at = $data["created_at"];
   isset($data["updated_at"]) && $this->updated_at = $data["updated_at"];
    return $this;
 }

 public function __construct($data='')
 {
   if(!$data) return $this;
   if(is_array($data))
     return $this->fillObject($data);
   else return $this->selectByPk($data);
 }

 public function selectByPk($pk='')
 {
   if(!$pk) return $this;
   $pk = (int)$pk;
     $db = sf::getLib("db");
   $sql = "SELECT * FROM `$this->table` WHERE `id` = '$pk' ";
   $query = $db->query($sql);
   if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
   return $this;
 }

 public function delete()
 {
   if(!$this->id) return false;
   $db = sf::getLib("db");
   $db->query("DELETE FROM `$this->table` WHERE `id` = '$this->id'");
   return $db->affected_rows();
 }

}