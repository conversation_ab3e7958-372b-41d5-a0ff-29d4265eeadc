<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseBudgetOutlays;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class BudgetOutlays extends BaseBudgetOutlays
{
    public $budget = NULL;
	function budget($year='2020',$f=false){
        if($this->budget === NULL || $f) $this->budget = sf::getModel("Budget".$year)->selectByProjectId(parent::getProjectId());
        return $this->budget;
    }
}