<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseArchives;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class Archives extends BaseArchives
{
    private $company = NULL;
    function getCompany($f=false)
    {
        if($this->company === NULL || $f) $this->company = sf::getModel("Corporations")->selectByUserId(parent::getCompanyId());
        return $this->company;
    }

    private $project = NULL;

    function getProject($f=false)
    {
        if($this->project === NULL || $f) $this->project = sf::getModel("Projects")->selectByProjectId(parent::getProjectId());
        return $this->project;
    }

    function getNewNo()
    {
        $objects = $this->selectAll("type = '".$this->getType()."'","order by no desc",1);
        $no = 1;
        if($objects->getTotal()>0){
            $no = substr($objects->getObject()->getNo(),6);
            $no++;
        }
        return $this->getType().date('Y').str_pad($no,4,"0",STR_PAD_LEFT);
    }

    function selectByArchiveId($archiveId = '')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `" . $this->table . "` WHERE `archive_id` = '{$archiveId}' ");
        if ($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
        else {
            $this->setArchiveId($archiveId ?: sf::getLib("MyString")->getRandString());
            $this->setUserId(input::getInput("session.roleuserid"));
            $this->setUserName(input::getInput("session.nickname"));
            $this->setCreatedAt(date('Y-m-d H:i:s'));
        }
        return $this;
    }

     public function getTypeStr()
     {
         switch (parent::getType()){
             case 'JJ':
                 return '基建档案';
             case 'SB':
                 return '设备档案';
             case 'KY':
                 return '科研档案';
             case 'CP':
                 return '产品档案';
         }
     }

     
    function getState()
    {
        switch ($this->getStatement()) {
            case 0:
            case 1:
                return '填写中';
            case 4:
                return '待单位/部门助理审核';
            case 5:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getArchiveId().'/type/archive').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门助理退回</span>';
            case 6:
                return '待单位/部门管理员审核';
            case 7:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getArchiveId().'/type/archive').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">单位/部门管理员退回</span>';
            case 9:
                return '待科技处助理审核';
            case 12:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getArchiveId().'/type/archive').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处助理退回</span>';
            case 15:
                return "待科技处管理员审核";
            case 17:
                return '<span style="color:red"  onclick="return showWindow(\'退回原因\',\''.site_url('admin/history/index/id/'.$this->getArchiveId().'/type/archive').'\',{area:[\'550px\',\'80%\']});" role="button" title="查看退回原因">科技处管理员退回</span>';
            case 20:
                return "已审核";
            default:
                return '<span style="color:red">未知状态</span>';
        }
    }

    /**
     * 获得附件
     * @param bool $f
     * @return null
     */
    public function getAttachment($itemType = 'archive', $limit = 0, $orders='order by no asc,id asc')
    {
        $addwhere = "`item_id` = '".$this->getArchiveId()."'";
        if ($itemType != 'all') {
            $addwhere .= " and `item_type` = '{$itemType}'";
        }
        return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
    }

    public function clearAttachments()
    {
        sf::getLib('Db')->exec("DELETE FROM `filemanager` where item_id = '".$this->getArchiveId()."' and used = 1");
    }

    public function pickAttachments()
    {
        $project = $this->getProject();
        //1.申报
        //申报书
        $attachments = $project->getAttachment('project_sbs');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'申报书');
        }
        //申报书
        $attachments = $project->getAttachment('project_ht');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'横向项目合同');
        }
        //申报书
        $attachments = $project->getAttachment('apply',1);
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'申报书存档');
        }
        //申请书中审批情况页盖章件
        $attachments = $project->getAttachment('project_spqk');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'申报书审批情况页盖章件');
        }
        //体现项目负责人科技创新能力的证明材料
        $attachments = $project->getAttachment('project_hzxy');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'申报书体现项目负责人科技创新能力的证明材料');
        }
        //申报书其他附件
        $attachments = $project->getAttachment('project_qt');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'申报书其他附件');
        }
        //2.任务书
        $attachments = $project->getAttachment('task_rws');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'任务书');
        }
        //2.任务书-项目研究人员承诺书
        $attachments = $project->getAttachment('task_cns');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'任务书-项目研究人员承诺书');
        }
        $attachments = $project->getAttachment('task',1);
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_task','任务书存档');
        }
        $attachments = $project->getAttachment('task_qt');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'任务书其他附件');
        }
        //3.项目执行
        //3.项目执行-开题报告
        $attachments = $project->getAttachment('startup');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'开题报告附件');
        }
        //3.项目执行-中期检查
        $attachments = $project->getAttachment('inspect');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'中期检查附件');
        }
        //3.项目执行-里程碑
        $milestones = $project->selectMilestones();
        while($milestone = $milestones->getObject()){
            $attachments = $milestone->getAttachment();
            while($attachment = $attachments->getObject()){
                $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'里程碑附件');
            }
        }
        //3.项目执行-项目年度报告
        $stages = $project->stages();
        while($stage = $stages->getObject()){
            $attachments = $stage->getAttachment();
            while($attachment = $attachments->getObject()){
                $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'年度报告附件');
            }
        }
        //3.项目执行-项目变更
        $changes = $project->changes();
        while($change = $changes->getObject()){
            $attachments = $change->attachments();
            while($attachment = $attachments->getObject()){
                $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'项目变更附件');
            }
        }
        //4.验收书
        $attachments = $project->getAttachment('complete_zjbg');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'项目完成情况总结报告');
        }
        $attachments = $project->getAttachment('complete_idea');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'验收专家组意见');
        }
        $attachments = $project->getAttachment('complete',1);
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_complete','验收书存档');
        }
        $attachments = $project->getAttachment('complete_qt');
        while($attachment = $attachments->getObject()){
            $attachment->copy($this->getArchiveId(),'archive_'.$attachment->getItemType(),'验收书其他附件');
        }
    }

    public function hasWrite()
    {
        $projectCount = sf::getLib('Db')->result_first("select count(*) c from archives where project_id = '".$this->getProjectId()."' and id!='".$this->getId()."'");
        return $projectCount>0;
    }

    public function getAssistantName($no=0)
    {
        $assistantName = parent::getAssistantName();
        if($no==0) return str_replace('|','、',$assistantName);
        $names = explode('|',$assistantName);
        return $names[$no-1];
    }

    public function getAssistantId($no=0)
    {
        $assistantId = parent::getAssistantId();
        $ids = explode('|',$assistantId);
        if($no==0) return $ids[0];
        return $ids[$no-1];
    }

    public function getAssistantIds()
    {
        return explode('|',parent::getAssistantId());
    }

    public function setAssistantId($arr=[])
    {
        return parent::setAssistantId(implode('|',$arr));
    }

    public function setAssistantName($arr=[])
    {
        return parent::setAssistantName(implode('|',$arr));
    }

    /**
     * 是否有编辑权限
     * @return bool
     */
    public function hasEditAuth()
    {
        if ($this->getUserId() != input::session("roleuserid") && !in_array(input::session("roleuserid"),$this->getAssistantIds())) {
            return false;
        }
        return true;
    }

    /**
     * 是否有查看权限
     * @return bool
     */
    public function hasShowAuth()
    {
        if (input::session('userlevel')==2 && $this->getUserId() != input::session("roleuserid") && !in_array(input::session("roleuserid"),$this->getAssistantIds())) {
            return false;
        }
        return true;
    }

    /**
     * 获取部门助理id
     * @return void
     */
    public function getDepartmentAssistantIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyAssistants = $company->selectAssistants();
        if ($companyAssistants->getTotal() == 0) return [];
        $userIds = [];
        while ($companyAssistant = $companyAssistants->getObject()) {
            if ($companyAssistant->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyAssistant->getUserId();
        }
        return $userIds;
    }

    /**
     * 获取部门管理员id
     * @return void
     */
    public function getDepartmentManagerIds()
    {
        $company = $this->getCompany(true);
        if ($company->isNew()) return [];
        $companyManagers = $company->selectManagers();
        if ($companyManagers->getTotal() == 0) return [];
        $userIds = [];
        while ($companyManager = $companyManagers->getObject()) {
            if ($companyManager->getUserType() != 'EMPLOYEE') continue;
            $userIds[] = $companyManager->getUserId();
        }
        return $userIds;
    }

    function sendMessage()
    {
        $message = sf::getLib('Message');
        $userIds = [];
        $roleIds = [];
        $msgTypes = [];
        $itemId = $this->getArchiveId();
        $itemType = 'archive';
        $taskId = uniqid($itemType, true);
        $url = 'manager/index';
        $oaMsgType = 1; //1:审批类待办  2:通知类待办
        switch (parent::getStatement()){
            case 4:
                $title = '【科技档案】你有一个待审核的档案';
                $content = "档案《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentAssistantIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(3);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 6:
                $title = '【科技档案】你有一个待审核的档案';
                $content = "档案《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $userIds = $this->getDepartmentManagerIds();
                $userIds = array_filter($userIds);
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(4);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 9:
                //待科技处助理审核
                $title = '【科技档案】你有一个待审核的档案';
                $content = "档案《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeAssistantIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(5);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 15:
                //待科技处管理员审核
                $title = '【科技档案】你有一个待审核的档案';
                $content = "档案《".$this->getSubject()."》需要你审核，请登录园区科研综合管理平台查看。";
                $officeUserIds = getOfficeManagerIds();
                $officeUserIds = array_filter($officeUserIds);
                foreach ($officeUserIds as $officeUserId){
                    $userIds[] = $officeUserId;
                    $user = sf::getModel('Users')->selectByUserId($officeUserId);
                    $roleIds[$officeUserId] = $user->getRoleId(6);
                    $msgTypes[$officeUserId] = $user->getMessageOpenType();
                }
                break;
            case 20:
                //已审核
                $oaMsgType = 2;
                $title = '【科技档案】你登记的档案已审核通过';
                $content = "你登记的档案《".$this->getSubject()."》已审核通过。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
            case 5:
                //部门助理退回
            case 7:
                //部门管理员退回
            case 12:
                //科技处助理退回
            case 17:
                //科技处管理员退回
                $roleName = $this->getRoleName(parent::getStatement());
                $title = "【科技档案】你登记的档案被{$roleName}退回";
                $content = "档案《{$this->getSubject()}》被{$roleName}退回，请登录园区科研综合管理平台查看。";
                //通知填报人员
                $userIds[] = $this->getUserId();
                foreach ($userIds as $userId){
                    $user = sf::getModel('Users')->selectByUserId($userId);
                    $roleIds[$userId] = $user->getRoleId(2);
                    $msgTypes[$userId] = $user->getMessageOpenType();
                }
                break;
        }
        if($userIds){
            foreach ($userIds as $userId){
                $message->setUserId($userId)->setRoleId($roleIds[$userId])->setTitle($title)->setContent($content)->setItemId($itemId)->setItemType($itemType)->setTaskId($taskId)->setMsgType($msgTypes[$userId])->setOaMsgType($oaMsgType)->setUrl($url)->add();
            }
        }
    }

    private function getRoleName($statement)
    {
        $roleName = '';
        switch ($statement){
            case 5:
                $roleName = '部门助理';
                break;
            case 7:
                $roleName = '部门管理员';
                break;
            case 12:
                $roleName = '科技处助理';
                break;
            case 17:
                $roleName = '科技处管理员';
                break;
            default:
                $roleName = '';
                break;
        }
        return $roleName;
    }
}