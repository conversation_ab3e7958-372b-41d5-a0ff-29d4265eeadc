<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use App\Model\BaseManagers;
class Managers extends BaseManagers
{
    private $user = NULL;

	function encode_password($password='')
	{
		$str = substr(md5(uniqid()),-3);
		return md5($password.$str).":".$str;
	}
	
	function setUserPassword($v)
	{
		parent::setUserPassword($this->encode_password($v));
	}
	
	function selectByName($userName='')
	{
		$db = sf::getLib("db");
		$result = $db->fetch_first("SELECT * FROM ".$this->table." WHERE `user_name` = '".$userName."'");
		if($result){
			$this->fillObject($result);
			return $this;
		}else return false;
	}
	
	function selectByUserId($user_id='')
	{
		if($user_id == '') $user_id = sf::getLib("MyString")->getRandString();
		$db = sf::getLib("db");
		$query = $db->query("SELECT * FROM `".$this->table."` WHERE `user_id` = '".$user_id."' ");
		if($db->num_rows($query)) $this->fillObject($db->fetch_array($query));
		else $this->setUserId($user_id);
		return $this;
	}

    public function selectUsers()
    {
        return sf::getModel("Users")->selectAll("user_id IN (SELECT user_id FROM user_roles WHERE user_role_id = '" . $this->getUserId() . "' AND role_id = '6')");
    }

    public function selectLeaders()
    {
        return sf::getModel("Managers")->selectAll("CONCAT(',', user_group_id, ',') LIKE '%,12,%'");
    }

    /**
     * 获取科技处助理
     * @return mixed
     */
    public function selectOfficeAssistants()
    {
        return sf::getModel("Managers")->selectAll("CONCAT(',', user_group_id, ',') LIKE '%,5,%'");
    }

    /**
     * 获取财务处助理
     * @return mixed
     */
    public function selectFinanceAssistants()
    {
        return sf::getModel("Managers")->selectAll("CONCAT(',', user_group_id, ',') LIKE '%,7,%'");
    }

    /**
     * 获取科技处管理员
     * @return mixed
     */
    public function selectOfficeManagers()
    {
        return sf::getModel("Managers")->selectAll("CONCAT(',', user_group_id, ',') LIKE '%,6,%'");
    }

    /**
     * 获取财务处管理员
     * @return mixed
     */
    public function selectFinanceManagers()
    {
        return sf::getModel("Managers")->selectAll("CONCAT(',', user_group_id, ',') LIKE '%,8,%'");
    }

    public function users()
    {
        return $this->selectUsers();
    }
	
	function setLoginNum($v)
	{
		parent::setLoginNum($this->getLoginNum() + 1);
	}
	
	function hasUser($userName='')
	{
		if($this->selectByName($userName)) return true;
		else return false;
	}
	
	function check($password='',$oldpass='')
	{
		$oldpass == '' && $oldpass = $this->getUserPassword();
		$pass_array = explode(":",$oldpass);
		if(count($pass_array) > 1){
			$oldpass = $pass_array[0];
			$tempstr = $pass_array[1];
			if($oldpass == md5($password.$tempstr)) return true;
			else return false;
		}else{
			if($oldpass == $password) return true;
			else return false;
		}
	}
	
	function getUserGroupIds()
	{
        return explode(',',parent::getUserGroupId());
	}

	function getUserGroupName()
	{
        $groupIds = explode(',',parent::getUserGroupId());
        $groupNames = [];
        foreach ($groupIds as $groupId){
            $groupNames[] = sf::getModel("UserGroups",$groupId)->getUserGroupName();
        }
		return implode('、', $groupNames);
	}
	
	
	function getState()
	{
		if(parent::getIsLock() == 4) return lang::get("Is lock!");
		else return lang::get("Is normal!");
	}
	
	function getOfficeSubject()
	{
		$office_id = parent::getOfficeId();
		if($office_id == -1 || $office_id == 0 ) return '<s style="color:red">未知</s>';
		return sf::getModel("Categorys",$office_id,'office')->getSubject();
	}

	/**
	 * 配合其他模型使用
	 */
	public function getUser($f=false)
	{
        if ($this->user === NULL || $f)
            $this->user = sf::getModel("Users")->selectByUserId($this->getUserId());
        return $this->user;
	}

    function getArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles WHERE `statement` = 9 ");
        return $row['num'];
    }
    
    /**
     * 待上报的联合协议数
     */
    function getJointNum()
    {
        $addwhere = "statement in (8,9)";//科技处助理
        if(input::session('userlevel')==6){//科技处管理员
            $addwhere = "statement in (14,15)";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_joints WHERE $addwhere");
        return $row['num'];
    }
    
    /**
     * 待上报的联合协议数
     */
    function getFinanceJointNum()
    {
        $addwhere = "statement in (8,11)";//财务处助理
        if(input::session('userlevel')==8){//财务处管理员
            $addwhere = "statement in (14,16)";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM user_joints WHERE $addwhere");
        return $row['num'];
    }

	/**
     *等待审核的新产品数
     */
    function getProductNum()
    {
		$addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products WHERE $addwhere ");
        return $row['num'];
    }

	/**
     *等待审核的专利成果数
     */
    function getPatentNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE $addwhere ");
        return $row['num'];
    }

    /**
     *等待审核的著作权数
     */
    function getSoftNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的奖励数
     */
    function getRewardNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科技论文数
     */
    function getPaperNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科技著作数
     */
    function getWorkNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getStandardNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM standards WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的成果转化数
     */
    function getTransformNum()
    {
		$addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM transforms WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getScientificSuiteNum()
    {
		$addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM scientific_suites WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getCertificateNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM certificates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getLicenseNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM licenses WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的标准数
     */
    function getQualificateNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM qualificates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的奖励数
     */
    function getAwardNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM awards WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科研考核
     */
    function getEvaluateWaitNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM evaluates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的论文奖金申请
     */
    function getBonusPaperWaitNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_papers WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的专利奖金申请
     */
    function getBonusPatentWaitNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_patents WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的绩效考核申请
     */
    function getBonusPerformanceWaitNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_performances WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科研考核奖金分配
     */
    function getBonusEvaluateAssignWaitNum()
    {
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_evaluates WHERE $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的论文奖金分配
     */
    function getBonusPaperAssignWaitNum()
    {
        return 0;
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_assign_companys WHERE bonus_type = 'paper' and $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的专利奖金分配
     */
    function getBonusPatentAssignWaitNum()
    {
        return 0;
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_assign_companys WHERE bonus_type = 'patent' and $addwhere");
        return $row['num'];
    }

    /**
     *等待审核的科研绩效分配
     */
    function getBonusPerformanceAssignWaitNum()
    {
        return 0;
        $addwhere = "statement = 9";
        if(input::session('userlevel')==6){
            $addwhere = "statement = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM bonus_assign_companys WHERE bonus_type = 'performance' and $addwhere");
        return $row['num'];
    }
	
	/**
	*等待科技处审核的项目计划数
	*/
	function getPlanWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_plans WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科技处审核的项目数
	*/
	function getAcceptWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement IN (14,15)");
		return $row['num'];
	}
	/**
	*等待所领导审核的项目数
	*/
	function getLeaderProjectWaitNum()
	{
        $leaderId = input::session('roleuserid');
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 25 and project_id in (SELECT item_id FROM `leader_accepts` where type = 'project' and leader_id = '{$leaderId}' and statement = 0)");
		return $row['num'];
	}
    /**
     *等待科研助理审核的项目数
     */
    function getOfficeAssistantProjectWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement IN (8,9)");
        return $row['num'];
    }
    /**
     *等待审核的开题报告数
     */
    function getStartupWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE state_for_startup = 9");
        return $row['num'];
    }
    /**
     *等待审核的中期检查数
     */
    function getInspectWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE state_for_inspect = 9");
        return $row['num'];
    }
    /**
     *等待财务助理审核的项目数
     */
    function getFinanceAssistantProjectWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement IN (8,11)");
        return $row['num'];
    }
    /**
     *等待财务管理员审核的项目数
     */
    function getFinanceManagerProjectWaitNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement IN (14,16)");
        return $row['num'];
    }
	/**
	*等待科技处审核的年度执行报告
	*/
	function getStageWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_stages WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科技处审核的里程碑
	*/
	function getMilestoneWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_milestones WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科技处审核的季度执行报告
	*/
	function getQuarterWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_quarters WHERE statement = 9");
		return $row['num'];
	}
	/**
	*等待科技处审核的变更申请
	*/
	function getChangeWaitnum()
	{
        $addwhere = "statement IN (14,15)";
        if($this->isAssistant()){
            $addwhere = "statement IN (8,9)";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_changes WHERE {$addwhere}");
		return $row['num'];
	}
	/**
	*等待财务处审核的变更申请
	*/
	function getFinanceChangeWaitnum()
	{
        $addwhere = "statement IN (14,16)";
        if($this->isFinanceAssistant()){
            $addwhere = "statement IN (8,11)";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_changes WHERE {$addwhere}");
		return $row['num'];
	}
	/**
	*等待科技处审核的外协申请
	*/
	function getOutWaitnum()
	{
        return 0;
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE state_for_out = 9");
		return $row['num'];
	}

    function getMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `statement` = 9 ");
        return $row['num'];
    }
	/**
     * 已注册的项目负责人数量
     */
	function getResearcherNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers where corporation_id like '9B581D83-B2BA-7337-0678%' and corporation_id not like '9B581D83-B2BA-7337-0678-05%'");
        return $row['num'];
    }
	/**
     * 已注册的单位数量
     */
	function getCompanyNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM corporations where is_lock = 0 and user_id like '9B581D83-B2BA-7337-0678%' and user_id not like '9B581D83-B2BA-7337-0678-05%' and (level = 2 or (subject IN ('物流公司','信息公司','北斗公司') and level = 3))");
        return $row['num'];
    }
	/**
     * 已注册的平台数量
     */
	function getPlatformNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platforms");
        return $row['num'];
    }

    function isAssistant()
    {
        return input::session('userlevel')==5;
    }

    function isFinanceAssistant()
    {
        return input::session('userlevel')==7;
    }

    /**
     *等待审核的平台数
     */
    function getPlatformWaitNum()
    {
        if($this->isAssistant()){
            $addwhere =" `statement` = 9 ";
        }else{
            $addwhere =" `statement` = 15 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platforms WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *等待审核的平台数
     */
    function getPlatformSummaryNum()
    {
        if($this->isAssistant()){
            $addwhere =" `statement` = 9 ";
        }else{
            $addwhere =" `statement` = 15 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM platform_summarys WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的协会组织登记
     */
    function getAssociationMemberNum()
    {
        if($this->isAssistant()){
            $addwhere =" `statement` = 9 ";
        }else{
            $addwhere =" `statement` = 15 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM association_members WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的团队数
     */
    function getTeamWaitNum()
    {
        if($this->isAssistant()){
            $addwhere =" `statement` = 9 ";
        }else{
            $addwhere =" `statement` = 15 ";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM teams WHERE {$addwhere} ");
        return $row['num'];
    }
	/**
     * 已认证的项目负责人数量
     */
	function getVerifiedResearcherNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers where is_lock = 0 and corporation_id like '9B581D83-B2BA-7337-0678%' and corporation_id not like '9B581D83-B2BA-7337-0678-05%'");
        return $row['num'];
    }
	/**
     * 已注册的专家数量
     */
	function getExpertNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM experts");
        return $row['num'];
    }

    /**
     *等待审核的专家数
     */
    function getExpertWaitnum()
    {
        if($this->isAssistant()){
            $addwhere = "  `is_lock` = 9 ";
        }else{
            $addwhere = "  `is_lock` = 15";
        }
        return sf::getModel("Experts")->selectAll($addwhere)->getTotal();

    }

    /**
     *等待审核的档案数
     */
    function getArchiveWaitNum()
    {
        if($this->isAssistant()){
            $addwhere = "  `statement` = 9 ";
        }else{
            $addwhere = "  `statement` = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM archives WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的档案数
     */
    function getArchiveBorrowWaitNum()
    {
        if($this->isAssistant()){
            $addwhere = "  `statement` = 9 ";
        }else{
            $addwhere = "  `statement` = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM archive_borrows WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的学术会议
     */
    function getConferenceWaitNum()
    {
        if($this->isAssistant()){
            $addwhere = "  `statement` = 9 ";
        }else{
            $addwhere = "  `statement` = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM conferences WHERE {$addwhere} ");
        return $row['num'];
    }

    /**
     *等待审核的设备使用情况
     */
    function getDeviceUsageWaitNum()
    {
        if($this->isAssistant()){
            $addwhere = "  `statement` = 9 ";
        }else{
            $addwhere = "  `statement` = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM device_usages WHERE {$addwhere} ");
        return $row['num'];
    }
    /**
     *待审提名申请
     */
    function getSocietyWaitNum()
    {
        if($this->isAssistant()){
            $addwhere = "  `statement` = 9 ";
        }else{
            $addwhere = "  `statement` = 15";
        }
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM society_members WHERE {$addwhere} ");
        return $row['num'];
    }
	/**
     * 已认证的专家数量
     */
	function getVerifiedExpertNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM experts where is_lock = 0");
        return $row['num'];
    }
	/**
     * 已立项
     */
	function getRadicateNum($catId=15)
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = {$catId} and statement = 29 and corporation_id like '9B581D83-B2BA-7337-0678%'");
        return $row['num'];
    }
	/**
     * 已结题
     */
	function getCompleteNum($catId=15)
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = {$catId} and statement = 30 and corporation_id like '9B581D83-B2BA-7337-0678%'");
        return $row['num'];
    }
	/**
     * 政府已立项
     */
	function getGovRadicateNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = 16 and statement = 29 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 政府已结题
     */
	function getGovCompleteNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects where cat_id = 16 and statement = 30 and first_id = '9B581D83-B2BA-7337-0678-A875B156B111'");
        return $row['num'];
    }
	/**
     * 已审核专利数量
     */
	function getAcceptPatentNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核著作数量
     */
	function getAcceptBookNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核论文数量
     */
	function getAcceptPaperNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核软著数量
     */
	function getAcceptSoftNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM softs where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核科技奖励数量
     */
	function getAcceptRewardNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核工法数量
     */
	function getAcceptMethodNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核新产品数量
     */
	function getAcceptProductNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM products where statement = 20");
        return $row['num'];
    }
	/**
     * 已审核科普文章数量
     */
	function getAcceptArticleNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM science_articles where statement = 20");
        return $row['num'];
    }
	/**
	*等待总工程师审核的项目数
	*/
	function getZgcsWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE (flow_type = 1 and statement = 20) or (flow_type > 1 and statement = 9)");
		return $row['num'];
	}
	/**
	*等待分组的项目数
	*/
	function getdevideWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 10  and type_group < 2 ");
		return $row['num'];
	}
	/**
	*等待分配专家的组数
	*/
	function getgroupWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_group WHERE `group_subject` LIKE '".substr(date('Y'),-2)."%' and  is_lock = 0 ");
		return $row['num'];
	}
	/**
	*等待分流的项目数
	*/
	function getpartitionWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement >= 10 AND statement < 20 AND statement <> 12 ");
		return $row['num'];
	}
	/**
	*等待立项的项目数
	*/
	function getRadicateWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE (cat_id = 15 and statement IN (23,27)) or (cat_id > 15 and statement = 20)");
		return $row['num'];
	}
	
	/**
	*等待推荐的项目数
	*/
	function getWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and statement = 20 and declare_year = ".date('Y'));
		return $row['num'];
	}
	/**
	*等待审定的项目预算数
	*/
	function getBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and `state_for_budget_book` = 15  and budget_open = 2 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(审定人员)
	*/
	function getsdBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` IN ('15','20','25')  and budget_open = 1 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(复审人员)
	*/
	function getfsBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` IN ('15','20')  and budget_open = 1 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}
	/**
	*等待审定的项目预算数(初审人员)
	*/
	function getcsBudgetWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` = 15  and budget_open = 1  AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
		
	}
	/**
	*等待审核的预算申报书数
	*/
	function getBudgetNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE `state_for_budget_book` = 6 AND type_id IN (select type_id from types_switch WHERE has_budget = 1) ");
		return $row['num'];
	}

	/**
	*待审核的计划任务书数
	*/
	function getTaskWaitnum()
	{
        if($this->isAssistant()){
            $addwhere =" `state_for_plan_book` IN (8,9) ";
        }else{
            $addwhere =" `state_for_plan_book` IN (14,15) ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addwhere}");
		return $row['num'];
	}

	/**
	*待财务审核的任务书数
	*/
	function getFinanceTaskWaitnum()
	{
        if($this->isFinanceAssistant()){
            $addwhere =" `state_for_plan_book` IN (8,11) ";
        }else{
            $addwhere =" `state_for_plan_book` IN (14,16) ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE {$addwhere}");
		return $row['num'];
	}
	/**
	*待审核的科技奖励数
	*/
	function getRewardWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM rewards WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的科技论文数
	*/
	function getPaperWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM papers WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的工法数
	*/
	function getMethodWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM methods WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的专利数
	*/
	function getPatentWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM patents WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的著作数
	*/
	function getWorkWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM works WHERE `statement` = 8 ");
		return $row['num'];
	}
	/**
	*待审核的科协会员
	*/
	function getMemberWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM declarers WHERE `talent` = 3 and  is_lock = 5 ");
		return $row['num'];
	}
	/**
	*待审核的中期报告数
	*/
	function getZqbgWaitnum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE office_id = '".input::getInput('session.office_id')."' and (`statement` = 29 OR task_open > 0) and `state_for_interimreport` = 5 ");
		return $row['num'];
	}
	/**
	*待审核的验收书数
	*/
	function getCompleteWaitnum()
	{
        if($this->isAssistant()){
            $addwhere =" `state_for_complete_book` IN (8,9) ";
        }else{
            $addwhere =" `state_for_complete_book` IN (14,15) ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE  {$addwhere}");
		return $row['num'];
		
	}
	/**
	*待财务审核的验收书数
	*/
	function getFinanceCompleteWaitnum()
	{
        if($this->isFinanceAssistant()){
            $addwhere =" `state_for_complete_book` IN (8,11) ";
        }else{
            $addwhere =" `state_for_complete_book` IN (14,16) ";
        }
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE  {$addwhere}");
		return $row['num'];

	}
	/**
	*待所领导审核的验收书数
	*/
	function getLeaderCompleteWaitnum()
	{
        $leaderId = input::session('roleuserid');
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE state_for_complete_book = 25 and project_id in (SELECT item_id FROM `leader_accepts` where type = 'complete' and leader_id = '{$leaderId}' and statement = 0)");
        return $row['num'];
	}
	/**
	*等待结题的项目数
	*/
	function getfinishWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND `state_for_complete_book` IN ('8','10')  AND type_id IN (select type_id from types_switch WHERE has_complete = 1) ");
		return $row['num'];
	}
	/**
	*需要复议的项目数
	*/
	function getreconsiderWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND `state_for_complete_book` = 13 ");
		return $row['num'];
	}
	/**
	*等待受理的国家项目数
	*/
	function getnationWaitNum()
	{
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_memo WHERE statement = 1 AND `office_id` = '".input::getInput('session.office_id')."' ");
		return $row['num'];
	}

	//等待受理的老项目
	function getOldWaitnum(){
		$row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM projects WHERE statement = 29 AND state_for_datum = 5 AND office_id = '".input::getInput('session.office_id')."' AND declare_year < 2016 ");
		return $row['num'];
	}
	
	/**
	 * 发送短消息给用户
	 */
	public function sendMessage($message='',$item_id='',$item_type='managers',$send_at='')
	{
		if(!isMobile($this->getUserMobile())) return false;
		return sf::getModel("ShortMessages")->sendSms($this->getUserMobile(),$message,$item_id,$item_type,$send_at);
	}

    public function coupling($userid = '',$userGroupId=5)
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, $userGroupId);
        $userrole->setUserRoleId($this->getUserId());
        return $userrole->save();
    }

    public function decoupling($userid = '',$userGroupId=5)
    {
        if (!$userid) return false;
        $userrole = sf::getModel("UserRoles")->selectByRole($userid, $userGroupId);
        $userrole->delete();
        return true;
    }

    public function isManager($userid = '')
    {
        if (!$userid) return false;
        $manager = sf::getModel('Managers')->selectByUserId($userid);
        if ($manager->getIsManager()) return true;
        else return false;
    }

    /**
     * 待审核的变更申请(加签)
     */
    function getChangeWaitSignNum()
    {
        $row = sf::getLib("db")->fetch_first("SELECT count(*) AS num FROM project_changes WHERE id in (select item_id from signs where user_id = '".input::getInput("session.roleuserid")."' and statement = 1) ");
        return $row['num'];
    }
}