<?php
namespace App\Model;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Model\BaseFruitSofts;
/**
 * 类名：数据模型扩展类
 * 说明：提供数据模型扩展方法。
 * $Id: Tools.php 2 2012-05-11 07:12:52Z meetcd $
 */
class FruitSofts extends BaseFruitSofts
{
    private $soft = NULL;

    function getSoft($f=false)
    {
        if($this->soft === NULL || $f) $this->soft = sf::getModel("Softs")->selectBySoftId(parent::getSoftId());
        return $this->soft;
    }

    public function getNewSort()
    {
        $count = (int)sf::getLib('db')->result_first("select count(*) c from `".$this->table."` where item_id = '".$this->getItemId()."' and `item_type` = '".$this->getItemType()."'");
        return $count+1;
    }

    public function selectBySoftId($softId,$itemId,$itemType='transform')
    {
        $db = sf::getLib("db");
        $query = $db->query("SELECT * FROM `".$this->table."` WHERE `soft_id` = '{$softId}' and `item_id` = '{$itemId}' and `item_type` = '{$itemType}'");
        if($db->num_rows($query)){
            $this->fillObject($db->fetch_array($query));
        }else{
            $this->setItemId($itemId);
            $this->setSoftId($softId);
            $this->setItemType($itemType);
            $this->setCreatedAt(date("Y-m-d H:i:s"));
        }
        return $this;
    }

    function delete()
    {
        parent::delete();
        //更新排序
        $softs = $this->selectAll("item_id = '" . parent::getItemId() . "' and item_type = '".parent::getItemType()."'","order by sort asc,id asc");
        while($soft = $softs->getObject()){
            $soft->setSort($softs->getIndex());
            $soft->save();
        }
        return true;
    }
}