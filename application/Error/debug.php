<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <title><?=config::get("site_name")?></title>
    <meta name="description" content="<?=config::get("site_name")?>" />
    <meta http-equiv="refresh" content="<?=$second?:5?>;url=<?=$url?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <link href="<?=site_url('assets/sweet-alert2/sweetalert2.min.css')?>" rel="stylesheet" type="text/css">
    <!-- jQuery  -->
    <script src="<?=site_url('assets/js/jquery.min.js')?>"></script>
    <script src="<?=site_url('assets/sweet-alert2/sweetalert2.min.js')?>"></script>
</head>
<body>
<script type="text/javascript">
    var index = 4;
    $(function(){
        index = getSecond();
        setInterval('countdown()',1000);
    });

    function getSecond()
    {
        var content = $('meta[http-equiv="refresh"]').attr('content');
        var strindex = content.indexOf(';');
        return content.substr(0,strindex);
    }

    function countdown()
    {
        index--;
        if(index<=0) index=0;
        $('.count').html(index);
    }
</script>
<!-- Loader -->
<!-- Navigation Bar-->

<!-- End Footer -->
<!-- Sweet Alert -->
<script>
    swal({
        title: '提示',
        text: "<?=$msg?>",
        type: '<?=$type?:'warning'?>',
        allowOutsideClick: false, // 禁止点击空白处关闭弹窗
        confirmButtonClass: 'btn btn-success',
        cancelButtonClass: 'btn btn-danger m-l-10',
        confirmButtonText: '立即跳转',
        animation:'false'
    }).then(function () {
        location.href='<?=$url?>';
    })
</script>
</body>
</html>