<?php

use Sofast\Core\Log;
use Sofast\Core\router;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
use App\Models\Fieldtype;
use App\Models\CmsArchives;
use App\Models\UserFieldtype;
use App\Models\Subject;
use App\Models\Guide;
use App\Models\BookMap;
use Illuminate\Database\Capsule\Manager as DB;
use App\Models\Region;
use App\Model\BookMaps;
use App\Models\CmsArctype;
use App\Models\DocumentType;
use App\Models\Personnel as PersonnelModel;
use App\Lib\BeanstalkClient;



function addReward($userId,$subject,$level='所级奖',$rewardName,$rank='',$companys=[],$members=[])
{
	// $companys=[
	// 	['company_id'=>'','company_name'=>''],
	// ];
	// $members=[
	// 	['member_id','company_id'=>'','member_name'=>'','company_name'=>''],
	// ];
	$declarer=sf::getModel('Declarers')->selectByUserId($userId);
	if($declarer->isNew()){
		return "人员不存在";
	}
	$corporation=sf::getModel('Corporations')->selectByUserId($declarer->getCorporationId());
	if($declarer->isNew()){
		return "人员所在单位不存在";
	}
	$category="科技奖励";
	$reward = sf::getModel('Rewards')->selectByRewardId();
//    if($rewardName=='科学技术奖') $rewardName = '科技进步奖';
//	if(!in_array($rewardName,["科技进步奖","软科学成果奖","创新团队奖"])){
//		return "未知奖项名称";
//	}
//	if(in_array($rewardName,["科技进步奖","软科学成果奖"])){
//		if(!in_array($rank,["特等奖","一等奖","二等奖","三等奖"])){
//			return "未知获奖名次";
//		}else{
//			$reward->setRank($rank);
//		}
//	}
	$secondCompany = $corporation->getSecondCompany();
	$reward->setUserId($userId);
	$reward->setRole(2);
	$reward->setCompanyLevel($secondCompany->getLevel());
	$reward->setCategory($category);
	$reward->setLevel($level);
	$reward->setSubject($subject);
	$reward->setRewardName($rewardName);
	$reward->setStatement(20);
	$reward->save();
	//完成单位
	foreach($companys as $key=>$company){
		$fruit = $reward->getCompanyById(0);
		$fruit->setItemId($reward->getRewardId());
        $fruit->setItemType('reward');
		$fruit->setSort($key+1);

		$corporation = sf::getModel('Corporations')->selectByUserId($company['company_id']);
		if(!$corporation->isNew()){
			$fruit->setCompanyType('inner');
			$fruit->setCompanyId($corporation->getUserId());
            $fruit->setCompanyName($corporation->getFullName());
		}else{
			$fruit->setCompanyType('outer');
			$fruit->setCompanyName($company['company_name']);
		}
		$fruit->setCreatedAt(date('Y-m-d H:i:s'));
		$fruit->save();
	}
	//完成人
	foreach($members as $key=>$member){
		$fruit = $reward->getMemberById(0);
		$fruit->setItemId($reward->getRewardId());
        $fruit->setItemType('reward');
		$fruit->setSort($key+1);

		$declarer = sf::getModel('Declarers')->selectByUserId($member['member_id']);
		if(!$declarer->isNew()){
			$fruit->setUserId($declarer->getUserId());
			$fruit->setUserName($declarer->getPersonname());
			$corporation = sf::getModel('Corporations')->selectByUserId($member['company_id']);
			if(!$corporation->isNew()){
				$fruit->setCompanyId($corporation->getUserId());
				$fruit->setCompanyName($corporation->getSubject());
			}else{
				$fruit->setCompanyName($member['company_name']);
			}
		}else{
			$fruit->setUserName($member['member_name']);
         	$fruit->setCompanyName($member['company_name']);
		}
		$fruit->setCreatedAt(date('Y-m-d H:i:s'));
		$fruit->save();
	}
	return true;
}
/**
 * 一些有用的全局函数
 *
 */
function getYearList($select = 0,$from=2015,$to=0)
{
	if(!$to) $to = date("Y")+1;
	$htmlStr = '';
	for($i=$to;$i >=$from;$i--){
		$htmlStr .= '<option value="'.$i.'" ';
		if($select == $i) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$i."</option>\r\n";
	}
	return $htmlStr;
}
function getQuarterList($select = 0,$from=1,$to=4)
{
	$htmlStr = '';
	for($i=$from;$i <= $to;$i++){
		$htmlStr .= '<option value="'.$i.'" ';
		if($select == $i) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$i."</option>\r\n";
	}
	return $htmlStr;
}

/**
 * 取得项目类型
 */
function getTypesList($select = -1)
{
	$result = sf::getModel("Types")->selectAll(" is_show = 1 ","order by id");
	$htmlStr = '';
	while($type = $result->getObject()){
		$htmlStr .= '<option value="'.$type->getId().'" ';
		if($select == $type->getId()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$type->getSubject()."</option>\r\n";
	}
	return $htmlStr;
}

function getGuidesList($select = -1)
{
	$result = Guide::where('is_show',1)->where('is_lastnode',1)->orderBy('id')->get();
	$htmlStr = '';
	foreach($result as $guide){
		$htmlStr .= '<option value="'.$guide->id.'" ';
		if($select == $guide->id) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$guide->subject."</option>\r\n";
	}
	return $htmlStr;
}

/**
 * 取得项目类型
 */
function getTypesArray($select = -1)
{
	$result = sf::getModel("Types")->selectAll("","ORDER BY id");
	$arr=array();
	while($type = $result->getObject()){
		$arr[$type->getId()] = $type->getSubject();
	}
	return $arr;
}

/**
 * 取得指南
 */
function getGuideArray($select = -1)
{
	$result = Guide::where('is_show',1)->where('is_lastnode',1)->orderBy('id')->get();
	$arr=array();
	foreach($result as $guide){
		$arr[$guide->id] = $guide->subject;
	}
	return $arr;
}

/**
 * 取得bookmap
 */
function getBookmapArray($select = -1)
{
	$result = BookMap::where('is_show',1)->orderBy('id')->get();
	$arr=array();
	foreach($result as $bookmap){
		$arr[$bookmap->id] = $bookmap->subject;
	}
	return $arr;
}

/**
 * 取得归口部门列表
 */
function getDepartmentList($select = '',$type=0)
{
	$addWhere = ' id not in ("1")';
	$type && $addWhere .= " and type IN (".$type.")";
	$result = sf::getModel("departments")->selectAll($addWhere,'ORDER BY id ASC,`type` ASC ');
	$htmlStr = '';
	while($type = $result->getObject()){
		$htmlStr .= '<option value="'.$type->getUserId().'" ';
		if($select == $type->getUserId()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$type->getSubject()."</option>\r\n";
	}
	return $htmlStr;
}

/**
 * 取得上级单位列表
 */
function getParentList($select = '',$level='1')
{
	$addWhere = ' id not in ("1","5","8")';
	$level && $addWhere .= " and level <= ".$level;
	$result = sf::getModel("Corporations")->selectAll($addWhere,'ORDER BY id ASC,`level` ASC ');
	$htmlStr = '';
	while($level = $result->getObject()){
		$htmlStr .= '<option value="'.$level->getUserId().'" ';
		if($select == $level->getUserId()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$level->getSubject()."</option>\r\n";
	}
	return $htmlStr;
}
/**
 * 取得申报单位列表
 */
function getCorporationList($select = '')
{
	$addWhere = ' id >=10 ';
	$result = sf::getModel("Corporations")->selectAll($addWhere,'ORDER BY id ASC,`level` ASC ');
	$htmlStr = '';
	while($level = $result->getObject()){
		$htmlStr .= '<option value="'.$level->getUserId().'" ';
		if($select == $level->getUserId()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$level->getSubject()."</option>\r\n";
	}
	return $htmlStr;
}
/**
 * 取得归口部门列表
 */
function getShiftList($select = '')
{
	return getSelectFromArray(array('2'=>'转移支付','3'=>'苗子工程','4'=>'自筹项目'),$select,false);
}


/**
 * 取得归口部门类型
 */
function getDepartmentType($select = '')
{
	return get_select_option('department',$select,false,0);
}

/**
 * 取得科室列表
 */
function getOfficeList($select = 0)
{
	return get_select_option('office',$select,false,0);
}

/**
 * 取得项目领域列表
 */
function getDomainList($select = 0)
{
	return get_select_option('domain',$select,true,0);
}
/**
 * 取得学科列表
 */
function getSubjectList($select = 0,$level=0,$type=1,$return_type='string')
{
	$data=[];
	$addWhere = '1=1 ';
	$level && $addWhere .= " and level = '".$level."' ";
	$type && $addWhere.=" and type={$type} order by code asc";

	$result = sf::getModel("subjects")->selectAll($addWhere);
	$htmlStr = '';
	while($subject = $result->getObject()){
		$htmlStr .= '<option value="'.$subject->getCode().'" ';
		if($select == $subject->getCode()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$subject->getSubject()."</option>\r\n";
		$data[$subject->getCode()] = $subject->getSubject();
	}
	if($return_type=='array')
		return $data;
	return $htmlStr;
}

function getSubjectArray($level=1,$type=1,$subject_ids=[])
{
	if(!$subject_ids)
	{
		$guide = Guide::find($_SESSION['guide_id']);
		if($guide)
		{
			if($type==1)
				$subject_ids = $guide->getSubjectIds();
			if($type==3)
				$subject_ids = $guide->getIndustryIds();
		}
		else $subject_ids=[];
	}

	$result=[];
	$subject=Subject::where('level',$level)->where('type',$type);
	if($subject->count()>0)
		if(count($subject_ids)>0)
			return $subject->whereIn('code',$subject_ids)->lists('subject','code')->toArray();
		else
			return $subject->lists('subject','code')->toArray();
	}

/**
 * 取得职称列表
 */
function getWorkList($select = 0, $level=0)
{
	return get_select_option('work',$select,true,$level);
}
/**
 * 取得职业资格列表
 */
function getCategorys($select = 0,$type)
{
	return get_select_option($type,$select,false,0);
}
/**
 * 取得项目类别列表
 */
function getProjectTypes($select = 0)
{
    $typeIds = [];
    $types = sf::getModel("Types")->selectAll("is_show=1",' order by id ASC ');
    while($type = $types->getObject()){
        $typeIds[$type->getId()] = '['.$type->getId().']'.$type->getSubject();
    }
    return getSelectFromArray($typeIds,$select,false);
}
/**
 * 取得地区列表
 */
function getRegionOld($select = 0,$parent_id=0,$type=0)
{
	$result = Region::where('parent_id',$parent_id)->where('region_type',$type)->get();
	$htmlStr = '';
	if(!empty($result)){
		$result = $result->toArray();
		foreach($result as $item){
			$htmlStr .= '<option value="'.$item['region_id'].'" ';
			if($select == $item['region_id']) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$item['region_name']."</option>\r\n";
		}
	}
	return $htmlStr;
}
/**
 * 取得地区列表
 */
function getRegion($select = 0,$type=0)
{
	if(strlen($select)<6 || $type==1){
		$result = Region::where('level',$type)->get();
		$select = substr($select,0,2).'0000';
	}else{
		switch($type){
			case 2:
			$code = substr($select,0,2);
			$select = substr($select,0,4).'00';
			break;
			case 3:
			$code = substr($select,0,4);
			break;
			default:
			$code = substr($select,0,2);
			break;
		}
		$result = Region::where('code','like',$code.'%')->where('level',$type)->orderBy('code','asc')->get();
	}

	$htmlStr = '';
	if(!empty($result)){
		$result = $result->toArray();
		foreach($result as $item){
			$htmlStr .= '<option value="'.$item['code'].'" ';
			if($select == $item['code']) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.getReginName($item['code'],$type)."</option>\r\n";
		}
	}
	return $htmlStr;
}

/**
 * 取得民族列表
 */
function getNationList($select = 0)
{
	return get_select_option('nation',$select,true,0);
}

/**
 * 取得用户组数据
 */
function getUserGroupList()
{
	$data = array();
	$groups = sf::getModel("UserGroups")->selectAll();
	while($group = $groups->getObject()){
		$data[str_pad($group->getId(),2,'0',STR_PAD_LEFT)] = $group->getUserGroupName();
	}
	return $data;
}

/**
 * 取得单位性质列表
 */
function getPropertyList($select = 0)
{
	return get_select_option('unit_property',$select,true,0);
}

/**
 * 取得学历列表
 */
function getDegreeList($select = 0)
{
	return get_select_option('degree',$select,true,0);
}

/**
 * 取得学位列表
 */
function getEducationList($select = 0)
{
	return get_select_option('education',$select,true,0);
}

function getAssociationList()
{
    $associations = sf::getModel("Associations")->selectAll();
    $datas = [];
    while($association = $associations->getObject()){
        $datas[$association->getAssociationId()] = $association->getSubject();
    }
    return $datas;
}

/**
 * 取得帮助分类列表
 */
function getHelpList($select = 0)
{
	return get_select_option('help',$select,false,0);
}


function filter($var) 
{ 
	if($var == '') return false; 
	else return true; 
}

function getNews($category_id=1,$showMax=5)
{
	$result = sf::getModel("Articles")->selectByCategoryId($category_id,$showMax);
	$htmlStr = "<ul>";
	if($category_id == 217)
	{
		while($news = $result->getObject())
		{
			$htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."<em>(".$news->getUpdatedAt("Y-m").")</em>"."</li>\r\n";	
		}
		
	}
	else if($category_id == 219)
	{
		while($news = $result->getObject())
		{
			$htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."</li>\r\n";	
		}
		
	}
	while($news = $result->getObject())
	{
		$htmlStr .= '<li>'.link_to("home/show/id/".$news->getId(),$news->getSubject(20))."</li>\r\n";	
	}
	$htmlStr .= "</ul>";
	return $htmlStr;
}

function getHelps($showMax='')
{
	$level = input::getInput("session.userlevel");
	$result = sf::getModel("Helps")->selectAll("user_group_id LIKE '".$level.",%' OR user_group_id LIKE '%,".$level.",%' OR user_group_id LIKE '%,".$level."' OR user_group_id = '".$level."' OR user_group_id = 'all' ","ORDER BY `updated_at` DESC",$showMax);
	
	$htmlStr = "<ul>\r\n";
	while($helps = $result->getObject()){
		$htmlStr .= '<li>'.link_to("help/show/cid/".$helps->getCategoryId()."/id/".$helps->getId()."/type/".$helps->getFileType(),$helps->getSubject(20))."</li>\r\n";
	}
	$htmlStr .= "</ul>";
	
	return $htmlStr;
}

function getSexList($select = '男')
{
	$htmlStr  = '<option value="">=性别=</option> ';
	$htmlStr  .= '<option value="男" ';
	if($select == '男') $htmlStr .='selected="selected"';
	$htmlStr .='>男</option>';
	$htmlStr .= '<option value="女" ';
	if($select == '女') $htmlStr .='selected="selected"';
	$htmlStr .='>女</option>';
	return $htmlStr;
}

function text_to_html($char)
{
	$char=htmlspecialchars($char);
	$char=str_replace(" ","&nbsp;",$char);
	$char=nl2br($char);
	$char=str_replace("<?","< ?",$char);
	return $char;
}

/**
 * 动态生成下拉列表
 */
function getSelectFromArray($rst=array(),$select = 0,$foce=true,$warp="\r\n")
{
	$htmlStr = '';
	foreach($rst as $key => $val){
		$foce && $key = $val;
		$htmlStr .= '<option value="'.$key.'" ';
		if(strlen($select)>0 && $select == $key) $htmlStr .= 'selected="selected" ';
		if(is_array($select) && in_array($key,$select)!==false) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>".$warp;
	}
	return $htmlStr;
}

function getOptionString($select = 0,$rst = array())
{
	$htmlStr = '';
	foreach($rst as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($select == $key) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

/**
 * 取得单选控件
 */
function get_radio($options=array(),$name='radio',$sel='',$class='',$accord=true,$required=false)
{
	$htmlStr = '';
    $requiredStr = '';
	$class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
    if($required) $requiredStr = 'required="required"';
	foreach($options as $key => $val){

		$accord && $key = $val;//是否强制键值一致
		if($sel == $key) $check = 'checked="checked"';
		else $check = '';
		$htmlStr .= '<div class="form-check form-check-inline"><input type="radio" class="form-check-input" name="'.$name.'" '.$check.' id="'.$name.$key.'" value="'.$key.'" '.$requiredStr.'> <label class="form-check-label" for="'.$name.$key.'">'.$val.'</label></div>';
	}
	return $htmlStr;
}

function get_checkbox($options=array(),$name='radio',$sel='',$class='',$accord=true,$col=12)
{
	$htmlStr='';
	$class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
	if(!$sel) $sel = array();
	foreach($options as $key => $val){
		$accord && $key = $val;//是否强制键值一致
		if(in_array($key,$sel) || $sel=='all')
		{
			$open = 'open';
			$check = 'checked="checked"';
		}
		else
		{
			$open = '';
			$check = '';
		}
		if(!$col)
			$colstr = '';
		else $colstr = 'col-xs-12 col-md-'.$col.' col-sm-'.$col;
		$htmlStr .= '<div class="form-check form-check-inline"><input type="checkbox" name="'.$name.'[]" id="'.$name.$key.'" class="'.$class.' '.$open.'" value="'.$key.'" '.$check.' /><label class="form-check-label '.$colstr.' text-left no-margin" for="'.$name.$key.'">'.$val.'</label></div>';
	}
	return $htmlStr;
}

function get_checkboxNew($options=array(),$name='radio',$sel='',$class='',$accord=true,$col=12)
{
    $htmlStr='';
    $class = $class ? ($class=='none'?'':'ace '.$class) : 'ace ';
    if(!$sel) $sel = array();
    foreach($options as $key => $val){
        $accord && $key = $val;//是否强制键值一致
        if(in_array($key,$sel) || $sel=='all')
        {
            $open = 'open';
            $check = 'checked="checked"';
        }
        else
        {
            $open = '';
            $check = '';
        }
        if(!$col)
            $colstr = '';
        else $colstr = 'col-xs-12 col-md-'.$col.' col-sm-'.$col;
        $htmlStr .= '<div class="form-check form-check-inline"><input id="'.$name.$key.'" name="'.$name.'[]" type="checkbox" class="'.$class.' '.$open.'" value="'.$key.'" '.$check.' /><label class="form-check-label" for="'.$name.$key.'">'.$val.'</label></div>';
    }
    return $htmlStr;
}


/**
 * 取得下拉列表的数据内容
 */ 
function get_select_data($type='nation',$level=0)
{
	$options = array();
	$addWhere = '';
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$options = $cache->getCache($type.$level)){
		$level && $addWhere .= "level = '".$level."'";
		$result = sf::getModel("categorys",0,$type)->selectAll($addWhere);
		while($option = $result->getObject())
			$options[$option->getId()] = $option->getSubject();
		$cache->setCache($type.$level,$options);
	}
	return $options;
}

/**
 * 取得数据字典内容的下拉option
 */
function get_select_option($type='nation',$sel='',$accord = false,$level=0)
{
	$options = get_select_data($type,$level);
	return getSelectFromArray($options,$sel,$accord);
}

/**
 * 取得数据字典内容的下拉option
 */
function get_budget_select($type='nation',$sel='',$accord = false,$level=0)
{
	$result = sf::getModel("categorys",0,$type)->selectAll($addWhere);
	while($option = $result->getObject()){
		$_data['id'] = $option->getId();
		$_data['subject'] = $option->getSubject();
		$_data['cover'] = $option->getCover();
		$options[] = $_data;
	}

	foreach($options as $val){
		$accord && $key = $val;
		$htmlStr .= '<option value="'.$val['subject'].'"';
		if($val['subject'] == $sel) $htmlStr .= ' selected="selected" ';
		$htmlStr .= ' title="'.$val['cover'].'">'.$val['subject'].'</option>';	
	}
	return $htmlStr;
}

/**
 * 序列化 替换特殊符号
 */
function get_SafeSerialize($v = '')
{
	if(empty($v)) return $v;
	$v = str_replace('"',"”",$v);
	$v = str_replace('\'',"’",$v);	
	$v = str_replace("\\","/",$v);	
	return $v;
}

function checkStr($v)
{
	if(is_array($v)){
		foreach($v as $key => $val){
			$v[$key] = checkStr($val);	
		}	
	}else{
		$v = str_replace('"','"',$v);
		$v = str_replace('\'','’',$v);
		$v = str_replace('\\','/',$v);
	}
	return $v;
}

/**
 * 取得目录
 */
function getFolder($type_id=1)
{
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),3600);
	if(!$data = $cache->getCache('type_cache')){
		$types = sf::getModel("types")->selectAll();
		while($type = $types->getObject()){
			$data[$type->getId()] = $type->getDir();
		}
		$cache->setCache('type_cache',$data);
	}
	return $data[$type_id];
}

/**
 * 搜索相关函数
 */
function getYearSearch($name='year',$sel=array())
{
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$data = $cache->getCache($name)){
		$data = array();
		for($i=2005,$n = config::get('current_declare_year',date("Y"));$i<=$n;$i++)
			$data[$i] = $i.'年';
		$cache->setCache($name,$data);
	}
	return get_checkbox($data,$name,$sel,' ',false,2);	
}

function getOfficeSearch($name='office',$sel=array())
{
	return get_checkbox(get_select_data('office'),$name,$sel,' ',false,2);	
}

function getTypeSearch($name='types',$sel=array())
{
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$data = $cache->getCache($name)){
		$types = sf::getModel("Types")->selectAll();
		while($type = $types->getObject()){
			$data[$type->getId()] = $type->getSubject();
		}
		$cache->setCache($name,$data);
	}
	return get_checkbox($data,$name,$sel,' ',false);	
}

function getGatherSearch($name='gather',$sel=array(),$type='')
{
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	$addWhere ='';
	
	$temp = array();
	if(strstr($type,',')) 
	{
		$temp = explode(',',$type);
		$addWhere = "`type` = '".$temp[0]."'";
		for($i=1;$i<count($temp);$i++)
		{
			$addWhere .= " or `type` = '".$temp[$i]."'"; 
		}
	}
	else $addWhere = "`type` = '".$type."'";
	
//	if(!$data = $cache->getCache($name)){
	$types = sf::getModel("departments")->selectAll($addWhere,"ORDER BY type ASC");
	while($type = $types->getObject()){
		$data[$type->getUserId()] = $type->getSubject();
	}
//		$cache->setCache($name,$data);
//	}
	return get_checkbox($data,$name,$sel,' ',false);
}

function getStateSearch($name='state',$sel=array())
{
	$data = array('2'=>'等待承担单位审核','5'=>'等待归口部门审核','9'=>'等待科技创新部受理','10'=>'科技创新部已受理','18'=>'项目评审中','20'=>'项目已分流','25'=>'分管科室已推荐','29'=>'项目已立项','30'=>'项目已结题');
	return get_checkbox($data,$name,$sel,' ',false);
}

function getStateWait($name='state',$sel=array())
{
	$data = array('1'=>'进入备选库','2'=>'未进入备选库');
	return get_radio($data,$name,$sel,' ',false);
}

function addAssess($project_id='',$content='',$is_back=0,$is_lock=0)
{
	$Assess = sf::getModel("Assess")->getByProjectId($project_id);
	//如过有没有锁定的直接打开编辑
	$Assess->setUserId(input::getInput("session.userid"));
	$Assess->setUserName(input::getInput("session.nickname"));
	$Assess->setUserGroupId(input::getInput("session.userlevel"));
	$Assess->setProjectId($project_id);
	$Assess->setIsBack($is_back?$is_back:0);
	$Assess->setContent($content);
	$Assess->setIsLock($is_lock);
	$Assess->setUpdatedAt(date("Y-m-d H:i:s"));
	return $Assess->save();
}

function addAudit($project_id='',$content='',$is_back=0,$is_lock=0)
{
	$audit = sf::getModel("Audits")->getByProjectId($project_id);
	//如过有没有锁定的直接打开编辑
	$audit->setUserId(input::getInput("session.userid"));
	$audit->setUserName(input::getInput("session.nickname"));
	$audit->setUserGroupId(input::getInput("session.userlevel"));
	$audit->setProjectId($project_id);
	$audit->setIsBack($is_back?$is_back:0);
	$audit->setContent($content);
	$audit->setIsLock($is_lock);
	$audit->setUpdatedAt(date("Y-m-d H:i:s"));
	return $audit->save();
}

function addHistory($project_id='',$content='',$type='project',$isHidden=0)
{
	$History = sf::getModel('Historys');
	$History->setUserId(input::getInput("session.userid"));
	$History->setUserName(input::getInput("session.nickname"));
	$History->setUserGroupId(input::getInput("session.userlevel"));
	$History->setType($type);
	$History->setProjectId($project_id);
	$History->setContent($content);
	$History->setIsHiden($isHidden);
	$History->setUpdatedAt(date("Y-m-d H:i:s"));
	return $History->save();
}

function addSign($itemId,$userId,$itemType='project')
{
    $user = sf::getModel('Users')->selectByUserId($userId);
    if($user->isNew()) return false;
	$sign = sf::getModel('Signs');
    $sign->setItemId($itemId);
    $sign->setItemType($itemType);
    $sign->setUserId($userId);
    $sign->setUserName($user->getUserUsername());
    $sign->setStatement(1);
    $sign->setCreatedAt(date("Y-m-d H:i:s"));
    $sign->setUpdatedAt(date("Y-m-d H:i:s"));
	return $sign->save();
}

/**
 * 发送电子邮件
 */
function sendMail($email='',$subject='',$content='')
{
    if(empty($email) || !isEmail($email) || empty($subject) || empty($content)) return false;
    return smtp_mail($email,$subject,$content);
}

function smtp_mail($sendto_email, $subject, $body, $extra_hdrs='', $user_name='') {
    $mail = sf::getLib('Phpmailer');
    $mail->IsSMTP(); // send via SMTP
    //$mail->Host = 'email.caacsri.com'; // SMTP servers 注意：好像听说是只有2006年以前申请的163邮箱具有此功能
    $mail->Host = 'smtp.caacsri.com'; // SMTP servers 注意：好像听说是只有2006年以前申请的163邮箱具有此功能
//    $mail->Host = 'emailtest.caacsri.com'; // SMTP servers 注意：好像听说是只有2006年以前申请的163邮箱具有此功能
    $mail->SMTPAuth = true; // turn on SMTP authentication
    $mail->Username = '<EMAIL>'; // SMTP username 注意：普通邮件认证不需要加 @域名
    $mail->Password = 'W83PdhKZUCCZigXf'; // SMTP password(我把我的密码给隐了)
//    $mail->Password = 'xdMFSfxb3vyPq7n8'; // SMTP password(我把我的密码给隐了)
    $mail->From = '<EMAIL>'; // 发件人邮箱
    $mail->FromName = "园区科研管理平台"; // 发件人
    $mail->CharSet = "utf-8"; // 这里指定字符集！
//    $mail->SMTPSecure = 'ssl';
    $mail->Port = 25;  //465
    $mail->SMTPDebug = 1;
    $mail->Encoding = "base64";
    $mail->AddAddress($sendto_email); // 收件人邮箱和姓名
    $mail->AddReplyTo('');
    if($extra_hdrs && $extra_hdrs!=$sendto_email){
        $mail->addCC($extra_hdrs);
    }
    $mail->IsHTML(true); // send as HTML
    // 邮件主题
    $mail->Subject = $subject;
    // 邮件内容
    $mail->Body = '
<html><head>
<meta http-equiv="Content-Language" content="zh-cn">
<meta http-equiv="Content-Type" content="text/html; charset=gb2312">
</head>
<body>
'.$body.
        '<br/>
</body>
</html>';
    $mail->AltBody ="text/html";
    if(!$mail->Send())
    {
        return $mail->ErrorInfo;
    }
    return true;
}

/**
 * 发送短信息
 */
function sendSms($mobile='',$content='',$send_at='',$item_id='',$item_type='projects')
{
	if(!isMobile($mobile) || empty($content)) return;
	$send_at = $send_at?$send_at:date("Y-m-d H:i:s");
	$sms = sf::getModel("ShortMessages");
	$sms->setUserId(input::getInput("session.userid"));
	$sms->setUserName(input::getInput("session.nickname"));
	$sms->setItemType($item_type);
	$sms->setItemId($item_id);
	$sms->setMobile($mobile);
	$sms->setMessage($content);
	$sms->setSendAt($send_at);
	$sms->setUserIp(input::getIp());
	$sms->setStatement(10);//等待发送
	$sms->setUpdatedAt(date("Y-m-d H:i:s"));
	$searchid = $sms->save();
	return postSms($mobile,$content,$searchid,$send_at);
}

/**
 * HTTP直接提交短信息
 */
function postSms($mobile,$content,$taskId='',$send_at='') {
	if(!isMobile($mobile) || empty($content)) return;
	if(!$taskId) $taskId=uniqid();
	$url = 'http://userinterface.vcomcn.com/Opration.aspx';
	$content = iconv("UTF-8","gbk//TRANSLIT",$content);
	$data='<Group Login_Name="'.config::get('sms.username').'" Login_Pwd="'.config::get('sms.password').'" OpKind="0" InterFaceID="" SerType="msg">
	<E_Time>'.date('Y-m-d H:i:s').'</E_Time>
	<Item>
		<Task>
			<Recive_Phone_Number>'.$mobile.'</Recive_Phone_Number>
			<Content><![CDATA['.$content.']]></Content>
			<Search_ID>'.$taskId.'</Search_ID>
		</Task>
	</Item>
</Group>';
$result = curlPost($url,$data);
if($result=='00'){
	$shortMessage = sf::getModel('ShortMessages')->selectByTaskId($taskId,$mobile);
	if(!$shortMessage->isNew()){
		$shortMessage->setStatement(20);
		$shortMessage->save();
	}
	return true;
}
return false;
}

function postMhesSms($mobile,$templateId,$paras,$taskId='')
{
    if(!isMobile($mobile) || empty($templateId)) return;
    if(!$taskId) $taskId=uniqid();
    $sms = sf::getLib('AliSMS');
    $sms->setMobile($mobile);
    $sms->setTemplateParas($paras);
    $response = $sms->send();
    $data = json_decode($response,true);
    if($data['Code']=='OK'){
//        echo '短信发送成功！BizId: '.$data['BizId'];
        $shortMessage = sf::getModel('ShortMessages')->selectByTaskId($taskId,$mobile);
        if(!$shortMessage->isNew()){
            $shortMessage->setStatement(20);
            $shortMessage->save();
        }
        return true;
    }else{
//        echo '短信发送失败！'.$response;
        return false;
    }
//    $sms = sf::getLib('SMS');
//    $sms->setMobile($mobile);
//    $sms->setTemplateId($templateId);
//    $sms->setTemplateParas($paras);
//    $response = $sms->send();
//    $data = json_decode($response,true);
////    Log::write(print_r($data,true));
//    if($data['code']=='000000'){
////        echo '短信发送成功！smsMsgId: '.$data['result'][0]['smsMsgId'];
//        $shortMessage = sf::getModel('ShortMessages')->selectByTaskId($taskId,$mobile);
//        if(!$shortMessage->isNew()){
//            $shortMessage->setStatement(20);
//            $shortMessage->save();
//        }
//        return true;
//    }else{
//        return false;
//    }
}
function curlPost($url = '', $postData = '', $options = array())
{
	if (is_array($postData)) {
		$postData = http_build_query($postData);
	}
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_POST, 1);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5); //设置cURL允许执行的最长秒数
    if (!empty($options)) {
    	curl_setopt_array($ch, $options);
    }
    //https请求 不验证证书和host
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}

function curlGet($url = '', $options = array())
{
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    if (!empty($options)) {
        curl_setopt_array($ch, $options);
    }
    //https请求 不验证证书和host
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
}
/**
 *  获取特定区域的HTML
 *
 * @access    public
 * @param     string  $s  开始标识符
 * @param     string  $e  末尾标识符
 * @param     string  $html  文档信息
 * @return    string
 */
function GetHtmlArea($sptag, &$areaRule, &$html)
{
    //用字符串模式匹配
    $areaRules = explode($sptag,$areaRule);
    if($html=='' || $areaRules[0]=='')
    {
        return '';
    }
    $posstart = @strpos($html,$areaRules[0]);
    if($posstart===FALSE)
    {
        return '';
    }
    $posstart = $posstart + strlen($areaRules[0]);
    $posend = @strpos($html,$areaRules[1],$posstart);
    if($posend > $posstart && $posend!==FALSE)
    {
        //return substr($html,$posstart+strlen($areaRules[0]),$posend-$posstart-strlen($areaRules[0]));
        return substr($html,$posstart,$posend-$posstart);
    }
    else
    {
        return '';
    }
}
/**
 * 评审打分控件
 */
function get_score($name='grade[]',$sel='',$start=0,$end=10,$step=1)
{
	for($i=$end;$i>=$start;$i=$i-$step){
		if($i<$step) $i = 0;
		if(sprintf("%.2f",$sel) == sprintf("%.2f",$i)) $check = 'checked="checked"';
		else $check = '';
		$htmlStr .= '<li><label><input type="radio" name="'.$name.'" value="'.$i.'" '.$check.' class="required" />'.$i.'</label></li>';
	}
	return $htmlStr;
}

function json_convert($str='')
{
	return preg_replace("#\\\u([0-9a-f]+)#ie", "iconv('UCS-2', 'UTF-8', pack('H4', '\\1'))", $str);	
}

function object_array($array){
	if(is_object($array)){
		$array = (array)$array;
	}
	if(is_array($array)){
		foreach($array as $key=>$value){
			$array[$key] = object_array($value);
		}
	}
	return $array;
}

//专家研究领域
function expert_subject_select($sel=0)
{
	$_arry = array('数学','信息科学与系统科学','力学','物理学','化学','天文学','地球科学','生物学','农学','林学','畜牧、兽医科学','水产学','基础医学','临床医学','预防医学与卫生学','军事医学与特种医学','药学','中医学与中药学','工程与技术科学基础学科','测绘科学技术','材料科学','矿山工程技术','冶金工程技术','机械工程','动力与电气工程','能源科学技术','核科学技术','电子、通信与自动控制技术','计算机科学技术','化学工程','食品科学技术','木建筑工程','水利工程','交通运输工程','航空、航天科学技术','环境科学技术','安全科学技术','管理学','经济学','政治学','法学','军事学','社会学','民族学','新闻学与传播学','教育学','体育学','统计学');
	return getSelectFromArray($_arry,$sel);
}

//判断机构代码的正确性
function isUnitCode($text)
{
	//如果位数不够，连接线出错直接报错
	if(strlen($text) != 10) return false;
	
	$s = $z = $c9 = $v =  0;//初始化
	$w = array(3,7,9,10,5,8,4,2);//加权因子
	$text = strtoupper($text);//全部转化成大写
	
	for($i=0;$i<8;$i++){
		$c = substr($text,$i,1);
		if(ord($c) >=65 && ord($c) <= 90) $z = (ord($c) - 55)*$w[$i];
		elseif($c >= 0 && $c <= 9) $z = $c*$w[$i];
		else return false;//非法字符
		$s += $z;//求和
	}
	
	//取得校验位c9
	$v = 11 - ($s%11);
	if($v == 10) $c9 = 'X';
	elseif($v == 11) $c9 = 0;
	else $c9 = trim($v);
	
	//判断是否有效
	if(substr($text,9,1) == $c9) return true;
	return false;
}

//判断机构代码的正确性
function isIdcard($text)
{
	if(sf::getLib("Idcard",$text)->valid()) return true;	
	else return false;
}

function isCreditCode($text)
{
	$text = strtoupper($text);//全部转化成大写
	$d = array('0'=>0,'1'=>1,'2'=>2,'3'=>3,'4'=>4,'5'=>5,'6'=>6,'7'=>7,'8'=>8,'9'=>9,'A'=>10,'B'=>11,'C'=>12,'D'=>13,'E'=>14,'F'=>15,'G'=>16,'H'=>17,'J'=>18,'K'=>19,'L'=>20,'M'=>21,'N'=>22,'P'=>23,'Q'=>24,'R'=>25,'T'=>26,'U'=>27,'W'=>28,'X'=>29,'Y'=>30);//字典
	$w = array(1,3,9,27,19,26,16,17,20,29,25,13,8,24,10,30,28);//加权因子
	//求级数
	for($i=0;$i<17;$i++)
		$_sum += $d[$text[$i]]*$w[$i];	
	//求验证码
	$v = 31- ($_sum%31);
	//if($v == 31) $v = 0;
	//if($v == 30) $v = 'Y';
	$v = array_search($v,$d);
	//比较验证码
	if(substr($text,-1,1) == $v) return true;
	else return false;
}

/**
 * 手机号验证
 */  
function isMobile($mobile){ 
	// $exp = "/^13[0-9]{1}[0-9]{8}$|15[0-9]{1}[0-9]{8}$|18[0-9]{1}[0-9]{8}$|14[57]{1}[0-9]{8}$|17[578]{1}[0-9]{8}$/"; 
	// $exp = "/^13[0-9]{1}[0-9]{8}$|15[0-9]{1}[0-9]{8}$|18[0-9]{1}[0-9]{8}$|14[57]{1}[0-9]{8}$|17[0-9]{1}[0-9]{8}$|19[9]{1}[0-9]{8}$/"; 
	$exp = "/^1[3456789]\d{9}$/";
	if(preg_match($exp,trim($mobile))) return true; 
	else return false; 	
}

/**
 * 判断是否是移动端
 * @return bool
 */
function isInMobile()
{
    return true;
    if (empty($_SERVER['HTTP_USER_AGENT'])) {
        return false;
    } elseif (strpos($_SERVER['HTTP_USER_AGENT'], 'Mobile') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'Android') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'Silk/') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'Kindle') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'BlackBerry') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'Opera Mini') !== false
        || strpos($_SERVER['HTTP_USER_AGENT'], 'Opera Mobi') !== false) {
        return true;
    } else {
        return false;
    }
}

/**
 * Email验证
 */
function isEmail($email){
	$exp = "/^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$/";
	if(preg_match($exp,trim($email))) return true;
	else return false;
}

/**
 * 生成文件
 */
function createFile($project_id,$type='budget')
{
	$project = sf::getModel("projects")->selectByProjectId($project_id);
	
	if($project->isNew())
		$this->page_debug(lang::get("The project do not exist!"),getFromUrl());	
	
	//保存文件
	$opts = array('http'=>array('method'=>"GET",'header'=>"USER_AGENT: PHPER_".substr(md5(date("YdmH")),-5)."\r\n"));	
	$context = stream_context_create($opts);
	$context = @file_get_contents(site_url("declare/budget/download/id/".$project_id),false,$context);
	$file_name = date("YmdHis").rand(1000,9999).'.doc';
	@file_put_contents(WEBROOT.'/up_files/doument/budget/'.$file_name,$context);
	//保存版本记录
	$ver = sf::getModel("Versions");
	$ver->setItemId($project->getProjectId());
	$ver->setSubject($project->getSubject());
	$ver->setFilePath($file_name);
	$ver->setType('budget');
	$ver->setUpdatedAt(date("Y-m-d H:i:s"));
	return $ver->save();
}

function Evaluate($str)
{
	$score = 0;
	if(preg_match("/[0-9]+/",$str)) $score ++;
	if(preg_match("/[0-9]{3,}/",$str)) $score ++;
	if(preg_match("/[a-z]+/",$str)) $score ++;
	if(preg_match("/[a-z]{3,}/",$str)) $score ++;
	if(preg_match("/[A-Z]+/",$str)) $score ++;
	if(preg_match("/[A-Z]{3,}/",$str)) $score ++;
	if(preg_match("/[_|\-|+|=|*|!|@|#|$|%|^|&|(|)]+/",$str)) $score += 2;
	if(preg_match("/[_|\-|+|=|*|!|@|#|$|%|^|&|(|)]{3,}/",$str)) $score ++ ;
	if(strlen($str) >= 6) $score ++;
	return $score;
}

function getBookMapList($type_id=0,$is_show=false)
{
    if($is_show) $addWhere = "is_show > 0 ";
    //任务合同
    if($type_id==1){
        $tasks = [];
        $arrs = sf::getModel("EngineWorkers")->selectAll("type = 'Task' and is_show > 0",' order by id DESC ')->toArray();
        array_map(function ($v) use(&$tasks){
            $tasks[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $tasks;
    }
    //申报书
    if($type_id==5){
        $declares = [];
        $arrs = sf::getModel("Types")->selectAll($addWhere,' order by id DESC ')->toArray();
        array_map(function ($v) use(&$declares){
            $declares[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $declares;
    }
    //评审指标
    if($type_id==4){
        $rules = [];
        $addwhere = "is_show > 0 ";
        if(input::session('userlevel')==3) $addwhere.=" and (company_id = '".input::session('roleuserid')."' or id = 1)";
        else $addwhere.=" and user_role = 6 ";
        $arrs = sf::getModel("Rules")->selectAll($addwhere,' order by id DESC')->toArray();
        array_map(function ($v) use(&$rules){
            $rules[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $rules;
    }
    if($type_id==10){
        $rules = [];
        $addwhere = "is_show > 0 ";
        if(input::session('userlevel')==3) $addwhere.=" and (company_id = '".input::session('roleuserid')."' or id = 1)";
        else $addwhere.=" and user_role = 6 ";
        $arrs = sf::getModel("AwardRules")->selectAll($addwhere,' order by id DESC')->toArray();
        array_map(function ($v) use(&$rules){
            $rules[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $rules;
    }
    //验收书
    if($type_id==2){
        $tasks = [];
        $arrs = sf::getModel("EngineWorkers")->selectAll("type = 'Complete' and is_show > 0",' order by id DESC ')->toArray();
        array_map(function ($v) use(&$tasks){
            $tasks[$v['id']] = '['.$v['id'].']'.$v['subject'];
        }, $arrs);
        return $tasks;
    }

    return $data;
}

function getReviewRuleList()
{
    $rules = [];
    $addwhere = "is_show > 0 ";
    $arrs = sf::getModel("ReviewRules")->selectAll($addwhere,' order by id DESC')->toArray();
    array_map(function ($v) use(&$rules){
        $rules[$v['id']] = '['.$v['id'].']'.$v['subject'];
    }, $arrs);
    return $rules;

}

function createWord($output,$hash='meetcd')
{
	//$output = preg_replace("/<\!--\[if.*?\]>.*?<\!\[endif\]-->/si","",$output);
	//$output = preg_replace("/<\!--\[if.*?\]-->/si","",$output);
	$hash = trim($hash);
	$mht = sf::getLib("MhtMaker");
	$mht->setSubject("中国民航局第二研究所科研项目申报材料");
	$mht->AddContents("index.htm",$mht->GetMimeType("index.htm"),$output);
	$mht->AddContents("header.htm",$mht->GetMimeType("header.htm"),file_get_contents(WEBROOT."/tpl/header.htm"));
	$mht->AddContents("filelist.xml",$mht->GetMimeType("filelist.xml"),file_get_contents(WEBROOT."/tpl/filelist.xml"));
	$mht->AddContents("colorschememapping.xml",$mht->GetMimeType("header.htm"),file_get_contents(WEBROOT."/tpl/colorschememapping.xml"));
	$mht->AddContents("themedata.thmx",$mht->GetMimeType("themedata.thmx"),file_get_contents(WEBROOT."/tpl/themedata.thmx"));
	$mht->AddContents("image001.gif",$mht->GetMimeType("image001.gif"),file_get_contents(site_url("q/image/hash/".$hash)));
	return $mht->GetFile();	
}

function isHome()
{
	return true;
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$homelist = $cache->getCache('is_home_list')){
		$types = sf::getModel("HomeList")->selectAll();
		while($type = $types->getObject()){
			$homelist[] = $type->getIp();
		}
		$cache->setCache('is_home_list',$homelist);
	}
	
	if(in_array(input::getIp(),$homelist)) return true;
	else return false;
}

function getSearchArray($type='base',$sel=array(),$is_config=false)
{
	$funcname = $type.'SearchList';
	$_data = $funcname();
	$default = $_data['default'];
	$data = $_data['data'];

	if($is_config)
		return $data;
	if(count($sel)>0)
	{
		$default = [];
		foreach($data as $key=>$value)
		{
			if(in_array($key,$sel))
				$default[$key] = $value;
		}
	}
	return $default;
}

function baseSearchList()
{
	$data['default'] = [
	'subject'=>'项目名称',
	'corporation_name'=>'单位名称'
	];
	$data['data'] = [
	'subject'=>'项目名称',
	'corporation_name'=>'单位名称',
	'cooperation'=>'参与单位',
	'user_name'=>'项目负责人',
	'accept_id'=>'申报编号',
	'radicate_id'=>'立项编号',
	'subject_name'=>'研究领域'
	];
	return $data;
}
function moreSearchList()
{
	$data['default'] = [
	'declare_year'=>'申报年度',
	'radicate_year'=>'立项年度'
	];
	$data['data'] = [
	'declare_year'=>'申报年度',
	'radicate_year'=>'立项年度',
	'type_current_group'=>'项目批次',
	'office_id'=>'所属科室',
	'cat_id'=>'所属大类',
	'type_id'=>'所属小类',
	'department_id'=>'所属归口部门',
	'is_shift'=>'项目属性',
	'statement'=>'项目状态',
	'state_for_plan_book'=>'任务书状态',
	'state_for_complete_book'=>'验收书状态'
	];
	return $data;
}

function searchContent($sel=array(),$input=array())
{
	$html='';
	$data = getSearchArray('more',$sel);
	foreach($data as $key=>$value)
	{
		$html .= '<select name="'.$key.'" id="'.$key.'">';
		$html .= '<option value="">='.$value.'=</option>';
		$html .= getSelect($key,$input[$key]);
		$html .= '</select>  ';
	}
	return $html;
}

function getSelect($name='',$sel='')
{
	switch($name)
	{
		case 'declare_year';{return getYearList($sel);}
		case 'radicate_year';{return getYearList($sel);}
		case 'type_current_group';{return getSelectFromArray(array('1'=>'第一批','2'=>'第二批','3'=>'第三批','4'=>'第四批','5'=>'第五批','6'=>'第六批','7'=>'第七批','8'=>'第八批'),$sel,false);}
		case 'office_id';{return getOfficeList($sel);}
		case 'cat_id';{return get_select_option('cat',$sel);}
		case 'type_id';{return getTypesList($sel);}
		case 'department_id';{return getDepartmentList($sel);}
		case 'is_shift';{return getShiftList($sel);}
		case 'statement';{return '<option value="2">等待单位审核</option>
		<option value="3">单位驳回</option>
		<option value="5">等待归口部门审核</option>
		<option value="6">归口部门驳回</option>
		<option value="9">等待科技创新部受理</option>
		<option value="10">科技创新部已受理</option>
		<option value="12">科技创新部不予受理</option>
		<option value="15">专家分配</option>
		<option value="17">项目已评审</option>
		<option value="20">项目已分流</option>
		<option value="22">未通过科室推荐</option>
		<option value="23">等待科长推荐</option>
		<option value="24">未通过科长推荐</option>
		<option value="25">通过科室推荐</option>
		<option value="29">科技创新部已立项</option>
		<option value="30">科技创新部同意结题</option>';}
		case 'state_for_plan_book';{return '<option value="2">待承担单位推荐</option>
		<option value="4">等待归口部门审核</option>
		<option value="6">等待科技创新部受理</option>
		<option value="10">科技创新部同意签署</option>';}
		case 'state_for_complete_book';{return '<option value="2">待承担单位推荐</option>
		<option value="4">待归口部门推荐</option>
		<option value="6">待科技创新部受理</option>
		<option value="10">分管科室已推荐</option>
		<option value="12">科技创新部已经受理</option>
		<option value="14">科技创新部同意结题</option>';}
		default:return '';
	}
}

function getSearchPart($input=array(),$sign='')
{
	$filter = sf::getModel('filters')->selectBySign($sign);
	return $filter->setTemplate('search')->getSearchPart($input);
}

function getSearchArea($role='default')
{
	$search = new \App\Module\Filter\Search;
	return $search->setRole($role)->render();
}

function getFilters($sign='')
{
	if(!$sign) return '';
	return sf::getModel('filters')->getFilterList($sign);
}

function getFilterArray($sign='')
{
	return sf::getModel('filters')->getFilterArray($sign);
}

/**
 * 字符截取 支持UTF8/GBK
 */
function strcut($string, $length, $dot = '...') {
	$charset = 'utf-8';
	if (strlen($string) <= $length) return $string;
	$string  = str_replace(array('&amp;', '&quot;', '&lt;', '&gt;'), array('&', '"', '<', '>'), $string);
	$strcut  = '';
	if (strtolower($charset) == 'utf-8') {
		$n   = $tn = $noc = 0;
		while ($n < strlen($string)) {
			$t = ord($string[$n]);
			if ($t == 9 || $t == 10 || (32 <= $t && $t <= 126)) {
				$tn = 1; $n++; $noc++;
			} elseif (194 <= $t && $t <= 223) {
				$tn = 2; $n += 2; $noc += 2;
			} elseif (224 <= $t && $t <= 239) {
				$tn = 3; $n += 3; $noc += 2;
			} elseif (240 <= $t && $t <= 247) {
				$tn = 4; $n += 4; $noc += 2;
			} elseif (248 <= $t && $t <= 251) {
				$tn = 5; $n += 5; $noc += 2;
			} elseif ($t == 252 || $t == 253) {
				$tn = 6; $n += 6; $noc += 2;
			} else {
				$n++;
			}
			if($noc >= $length) break;
		}
		if ($noc > $length) $n -= $tn;
		$strcut = substr($string, 0, $n);
	} else {
		for ($i = 0; $i < $length; $i++) {
			$strcut .= ord($string[$i]) > 127 ? $string[$i] . $string[++$i] : $string[$i];
		}
	}
	$strcut = str_replace(array('&', '"', '<', '>'), array('&amp;', '&quot;', '&lt;', '&gt;'), $strcut);
	return $strcut . $dot;
}

function clearTags($html='')
{
	return preg_replace("/(\s|\&nbsp\;|　|\xc2\xa0)/", "", strip_tags($html));
}

function role($role_id='')
{
	if(!$role_id) return false;
	$role = [
	'1'=>'admin',
	'2'=>'researcher',
	'10'=>'expert',
	'3'=>'company',
	];
	return $role[$role_id];
}

//百度ip接口
function get_city_baidu($ip=null) {
	$ch = curl_init();
	$url = "http://apis.baidu.com/apistore/iplookupservice/iplookup?ip=$ip";
	$header = array(
		'apikey: ',
		);
    // 添加apikey到header
	curl_setopt($ch, CURLOPT_HTTPHEADER  , $header);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    // 执行HTTP请求
	curl_setopt($ch , CURLOPT_URL , $url);
	$res = curl_exec($ch);

	var_dump(json_decode($res));
}

//新浪ip接口
function get_city($ip=null,$api='sina')
{
	$outernet = @file_get_contents("http://int.dpool.sina.com.cn/iplookup/iplookup.php?format=json");
	$outernet = json_decode($outernet);dd($location);
}

function getFiledTypeArray($column_key='id',$column_value='subject')
{
	return Fieldtype::where('status',1)
	->orderBy('type')
	->orderBy('sort')
	->lists($column_value,$column_key)
	->toArray();
}

//获取字段类型
function getFiledType($select='',$type='',$column_key='id',$column_value='subject')
{
	$htmlStr = '';
	$array_base = [];
	$array_extend = [];
	$types = Fieldtype::where('status',1)->orderBy('type')->orderBy('sort');
	if($type)
		$types = $types->where('type',$type);
	$types = $types->get()->toArray();

	foreach($types as $fieldtype)
		if($fieldtype['type']=='base')
			$array_base[$fieldtype[$column_key]] = $fieldtype[$column_value];
		else if($fieldtype['type']=='extend')
			$array_extend[$fieldtype[$column_key]] = $fieldtype[$column_value];

		$htmlStr = '<option value="" class="grey">请选择字段类型</option>'."\r\n";
		$htmlStr .= '<option disabled class="alert alert-info">基础字段</option>'."\r\n";
		foreach($array_base as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		$htmlStr .= '<option disabled class="alert alert-info">预设字段</option>'."\r\n";

		foreach($array_extend as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		return $htmlStr;
	}
	function getUserFiledType($select='',$type='')
	{
		$htmlStr = '';
		$array_base = [];
		$array_extend = [];
		$types = UserFieldtype::where('status',0)->orderBy('type')->orderBy('sort');
		if($type)
			$types = $types->where('type',$type);
		$types = $types->get()->toArray();

		foreach($types as $fieldtype){
			switch($fieldtype['type']){
				case 'base':
				$array_base[$fieldtype['id']] = $fieldtype['subject'];
				break;
				case 'extend':
				$array_extend[$fieldtype['id']] = $fieldtype['subject'];
				break;
				case 'expert':
				$array_expert[$fieldtype['id']] = $fieldtype['subject'];
				break;
				case 'declarer':
				$array_declarer[$fieldtype['id']] = $fieldtype['subject'];
				break;
				case 'unit':
				$array_units[$fieldtype['id']] = $fieldtype['subject'];
				break;
			}
		}
		if($fieldtype['type']=='base')
			$array_base[$fieldtype['id']] = $fieldtype['subject'];
		else if($fieldtype['type']=='extend')
			$array_extend[$fieldtype['id']] = $fieldtype['subject'];

		$htmlStr = '<option value="" class="grey">请选择字段类型</option>'."\r\n";
		$htmlStr .= '<option disabled class="alert alert-info">基础字段</option>'."\r\n";
		foreach($array_base as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		$htmlStr .= '<option disabled class="alert alert-info">预设字段</option>'."\r\n";

		foreach($array_extend as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		$htmlStr .= '<option disabled class="alert alert-info">专家字段</option>'."\r\n";

		foreach($array_expert as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		$htmlStr .= '<option disabled class="alert alert-info">项目负责人字段</option>'."\r\n";

		foreach($array_declarer as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		$htmlStr .= '<option disabled class="alert alert-info">申报单位字段</option>'."\r\n";

		foreach($array_units as $id=>$subject)
		{
			$htmlStr .= '<option value="'.$id.'" ';
			if($select == $id) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$subject."</option>\r\n";
		}
		return $htmlStr;
	}

	function getLayoutList()
	{
		return ['linear'=>'列表','table'=>'表格','tab'=>'标签'];
	}

/**
 * 取得单位类别列表
 */
function company_category($sel=0)
{
	$array = array();
	return getSelectFromArray($array,$sel);
}

/**
 * 打印最后执行的sql语句
 */
function ds()
{
	$sql = DB::getQueryLog();
	$query = end($sql);
	dd($query);
}

/**
 * 获取配置信息并生成select选项
 * @param int $select 选中的选项
 * @param string $value	配置类型
 * @param string $type select|checkbox|array select:返回select的html；array：返回原生数组
 * @param string $inputName 表单名
 * @return string
 */
function getConfig($select=0,$value='nation',$type='select',$inputName='')
{
	if($select!=0 && strstr($select,',')) $select = explode(',',$select);
	$expert = config::get($value);
	$htmlStr = '';
	if($type=='select'){
		foreach($expert as $k=>$v){
			$htmlStr .= '<option value="'.$k.'" ';
			if($select == $k) $htmlStr .= 'selected="selected" ';
			$htmlStr .= '>'.$v."</option>\r\n";
		}
		return $htmlStr;
	}elseif($type=='checkbox'){
		foreach($expert as $k=>$v){
			$htmlStr .= '<label class="checkbox-inline"><input type="checkbox" name="'.$inputName.'" value="'.$k.'"';
			if(is_array($select)){
				foreach($select as $se){
					if($se == $k) $htmlStr .= 'checked="checked" ';
				}
			}else{
				if($select == $k) $htmlStr .= 'checked="checked" ';
			}
			$htmlStr .= '> '.$v."</label>\r\n";
		}
		return $htmlStr;
	}elseif($type=='text'){
		if(empty($select)){
			return '';
		}else{
			if(is_array($select)){
				$str = '';
				foreach($select as $se){
					$str[] = $expert[$se];
				}
				return implode(',',$str);
			}else{
				return $expert[$select];
			}
		}
	}else{
		return empty($select) ? $expert : $expert[$select];
	}
}

/**
 * 获取专家类别html
 */
function getExpertType($select=0)
{
	if($select!=0) $select = explode(',',$select);
	$expert = config::get('expert_type');
	$htmlStr = '';
	foreach($expert as $k=>$v){
		$htmlStr .= '<label class="checkbox-inline"><input type="checkbox" name="baseinfo[expert_type][]" value="'.$k.'"';
		if(is_array($select)){
			foreach($select as $se){
				if($se == $k) $htmlStr .= 'checked="checked" ';
			}
		}else{
			if($select == $k) $htmlStr .= 'checked="checked" ';
		}
		$htmlStr .= '> '.$v."</label>\r\n";
	}
	return $htmlStr;
}

function getTypesListForMemo($sel='')
{
	$array = array('0'=>'————请选择项目类型————',
		'1'=>'863计划',
		'2'=>'国家科技支撑计划',
		'3'=>'973计划',
		'4'=>'科技基础条件平台建设计划',
		'16'=>'———政策引导类科技计划———计划',
		'5'=>'星火计划',
		'6'=>'农业科技成果转化资金',
		'7'=>'火炬计划',
		'8'=>'科技型中小企业技术创新基金',
		'9'=>'国家重点新产品',
		'10'=>'国际科技合作计划',
		'11'=>'国家软科学研究计划',
		'12'=>'科技兴贸行动专项',
		'13'=>'科技基础性工作专项',
		'14'=>'重大专项',
		'16'=>'国家科技富民强县专项',
		'15'=>'其他');
	$htmlStr = '';
	foreach($array as $key => $val)
	{
		$htmlStr .= '<option value="'.$key.'" ';
		if($val === $sel) $htmlStr .= 'selected="selected"';
		$htmlStr .= '/>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

function getTypeSubject($id=0)
{
	$array = array('0'=>'————请选择项目类型————',
		'1'=>'863计划',
		'2'=>'国家科技支撑计划',
		'3'=>'973计划',
		'4'=>'科技基础条件平台建设计划',
		'16'=>'———政策引导类科技计划———计划',
		'5'=>'星火计划',
		'6'=>'农业科技成果转化资金',
		'7'=>'火炬计划',
		'8'=>'科技型中小企业技术创新基金',
		'9'=>'国家重点新产品',
		'10'=>'国际科技合作计划',
		'11'=>'国家软科学研究计划',
		'12'=>'科技兴贸行动专项',
		'13'=>'科技基础性工作专项',
		'14'=>'重大专项',
		'16'=>'国家科技富民强县专项',
		'15'=>'其他');
	if($id) return $array[$id];
	else return '未知类型';
}
/*
研究对象
 */
function getProjectType($select = 0)
{
    return getProjectSource($select);
}
//奖励类别
function getAwardType($select = 0)
{
    $typeIds[8] = '科学技术奖';
    $typeIds[9] = '软科学成果奖';
    $typeIds[10] = '青年科技创新团队';
    $typeIds[11] = '外部奖励';
    return getSelectFromArray($typeIds,$select,false);
}
/*
项目来源
 */
function getProjectSource($select = 0)
{
    $catIds = get_select_data('cat');
    unset($catIds[896]);
    return getSelectFromArray($catIds,$select,false);
}

/**
 * 研究对象
 */
function  getResearchObject($select = 0)
{
    return getSelectFromArray(getBookMapList(5, true), $select, false);
}
/*
指南领域
 */
function getGuideField($select = -1)
{
	$result = Guide::where('is_show',1)->where('is_lastnode',1)->whereNotNull('guide_field')->orderBy('id')->get();
	$htmlStr = '';
	foreach($result as $guide){
		$htmlStr .= '<option value="'.$guide->id.'" ';
		if($select == $guide->id) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$guide->guide_field."</option>\r\n";
	}
	return $htmlStr;
}

function time2string($second){
	$day = floor($second/(3600*24));
	$second = $second%(3600*24);//除去整天之后剩余的时间
	$hour = floor($second/3600);
	$second = $second%3600;//除去整小时之后剩余的时间 
	$minute = floor($second/60);
	$second = $second%60;//除去整分钟之后剩余的时间 
	//返回字符串
	//return $day.'天'.$hour.'小时'.$minute.'分'.$second.'秒';
	return $day.'天'.$hour.'小时'.$minute.'分';
}

function getMask($str,$len=4,$start=-3,$mask='*')
{
	$size = strlen($str);
	$size = $size > $len ? $len : ceil($size*0.6);
	$str = substr_replace($str,str_pad('',$size,$mask),$start,$len);
	return $str;
}

/**
 * 验证专利号
 * @param array $noArray
 */
function checkPatentSn($noArray=array())
{
	return true;
	//专利号验证规则参见：http://www.cnblogs.com/unruledboy/archive/2007/04/03/PatenVerify.html
	foreach($noArray as $no){
		//1.专利号总长度为12位
		if(strlen($no)!=12){
			return false;
		}
		//2.是否以“ZL”开头
		if(strtoupper(substr($no,0,2))!='ZL'){
			return false;
		}
		//3.“ZL”后8位是否为纯数字
		if(!is_numeric(substr($no,2,8))){
			return false;
		}
		//4.“ZL”后第3位数字是否为1,2,3其中的一个
		if(substr($no,4,1)!='1' && substr($no,4,1)!='2' && substr($no,4,1)!='3'){
			return false;
		}
		//5.8位数字后是否为小数点
		if(substr($no,10,1)!='.'){
			return false;
		}
		//6.验证最后一位校验码
		//校验码这样计算：用前8位数依次与2、3、4、5、6、7、8、9相乘,
		//将它们的乘积相加所得之和再用11除后的余数，当余数为10时，用X表示
		$code = $no[2]*2+$no[3]*3+$no[4]*4+$no[5]*5+$no[6]*6+$no[7]*7+$no[8]*8+$no[9]*9;
		$code = $code%11;
		$code = $code==10 ? 'X' : $code;
		if(strtoupper($no[11])!=$code){
			return false;
		}

	}
	return true;
}

/**
 * 登录密码加密函数
 * @param string $password
 * @return string
 */
function encode_password($password='')
{
	$str = substr(md5(uniqid()),-3);
	return md5($password.$str).":".$str;
}

/**
 * 获取选中的选项的中文
 * @param $str
 * @param $configName
 * @return string
 */
function getSelectText($str,$configName)
{
	if(strstr($str,',')){
		$arr = explode(',',$str);
		$str = $arr[0];
	}
	return getConfig($str,$configName,'text');
}

/**
 * 检查组织机构代码
 * @param string $text
 * @return string
 */
function checkCorporationCode($text=''){
	if(strlen($text) == 18){
		if(!isCreditCode($text)) return false;
	}else{
		if(!isUnitCode($text)) return false;
	}
	return true;
}

/**
 * 获取文章列表
 * @param string $attr 文章属性
 * @param int $count	调用数量
 * @param string $orderby 排序字段
 * @param string $orderway 排序方式
 * @param string $field 查询字段
 * @return mixed
 */
function getArcList($attr='',$count=5,$orderby='sortrank',$orderway='desc',$field='id,title')
{
	$field = explode(',',$field);
	if(!empty($attr)){
		$result = CmsArchives::orderby($orderby,$orderway)->where('channel',1)->where('flag','like',"%{$attr}%")->limit($count)->get($field);
	}else{
		$result = CmsArchives::orderby($orderby,$orderway)->where('channel',1)->limit($count)->get($field);
	}
	return $result;
}

/**
 * 获取专题列表
 * @param string $attr 文章属性
 * @param int $count	调用数量
 * @param string $orderby 排序字段
 * @param string $orderway 排序方式
 * @param string $field 查询字段
 * @return mixed
 */
function getSpecialList($attr='',$count=5,$orderby='sortrank',$orderway='desc',$field='id,title')
{
	$field = explode(',',$field);
	if(!empty($attr)){
		$result = CmsArchives::orderby($orderby,$orderway)->where('channel',-1)->where('flag','like',"%{$attr}%")->limit($count)->get($field);
	}else{
		$result = CmsArchives::orderby($orderby,$orderway)->where('channel',-1)->limit($count)->get($field);
	}
	return $result;
}

/**
 * 获取区域名称
 * @param $code
 * @param $level
 */
function getReginName($code,$level=1)
{
	$region_name = Region::where('code',$code)->value('region_name');
	if($level==1){
		return $region_name;
	}
	if($level==2){
		$code = substr($code, 0, 2).'0000';
		$province_name =  Region::where('code',$code)->value('region_name');
		$region_name = str_replace($province_name,'',$region_name);		//替换掉省份名称
	}elseif($level==3){
		$code = substr($code, 0, 4).'00';
		$city_name = Region::where('code',$code)->value('region_name');
		$region_name = str_replace($city_name,'',$region_name);			//1.先替换掉城市名称

		$code = substr($code, 0, 2).'0000';
		$province_name =  Region::where('code',$code)->value('region_name');
		$region_name = str_replace($province_name,'',$region_name);		//2.再替换掉省份名称

	}
	return $region_name;
}

//根据指定学科代码获取联动关系 type:学科类型，返回类型：$return_type string|array
function resolveSubjectByCode($code='',$type=1,$return_type='array')
{
	$result=[];
	switch($type)
	{
		case 1:
		$result[] = substr($code,0,3);
		$result[] = substr($code,0,6);
		$result[] = substr($code,0,8);
		break;
		case 2:
		
		break;
		case 3:
		$result[] = substr($code,0,2);
		$result[] = substr($code,0,3);
		$result[] = substr($code,0,4);
		break;
		default:
		break;
	}

	$result = array_unique($result);

	if($return_type=='string')
		return implode(',',$result);
	else return $result;
}

/**
 * 区域代码转为区域名称
 */
function areaCode2Name($code)
{
	if(empty($code)){
		return;
	}
	return Region::where('code',$code)->value('region_name');
}

/**
 * 获取顶级栏目列表
 */
function getTopChannel()
{
	$acttypes  = CmsArctype::where('topid',0)->orderBy('sortrank', 'asc')->orderBy('id', 'asc')->get()->toArray();
	return $acttypes;
}

function getRoleList($is_all=false)
{
	$rolelist = array();
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	$hash = 'role_list_'.input::getInput("session.userid");
	if(!$rolelist = $cache->getCache($hash)){
		$roles = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"))->selectRoles();
		while($role = $roles->getObject()){
			$rolelist[$role->getRoleId()] = $role->getRoleName();
		}
		$cache->setCache($hash,$rolelist);
	}
	$htmlStr = '';
	if($is_all)
	{
		foreach($rolelist as $key => $val){
			$htmlStr .= '<li><a href="'.site_url('manager/role/role_id/'.$key).'" title="点击进入用户中心" class="show"> <i class="ace-icon glyphicon glyphicon-transfer"></i> '.$val.'</a> </li>';		
		}
	}
	else
	{
		foreach($rolelist as $key => $val){
			if($key != input::getInput("session.userlevel"))
				$htmlStr .= '<li><a href="javascript:void(0)" title="点击切换角色" class="show" onClick="return showConfirm(\'是否切换角色？\',\''.site_url('manager/role/role_id/'.$key).'\')"> <i class="ace-icon glyphicon glyphicon-transfer"></i> '.$val.'</a> </li>';		
		}
	}
	$htmlStr && $htmlStr .= '<li class="divider"></li>';
	return $htmlStr;
}

function changeRole($role_id)
{
	if(!$role_id) return false;
	$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
	if($user->hasRole($role_id)){
		//$user->setUserGroupId($role_id);
		//$user->save();
		$_SESSION['userlevel'] 	=  $role_id;
		//if(input::session('username')=='lijx') dd($user->getOfficeId());
		$_SESSION['groupname'] 	=  sf::getModel("UserGroups",$role_id)->getUserGroupName();
		$_SESSION['roleuserid'] =  $user->getRoleUserId($role_id);
		$_SESSION['office_id']  =  $user->getOfficeId();
		$_SESSION['group_id']   =  $user->getGroupId();
		return true;
	}else return false;
}


function is_article_manager()
{
	//文章管理员用户组id
	$group_id=[
		1,  //超级管理员
		6,  //科技创新部科长
//		7,  //科室工作人员
		8,  //科室科长
		38,  //办公室主任
		];

		$session_level = $_SESSION['userlevel'];
		if(!$session_level) return false;

		if(in_array($session_level,$group_id))
			return true;
		return false;
	}

	function is_leader()
	{
		$group_id=[
		1,  //超级管理员
		6,  //科技创新部科长
        8,  //科室科长
        14,  //副局长
		36,  //科室副科长
		37,  //办公室副主任
		38,  //办公室主任
		39,  //局长
		42,  //所长
		43,  //副所长
		47,  //局知识产权科科长
		48,  //局成果科科长
		49,  //局高新科科长
		50,  //局农业科科长
		52,  //中心党委书记
		55,  //所技术科科长
		56,  //所办公室主任
		59,  //所信息科主任
		];

		$session_level = $_SESSION['userlevel'];
		if(!$session_level) return false;

		if(in_array($session_level,$group_id))
			return true;
		return false;
	}

/**
 * 是否是办公室主任
 * @return bool
 */
function is_office_leader()
{
	$group_id=[
		1,  //超级管理员
		38,  //办公室主任
        56, //所办公室主任
        ];

        $session_level = $_SESSION['userlevel'];
        if(!$session_level) return false;

        if(in_array($session_level,$group_id))
        	return true;
        return false;
    }

// 系统工作人员下拉菜单
    function worker()
    {
    	$html = '';
    	$personnels = PersonnelModel::all();
    	$office = array_filter(PersonnelModel::lists('office')->unique()->toArray());
    	foreach ($office as $key => $value) {
    		$html .= '<option class="NO" disabled value="'.$value.'">'.$value.'</option><br>';
    		foreach ($personnels as $personnel) {
    			if($personnel->office == $value){
    				$html .= '<option value="'.$personnel->id.'">'.$personnel->name.'（'.$personnel->position.'）</option><br>';
    			}
    		}
    	}
    	return $html;
    }
// 个人文档柜--文档分类下拉菜单
    function get_doctype_option($user_id,$type_id)
    {
    	$html = '';
    	$d = DocumentType::where('user_id',$user_id)->get();
    	foreach ($d as $key => $value) {
    		if($type_id == $value->id){
    			$html .= '<option selected value="'.$value->id.'">'.$value->name.'</option><br>';
    		}else{
    			$html .= '<option value="'.$value->id.'">'.$value->name.'</option><br>';
    		}
    	}
    	return $html;
    }

    function page_loading($subject,$url='',$message='')
    {
    	$message = $message?'/message/'.base64_encode($message):'';
    	$url = site_url('common/page_loading/url/'.base64_encode($url)).$message;
    	return '<a target="_black" href="'.$url.'" >'.$subject.'</a>';
    }

/**
 * 获取广告位
 * @param int $id 广告位id
 * @param bool $img 只获取图片src地址
 */
function getAds($id=0,$imgsrc=false)
{
	$content = '';	//广告内容
	if($id==0) $content = '';
	$ad = M('Ads');
	$ads = $ad->selectAll("id = {$id}");
	if($ads->getTotal()==0) $content = '';
	$obj = $ads->getObject();
	if($obj->getIsLimit()){
		//限制了广告位投放时间
		$now_time = time();
		$start_at = strtotime($obj->getStartAt());
		$end_at = strtotime($obj->getEndAt());
		if($now_time>=$start_at && $now_time<$end_at){
			$content = $obj->getContent();
		}else{
			$content = $obj->getexpContent();
		}
	}else{
		$content = $obj->getContent();
	}
	if($imgsrc){
		//匹配图片src
		preg_match_all('/<img.*?src="(.*?)".*?>/is',$content,$array);
		$content = $array[1][0];
	}
	return $content;
}

/**
 * 获取友情链接
 * @return mixed
 */
function getFlinks()
{
	$flinksModel = M('Flinks');
	$flinks = $flinksModel->selectAll("`is_show` = 1 and `logo` is not null","ORDER BY `orders` ASC,`id` ASC",7);
	return $flinks;
}

function console_log($data)
{
	if(is_array($data) || is_object($data))
	{
		echo("<script>console.log('".json_encode($data)."');</script>");
	}
	else
	{
		echo("<script>console.log('".$data."');</script>");
	}
}

/**
 * 导出excel
 * @param $head array 表头数据
 * @param $body  array 主体数据
 * @param $name string 表格标签名
 * @param $filename string 导出的文件名
 *
 * Demo:
 * 		$head = ['姓名','身份证','性别','地址'];
		$body = [
					['周杰伦','500242197605300015','男','四川省成都市双流县成都信息工程大学航空港校区'],
					['蔡依林','500242197605300014','女','成都市武侯区成科西路3号'],
				];
		excel_out($head,$body,'测试','test');
 */
function excel_out($head,$body,$name,$filename,$widths=[],$superHead='',$savePath='')
{
    $objPHPExcel = new \PHPExcel();
    $startRow = 1;  //从第一行开始
    //超级表头（合并单元格，显示一行字）
    if($superHead){
        $j = $i = 'A';
        $count = count($head)-1;
        for($index=0;$index<$count;$index++){
            $j++;
        }
        $objPHPExcel->setActiveSheetIndex(0)->mergeCells($i.$startRow.':'.$j.$startRow);   //合并
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue($i.$startRow, $superHead);
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow)->applyFromArray(
            array(
                'font' => array (
                    'bold' => true
                ),
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
                )
            )
        );
        $objPHPExcel->getActiveSheet()->getStyle($i.$startRow.':'.$j.$startRow)->getFont()->setSize(20);
        $startRow++;
    }

    //表头
    $i='A';
    foreach($head as $item){
        $objPHPExcel->setActiveSheetIndex(0)->setCellValue($i.$startRow, $item);
        $i++;
    }
    $count = count($head);

    //主体数据
    foreach($body as $key=>$value){
        $startRow++;
        $index='A';
        for($i=0;$i<$count;$i++){
            $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getNumberFormat()->setFormatCode("@"); //文本格式
            if(strlen($value[$i]) < 10 && floatval($value[$i])==$value[$i]){
                //数字
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue($index.$startRow,$value[$i]);
            }else{
                //文本
                $objPHPExcel->getActiveSheet()->setCellValueExplicit($index.$startRow,$value[$i],PHPExcel_Cell_DataType::TYPE_STRING);
            }


            $index++;
        }
    }

    //设置宽度
    if($widths){
        $index='A';
        for($i=0;$i<$count;$i++){
            $objPHPExcel->getActiveSheet()->getColumnDimension($index)->setWidth($widths[$i]);
            $index++;
        }
    }

    $objPHPExcel->getActiveSheet()->setTitle($name);
    $objPHPExcel->setActiveSheetIndex(0);
    if(!$savePath){
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$filename.'.xls"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        ob_end_clean();
        $objWriter->save('php://output');
        exit;
    }else{
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter->save($savePath);
    }
}

function excel_in($excelPath,$startRow=1,$maxColumn=50)
{
    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;

    $cacheSettings = array();
    \PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $inputFileType = PHPExcel_IOFactory::identify($excelPath);
    $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
    $objReader->setReadDataOnly(true);
    $objPHPExcel = $objReader->load($excelPath);
    $sheet = $objPHPExcel->getSheet(0);
    $highestRow = $sheet->getHighestRow();           //取得总行数
    $highestColumn = $sheet->getHighestColumn();     //取得总列数
    $objWorksheet = $objPHPExcel->getActiveSheet();
    $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);//总列数
    if ($highestColumnIndex > $maxColumn) $highestColumnIndex = $maxColumn;
    $bodys = [];
    $line = 0;
    for ($row = $startRow; $row <= $highestRow; $row++) {
        $line++;
        $tmp = [];
        for ($col = 0; $col < $highestColumnIndex; $col++) {
            $value = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
            if(strlen($value)>0) $tmp[$col+1] = $value;
        }
        if($tmp) $bodys[$line] = $tmp;

    }
    return $bodys;
}

function excel_in_all($excelPath,$startRow=1,$maxColumn=50)
{
//    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_to_phpTemp;

    $cacheSettings = array();
    \PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $inputFileType = PHPExcel_IOFactory::identify($excelPath);
    $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
    $objReader->setReadDataOnly(true);
    $objPHPExcel = $objReader->load($excelPath);
    $sheets = $objPHPExcel->getAllSheets();
    $bodys = [];
    foreach ($sheets as $pIndex=>$sheet){
        $sheet = $objPHPExcel->getSheet($pIndex);
        $highestRow = $sheet->getHighestRow();           //取得总行数
        $highestColumn = $sheet->getHighestColumn();     //取得总列数
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);//总列数
        if ($highestColumnIndex > $maxColumn) $highestColumnIndex = $maxColumn;
        $line = 0;
        for ($row = $startRow; $row <= $highestRow; $row++) {
            $line++;
            $tmp = [];
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $value = (string)$sheet->getCellByColumnAndRow($col, $row)->getValue();
                if(substr($value,0,1)=='='){
                    //包含公式
                    $value = (string)$sheet->getCellByColumnAndRow($col, $row)->getCalculatedValue();
                }
                if(strlen($value)>0) $tmp[$col+1] = $value;
            }
            if($tmp) $bodys[$pIndex][$line] = $tmp;

        }
    }

    return $bodys;
}

function getExcelSheets($excelPath)
{
//    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_in_memory_gzip;
    $cacheMethod = \PHPExcel_CachedObjectStorageFactory::cache_to_phpTemp;

    $cacheSettings = array();
    \PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);
    $inputFileType = PHPExcel_IOFactory::identify($excelPath);
    $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
    $objReader->setReadDataOnly(true);
    $objPHPExcel = $objReader->load($excelPath);
    $sheets = $objPHPExcel->getAllSheets();
    $datas = [];
    foreach ($sheets as $pIndex=>$sheet){
        $datas[$pIndex] = $sheet->getTitle();
    }

    return $datas;
}

function getLastSql()
{
	dd(base64_decode($_SESSION['last_sql']));
}

function getSelectGroup($select='',$f=false)
{
	$htmlStr = '';
	$result = get_select_data('group');
	foreach((array)$result as $key => $val){
		if(!$f && !in_array($key,(array)input::getInput("session.group_id"))) continue;
		$htmlStr .= '<option value="'.$key.'" ';
		if($select == $key) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

function getSelectGroupArray($f=false)
{
	$data = [];
	$result = get_select_data('group',1);
	foreach((array)$result as $key => $val){
		if(!$f && !in_array($key,(array)input::getInput("session.group_id"))) continue;
		$data[$key] = $val;
	}
	return $data;
}

function getStateOption($select=0)
{
	$options = array('2'=>'等待申报单位审核','3'=>'申报单位退回','5'=>'等待推荐部门审核','6'=>'推荐部门退回','9'=>'等待科知局受理','10'=>'科知局已受理','12'=>'科知局退回','25'=>'网络评审结束','28'=>'大会评审结束','29'=>'项目不授奖','30'=>'项目已授奖');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

function getGroupList($select='')
{
	$addWhere = "`year` = '".config::get('current_declare_year',date("Y"))."' AND group_id IN ('".implode("','",input::getInput("session.office_id"))."') ";
	$result = sf::getModel("ProjectAwardGroup")->selectAll($addWhere);
	$htmlStr = '';
	while($nation = $result->getObject()){
		$htmlStr .= '<option value="'.$nation->getSubject().'" ';
		if($select == $nation->getSubject()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$nation->getSubject()."(".$nation->getNote().")</option>\r\n";
	}
	return $htmlStr;
}

function getGroupListPropertyright($select='')
{
	$addWhere = "`year` = '".config::get('current_declare_year',date("Y"))."' AND group_id IN ('".implode("','",input::getInput("session.office_id"))."') ";
	$result = sf::getModel("PropertyrightGroup")->selectAll($addWhere);
	$htmlStr = '';
	while($nation = $result->getObject()){
		$htmlStr .= '<option value="'.$nation->getSubject().'" ';
		if($select == $nation->getSubject()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$nation->getSubject()."(".$nation->getNote().")</option>\r\n";
	}
	return $htmlStr;
}

function getSelectCglx($select='')
{
	$result = sf::getModel("guideline")->selectAll();
	$htmlStr = '';
	while($nation = $result->getObject()){
		$htmlStr .= '<option value="'.$nation->getCode().'" ';
		if($select == $nation->getCode()) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$nation->getSubject()."</option>\r\n";
	}
	return $htmlStr;
}

function getSelectXylx($select='')
{
	$array = array('J'=>'经济效益为主','S'=>'社会效益为主','Z'=>'综合类型');
	$htmlStr = '';
	foreach($array as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($select == $key) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

/**
 * 弹出消息(适用于在子窗口弹出消息并刷新父窗口)
 * @param $str
 */
function showMsg($str)
{
	echo "<script>alert('{$str}');parent.document.location.reload();</script>";
	exit();
}

/**
 * 生成按钮
 */
function btn()
{
	$agrs = func_get_args();
	$type = array_shift($agrs);
	return call_user_func_array(['\\App\\Lib\\Button',$type],$agrs);	
}

/**
 * 51:四川省
 */ 
function get_region_data($code='51',$level=0,$fields=['code','region_name'])
{
	$options = array();
	$addWhere = '';
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$options = $cache->getCache($code.$level)){

		$result = Region::where('code','like',$code.'%');
		$level && $result = $result->where('level',$level);
		$result = $result->get();

		foreach($result as $option)
			$options[$option->$fields[0]] = $option->$fields[1];
		$cache->setCache($code.$level,$options);
	}
	return $options;
}

function isSuper()
{
	if(in_array(input::getInput("session.userlevel"),[1,6])) return true;
	else return false;
}

function is_serialized($data)
{
	if(is_array($data)) return false;
	$data = trim( $data );
	if ( 'N;' == $data )
	return true;
	if ( !preg_match( '/^([adObis]):/', $data, $badions ) )
		return false;
	switch ( $badions[1] ) {
		case 'a' :
		case 'O' :
		case 's' :
		if ( preg_match( "/^{$badions[1]}:[0-9]+:.*[;}]\$/s", $data ) )
		return true;
		break;
		case 'b' :
		case 'i' :
		case 'd' :
		if ( preg_match( "/^{$badions[1]}:[0-9.E-]+;\$/", $data ) )
		return true;
		break;
	}
	return false;
}

function unserializeAll($str)
{
	$arr = [];
	if(is_serialized($str)){
		$arr = unserialize($str);
		if(is_array($arr)){
			foreach ($arr as $k=>&$v){
				$v = unserializeAll($v);
			}
		}
		return $arr;

	}else return $str;
}

function makeTemplate($id)
{
	$form = sf::getModel('Forms',$id);
	if($form->isNew() || !$form->getTemplate() || !$form->getConfig()) return;
    $filename = APPPATH.'view/declare/pdefault/'.$form->getTemplate();    //写入的文件名
    $content =  $form->getConfig();    //写入的内容
    sf::getLib('MakeTemplate')->make($id,$filename,$content);
}

/**
 * 判断是否是ajax请求
 * @return bool
 */
function isAjax()
{
	return file_get_contents('php://input') || (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && (strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'));
}

/**
 * 取得表单列表
 */
function get_form_option()
{
	$options = array();
	$cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
	if(!$options = $cache->getCache('get_form_option')){
		$result = sf::getModel("Forms")->selectAll('is_show > 0','ORDER BY id ASC');
		while($option = $result->getObject())
			$options[$option->getId()] = $option->getSubject();
		$cache->setCache('get_form_option',$options);
	}
	return $options;
}

/*
	选中的选项加黑
 */
function getCheckedStr($array,$selstr,$br=false,$checkType='value')
{
    $flag = "□";
    $str = "";
    foreach ($array as $key => $item){
        if(is_array($selstr)){
            if(in_array($item,$selstr)!==false){
                $flag='☑';
            }else{
                $flag='□';
            }
            if($br){
                if($key == count($array)-1){
                    $str.=$flag.$item;
                }else{
                    $str.=$flag.$item.'<br/>';
                }
            }else{
                $str.=$flag.$item.'  ';
            }
        }else{
            $flag='□';
            if($checkType=='value' && $item==$selstr) $flag='☑';
            if($checkType=='key' && $key===$selstr) $flag='☑';
            if($br){
                if($key == count($array)-1){
                    $str.=$flag.$item;
                }else{
                    $str.=$flag.$item.'<br/>';
                }
            }else{
                $str.=$flag.$item.'  ';
            }
        }
    }
    return $str;
}

/**
 * 精确加法
 */
function mathAdd($a,$b,$scale = '2') {
	return bcadd($a,$b,$scale);
}

/**
 * 精确减法
 */
function mathSub($a,$b,$scale = '2') {
	return bcsub($a,$b,$scale);
}
/**
 * 精确乘法
 */
function mathMul($a,$b,$scale = '2') {
	return bcmul($a,$b,$scale);
}
/**
 * 精确除法
 */
function mathDiv($a,$b,$scale = '2') {
	return bcdiv($a,$b,$scale);
}
function methodSelect(){
	return getSelectFromArray([
        '1'=>'填写中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已审核'],'',false);
}
function patentSelect($statement=''){
	return getSelectFromArray([
		'1'=>'填写中',
		'4'=>'待单位/部门助理审核',
		'5'=>'单位/部门助理退回',
		'6'=>'待单位/部门管理员审核',
		'7'=>'单位/部门管理员退回',
		'9'=>'待科技处助理审核',
		'12'=>'科技处助理退回',
		'15'=>'待科技处管理员审核',
		'17'=>'科技处管理员退回',
		'20'=>'已审核'],$statement,false);
}
function paperSelect($statement=''){
	return getSelectFromArray([
        '1'=>'填写中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已审核'],$statement,false);
}
function societySelect($statement=''){
	return getSelectFromArray([
        '1'=>'填写中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已审核',
        '29'=>'已提名',
        '30'=>'已提名（增补）',
        '31'=>'已免除'],$statement,false);
}
function workSelect(){
	return getSelectFromArray([
        '1'=>'填写中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已审核'],'',false);
}
function teamSelect($statement=''){
	return getSelectFromArray([
        '1'=>'填写中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已审核'],$statement,false);
}
function expertSelect($statement=''){
    return getSelectFromArray([
        '1'=>'资料修改中',
        '4'=>'待单位/部门助理审核',
        '5'=>'单位/部门助理退回',
        '6'=>'待单位/部门管理员审核',
        '7'=>'单位/部门管理员退回',
        '9'=>'待科技处助理审核',
        '12'=>'科技处助理退回',
        '15'=>'待科技处管理员审核',
        '17'=>'科技处管理员退回',
        '20'=>'已认证'],$statement,false);
}
//判断是否是上级单位（二级单位）
function isParent($user_id='')
{
	$corporation = sf::getModel("Corporations")->selectByUserId($user_id);
	if(in_array($corporation->getLevel(),['0','1'])){
		return true;
	}else{
		return false;
	}
}
//单位级别
function getUnitLevelName()
{
	if(!input::getInput("session.unitlevel")) return;
	if(input::getInput("session.unitlevel")==1){
		return '（一级公司）';
	}
	if(input::getInput("session.unitlevel")==2){
		return '（二级公司）';
	}
	return '（三级公司）';
}
//申报书状态
function projectStateSelect($select=0){
	$options = array('1'=>'项目填写中','2'=>'待项目负责人审核','3'=>'项目负责人退回','4'=>'待单位/部门助理审核','5'=>'单位/部门助理退回','6'=>'待单位/部门管理员审核','7'=>'单位/部门管理员退回','8'=>'待科技处/财务处助理审核','9'=>'待科技处助理审核','11'=>'待财务处助理审核','12'=>'科技处助理退回','13'=>'财务处助理退回','14'=>'待科技处/财务处管理员审核','15'=>'待科技处管理员审核','16'=>'待财务处管理员审核','17'=>'科技处管理员退回','18'=>'财务处管理员退回','20'=>'已审核','21'=>'项目已分组','22'=>'已分配专家','23'=>'评审完毕','25'=>'待所领导审批','26'=>'所领导退回','27'=>'已受理','28'=>'未立项','29'=>'已立项','2901'=>'已立项(执行中)','2902'=>'已立项(已逾期)','30'=>'已结题','40'=>'已终止','99'=>'已过期');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//奖励状态
function awardStateSelect($select=0){
	$options = array('1'=>'项目填写中','2'=>'待项目负责人审核','3'=>'项目负责人退回','4'=>'待单位/部门助理审核','5'=>'单位/部门助理退回','6'=>'待单位/部门管理员审核','7'=>'单位/部门管理员退回','9'=>'待科技处助理审核','12'=>'科技处助理退回','15'=>'待科技处管理员审核','17'=>'科技处管理员退回','20'=>'已受理','21'=>'项目已分组','22'=>'已分配专家','23'=>'评审完毕','27'=>'待授奖','28'=>'不授奖','29'=>'已授奖','40'=>'已终止','99'=>'已过期');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//档案状态
function archiveStateSelect($select=0){
	$options = array(1=>'入库中',10=>'在库','20'=>'已借出');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//任务书书状态
function getTaskOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待项目负责人审核','3'=>'项目负责人退回','4'=>'待单位/部门助理审核','5'=>'单位/部门助理退回','6'=>'待单位/部门管理员审核','7'=>'单位/部门管理员退回','8'=>'待科技处/财务处助理审核','9'=>'待科技处助理审核','11'=>'待财务处助理审核','12'=>'科技处助理退回','13'=>'财务处助理退回','14'=>'待科技处/财务处管理员审核','15'=>'待科技处管理员审核','16'=>'待财务处管理员审核','17'=>'科技处管理员退回','18'=>'财务处管理员退回','20'=>'已审核');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//验收书状态
function getCompleOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待部门审核','3'=>'部门退回','9'=>'待单位审核','12'=>'单位退回','10'=>'同意验收');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//进步奖状态
function getAwardOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待申报单位审核','3'=>'申报单位退回','5'=>'待上级单位审核','6'=>'上级单位退回','9'=>'待科技创新部受理','10'=>'科技创新部已受理');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

function getPlanOption($select=0)
{
    $plans = sf::getModel("ProjectPlans")->selectAll("first_id = '9B581D83-B2BA-7337-0678-A875B156B111' and statement = 10");
    while($plan = $plans->getObject()){
        $htmlStr .= '<option value="'.$plan->getPlanId().'" ';
        if($plan->getPlanId() == $select) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$plan->getSubject()."</option>\r\n";
    }
    return $htmlStr;
}
//项目负责人等级
function getDeclareLevel($select=0)
{
	$options = array('A'=>'A 级别（正高级）','B'=>'B 级别（副高级）','C'=>'C 级别（中级）','D'=>'D 级别（初级）','E'=>'E 级别（其他）');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

function moneyMark($money)
{
	switch($money)
	{
		case 'device':
		return '设备费';
		break;
		case 'device1':
		return '购置设备费';
		break;
		case 'device2':
		return '设备试制、改造、租赁费';
		break;
		case 'device3':
		return '设备改造与租赁费';
		break;
		case 'material':
		return '材料费';
		break;
		case 'test':
		return '测试化验加工费';
		break;
		case 'power':
		return '燃料动力费';
		break;
		case 'travel':
		return '差旅费';
		break;
		case 'conference':
		return '会议费';
		break;
		case 'cooperation':
		return '差旅费/会议费/国际合作与交流费';
		break;
		case 'reference':
		return '出版/文献/信息传播/知识产权事务费';
		break;
		case 'manpower':
		return '劳务费';
		break;
		case 'consultancy':
		return '专家咨询费';
		break;
		case 'other':
		return '其他支出';
		break;
		case 'indirect':
		return '间接费用';
		break;
		case 'manage':
		return '管理费（税费）';
		break;
		case 'performance':
		return '绩效支出';
		break;
		default:
		return '未知项';
		break;	
	}
}
//经费支出
function getPaymentOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位退回','10'=>'已审定');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//项目类别
function getFlowType($select=0)
{
	$options = array('1'=>'集团审批类','2'=>'二级备案类','3'=>'三级备案类');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//项目外协申请状态
function getProjectOutOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位审核退回','4'=>'待科技创新部审核','5'=>'科技创新部退回','10'=>'已审批');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//项目结题申请状态
function getProjectApplyOption($select=0)
{
	$options = array('1'=>'填写中','2'=>'待单位审核','3'=>'单位审核退回','4'=>'待科技创新部审核','5'=>'科技创新部退回','10'=>'已审批');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}

//调整申请状态
function getProjectChangeOption($select='')
{
	$options = array('1'=>'填写中','2'=>'待承担单位审核','3'=>'承担单位退回','4'=>'待上级单位推荐','5'=>'上级单位退回','6'=>'待科技创新部受理','7'=>'科技创新部退回','10'=>'科技创新部已受理');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if(($key == $select) && $key != '') $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//研究性质
function getResearchType($select=0)
{
	$options = array('1'=>'创新引导课题','2'=>'技术攻关课题','3'=>'工程服务课题','4'=>'成果推广课题');
	foreach($options as $key => $val){
		$htmlStr .= '<option value="'.$key.'" ';
		if($key == $select) $htmlStr .= 'selected="selected" ';
		$htmlStr .= '>'.$val."</option>\r\n";
	}
	return $htmlStr;
}
//根据条件计算项目数
function selectNumBySql($data=[],$year){
	$addWhere = "SELECT * FROM `projects` WHERE declare_year = ".$year." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
	foreach ($data as $key => $value) {
		if(is_array($value)){
			$addWhere .= " and ".$key." in ('".implode("','",$value)."')";
		}else{
			$addWhere .= " and ".$key." = '".$value."' ";
		}
	}
	$db = sf::getLib("db");
	$query = $db->query($addWhere);
	return $db->num_rows($query);
}
//根据条件算经费
function selectMoneyBySql($data=[],$year){
	$addWhere = "SELECT sum(total_money) as money FROM `projects` WHERE declare_year = ".$year." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
	foreach ($data as $key => $value) {
		if(is_array($value)){
			$addWhere .= " and ".$key." in ('".implode("','",$value)."')";
		}else{
			$addWhere .= " and ".$key." = '".$value."' ";
		}
	}
	$db = sf::getLib("db");
	$query = $db->query($addWhere);
	return round($db->fetch_array($query)['money'],2);
}
//根据条件计算项目数
function selectNumByBetween($data=[],$start,$end){
	$addWhere = "SELECT * FROM `projects` WHERE declare_year between ".$start." and ".$end." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
	foreach ($data as $key => $value) {
		if(is_array($value)){
			$addWhere .= " and ".$key." in ('".implode("','",$value)."')";
		}else{
			$addWhere .= " and ".$key." = '".$value."' ";
		}
	}
	$db = sf::getLib("db");
	$query = $db->query($addWhere);
	return $db->num_rows($query);
}
//根据条件算经费
function selectMoneyByBetween($data=[],$start,$end){
	$addWhere = "SELECT sum(total_money) as money FROM `projects` WHERE declare_year between ".$start." and ".$end." and corporation_id not in ('D9F89BA0-7526-5119-8FD8-1215FA2850F9')";
	foreach ($data as $key => $value) {
		if(is_array($value)){
			$addWhere .= " and ".$key." in ('".implode("','",$value)."')";
		}else{
			$addWhere .= " and ".$key." = '".$value."' ";
		}
	}
	$db = sf::getLib("db");
	$query = $db->query($addWhere);
	return round($db->fetch_array($query)['money'],2);
}
//删除项目名称为空的项目
function delEmpPro(){
	$db = sf::getLib("db");
	$sql = "DELETE FROM projects WHERE subject is NULL";
	$db->query($sql);
}
function itemType($item_type){
	switch ($item_type) {
		case 'must_sjbg':
		return '企业年度财务审计报告';
		break;
		case 'must_cxxcl':
		return '项目创新性突出的证明材料';
		break;
		case 'must_jjxycl':
		return '项目经济效益或社会效益证明材料';
		break;
		case 'must_zscqzm':
		return '核心知识产权证明';
		break;
		case 'must_yyqk':
		return '应用情况和效益佐证材料';
		break;
		case 'must_pzwj':
		return '国家法律法规要求审批的批准文件';
		break;
		case 'must_cns':
		return '承诺书签字页';
		break;
		case 'must_hxzscq':
		return '核心知识产权证明';
		break;
		case 'must_hzb':
		return '完成人合作关系说明及情况汇总表';
		break;
		case 'must_fblw':
		return '公开发表的代表性论文及专著';
		break;
		case 'must_yylw':
		return '他人引用的代表性论文、专著';
		break;
		case 'must_zscqzm':
		return '知识产权证明';
		break;
		case 'must_hjzs':
		return '重要获奖证书';
		break;
		case 'must_zp':
		return '候选人近期标准照片及工作照片';
		break;
		case 'other_gfcl':
		return '其他主要知识产权和标准规范证明材料';
		break;
		case 'other_kgpj':
		return '客观评价材料';
		break;
		case 'other_hygx':
		return '完成单位行业贡献的证明材料';
		break;
		case 'other_xsgx':
		return '完成人学术贡献的证明材料';
		break;
		default:
		return "其他附件";
		break;
	}
}

function getBudgetMoneyByType($budget = []){
	$data['zxjf'] = $data['zcjf'] = $data['total'] = 0;
	foreach ($budget as $key => $value) {
		if($value['from'] == '专项经费'){
			$data['zxjf'] += $value['money'];
		}
		if($value['from'] == '自筹经费'){
			$data['zcjf'] += $value['money'];
		}
		$data['total'] += $value['money'];
	}
	return $data;
}


if (! function_exists('removeQuotation')) {
	function removeQuotation($char)
	{
		if(is_array($char)){
			foreach($char as $key => $val){
				$char[$key] = removeQuotation($val);
			}
		}

		$char = str_replace('\"','”',$char);
		$char = str_replace("\'",'’',$char);
		$char = str_replace('\\','|',$char);
		return $char;
	}
}


/**
 * 是否为测试人员
 */
function isTester()
{
    if(input::session("roleuserid") == '963049C2-EB30-1A8B-22A3-2CD688C40449') return true;
    else return false;
}

/**
 * 取得执行器列表
 */
function get_worker_option()
{
    $options = array();
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
    if(!$options = $cache->getCache('get_worker_option')){
        $result = sf::getModel("EngineWorkers")->selectAll('','ORDER BY id DESC');
        while($option = $result->getObject())
            $options[$option->getId()] = $option->getSubject();
        $cache->setCache('get_worker_option',$options);
    }
    return $options;
}

//获取二维码
function getQRcode($msg='')
{
    require_once WEBROOT.'/../vendor/mpdf/mpdf/qrcode/qrcode.class.php';
    $err = 'L';
    if (!in_array($err, array('L', 'M', 'Q', 'H'))) $err = 'L';

    $qrcode = new QRcode(utf8_encode($msg), $err);
    $qrcode->disableBorder();
    $imageData = base64_encode($qrcode->displayPNG());
    $src = 'data:image/png;base64,' . $imageData;
    return $src;
}


/**
 * 获取安全token
 */
function get_security_token($str)
{
    return md5(trim($str).date("YmdH").'20190923');
}

/**
 * 加密解密函数
 */
function myencrypt($string,$operation='E')
{
    $key = md5('MY-'.date("Ymd"));
    $key_length=strlen($key);
    $string=$operation=='D'?base64_decode(str_replace('-','/',$string)):substr(md5($string.$key),0,8).$string;
    $string_length=strlen($string);
    $rndkey=$box=array();
    $result='';
    for($i=0;$i<=255;$i++){
        $rndkey[$i]=ord($key[$i%$key_length]);
        $box[$i]=$i;
    }
    for($j=$i=0;$i<256;$i++){
        $j=($j+$box[$i]+$rndkey[$i])%256;
        $tmp=$box[$i];
        $box[$i]=$box[$j];
        $box[$j]=$tmp;
    }
    for($a=$j=$i=0;$i<$string_length;$i++){
        $a=($a+1)%256;
        $j=($j+$box[$a])%256;
        $tmp=$box[$a];
        $box[$a]=$box[$j];
        $box[$j]=$tmp;
        $result.=chr(ord($string[$i])^($box[($box[$a]+$box[$j])%256]));
    }
    if($operation=='D'){
        if(substr($result,0,8)==substr(md5(substr($result,8).$key),0,8)){
            return substr($result,8);
        }else{
            return'';
        }
    }else{
        return str_replace(['/','='],['-',''],base64_encode($result));
    }
}

/**
 * 根据父ID获取下级
 */
function getOptionByParent($parent_id,$subject)
{
    if(!$parent_id){
        return "<option value=''>==请选择==</option>";
    }
    $cats = sf::getModel('Categorys','','work')->selectAll('parent_id = '.$parent_id);
    $htmlStr = '';
    while ($cat = $cats->getObject()) {
        $sub = $cat->getSubject();
        $htmlStr .= '<option value="'.$sub.'" ';
        if($subject == $sub) $htmlStr .= 'selected="selected" ';
        $htmlStr .= '>'.$sub."</option>\r\n";
    }
    return $htmlStr;
}

//分类是否有子分类
function hasSon($code,$type = 1)
{
    if(!in_array($type,array('1','4'))) $type = 1;
    $db = sf::getLib("Db");
    $row = $db->fetch_first("SELECT count(*) as num FROM `subjects` WHERE `type` = '".$type."' and `code` like '".$code."%' ");
    if($row['num'] > 1) return true;
    else return false;
}

function getAreaCodeName($code,$type=1)
{
    $subject = sf::getModel('Subjects')->selectByCode($code,$type);
    return $subject->isNew() ? '' : $subject->getSubject();
}

/**
 * 开启经费支出填写
 * @param $projectId
 */
function openPayment($projectId){
    $db = sf::getLib("db");
    $project = sf::getModel("Projects")->selectByProjectId($projectId);
    //申报书
    $type = 'apply';
    if($project->isSupportProject() && $project->getStateForTask()==10){
        //资助类项目从任务书中抓取
        $type = 'task';
    }
    $query = $db->query("SELECT device1,device2,material,test,power,cooperation,reference,manpower,consultancy,other,indirect,datas FROM `project_moneys` WHERE project_id = '{$projectId}' and type = '{$type}'");
    $items = $db->fetch_array($query);
    $sql = "DELETE FROM project_money WHERE project_id = '{$projectId}' and type = '{$type}'";
    $db->exec($sql);
    foreach ($items as $mark => $value){
        $item = json_decode($value,true);
        if($mark=='datas'){
            $value = json_decode($value,true);
            $item = $value['performance'];
            $mark = 'performance';
        }
        $projectMoney = sf::getModel("ProjectMoney")->selectByProjectIdAndMark($projectId,$mark,$type);
        $projectMoney->setMarkSubject(moneyMark($mark));
        $projectMoney->setApplySpecial($item['cz']);
        $projectMoney->setApplyOwn($item['zc']);
        $projectMoney->setApplyOther($item['qt']);
        $projectMoney->setApplyTotal($item['hj']);
        $projectMoney->save();
    }
}

/**
 * 更新经费支出
 * @param $projectId
 */
function updatePayment($projectId){
    $db = sf::getLib("db");
    $project = sf::getModel("Projects")->selectByProjectId($projectId);
    //申报书
    $type = 'apply';
    if($project->isSupportProject() && $project->getStateForTask()==10){
        //资助类项目从任务书中抓取
        $type = 'task';
    }
    $query = $db->query("SELECT device1,device2,material,test,power,cooperation,reference,manpower,consultancy,other,indirect FROM `project_moneys` WHERE project_id = '{$projectId}' and type = '{$type}'");
    $items = $db->fetch_array($query);
    foreach ($items as $mark => $value){
        $item = json_decode($value,true);
        $projectMoney = sf::getModel("ProjectMoney")->selectByProjectIdAndMark($projectId,$mark,$type);
        $projectMoney->setMarkSubject(moneyMark($mark));
        $projectMoney->setApplySpecial($item['cz']);
        $projectMoney->setApplyOwn($item['zc']);
        $projectMoney->setApplyOther($item['qt']);
        $projectMoney->setApplyTotal($item['hj']);
        $projectMoney->save();
    }
}

/**
 * 添加OA待办提醒
 */
function addOAMsg($userId,$roleId,$title='',$url='')
{
    $title = $title ?: '你有一个待办事项需要处理';
    $count = sf::getModel('OaMessages')->selectAll("is_read = 1 and user_id = '{$userId}'")->getTotal();
    if($count==0){
        $user = sf::getModel('Users')->selectByUserId($userId);
        $oaMessage = sf::getModel('OaMessages')->selectByMsgId('');
        $oaMessage->setUserId($user->getUserId());
        $oaMessage->setUserName($user->getUserName());
        $oaMessage->setUrl($url?:'manager/index');
        $oaMessage->setRoleId($roleId);
        $oaMessage->setTitle($title);
        $oaMessage->setSenderId(input::getInput("session.roleuserid"));
        $oaMessage->setSenderName(input::getInput("session.nickname"));
        $oaMessage->setCreatedAt(date('Y-m-d H:i:s'));
        $oaMessage->save();
        $oaMessage->doSend();
    }
}

/**
 * 发送OA待办提醒
 */
function sendOAMsg($msgId)
{
    $oaMessage = sf::getModel('OaMessages')->selectByMsgId($msgId);
    if($oaMessage->isNew()) return false;
    $oaMessage->doSend();
}

/**
 * 推送消息至单位管理员
 * @param $msg 消息标题
 * @param $url 跳转的地址
 * @param string $companyId 单位id
 */
function pushMsgToCompanyManager($msg,$url,$companyId='')
{
    $users = false;
    if($companyId){
        $users = sf::getModel('Corporations')->selectByUserId($companyId)->users();
    }else{
        switch (input::session('userlevel')){
            case 2:
                $researcher = sf::getModel('Declarers')->selectByUserId(input::session('roleuserid'));
                $users = sf::getModel('Corporations')->selectByUserId($researcher->getCorporationId())->users();
                break;
            case 3:
                $users = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'))->users();
                break;
            case 10:
                $users = sf::getModel('Experts')->selectByUserId(input::session('roleuserid'))->users();
                break;
            default:
                $users = sf::getModel('Corporations')->selectByUserId($companyId)->users();
                break;
        }
    }
    if($users){
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(3);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

/**
 * 推送消息至科研人员
 * @param $msg 标题
 * @param $url 跳转的地址
 * @param $userId 科研人员id
 */
function pushMsgToResearcher($msg,$url,$userId)
{
    $reseacher = sf::getModel('Declarers')->selectByUserId($userId);
    if(!$reseacher->isNew()){
        $users = $reseacher->users();
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(2);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

/**
 * 推送消息至科创部管理员
 * @param $msg 标题
 * @param $url 跳转的地址
 * @param $userId 科研人员id
 */
function pushMsgToOffice($msg,$url)
{
    $managers = sf::getModel('Managers')->selectAll("user_group_id = 6");
    while($manager = $managers->getObject()){
        $users = $manager->users();
        while($user = $users->getObject()){
            if(!$user->getEmpno()) continue;
            $empno = $user->getEmpno();
            $roleId = $user->getRoleId(6);
            pushOAMsg([$empno],$url,$roleId,$msg);
        }
    }
}

function isImage($filename)
{
    $exts = ['.gif','.jpg','.jpeg','.png','.bmp']; //定义检查的图片类型
    $ext = strrchr($filename,'.');
    return in_array($ext,$exts);
}

function isPdf($filename)
{
    $exts = ['.pdf']; //定义检查的文件类型
    $ext = strrchr($filename,'.');
    return in_array($ext,$exts);
}

function img_path($filePath)
{
    return isImage($filePath) ? site_path('images/index.html').'?url='.site_path($filePath) : site_path($filePath);
}

function getIframeUrl()
{
    $scheme = $_SERVER['HTTPS']=='on' ? 'https://' : 'http://';
    if($_SERVER['QUERY_STRING']) $_SERVER['QUERY_STRING'] = '?'.$_SERVER['QUERY_STRING'];
    $baseUrl = $scheme.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'/open_model/frame'.$_SERVER['QUERY_STRING'];
    return $baseUrl;
}

/**
 * 高企编号验证
 */
function isHightechNo($no){
    $exp = "/^GR20\d{2}51\d{6}$/";
    if(preg_match($exp,trim($no))) return true;
    else return false;
}

/**
 * 瞪羚企业备案编号验证
 */
function isGazelleNo($no){
    $exp = "/^SCDL\d{8}$/";
    if(preg_match($exp,trim($no))) return true;
    else return false;
}

function getCompanys()
{
    $companys = sf::getModel('Corporations')->selectAll("1 ");
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getSubject();
    }
    return $datas;
}

/**
 * 获取二级单位
 * @return array
 */
function getSecondCompanys()
{
    $companys = sf::getModel('Corporations')->selectAll("user_id like '9B581D83-B2BA-7337-0678%' and level = 2 ","order by orders asc");
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getSubject();
    }
    //加上物流公司、信息公司、北斗公司
    $companys = sf::getModel('Corporations')->selectAll("subject IN ('物流公司','信息公司','北斗公司') and level = 3","order by field(subject,'物流公司','信息公司','北斗公司')");
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getSubject();
    }
    return $datas;
}

/**
 * 获取二级单位（除了指定的单位）
 * @return array
 */
function getSecondCompanysWithoutCompany($companyId)
{
    $companys = getSecondCompanys();
    $datas = [];
    foreach ($companys as $cId=>$company){
        if($cId===$companyId) continue;
        $datas[$cId] = $company;
    }
    return $datas;
}


/**
 * 获取二级单位
 * @return array
 */
function getSecondCompanysShort()
{
    $companys = sf::getModel('Corporations')->selectAll("user_id like '9B581D83-B2BA-7337-0678%' and level = 2 and short_name is not null","order by orders asc");
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getShortName();
    }
    //加上物流公司、信息公司、北斗公司
    $companys = sf::getModel('Corporations')->selectAll("subject IN ('物流公司','信息公司','北斗公司') and level = 3","order by field(subject,'物流公司','信息公司','北斗公司')");
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getSubject();
    }
    return $datas;
}


/**
 * 获取独立法人单位
 * @return array
 */
function getLegalCompanys()
{
    $addwhere = "full_name is not null or full_name != ''";
    $companys = sf::getModel('Corporations')->selectAll($addwhere,"order by orders asc");
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getFullName();
    }
    return $datas;
}


/**
 * 获取独立法人单位
 * @return array
 */
function getLegalCorporations()
{
    $addwhere = "`property` = 'company'";
    $companys = sf::getModel('Corporations')->selectAll($addwhere,"order by `level` asc,orders asc");
    $datas = [];
    while($company = $companys->getObject()){
        $datas[$company->getUserId()] = $company->getFullName();
    }
    if(input::session('username')=='test2009'){
        $datas['9B581D83-B2BA-7337-0678-050000000000'] = '合作伙伴';
    }
    return $datas;
}


/**
 * 是否是独立法人单位
 * @return array
 */
function isLegalCompany($companyId='')
{
    if(!$companyId) $companyId = input::session('roleuserid');
    $companys = getLegalCorporations();
    return $companys[$companyId] ? true : false;
}

function getDepartments($companyId='')
{
    $addwhere = "1 ";
    if($companyId){
        $addwhere.=" and corporation_id = '{$companyId}'";
    }
    $departments = sf::getModel('Departments')->selectAll($addwhere);
    $datas = [];
    while($department = $departments->getObject()){
        $datas[$department->getUserId()] = $department->getSubject();
//        $datas[$department->getUserId()] = $department->getSubject()."(".$department->getCorporationName().")";
    }
    return $datas;
}

function getResearchers($companyId='')
{
    $addwhere = "is_lock = 0 ";
    if($companyId) $addwhere.=" and `corporation_id` = '{$companyId}'";
    $declarers = sf::getModel('Declarers')->selectAll($addwhere);
    $datas = [];
    while($declarer = $declarers->getObject()){
        $datas[$declarer->getUserId()] = $declarer->getPersonname();
    }
    return $datas;
}

function getBudgetSubjects()
{
    $subjects = sf::getModel('BudgetSubjects')->selectAll('',"order by code asc,id asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getId()] = $subject->getSubject();
    }
    return $datas;
}

function getPerformanceSubjects()
{
    $subjects = sf::getModel('PerformanceSubjects')->selectAll('',"order by code asc,id asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getId()] = $subject->getSubject();
    }
    return $datas;
}

function getPurchaseSubjects()
{
    $subjects = sf::getModel('PurchaseSubjects')->selectAll('',"order by code asc,id asc");
    $datas = [];
    while($subject = $subjects->getObject()){
        $datas[$subject->getId()] = $subject->getSubject();
    }
    return $datas;
}

function rsa_decode($str)
{
    //私钥
    $private_key = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    $hex_encrypt_data = trim($str); //十六进制数据
    $encrypt_data = pack("H*", $hex_encrypt_data);//对十六进制数据进行转换
    openssl_private_decrypt($encrypt_data, $decrypt_data, $private_key);
    return $decrypt_data;
}

/**
 * 将IP加入黑名单
 */
function addBlacklist($ip,$time='')
{
    $cache = sf::getLib("cache",config::get('cache_dir','cache'),1800);
    $options = $cache->getCache('ip_black_list');
    $options[$ip] = $time;
    $cache->setCache('ip_black_list',$options);
//	Log::write('['.date("Y-m-d H:i:s").'] '.$ip.' IS LOCK.','error');
    return $options;
}


function setBaseUrl()
{
    $scheme = $_SERVER['HTTPS']=='on' ? 'https://' : 'http://';
    $url = $scheme.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'].'/public/';
    Config::set('base_url',$url);
}

function getFunds($key='id',$withCode=true,$version='self',$levelCount=2)
{
    $funds = sf::getModel('Funds')->selectAll("level = 1 and is_show = 1 and version='{$version}'","order by orders asc,id asc");
    $datas = [];
    $i=0;
    while($fund = $funds->getObject()){
        $numberStr = getBigNumber($i+1).'、';
        if($fund->getVersion()=='gxb') $numberStr = ($i+1).'.';
        $datas[$key=='id'?$fund->getId():$fund->getCode()] = $withCode ? ($fund->getSubject().'-'.$fund->getCode()) : $numberStr.$fund->getSubject();
        if($levelCount>1){
            $sonindexs = $fund->getSonIndexs();
            $j=0;
            while($sonindex = $sonindexs->getObject()){
                $numberStr='';
                if($sonindexs->getTotal()>1) $numberStr = '（'.getBigNumber($j+1).'）';
                $datas[$key=='id'?$sonindex->getId():$sonindex->getCode()] = $withCode ? ($sonindex->getSubject().'-'.$sonindex->getCode()) : $numberStr.$sonindex->getSubject();
                if($levelCount>2){
                    $grandsonindexs = $sonindex->getSonIndexs();
                    $k=0;
                    while($grandsonindex = $grandsonindexs->getObject()){
                        $numberStr = ($k+1).'.';
                        $datas[$key=='id'?$grandsonindex->getId():$grandsonindex->getCode()] = $withCode ? ($grandsonindex->getSubject().'-'.$grandsonindex->getCode()) : $numberStr.$grandsonindex->getSubject();
                        if($levelCount>3){
                            $fourthindexs = $grandsonindex->getSonIndexs();
                            $m=0;
                            while($fourthindex = $fourthindexs->getObject()){
                                $numberStr = '（'.($m+1).'）';
                                $datas[$key=='id'?$fourthindex->getId():$fourthindex->getCode()] = $withCode ? ($fourthindex->getSubject().'-'.$fourthindex->getCode()) : $numberStr.$fourthindex->getSubject();
                                $m++;
                            }
                        }
                        $k++;
                    }
                }
                $j++;
            }
        }
        $i++;
    }
    return $datas;
}

function getBigNumber($num)
{
    $arr = [1=>'一',2=>'二',3=>'三',4=>'四',5=>'五',6=>'六',7=>'七',8=>'八',9=>'九',10=>'十'];
    return $arr[$num];
}

function getSecondNumber($num)
{
    $arr = [1=>'一',2=>'二',3=>'三',4=>'四',5=>'五',6=>'六',7=>'七',8=>'八',9=>'九',10=>'十'];
    return $arr[$num];
}


function getFundName($indexCode,$version='kjt')
{
    $fund = sf::getModel('Funds')->selectByCode($indexCode,$version);
    return $fund->isNew() ? false : $fund->getSubject();
}

/**
 * 获得附件
 * @param bool $f
 * @return null
 */
function getAttachments($itemId,$itemType = 'attachement', $limit = 0, $orders='order by no asc,id asc')
{
    $addwhere = "`item_id` = '{$itemId}'";
    if(strstr($itemType,'%')!==false){
        $addwhere.=" and item_type like '{$itemType}'";
    }else{
        $addwhere.=" and item_type = '{$itemType}'";
    }
    return sf::getModel("Filemanager")->selectAll($addwhere, $orders, $limit);
}

function getTopAndSecondIndexs($ruleId=1)
{
    $indexs = sf::getModel('EvaluateRuleItems')->selectAll("level = 1 and rule_id = '{$ruleId}'","order by sort asc,id asc");
    $datas = [];
    while($index = $indexs->getObject()){
        $datas[$index->getId()] = $index->getSubject().'-'.$index->getCode();
        $sonindexs = $index->getSonIndexs();
        while($sonindex = $sonindexs->getObject()){
            $datas[$sonindex->getId()] = '——'.$sonindex->getSubject().'-'.$sonindex->getCode();
        }
    }
    return $datas;
}

function getTopAndSecondFunds()
{
    $funds = sf::getModel('Funds')->selectAll("level = 1","order by orders asc,id asc");
    $datas = [];
    while($fund = $funds->getObject()){
        $datas[$fund->getId()] = $fund->getSubject().'-'.$fund->getCode();
        $sonindexs = $fund->getSonIndexs();
        while($sonindex = $sonindexs->getObject()){
            $datas[$sonindex->getId()] = '——'.$sonindex->getSubject().'-'.$sonindex->getCode();
            $thirdindexs = $sonindex->getSonIndexs();
            while($thirdindex = $thirdindexs->getObject()){
                $datas[$thirdindex->getId()] = '————'.$thirdindex->getSubject().'-'.$thirdindex->getCode();
            }
        }
    }
    return $datas;
}


/**
 * 合并单元格
 * 示例：将A1至A5的单元格合并：mergeCells($objPHPExcel,'A1:A5')
 * @param $objPHPExcel
 * @param $cells
 * @return void
 */
function mergeCells($objPHPExcel,$cells)
{
    if(is_array($cells)){
        foreach ($cells as $cell){
            $cellArr = explode(':',$cell);
            $objPHPExcel->getActiveSheet()->mergeCells($cell);
            $objPHPExcel->getActiveSheet()->getStyle($cellArr[0])->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
            //水平居左
//            $objPHPExcel->getActiveSheet()->getStyle($cellArr[0])->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
        }
    }else{
        $cellArr = explode(':',$cells);
        $objPHPExcel->getActiveSheet()->mergeCells($cells);
        $objPHPExcel->getActiveSheet()->getStyle($cellArr[0])->getAlignment()->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
    }
}


/**
 * 加粗单元格
 * 示例：将A1单元格加粗：boldCells($objPHPExcel,['A1'])
 * @param $objPHPExcel
 * @param $cells
 * @return void
 */
function boldCells($objPHPExcel,$cells)
{
    if(is_array($cells)){
        foreach ($cells as $cell){
            $objPHPExcel->getActiveSheet()->getStyle($cell)->getFont()->setBold(true);
        }
    }else{
        $objPHPExcel->getActiveSheet()->getStyle($cells)->getFont()->setBold(true);
    }
}


/**
 * 居左单元格
 * 示例：将A1单元格居左：leftCells($objPHPExcel,['A1'])
 * @param $objPHPExcel
 * @param $cells
 * @return void
 */
function leftCells($objPHPExcel,$cells)
{
    if(is_array($cells)){
        foreach ($cells as $cell){
            $objPHPExcel->getActiveSheet()->getStyle($cell)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
        }
    }else{
        $objPHPExcel->getActiveSheet()->getStyle($cells)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
    }
}

/**
 * 创建新进程处理
 * @param $path  控制器/方法 例如：cmd/test
 */
function createNewProcess($path)
{
    require_once APPPATH.'/Plugin/process/Process.php';
    require_once APPPATH.'/Plugin/process/ProcessUtils.php';
    require_once APPPATH.'/Plugin/process/Pipes/PipesInterface.php';
    require_once APPPATH.'/Plugin/process/Pipes/AbstractPipes.php';
    require_once APPPATH.'/Plugin/process/Pipes/UnixPipes.php';
    require_once APPPATH.'/Plugin/process/Pipes/WindowsPipes.php';
    $process = new \Symfony\Component\Process\Process(['php',WEBROOT.'/../cmder/CLI',$path]);
    $process->setOptions(['create_new_console' => true]);
    $process->start();
}

function _cName($name)
{
    if(!strpos($name,"_")) return ucfirst($name);
    $name = explode("_",$name);
    $str = '';
    foreach($name as $val)
    {
        $str .= ucfirst($val);
    }
    return $str;
}

/**
 * PDF2PNG
 * @param $pdf  待处理的PDF文件
 * @param $path 待保存的图片路径
 * @param $page 待导出的页面 -1为全部 0为第一页 1为第二页
 * @return      保存好的图片路径和文件名
 */
function pdf2png($pdf, $path, $page = -1)
{
    if (!extension_loaded('imagick')) {
        return false;
    }
    if (!file_exists($pdf)) {
        return false;
    }
    $pdf = str_replace('\\','/',$pdf);
    $pathinfo = pathinfo($pdf);
    $im = new \Imagick();
    $im->setResolution(120, 120);
    $im->setCompressionQuality(100);
    if ($page == -1)
        $im->readImage($pdf);
    else
        $im->readImage($pdf . "[" . $page . "]");
    $pngs = [];
    foreach ($im as $key => $val) {
        $filename = $pathinfo['filename'].'_'.$key;
        $val->setImageFormat('png');
        $filepath = $path . "/" . $filename . '.png';
        if ($val->writeImage($filepath) == true) {
            $pngs[] = $filename . '.png';
        }
    }
    return $pngs;
}

function clearMenuCache() {
    // 打开目录
    $dirPath = APPPATH . "../cache/menus";
    $dirHandle = opendir($dirPath);
    if (!$dirHandle) return;

    // 循环读取目录中的文件和子目录
    while (($file = readdir($dirHandle)) !== false) {
        // 忽略当前目录(.)和上级目录(..)
        if ($file != '.' && $file != '..') {
            // 构建文件路径
            $filePath = $dirPath . '/' . $file;
            // 删除文件
            unlink($filePath);
        }
    }

    // 关闭目录句柄
    closedir($dirHandle);
}

function getParentCompanyId($companyId,$level=0,$fill0=false)
{
    if($level){
        switch ($level){
            case 1:
                $companyId = substr($companyId,0,24);
            case 2:
                $companyId =  substr($companyId,0,26);
            case 3:
                $companyId =  substr($companyId,0,28);
            case 4:
                $companyId =  substr($companyId,0,30);
        }
        return $fill0 ? str_pad($companyId,36,'0',STR_PAD_RIGHT) : $companyId;
    }

    return rtrimZero($companyId);
}

function rtrimZero($companyId)
{
    $companyId = rtrim($companyId,'0');
    $arr = explode('-',$companyId);
    if(strlen($arr[4])%2){
        //奇数补零
        $companyId = $companyId.'0';
    }
    return $companyId;
}


/**
 * 取得管理责任人下拉列表
 */
function getManagerList($sel = 0) {
    $options = array();
    $addWhere = "id>7 and user_group_id like '%5%' and is_lock = 0 ";
    $result = sf::getModel("Managers")->selectAll($addWhere, 'ORDER BY id ASC');
    while ($option = $result->getObject()) {
        $options[$option->getUserId()] = $option->getUserUsername();
    }
    return getSelectFromArray($options, $sel, false);
}


/**
 * 取得管理员
 */
function getManagers() {
    $options = array();
    $addWhere = "id>7 and is_lock = 0 and (CONCAT(',', user_group_id, ',') LIKE '%,5,%' or CONCAT(',', user_group_id, ',') LIKE '%,6,%')";
    $result = sf::getModel("Managers")->selectAll($addWhere, 'ORDER BY id ASC');
    while ($option = $result->getObject()) {
        $options[$option->getUserId()] = $option->getUserUsername();
    }
    return $options;
}


/**
 * 取得财务处人员
 */
function getFinances() {
    $options = array();
    $addWhere = "id>7 and is_lock = 0 and (CONCAT(',', user_group_id, ',') LIKE '%,7,%' or CONCAT(',', user_group_id, ',') LIKE '%,8,%')";
    $result = sf::getModel("Managers")->selectAll($addWhere, 'ORDER BY id ASC');
    while ($option = $result->getObject()) {
        $options[$option->getUserId()] = $option->getUserUsername();
    }
    return $options;
}

/**
 * 计算两个日期相差多少天
 * @param $startDate 开始日期
 * @param $endDate  结束日期
 * @return string
 * @throws Exception
 */
function getDateDiff($startDate,$endDate)
{
    // 将字符串转换为DateTime对象
    $datetimeStart = new DateTime($startDate);
    $datetimeEnd = new DateTime($endDate);

    // 计算日期差值并获取天数
    $interval = $datetimeStart->diff($datetimeEnd);
    $daysDiff = $interval->format('%a');

    return $daysDiff;
}

function isSpecialCompany($companyName)
{
    return in_array($companyName,['物流公司','信息公司','北斗公司']);
}


function filterStr($str)
{
    if(mb_detect_encoding($str, 'UTF-8', true)===false){
        $str = iconv('ISO-8859-1','UTF-8',$str);
    }
    $str = trim($str);
    $str = str_replace(' ','',$str);
    $str = str_replace('　','',$str);
    $str = str_replace('  ','',$str);
    $str = str_replace(' ','',$str);
    $str = str_replace('\'','‘',$str);
    $str = str_replace("\r\n",'',$str);
    $str = str_replace("\n\r",'',$str);
    $str = str_replace("\r",'',$str);
    $str = str_replace("\n",'',$str);
    return $str;
}

function makeBillId($name='ZC'){
    $str = date('Ymd') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    return $name.$str;
}

/**
 * 返回数组的维度
 * @param  [type] $arr [description]
 * @return [type]      [description]
 */
function arrayLevel($arr)
{
    $al = array(0);
    aL($arr, $al);
    return max($al);
}

function aL($arr, &$al, $level = 0)
{
    if (is_array($arr)) {
        $level++;
        $al[] = $level;
        foreach ($arr as $v) {
            aL($v, $al, $level);
        }
    }
}


/**
 * 将姓名为两个字的中间加上空格
 * @param $str
 * @return string
 */
function filterName($str)
{
    if(strlen($str)==6){
        $str = substr($str,0,3).'　'.substr($str,3,3);
    }
    return $str;
}


function getBonusList($bonusType)
{
    $bonus = sf::getModel('Bonus')->selectAll("bonus_type = '{$bonusType}'","order by id desc");
    $datas = [];
    while($bonu = $bonus->getObject()){
        $datas[$bonu->getBonusId()] = $bonu->getSubject();
    }
    return $datas;
}

function getEvaluateList()
{
    $rules = sf::getModel('EvaluateRules')->selectAll("","order by id desc");
    $datas = [];
    while($rule = $rules->getObject()){
        $datas[$rule->getRuleId()] = $rule->getSubject();
    }
    return $datas;
}

function getCpu()
{
    //注意服务器上php不能禁用exec函数
    $cpu = '未知';
    if(strpos(PHP_OS,"WIN")!==false){
        //windows
        $cmd = 'wmic cpu get name';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $cpu = $output[0];
        }else{
            $cpu = $output[1];
        }
    }else{
        $cmd = 'lscpu';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $cpu = $output[0];
        }else{
            foreach ($output as $line) {
                if (strpos($line, 'Model name:') === 0) {
                    $cpu = trim(substr($line, strlen('Model name:')));
                    break;
                }
            }
        }
    }
    return $cpu;
}

function getServerMemorySize() {
    $memorySize = '未知';
    if(strpos(PHP_OS,"WIN")!==false){
        //windows
        $cmd = 'wmic ComputerSystem get TotalPhysicalMemory';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $memorySize = $output[0];
        }else{
            $memorySize = $output[1];
            $memorySize = round($memorySize / 1024 / 1024 / 1024, 2);
            $memorySize .= ' GB';
        }
    }else{
        $cmd = 'free -m';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $memorySize = $output[0];
        }else{
            $mem_array = explode(" ", $output[1]);
            $mem_array = array_filter($mem_array);
            $mem_array = array_values($mem_array);
            if (count($mem_array) > 1) {
                $total_memory = $mem_array[1]; // 总内存（以MB为单位）
            }
            $memorySize = round($total_memory / 1024, 2).' GB';
        }
    }

    return $memorySize;
}

function getServerMemoryUsed() {
    $memoryUsedSize = '未知';
    if(strpos(PHP_OS,"WIN")!==false){
        //windows
        $cmd = 'wmic os get TotalVisibleMemorySize,FreePhysicalMemory';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $memorySize = $output[0];
        }else{
            //多个空格转为一个空格
            $phymem = preg_replace ( "/\s(?=\s)/","\\1",$output[1]);
            $phymem_array = explode(' ',$phymem);
            $freephymem = round($phymem_array[0]/1024/1024,2);
            $totalphymem = round($phymem_array[1]/1024/1024,2);
            $usephymem = $totalphymem - $freephymem;
            $memoryUsedSize = round($usephymem,2).'GB';
        }
    }else{
        $cmd = 'free -m';
        exec($cmd, $output, $return_val);
        if($return_val!=0){
            $memorySize = $output[0];
        }else{
            $mem_array = explode(" ", $output[1]);
            $mem_array = array_filter($mem_array);
            $mem_array = array_values($mem_array);
            if (count($mem_array) > 1) {
                $total_memory = $mem_array[2]; // 已使用内存（以MB为单位）
            }
            $memoryUsedSize = round($total_memory / 1024, 2).' GB';
        }
    }

    return $memoryUsedSize;
}


function isFruitShow($code)
{
    $setting = sf::getModel('FruitSettings')->selectByCode($code);
    return (!$setting->isNew() && $setting->getIsShow()==1) ? true : false;
}


function array_contains($array, $sub_array) {
    $intersection = array_intersect($array, $sub_array);
    return count($intersection) === count($sub_array);
}

function getFormId($postdata)
{
    $keys = array_keys($postdata);
    $csrfKey = false;
    foreach ($keys as $k){
        if(strpos($k,'__csrf')===0) {
            $csrfKey = $k;
            break;
        }
    }
    if($csrfKey===false) return $csrfKey;
    $csrfKey = explode('_',$csrfKey);
    return array_pop($csrfKey);
}

/**
 * 将模板中表单里的select、checkbox等值存入session，用于后端验证
 * @param $html
 * @return void
 */
function setSelectValue2Session($html)
{
    if($_SESSION['__form']) unset($_SESSION['__form']);
    $pattern = '/\<form.*?\>(.*?)\<\/form\>/s';
    preg_match_all($pattern,$html,$formMatches);
    foreach ($formMatches[1] as $fk=>$fv){
        //查找表单里是否有select
        $inputNames = [];
        $pattern = '/\<select.*? name="(.*?)".*?\>(.*?)\<\/select\>/s';
        preg_match_all($pattern,$fv,$matches);
        foreach ($matches[1] as $k=>$v){
            $inputNames[$v] = [];
            if(empty($matches[2][$k])) continue;
            $optionPattern = '/\<option.*? value="(.*?)".*?\>.*?\<\/option\>/s';
            preg_match_all($optionPattern,$matches[2][$k],$optionMatches);
            $inputNames[$v] = array_filter($optionMatches[1]);
        }
        //查找表单里是否有checkbox
        $fv = preg_replace('/name="(.*?)" type="checkbox"/','type="checkbox" name="$1"',$fv);
        $pattern = '/\<input.*?type="checkbox".*?name="(.*?)".*?value="(.*?)".*?\>/is';
        preg_match_all($pattern,$fv,$matches);
        foreach ($matches[1] as $k=>$v){
            $v = str_replace('[]','',$v);
            if(!$inputNames[$v]) $inputNames[$v] = [];
            if(empty($matches[2][$k])) continue;
            $inputNames[$v][] = $matches[2][$k];
        }
        //查找表单里是否有radio
        $fv = preg_replace('/name="(.*?)" type="radio"/','type="radio" name="$1"',$fv);
        $pattern = '/\<input.*?type="radio".*?name="(.*?)".*?value="(.*?)".*?\>/is';
        preg_match_all($pattern,$fv,$matches);
        foreach ($matches[1] as $k=>$v){
            $v = str_replace('[]','',$v);
            if(!$inputNames[$v]) $inputNames[$v] = [];
            if(empty($matches[2][$k])) continue;
            $inputNames[$v][] = $matches[2][$k];
        }
        if($inputNames){
            $_SESSION['__form'][Router::getController().'_'.Router::getMethod()][$fk+1] = $inputNames;
        }
    }
}

function getOfficeAssistantIds()
{
    $userIds = [];
    $managers = sf::getModel('Managers')->selectOfficeAssistants();
    while($manager = $managers->getObject()){
        $userIds[] = $manager->getUserId();
    }
    return $userIds;
}

function getOfficeManagerIds()
{
    $userIds = [];
    $managers = sf::getModel('Managers')->selectOfficeManagers();
    while($manager = $managers->getObject()){
        $userIds[] = $manager->getUserId();
    }
    return $userIds;
}

function getFinanceAssistantIds()
{
    $userIds = [];
    $managers = sf::getModel('Managers')->selectFinanceAssistants();
    while($manager = $managers->getObject()){
        $userIds[] = $manager->getUserId();
    }
    return $userIds;
}

function getFinanceManagerIds()
{
    $userIds = [];
    $managers = sf::getModel('Managers')->selectFinanceManagers();
    while($manager = $managers->getObject()){
        $userIds[] = $manager->getUserId();
    }
    return $userIds;
}

function getLeaderIds()
{
    $userIds = [];
    $managers = sf::getModel('Managers')->selectLeaders();
    while($manager = $managers->getObject()){
        $userIds[] = $manager->getUserId();
    }
    return $userIds;
}

function getTrueCorporationName($name)
{
    $sql = "SELECT * FROM `corporation_maps` where old_name = '{$name}'";
    $db = sf::getLib("db");
    $query = $db->query($sql);
    if ($db->num_rows($query)) {
        while ($data = $db->fetch_array($query)) {
            return $data['name'];
        }
    }
    return $name;
}

function getFullCompanyName($name)
{
    $company = sf::getModel('Corporations')->selectBySubject($name);
    return $company->isNew() ? $name : $company->getFullName();
}

function str2DBC($str)
{
    return convertStrType($str,'TOSBC');
}
/**
 * 字符串半角和全角间相互转换
 * @param string $str 待转换的字符串
 * @param int  $type TOSBC:转换为半角；TODBC，转换为全角
 * @return string 返回转换后的字符串
 */
function convertStrType($str, $type) {

    $dbc = array(
        '０' , '１' , '２' , '３' , '４' ,
        '５' , '６' , '７' , '８' , '９' ,
        'Ａ' , 'Ｂ' , 'Ｃ' , 'Ｄ' , 'Ｅ' ,
        'Ｆ' , 'Ｇ' , 'Ｈ' , 'Ｉ' , 'Ｊ' ,
        'Ｋ' , 'Ｌ' , 'Ｍ' , 'Ｎ' , 'Ｏ' ,
        'Ｐ' , 'Ｑ' , 'Ｒ' , 'Ｓ' , 'Ｔ' ,
        'Ｕ' , 'Ｖ' , 'Ｗ' , 'Ｘ' , 'Ｙ' ,
        'Ｚ' , 'ａ' , 'ｂ' , 'ｃ' , 'ｄ' ,
        'ｅ' , 'ｆ' , 'ｇ' , 'ｈ' , 'ｉ' ,
        'ｊ' , 'ｋ' , 'ｌ' , 'ｍ' , 'ｎ' ,
        'ｏ' , 'ｐ' , 'ｑ' , 'ｒ' , 'ｓ' ,
        'ｔ' , 'ｕ' , 'ｖ' , 'ｗ' , 'ｘ' ,
        'ｙ' , 'ｚ' , '－' , '　' , '：' ,
        '．' , '，' , '／' , '％' , '＃' ,
        '！' , '＠' , '＆' , '（' , '）' ,
        '＜' , '＞' , '＂' , '＇' , '？' ,
        '［' , '］' , '｛' , '｝' , '＼' ,
        '｜' , '＋' , '＝' , '＿' , '＾' ,
        '￥' , '￣' , '｀'

    );

    $sbc = array( //半角
                  '0', '1', '2', '3', '4',
                  '5', '6', '7', '8', '9',
                  'A', 'B', 'C', 'D', 'E',
                  'F', 'G', 'H', 'I', 'J',
                  'K', 'L', 'M', 'N', 'O',
                  'P', 'Q', 'R', 'S', 'T',
                  'U', 'V', 'W', 'X', 'Y',
                  'Z', 'a', 'b', 'c', 'd',
                  'e', 'f', 'g', 'h', 'i',
                  'j', 'k', 'l', 'm', 'n',
                  'o', 'p', 'q', 'r', 's',
                  't', 'u', 'v', 'w', 'x',
                  'y', 'z', '-', ' ', ':',
                  '.', ',', '/', '%', ' #',
                  '!', '@', '&', '(', ')',
                  '<', '>', '"', '\'','?',
                  '[', ']', '{', '}', '\\',
                  '|', '+', '=', '_', '^',
                  '￥','~', '`'

    );
    if($type == 'TODBC'){
        return str_replace( $sbc, $dbc, $str ); //半角到全角
    }elseif($type == 'TOSBC'){
        return str_replace( $dbc, $sbc, $str ); //全角到半角
    }else{
        return $str;
    }
}

/**
 * 判断是否是中文字符串
 * @param $str
 * @return false|int
 */
function isChinese($str) {
    return preg_match('/[\x{4e00}-\x{9fa5}]/u', $str);
}

/**
 * 判断是否是英文字符串
 * @param $str
 * @return false|int
 */
function isEnglish($str) {
    return ctype_alpha($str);
}

function getColumns($subject='paper')
{
    $column = sf::getModel('Columns')->selectBySubjectAndUserId($subject,input::session('userid'));
    if(empty($column->getConfigs())){
        $defaultColumns =  getDefaultColumns($subject);
//        $column->setConfigs($defaultColumns);
//        $column->save();
        return $defaultColumns;
    }
    return $column->getConfigs();
}

function getAllColumns($subject='paper')
{
    $column = sf::getModel('ColumnSubjects')->selectBySubject($subject);
    return $column->getColumns();
}

function getDefaultColumns($subject='paper')
{
    $column = sf::getModel('ColumnSubjects')->selectBySubject($subject);
    return $column->getDefaultColumns();
}

function getAnalysisDefaultCompanys()
{
    $db = sf::getLib('db');
    $query = $db->query("SELECT corporation_id,corporation_name,count(corporation_id) c FROM `projects` where cat_id = 16 group by corporation_id order by c desc limit 3");
    $companys = [];
    while($row = $db->fetch_array($query))
    {
        $company = sf::getModel('Corporations')->selectByUserId($row['corporation_id']);
        $companys[$row['corporation_id']] = $company->getShortName()?:$company->getSubject();
    }
    return $companys;
}

function getAnalysisDefaultUsers()
{
    $db = sf::getLib('db');
    $query = $db->query("SELECT user_id,user_name,count(user_id) c FROM `projects` where cat_id = 16 group by user_id order by c desc limit 3");
    $users = [];
    while($row = $db->fetch_array($query))
    {
        $user = sf::getModel('Users')->selectByUserId($row['user_id']);
        $users[$row['user_id']] = $user->getUserUsername();
    }
    return $users;
}

function getAnalysisDefaultIndicators()
{
    $indicators = sf::getModel('Indicators')->selectAll("is_default = 1");
    $datas = [];
    while($indicator = $indicators->getObject())
    {
        $datas[$indicator->getCode()] = $indicator->getSubject();
    }
    return $datas;
}

function getIndicators($usage='company')
{
    $indicators = sf::getModel('Indicators')->selectAll("`usage` like '%{$usage}%'","order by sort asc,id asc");
    $datas = [];
    while($indicator = $indicators->getObject()){
        $datas[$indicator->getCode()] = $indicator->getSubject();
    }
    return $datas;
}

function isJson($string) {
    $json = json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE) && (!is_null($json));
}

function getTeams()
{
    $teams = sf::getModel('Teams')->selectAll();
    $datas = [];
    while($team = $teams->getObject()){
        $datas[$team->getTeamId()] = $team->getSubject();
    }
    return $datas;
}

function getAnalysisDefaultTeams()
{
    $db = sf::getLib('db');
    $query = $db->query("select * from teams where team_id in (select team_id from team_members where user_id in (select user_id from (select count(*) c,user_id from projects where user_id in (SELECT user_id FROM `team_members`) GROUP BY user_id ORDER BY c desc) t)) limit 3");
    $teams = [];
    while($row = $db->fetch_array($query))
    {
        $teams[$row['team_id']] = $row['subject'];
    }
    $count = count($teams);
    $teamId = array_keys($teams);
    if($count<3){
        $limit = 3-$count;
        $query = $db->query("SELECT team_id,subject FROM `teams` where team_id not in ('".implode("','",$teamId)."') order by rand() limit {$limit}");
        while($row = $db->fetch_array($query))
        {
            $teams[$row['team_id']] = $row['subject'];
        }
    }

    return $teams;
}

function getSmsTemplate()
{
    $templates = sf::getModel('ShortMessageTemplates')->selectAll();
    $datas = [];
    while($template = $templates->getObject()){
        $datas[$template->getTemplateId()] = $template->getSubject();
    }
    return $datas;
}

function isBelongToSDZX($companyId)
{
    $companyIds[] = '9B581D83-B2BA-7337-0678-240000000000';
    $companyIds[] = '9B581D83-B2BA-7337-0678-030000000000';
    $companyIds[] = '9B581D83-B2BA-7337-0678-220000000000';
    if(in_array($companyId,$companyIds)) return true;
    return false;
}

function getSDZXCompanyIds()
{
    $companyIds[] = '9B581D83-B2BA-7337-0678-240000000000';
    $companyIds[] = '9B581D83-B2BA-7337-0678-030000000000';
    $companyIds[] = '9B581D83-B2BA-7337-0678-220000000000';
    return $companyIds;
}

/**
 * 保留小数
 * @param $num 数字
 * @param $scale 小数位数
 * @return string
 */
function getDecimals($num,$scale=2)
{
    $num = number_format($num, $scale, '.', '');  //格式化为指定位数小数
    $num = rtrim(rtrim($num, '0'), '.');     //去掉末尾的零和小数点
    return $num;
}