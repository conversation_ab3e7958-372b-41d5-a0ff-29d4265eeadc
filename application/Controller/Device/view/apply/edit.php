<link rel="stylesheet" href="<?=site_path('assets/css/bootstrap-select.min.css')?>" />
<script src="<?=site_path('assets/js/bootstrap-select.min.js')?>"></script>
<style>
    .btn.dropdown-toggle.btn-light{
        border: 1px solid #ced4da;
        background-color: #fff;
    }
    .filter-option-inner{
        color: #495057;
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.5;
    }
</style>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑设备
        </h2>
        <div class="content-options">
            <?=Button::setUrl(site_url('device/company/index'))->setIcon('back')->link('返回')?>
            <?php
            if(!$device->isNew()):
                ?>
                <?=Button::setUrl(site_url("device/apply/show/id/".$device->getDeviceId()))->setIcon('show')->link('查看预览')?>
            <!--
                <?=Button::setUrl(site_url("device/apply/submit/id/".$device->getDeviceId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
                -->
            <?php endif;?>
        </div>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>

                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">仪器设备名称<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="subject" type="text" id="subject" value="<?=$device->getSubject()?>" required="required" class="form-control" pattern="^.{5,}$" title="字符个数应不少于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">英文名称<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="subject_en" type="text" id="subject_en" value="<?=$device->getSubjectEn()?>" required="required" class="form-control" pattern="^.{5,}$" title="字符个数应不少于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">规格型号<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="specification" type="text" id="specification" value="<?=$device->getSpecification()?>" required="required" class="form-control" pattern="^.{5,}$" title="字符个数应不少于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">仪器设备来源<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="source" id="source">
                                    <?=getSelectFromArray(['购置','租赁','其他'],$device->getSource())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">产地国别<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select name="country" id="country" class="form-control selectpicker" data-live-search="true" required>
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(get_select_data('country'),$device->getCountry())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">生产制造商<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="maker" type="text" id="maker" value="<?=$device->getMaker()?>" required="required" class="form-control" pattern="^.{5,}$" title="字符个数应大于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">海关监管情况<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="customs" id="customs">
                                    <?=getSelectFromArray(['否','是'],$device->getCustoms())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所属单位<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="owner" id="owner">
                                    <?=getSelectFromArray(getLegalCompanys(),$device->getOwner())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">管理部门<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="company_id" id="company_id" required>
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(getSecondCompanys(),$device->getCompanyId(),false)?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">安放地址<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="store_address" type="text" id="store_address" value="<?=$device->getStoreAddress()?>" required="required" class="form-control" pattern="^.{10,}$" title="字符个数应不少于10位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">国家网络管理平台仪器编号</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="nrii_id" type="text" id="nrii_id" value="<?=$device->getNriiId()?>"  class="form-control" pattern="^.{5,}$" title="字符个数应不少于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所属单位内部编号</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="inner_no" type="text" id="inner_no" value="<?=$device->getInnerNo()?>"  class="form-control" pattern="^.{5,}$" title="字符个数应不少于5位"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">原值<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <div class="input-group">
                                    <input name="money" type="text" id="money" value="<?=$device->getMoney()?>"  class="form-control float" required />
                                    <div class="input-group-append">
                                        <span class="input-group-text">万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所属仪器类型</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="device_type" type="text" id="device_type" value="<?=$device->getDeviceType()?:'无'?>"  class="form-control" />
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('save')->button('保存并填写下一页')?>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
<script>
    function btnLoading(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            form1.submit();
        }
    }

</script>