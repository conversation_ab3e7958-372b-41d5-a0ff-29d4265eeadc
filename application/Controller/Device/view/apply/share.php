<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<link href="<?=site_path('assets/js/clockpicker/bootstrap-clockpicker.min.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<script src="<?=site_path('assets/js/clockpicker/bootstrap-clockpicker.min.js')?>"></script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑设备
        </h2>
        <div class="content-options">
            <?=Button::back()?>
            <?php
            if(!$device->isNew()):
                ?>
                <?=Button::setUrl(site_url("device/apply/show/id/".$device->getDeviceId()))->setIcon('show')->link('查看预览')?>
                <!--
                <?=Button::setUrl(site_url("device/apply/submit/id/".$device->getDeviceId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
                -->
            <?php endif;?>
        </div>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">共享模式<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="share_type" id="share_type" required>
                                    <?=getSelectFromArray(['对内共享','对外共享','不共享'],$device->getShareType())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">预约服务网址</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="book_url" type="text" id="book_url" value="<?=$device->getBookUrl()?>" class="form-control" pattern="^(http|https):\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}(\/\S*)?$" title="网址格式错误"/>
                                <small class="help-block">示例：http://www.xxx.com，没有则不填</small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">联系人<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="linkman" type="text" id="linkman" value="<?=$device->getLinkman()?>" required="required" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">手机<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="linkman_mobile" type="text" id="linkman_mobile" value="<?=$device->getLinkmanMobile()?>" required="required" class="form-control" pattern="^1[3456789]\d{9}$" title="手机号格式错误"/>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">电子邮箱<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="linkman_email" type="text" id="linkman_email" value="<?=$device->getLinkmanEmail()?>" required="required" class="form-control" pattern="^\S+@\S+\.\S+$" title="电子邮箱格式错误"/>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">通讯地址<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="address" type="text" id="address" value="<?=$device->getAddress()?>" required="required" class="form-control" pattern="^.{10,}$" title="字符个数应不少于10位" />
                            </div>
                        </div>
                        <div class="form-group row period" style="<?=$device->getShareType()=='不共享'?'display:none':''?>">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">开放时段<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select name="period[type]" id="period_type" class="form-control w-auto custom-control-inline">
                                    <?=getSelectFromArray(['everyday'=>'每天','mon-fri'=>'周一至周五','workday'=>'法定工作日','custom'=>'自定义'],$device->getPeriod('type')?:'everyday',false)?>
                                </select>
                                <div class="clockrange custom-control-inline" style="line-height: 35px;<?=$device->getPeriod('type')!='custom'?'':'display: none'?>">
                                <input type="text" name="period[start_time]" id="start_time" class="form-control custom-control-inline" style="width: 80px;margin-right: 10px;" value="<?=$device->getPeriod('start_time')?:'09:00'?>">至
                                <input type="text" name="period[end_time]" id="end_time" class="form-control custom-control-inline"  style="width: 80px;margin-left: 10px;" value="<?=$device->getPeriod('end_time')?:'17:00'?>">
                                </div>
                                <div class="daterange custom-control-inline" style="<?=$device->getPeriod('type')=='custom'?'':'display: none'?>">
                                <button type="button" class="btn" id="daterange-btn" style="border: 1px solid #d4dcec">
                                    <i class="fa fa-calendar"></i>
                                    <span>时间</span>
                                    <i class="fa fa-caret-down"></i>
                                </button>
                                <input type="hidden" name="period[start_at]" value="<?=$device->getPeriod('start_at')?>" id="start_at">
                                <input type="hidden" name="period[end_at]" value="<?=$device->getPeriod('end_at')?>" id="end_at">
                                </div>
                            </div>
                        </div>
                        <div class="form-group row d-none">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">邮政编码</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="postcode" type="text" id="postcode" value="<?=$device->getPostcode()?>" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">典型服务案例和方向</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <textarea name="case" class="no-margin form-control" id="case" rows='10' placeholder="" ><?=$device->getCase()?:'无'?></textarea>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">开放共享规定</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <textarea name="rule" class="no-margin form-control" id="rule" rows='10' placeholder="" ><?=$device->getRule()?:'无'?></textarea>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">参考收费标准</label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="fee" type="text" id="fee" value="<?=$device->getFee()?:'无'?>" class="form-control" />
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('save')->button('保存')?>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
<script>

    function isDateGreater(dateA, dateB) {
        var timeAString = '2024-01-01T'+dateA+':00';
        var timeBString  = '2024-01-01T'+dateB+':00';
        return new Date(timeAString).getTime() > new Date(timeBString).getTime();
    }

    function btnLoading(me)
    {
        var form = document.getElementById('validateForm');

        if($("#period_type").val()=='custom'){
            var start_at = $("#start_at").val();
            var end_at = $("#end_at").val();
            if(start_at=='' || end_at==''){
                showError('请选择起始时间');
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        }else{
            var start_time = $("#start_time").val();
            var end_time = $("#end_time").val();
            if(isDateGreater(start_time,end_time)){
                document.getElementById('start_time').setCustomValidity('开始时间不能大于结束时间');
            }else{
                document.getElementById('start_time').setCustomValidity('');
            }
        }
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            form1.submit();
        }
    }

    function init() {
        //定义locale汉化插件
        var locale = {
            "format": 'YYYY-MM-DD HH:mm',
            "separator": " -222 ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间'",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        //初始化显示当前时间
        var start_at = "<?=$device->getPeriod('start_at')?>";
        var end_at = "<?=$device->getPeriod('end_at')?>";
        $("#start_at").val(start_at);
        $("#end_at").val(end_at);
        if(start_at && end_at){
            $('#daterange-btn span').html(start_at + ' - ' + end_at);
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    startDate: start_at,
                    endDate: end_at,
                    timePicker : true, //是否显示小时和分钟
                    timePickerIncrement : 1, //时间的增量，单位为分钟
                    timePicker24Hour : true, //是否使用24小时制来显示时间
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD HH:mm') + ' - ' + end.format('YYYY-MM-DD HH:mm'));
                    $("#start_at").val(start.format('YYYY-MM-DD HH:mm'));
                    $("#end_at").val(end.format('YYYY-MM-DD HH:mm'));
                }
            );
        }else{
            $('#daterange-btn span').html('请选择起始时间');
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    timePicker : true, //是否显示小时和分钟
                    timePickerIncrement : 1, //时间的增量，单位为分钟
                    timePicker24Hour : true, //是否使用24小时制来显示时间
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD HH:mm') + ' - ' + end.format('YYYY-MM-DD HH:mm'));
                    $("#start_at").val(start.format('YYYY-MM-DD HH:mm'));
                    $("#end_at").val(end.format('YYYY-MM-DD HH:mm'));
                }
            );
        }
    };
    $(document).ready(function() {
        init();
        $("#period_type").change(function (){
            if($(this).val()=='custom'){
                $(".daterange").show();
                $(".clockrange").hide();
            }else{
                $(".clockrange").show();
                $(".daterange").hide();
                $("#start_at").val('');
                $("#end_at").val('');
            }
        });
        $("#share_type").change(function (){
            if($(this).val()=='不共享'){
                $(".period").hide();
            }else{
                $(".period").show();
            }
        });
        $('#start_time').clockpicker({
            autoclose: true
        });
        $('#end_time').clockpicker({
            autoclose: true
        });
    });
</script>