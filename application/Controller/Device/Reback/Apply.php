<?php
namespace App\Controller\Device\Reback;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Apply extends BaseController
{
    private $view = NULL;
    private $borrow = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
        $this->borrow = sf::getModel('DeviceBorrows')->selectByBorrowId(input::getMix('id'));
        if($this->borrow->isNew()){
            $this->error('没有找到该预约申请');
        }
    }

    function edit()
    {
        if (!$this->borrow->hasEditAuth()) {
            $this->page_debug("你没有权限执行该操作！", getFromUrl());
        }
        if(input::post()){
            $this->borrow->setReturnAt(input::post('return_at'));
            $this->borrow->setStatement(24);
            $this->borrow->save();
            $this->refresh();
        }
        $this->view->set("borrow",$this->borrow);
        $this->view->apply('inc_body','reback/apply/edit');
        $this->view->display('page_blank');
    }

}