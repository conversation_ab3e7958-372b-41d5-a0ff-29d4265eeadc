<style>
    .custom-file-label::after {
        content: "浏览";
    }
</style>
<div class="content">
    <div class="block">
        <div class="block-content">
            <div class="container" style="margin-bottom: 100px">
                <form name="form1" action="<?=site_url('excel/project/relation')?>" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="step" value="2">
                    <div class="row push">
                        <div class="col-lg-12">
                            <p>请选择要导入的表。</p>
                            <table class="table table-bordered">
                                <tr>
                                    <th class="text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th>表名</th>
                                </tr>
                                <?php
                                    foreach ($sheets as $k=>$sheet):
                                ?>
                                <tr>
                                    <td align="center"><input name="select_id[]" type="checkbox" value="<?=$k?>" <?=$k===0?'checked':''?> /></td>
                                    <td><?=$sheet?></td>
                                </tr>
                                <?php endforeach;?>
                            </table>
                        </div>
                    </div>
                    <div class="row push">
                        <div class="col-lg-8 col-xl-5 offset-lg-4">
                            <div class="form-group">
                                <button type="submit" class="btn btn-alt-primary" onclick="btnLoading(this)"><i class="fa fa-check-circle mr-1"></i> 下一步</button>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1.3rem;height: 1.3rem"></span> 正在导入...');
        form1.submit();
    }
</script>


