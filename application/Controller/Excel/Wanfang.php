<?php
namespace App\Controller\Excel;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Core\lang;
use Sofast\Core\Log;
use Sofast\Core\router;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

set_time_limit(300);

class Wanfang extends BaseController
{
    private $error    = [];
    private $totalRow = 0;

    function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/view/');
    }

    function index()
    {
        unset($_SESSION['excel']);
        $filelink = site_path('up_files/tpl/wanfang.xlsx');
        $filename = '离线数据导入模板';
        $this->view->set('filelink', $filelink);
        $this->view->set('filename', $filename . '.xlsx');
        $this->view->apply("inc_body", 'wanfang/index');
        $this->view->display("page");
    }

    function sheet()
    {
        if (input::post()) {
            if (input::post('step') == 1 && empty($_FILES)) {
                $this->error('请选择上传文件');
            }
            //上传文件
            $datas = $this->uploadFile();
            if ($datas === false) {
                $this->showError();
            }
            //获取子表
            $sheets = $this->getExcelSheets();
            if ($sheets === false) {
                $this->showError();
            }
            $_SESSION['excel']['headline_no'] = input::post('headline_no');
            $_SESSION['excel']['dataline_first_no'] = input::post('dataline_first_no');
            $_SESSION['excel']['dataline_last_no'] = input::post('dataline_last_no');
            $_SESSION['excel']['sheets'] = $sheets;
            $this->view->set("sheets", $sheets);
            $this->view->apply("inc_body", "wanfang/sheet");
            $this->view->display("page");

        }
    }

    function relation()
    {
        if (input::post()) {
            //获取指定表的数据
            $sheetIds = input::post('select_id');
            if (empty($sheetIds)) {
                $this->error[] = "请至少选择一个表";
                $this->showError();
            }
            $headLineNo = $_SESSION['excel']['headline_no'];
            $datalineFirstNo = $_SESSION['excel']['dataline_first_no'];
            $datalineLastNo = $_SESSION['excel']['dataline_last_no'];
            $excelDatas = $this->getExcelData($headLineNo, (int)$datalineFirstNo, (int)$datalineLastNo);
            if ($excelDatas === false) {
                $this->showError();
            }
            $datas = [];
            foreach ($excelDatas['head'] as $sheetId => $headData) {
                if (!in_array($sheetId, $sheetIds)) continue;
                $datas['head'][$sheetId] = $headData;
                $datas['body'][$sheetId] = $excelDatas['body'][$sheetId];
            }
            $_SESSION['excel']['datas'] = $datas;
            //字段猜测及关联
            $fields = [
                [
                    'subject'             => '论文题目',
                    'publication'         => '刊物名称',
                    'writer'              => '作者',
                    'company'             => '署名单位',
                    'date'                => '发表日期',
                    'summary'             => '论文摘要',
                ]
            ];
            $guess = $this->guess($datas['head'], $fields);
            $this->view->set("sheetIds", $sheetIds);
            $this->view->set("sheets", $_SESSION['excel']['sheets']);
            $this->view->set("heads", $datas['head']);
            $this->view->set("fields", $fields);
            $this->view->set("guess", $guess);
            $this->view->set("type", router::getMethod());
            $this->view->apply("inc_body", "wanfang/relation");
            $this->view->display("page");
        }
    }

    public function import()
    {
        $msg = $this->doImport(input::post('relation'));
        $this->view->set('msg',$msg);
        $this->view->set('error',$this->error);
        $this->view->set('totalRow',$this->totalRow);
        $this->view->apply("inc_body", "wanfang/result");
        $this->view->display("page");
    }


    private function uploadFile()
    {
        $upload = sf::getLib('upload','upload','/up_files/','20971520',['xls','xlsx']);
        $result = $upload->upload();
        if($result){
            $saveInfo =$upload->getSaveFileInfo();
            $savepath = $saveInfo[0]['path'];
            if(empty($savepath)){
                $this->error[] = "上传文件失败";
                return false;
            }
//            $sheets = getExcelSheets(WEBROOT.'/up_files/'.$savepath);
//            if(count($sheets)!=3){
//                $this->error[] = "上传失败：子表个数错误，请下载指定模板填写";
//                return false;
//            }
            $_SESSION['excel']['path'] = $savepath;
        }else{
            $this->error[] = "上传文件失败";
            return false;
        }
    }
    private function showError()
    {
        $this->view->set('msg',false);
        $this->view->set('error',$this->error);
        $this->view->set('totalRow',$this->totalRow);
        $this->view->apply("inc_body", "import/result");
        $this->view->display("page");
    }

    private function guess($heads,$fields)
    {
        $guess = [];
        foreach ($heads as $sheetNo=>$head){
            foreach ($head as $k=>$v){
                if($sheetNo==0 && $v=='序号') continue;
                $keys = array_keys($fields,$v);
                if($keys[0]){
                    $guess[$sheetNo][$k] = $keys[0];
                    continue;
                }
                $threshold = 30;
                foreach ($fields[$sheetNo] as $code=>$field){
                    if($v===$field){
                        $percent = 100;
                        $threshold = $percent;
                        $guess[$sheetNo][$k] = $code;
                        continue;
                    }
                    $v = str_replace("*",'',$v);
                    $v = str_replace("\n",'',$v);
                    $v = str_replace('（','(',$v);
                    $v = str_replace('）',')',$v);
                    $v = preg_replace('/\(.*\)/','',$v);
                    similar_text($field,$v,$percent);
//                if(strpos($field,$v)===0 && strlen($v)>(strlen($field)/3) && $percent<90){
                    if(strpos($field,$v)===0 && $percent<90){
                        $percent = 90;
                    }
                    if($percent>$threshold) {
                        $threshold = $percent;
                        $guess[$sheetNo][$k] = $code;
                    }
                }
            }
        }
        return $guess;
    }

    private function getExcelSheets()
    {
        if(empty($_SESSION['excel']['path'])){
            $this->error[] = "请先上传文件";
            return false;
        }
        $sheets = getExcelSheets(WEBROOT.'/up_files/'.$_SESSION['excel']['path']);
        if(count($sheets)!=1){
            $this->error[] = "上传失败：子表个数错误，请下载指定模板填写";
            return false;
        }
        return $sheets;
    }

    private function getExcelData($headlineNo=1,$datalineFirstNo=2,$datalineLastNo=0)
    {
        if(empty($_SESSION['excel']['path'])){
            $this->error[] = "请上传excel文档";
            return false;
        }
        $uploadExcelPath = WEBROOT.'/up_files/'.$_SESSION['excel']['path'];
        $datas = excel_in_all($uploadExcelPath,1,200);
        $head = [];
        $body = [];
        foreach ($datas as $sheetNo=>$data){
            foreach ($data as $k=>$v){
                if(is_array($headlineNo)){
                    foreach ($headlineNo as $hno){
                        if($k==$hno){
                            //获取表头
                            $head[$sheetNo][] = $v;
                        }
                    }
                }else{
                    if($k==$headlineNo){
                        //获取表头
                        $head[$sheetNo] = $v;
                    }
                }
                if($k<$datalineFirstNo){
                    continue;
                }
                if($datalineLastNo && ($k>$datalineLastNo)){
                    break;
                }
                $body[$sheetNo][$k] = $v;
            }
        }

        return ['head'=>$head,'body'=>$body];

    }

    /**
     * 保留两位小数
     * @param $num
     * @return float
     */
    private function filterNumber($num)
    {
        if(strlen($num)==0) return '无';
        if($num==='#N/A') return '无';
        if($num==='-') return '无';
        if($num==='/') return '无';
        $num = round($num,2);
        return $num;
    }

    private function doImport($relations=[])
    {
        if ($_SESSION['excel']['datas']) //如果上传文件成功，就执行导入excel操作
        {
            $this->totalRow = 0;
            foreach ($relations as $sheetId=>$relation){
                $this->totalRow += count($_SESSION['excel']['datas']['body'][$sheetId]);
            }
            $i = 0;
            foreach ($relations as $sheetId=>$relation){
                switch ($sheetId){
                    case 0:
                        //导入基本信息
                        $i+=$this->doImportBasic($_SESSION['excel']['datas']['body'][$sheetId],$relation);
                        break;
                }
            }
            unset($_SESSION['excel']);
            return $i;
        } else {
            $this->error[] = "上传失败";
            return false;
        }
    }

    private function doImportBasic($datas,$relation)
    {
        $i = 0;
        foreach ($datas as $k=>$v){
            $subjectKey = array_search('subject',$relation);
            $publicationKey = array_search('publication',$relation);
            foreach ($v as $kk=>&$item){
                $deleteBlank = true;
                if($kk==$subjectKey) $deleteBlank = false;
                if($kk==$publicationKey) $deleteBlank = false;
                $item = $this->filterStr($item,$deleteBlank);
            }

            if($subjectKey===false){
                $this->error[] = "论文题目没有选择对应的目标字段，不能导入！";
                continue;
            }
            $companyNameKey = array_search('company',$relation);
            if($companyNameKey===false){
                $this->error[] = "署名单位没有选择对应的目标字段，不能导入！";
                continue;
            }

            if($publicationKey===false){
                $this->error[] = "刊物名称没有选择对应的目标字段，不能导入！";
                continue;
            }

            $writerKey = array_search('writer',$relation);    //中科院分区-大类
            $dateKey = array_search('date',$relation);    //发表日期
            if($dateKey===false){
                $this->error[] = "发表日期没有选择对应的目标字段，不能导入！";
                continue;
            }
            $summaryKey = array_search('summary',$relation);    //论文摘要

            $subject = $v[$subjectKey];
            $company = $v[$companyNameKey];
            $publication = $v[$publicationKey];
            $date = $v[$dateKey];
            $summary = $v[$summaryKey];
            $writer = $v[$writerKey];

            if(empty($subject)){
                $this->error[] = "第{$k}行的论文题目没有录入，不能导入！";
                continue;
            }
            if(empty($publication)){
                $this->error[] = "第{$k}行的刊物名称没有录入，不能导入！";
                continue;
            }
            if(empty($date)){
                $this->error[] = "第{$k}行的发表日期没有录入，不能导入！";
                continue;
            }else{
                $date = $this->filterDate($date);
                preg_match ("/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/", $date, $matches);
                if(empty($matches)){
                    $this->error[] = "第{$k}行的发表日期格式错误，正确的格式示例：2021-01-01";
                    continue;
                }
            }

//            $rawSubject = $subject;
//            $subject = $this->filterSubject($subject);
            $paper = sf::getModel("Wanfang")->selectBySubject(trim($subject));
            if(!$paper->isNew()){
                $this->error[] = "第{$k}行的论文题目已在系统中存在，不能重复导入";
                continue;
            }
            $paper->setCompany($company);
            $paper->setSubject($subject);
            $paper->setJournal($publication);
            $paper->setPublishYear(substr($date,0,4));
            $paper->setPublishAt($date);
            $paper->setDesc($summary);
            $paper->setAuthor($writer);
            $paper->setCreatedAt(date('Y-m-d H:i:s'));
            $paper->setUpdatedAt(date('Y-m-d H:i:s'));
            $paper->save();
            sf::getModel("historys")->addHistory($paper->getId(),'导入论文！','wanfang');
            $i++;
        }
        return $i;
    }

    private function doImportWriter($datas,$relation)
    {
        $i = 0;
        foreach ($datas as $k=>$v) {
            $subjectKey = array_search('subject',$relation);
            $userNameKey = array_search('user_name',$relation);
            $companyNameKey = array_search('company_name',$relation);
            foreach ($v as $kk=>&$item){
                $deleteBlank = true;
                if($kk==$subjectKey) $deleteBlank = false;
                if($kk==$userNameKey) $deleteBlank = false;
                if($kk==$companyNameKey) $deleteBlank = false;
                $item = $this->filterStr($item,$deleteBlank);
            }
            $subjectKey = array_search('subject', $relation);
            if ($subjectKey === false) {
                $this->error[] = "【作者】论文题目没有选择对应的目标字段，不能导入！";
                continue;
            }
            $sortKey = array_search('sort', $relation);
            if ($sortKey === false) {
                $this->error[] = "【作者】作者排序没有选择对应的目标字段，不能导入！";
                continue;
            }
            $userTypeKey = array_search('user_type', $relation);
            if ($userTypeKey === false) {
                $this->error[] = "【作者】人员类型没有选择对应的目标字段，不能导入！";
                continue;
            }
            $userNameKey = array_search('user_name', $relation);
            if ($userNameKey === false) {
                $this->error[] = "【作者】姓名没有选择对应的目标字段，不能导入！";
                continue;
            }
            $userIdcardKey = array_search('user_idcard', $relation);
            $companyNameKey = array_search('company_name', $relation);
            if ($companyNameKey === false) {
                $this->error[] = "【作者】署名单位没有选择对应的目标字段，不能导入！";
                continue;
            }
            $companySortKey = array_search('company_sort', $relation);
            if ($companySortKey === false) {
//                $this->error[] = "【作者】单位排序没有选择对应的目标字段，不能导入！";
//                continue;
            }


            $subject = $v[$subjectKey];
            $sort = (int)$v[$sortKey];
            $companyName = $v[$companyNameKey];
            $userType = $v[$userTypeKey];
            $userName = $v[$userNameKey];
            $userIdcard = $v[$userIdcardKey];
            $companySort = (int)$v[$companySortKey];
            $companySort = $companySort?:$sort;

            $paper = sf::getModel('Papers')->selectBySubject($subject);
            if($paper->isNew()){
                $this->error[] = "【作者】第{$k}行的论文题目不存在，不能导入！";
                continue;
            }
            if (!in_array($userType, ['内部人员', '外单位人员'])) {
                $this->error[] = "【作者】第{$k}行的人员类型只能从“内部人员、外单位人员”中选择！";
                return false;
            }
            if($userType=='内部人员') $userType = 'inner';
            if($userType=='外单位单位') $userType = 'outer';
            if(empty($companyName)) {
                $this->error[] = "【作者】第{$k}行的署名单位未录入，不能导入！";
                continue;
            }
            if(empty($userName)) {
                $this->error[] = "【作者】第{$k}行的姓名未录入，不能导入！";
                continue;
            }
//            if(empty($userIdcard)) {
//                $this->error[] = "【作者】第{$k}行的身份证未录入，不能导入！";
//                continue;
//            }
            if(empty($companySort)) {
                $this->error[] = "【作者】第{$k}行的单位排序未录入，不能导入！";
                continue;
            }

            if($userType=='inner') {
                if(isIdcard($userIdcard)){
                    $user = sf::getModel('Declarers')->selectByIdcard($userIdcard);
                }else{
                    $user = sf::getModel('Declarers')->selectByInnerName($userName);
                }
//                $user = sf::getModel('Declarers')->selectByIdcard($userIdcard);
//                if($user->isNew()){
//                    $this->error[] = "【作者】第{$k}行对应的身份证不存在，不能导入！";
//                    continue;
//                }
            }

            $member = $paper->getMemberByName($userName);
            if($member->isNew()) {
                $member->setItemId($paper->getPaperId());
                $member->setItemType('paper');
            }
            $member->setSort($sort);
            $member->setUserType($userType);
            if($userType=='inner'){
                $member->setUserId($user->getUserId());
                $member->setUserName($user->getPersonname());
                if(!$user->getCorporation()->isNew()){
                    $secondCompany = $user->getCorporation()->getSecondCompany();
                    $member->setCompanyId($secondCompany->getUserId());
                    $member->setCompanyName($secondCompany->getFullName()?:$secondCompany->getSubject());
                    $member->setDepartmentName($secondCompany->getSubject());
                }else{
                    $member->setCompanyName($companyName);
                }

            }else{
                $member->setUserName($userName);
                $member->setUserIdcard($userIdcard);
                $member->setCompanyName($companyName);
            }
            $member->setCompanySort($companySort);
            if($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
            $member->save();

            $i++;
        }
        return $i;
    }

    private function filterStr($str,$deleteBlank=true)
    {
        if(mb_detect_encoding($str, 'UTF-8', true)===false){
            $str = iconv('ISO-8859-1','UTF-8',$str);
        }
        $str = trim($str);
        if($deleteBlank) $str = str_replace(' ','',$str);
        $str = str_replace('　','',$str);
        $str = str_replace('  ','',$str);
        $str = str_replace(' ','',$str);
//        $str = str_replace('\'','‘',$str);
        $str = str_replace("\r\n",'',$str);
        $str = str_replace("\n\r",'',$str);
        $str = str_replace("\r",'',$str);
        $str = str_replace("\n",'',$str);
        return $str;
    }

    private function filterSn($sn)
    {
        $sn = strtoupper(substr($sn,0,2))!=='ZL' ? 'ZL'.$sn : $sn;
        $point = substr($sn,-2,1);
        if($point!=='.') $sn = substr($sn,0,strlen($sn)-1).'.'.substr($sn,-1);
        return $sn;
    }

    private function filterDate($date)
    {
        if(is_numeric($date) && strlen($date)==5)  $date = date("Y-m-d",\PHPExcel_Shared_Date::ExcelToPHP($date));
        $date = str_replace(' ','',$date);
        $date = str_replace('（','(',$date);
        $date = str_replace('）',')',$date);
        $strpos = strpos($date,'(');
        if($strpos){
            $date = substr($date,0,$strpos);
        }
        $date = preg_replace('/\(.*\)/','',$date);
        $date = str_replace('.','-',$date);
        $date = str_replace(',','-',$date);
        $date = str_replace('，','-',$date);
        $date = str_replace('/','-',$date);
        $date = str_replace('年','-',$date);
        $date = str_replace('月','-',$date);
        $date = str_replace('日','-',$date);
        $date = rtrim($date,'-');
        if(strlen($date)==4){
            $date = $date.'-01-01';
        }
        $dateArr = explode('-',$date);
        $newDateArr = [];
        foreach ($dateArr as $k=>&$d){
            if(strlen($d)==0) $d='01';
            if(strlen($d)==1) $d='0'.$d;
            if($k> 0 && strlen($d)>2) break;
            if($k==1 && $d>12) break;
            if($k==2 && $d>31) break;
            if($k>2) break;
            $newDateArr[] = $d;
        }
        $date = implode('-',$newDateArr);
        if(strlen($date)==7){
            $date = $date.'-01';
        }
        if(is_numeric($date) && strlen($date)==8){
            $date = substr($date,0,4).'-'.substr($date,4,2).'-'.substr($date,6,2);
        }
        if(is_numeric($date) && strlen($date)==6){
            $date = substr($date,0,4).'-'.substr($date,4,2).'-01';
        }
        if(is_numeric($date) && strlen($date)==4){
            $date = $date.'-01-01';
        }
        return $date;
    }

    private function getSnFromSubject($subject)
    {
        $subject = str2DBC($subject);
        $subject = str_replace('：',':',$subject);
        $sn = '';
        $pos = stripos($subject,'DOI:');
        if($pos!==false){
            $sn = substr($subject,$pos);
        }
        $pos = stripos($subject,'WOS:');
        if($pos!==false){
            $sn = substr($subject,$pos);
        }
        $pos = strpos($subject,'检索号:');
        if($pos!==false){
            $sn = substr($subject,$pos);
        }
        $pos = strpos($subject,'检索号');
        if($pos!==false){
            $sn = substr($subject,$pos);
        }
        $pos = stripos($subject,'Accession number:');
        if($pos!==false){
            $sn = substr($subject,$pos);
        }
        if($sn){
            $sn = str_ireplace('DOI: ','DOI:',$sn);
            $sn = str_replace('检索号:','',$sn);
            $sn = str_replace('WOS:','',$sn);
            $sn = str_replace('Accession number:','',$sn);
            $sn = str_replace('检索号','',$sn);
            $snArr = explode(' ',$sn);
            if(count($snArr)>1){
                $sn = array_shift($snArr);
            }
            $snArr = explode(')',$sn);
            if(count($snArr)>1){
                $sn = array_shift($snArr);
            }
            $sn = rtrim($sn,'()（） ');
        }
        return $sn;
    }

    private function filterSubject($subject)
    {
        $subject = str2DBC($subject);
        $subject = str_replace('：',':',$subject);
        $subject = str_replace('（','(',$subject);
        $subject = str_replace('）',')',$subject);
        preg_match_all('/(?<=\()[^\)]+/',$subject,$matches);
        if(count($matches[0])>0){
            $sn = array_pop($matches[0]);
            if(strstr($sn,'检索号')!==false || strstr($sn,'WOS')!==false || strstr($sn,'DOI')!==false || strstr($sn,'Accession number')!==false || strstr($sn,'影响因子')!==false){
                $subject = str_replace('('.$sn.')','',$subject);

            }
        }
        $pos = stripos($subject,'DOI:');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $pos = stripos($subject,'WOS:');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $pos = strpos($subject,'检索号:');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $pos = strpos($subject,'检索号');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $pos = stripos($subject,'Accession number:');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $pos = strpos($subject,'影响因子:');
        if($pos!==false){
            $subject = substr($subject,0,$pos);
        }
        $subject = rtrim($subject,'()（） ');
        return $subject;
    }

    function filterWriter($str)
    {
        if(empty($str)) return $str;
        $str = str_replace('（','(',$str);
        $str = str_replace('）',')',$str);
        $str = str_replace('  ','$',$str);
        $str = str_replace(', 等','',$str);
        $str = str_replace('，等','',$str);
        $str = str_replace(',等','',$str);
        $str = str_replace(';','$',$str);
        $str = str_replace('；','$',$str);
        $str = str_replace(',','$',$str);
        $str = str_replace('，','$',$str);
        $str = str_replace('、','$',$str);
        if(strstr($str,'$')===false){
            $str = str_replace(' ','$',$str);
        }else{
            $str = str_replace(' ','',$str);
        }
        $str = str_replace('中国民用航空总局第二研究所','',$str);
        preg_match_all('/(?<=\()[^\)]+/',$str,$matches);
        if(count($matches[0])>0){
            $str = implode('$',$matches[0]);
        }
        $str = rtrim($str,'/等');
        return $str;
    }
}