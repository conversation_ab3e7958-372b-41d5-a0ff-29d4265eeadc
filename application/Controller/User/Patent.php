<?php
namespace App\Controller\User;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Core\Log;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class patent extends BaseController
{
    private $patent;

    public function load()
    {
        parent::load();
        $this->patent = sf::getModel('Patents')->selectByPatentId(input::getMix('id'));
        if($this->patent->isNew()){
            if(input::session('userlevel')==2){
                $user = sf::getModel('Declarers')->selectByUserId(input::getInput('session.roleuserid'));
                $company = $user->getCorporation();
            }else{
                $company = sf::getModel('Corporations')->selectByUserId(input::getInput('session.roleuserid'));
            }
            if($company->isNew()){
                $this->page_debug('请先完善你的单位信息',site_url('user/profile/base'));
            }
            $this->patent->setCorporationId($company->getUserId());
            $this->patent->setCorporationName($company->getSubject());
            $this->patent->setUserId(input::getInput('session.roleuserid'));
            $this->patent->setUserName(input::getInput('session.nickname'));
            $company = $this->patent->getCorporation();
            $secondCompany = $company->getSecondCompany();
            $this->patent->setCompanyId($secondCompany->getUserId());
            $this->patent->setCompanyName($secondCompany->getSubject());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('user/patent/edit/id/' . $this->patent->getPatentId())
            ],
            [
                'method' => 'owner',
                'text' => '专利权人',
                'url' => site_url('user/patent/owner/id/' . $this->patent->getPatentId())
            ],
            [
                'method' => 'member',
                'text' => '发明/设计人',
                'url' => site_url('user/patent/member/id/' . $this->patent->getPatentId())
            ],
            [
                'method' => 'project',
                'text' => '关联项目',
                'url' => site_url('user/patent/project/id/' . $this->patent->getPatentId())
            ],
            [
                'method' => 'product',
                'text' => '关联产品',
                'url' => site_url('user/patent/product/id/' . $this->patent->getPatentId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('user/patent/attachment/id/' . $this->patent->getPatentId())
            ]
        ];
        view::set('method', input::getInput("mix.method"));
        view::set('tabs', $this->tabs);
    }

	/**
	 * 专利综合搜索
	 * @return [type] [description]
	 */
	function index()
	{
		$addwhere = " statement > 0  AND (user_id = '".input::getInput('session.roleuserid')."' or patent_id in (SELECT item_id FROM `fruit_members` where item_type = 'patent' and user_type = 'inner' and user_id = '".input::getInput('session.roleuserid')."')) ";
		$this->patentGrid('user/patent/index',$addwhere);
	}
	/**
	 * 填写中专利
	 */
	function write_list()
	{
        //临时打开专利的同部门编辑权限
//		$addwhere = " statement in (1,3,5,7,12,13,17,18) AND user_id = '".input::getInput('session.roleuserid')."' ";
		$addwhere = " statement in (1,3,5,7,12,13,17,18) AND  (user_id = '".input::getInput('session.roleuserid')."' or company_id = '".input::getInput('session.companyid')."') ";
		$this->patentGrid('user/patent/write_list',$addwhere);

	}
	/**
	 * 已上报专利
	 */
	function submit_list()
	{
		$addwhere = " statement > 1  AND user_id = '".input::getInput('session.roleuserid')."' ";
		$this->patentGrid('user/patent/submit_list',$addwhere);

	}

	/**
	 * 专利登记
	 */
	function edit()
	{
        //临时打开专利的同部门编辑权限
        $this->patent->setUserId(input::getInput('session.roleuserid'));
        $this->patent->setUserName(input::getInput('session.nickname'));
        if (!$this->patent->hasEditAuth()) $this->error("你没有权限执行该操作！", getFromUrl());
		if($post = input::getInput('post')){
			//若存在则不添加
            $sn = filterStr($post['sn']);
            if (!$sn) $this->error('专利号必须填写');
            $sn = strtoupper(substr($sn,0,2))!=='ZL' ? 'ZL'.$sn : $sn;
            $point = substr($sn,-2,1);
            if($point!=='.') $sn = substr($sn,0,strlen($sn)-1).'.'.substr($sn,-1);
            if($post['belong']=='独占'){
                $db = sf::getLib("db");
                $query = $db->query("SELECT * FROM `patents` WHERE  sn = '{$sn}' and patent_id !='".$this->patent->getPatentId()."'");
                if($db->num_rows($query)){
                    $this->error('该专利已登记过，不能重复登记！');
                }
            }else{
                $db = sf::getLib("db");
                $query = $db->query("SELECT * FROM `patents` WHERE  sn = '{$sn}' and patent_id !='".$this->patent->getPatentId()."' and company_id = '".input::post('company_id')."'");
                if($db->num_rows($query)){
                    $this->error('该专利已登记过，不能重复登记！');
                }
            }
            if (!$post['type']) $this->error('专利类型必须填写');
            if (!$post['status'])$this->error( '法律状态必须填写');
            if (!$post['level']) $this->error( '专利级别必须填写');
            if (!$post['subject']) $this->error( '专利名称必须填写');
            //$company = $this->patent->getCorporation();
            //$secondCompany = $company->getSecondCompany();
            //$this->patent->setRole(input::session('userlevel'));
//            $this->patent->setFirstId($company->getFirstId());
//            $this->patent->setSecondId($company->getSecondId());
//            $this->patent->setThirdId($company->getThirdId());
//            $this->patent->setFourthId($company->getFourthId());
            //$this->patent->setCompanyId($secondCompany->getUserId());
            //$this->patent->setCompanyName($secondCompany->getSubject());
            //$this->patent->setCompanyLevel($secondCompany->getLevel());
            $company = sf::getModel('Corporations')->selectByUserId(input::post('company_id'));
            $this->patent->setRole(input::session('userlevel'));
            $this->patent->setCompanyId($company->getUserId());
            $this->patent->setCompanyName($company->getSubject());
            $this->patent->setCompanyLevel($company->getLevel());

            $this->patent->setProjectId($post['project_id']);
            $this->patent->setSubject($post['subject']);
            $this->patent->setType($post['type']);
            $this->patent->setLevel($post['level']);
            $this->patent->setCountry($post['country']);
            $this->patent->setSn(filterStr($sn));
            $this->patent->setDate($post['date']);
            $this->patent->setFmr($post['fmr']);
            $this->patent->setStatus($post['status']);
            $this->patent->setTransform($post['transform']);
            $this->patent->setIndustry($post['industry']);
            $this->patent->setFruitDomain($post['fruit_domain']);
            $this->patent->setZlqr($post['zlqr']);
            $this->patent->setEndAt($post['end_at']);
            $this->patent->setApplyAt($post['apply_at']);
            $this->patent->setSummary($post['summary']);
            $this->patent->setBelong($post['belong']);
            if(!in_array(input::session('userlevel'),[5,6])) $this->patent->setStatement(1);
            $this->patent->save();
            //添加当前人员至人员名单
//            if(input::session('userlevel')==2){
//                $this->patent->addUserToMember();
//            }
            $this->success('保存成功！',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
		}
		view::set("patent",$this->patent);
		View::apply('inc_body','user/patent/edit');
		View::display('page_main');
	}

	/**
	 * 专利权人
	 */
	function owner()
	{
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $pid=>$sort){
                $owner = $this->patent->getOwnerById($pid);
                if($owner->isNew()) continue;
                $owner->setSort($sort);
                $owner->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
		view::set("patent",$this->patent);
		View::apply('inc_body','user/patent/owner');
		View::display('page_main');
	}

	/**
	 * 添加专利权人
	 */
	function edit_owner()
	{
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        $owner = $this->patent->getOwnerById(input::getInput('mix.mid'));
        if($owner->isNew()) {
            $owner->setPatentId($this->patent->getPatentId());
            $owner->setCompanyType('inner');
            $owner->setUserType('inner');
        }
        if(input::post()){
            if(input::post('owner_type')=='company' && input::post('company_type')=='inner' && empty(input::post('company_id'))) $this->error('请选择单位',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));
            if(input::post('owner_type')=='company' && input::post('company_type')=='outer' && empty(input::post('company_name'))) $this->error('请填写外单位名称',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));
            if(input::post('owner_type')=='user' && input::post('user_type')=='inner' && empty(input::post('user_id'))) $this->error('请选择人员',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));
            if(input::post('owner_type')=='user' && input::post('user_type')=='outer' && empty(input::post('user_name'))) $this->error('请填写外单位人员姓名',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));

            $owner->setSort(input::post('sort'));
            $owner->setOwnerType(input::post('owner_type'));
            $owner->setCompanyType(input::post('company_type'));
            $owner->setUserType(input::post('user_type'));
            if($owner->getOwnerType()=='company'){
                $owner->setUserType('');
                if(input::post('company_type')=='inner' && input::post('company_id')){
                    $company = sf::getModel('Corporations')->selectByUserId(input::post('company_id'));
                    if($company->isNew()) $this->error('找不到该单位',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));
                    $owner->setCompanyId($company->getUserId());
                    $owner->setSubject($company->getFullName());
                }elseif(input::post('company_name')){
                    $owner->setSubject(input::post('company_name'));
                }
            }else{
                $owner->setCompanyType('');
                if(input::post('user_type')=='inner' && input::post('user_id')){
                    $user = sf::getModel('Declarers')->selectByUserId(input::post('user_id'));
                    if($user->isNew()) $this->error('找不到该人员',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));
                    $owner->setUserId($user->getUserId());
                    $owner->setSubject($user->getPersonname());
                    $owner->setCompanyId($user->getCorporationId());
                    $owner->setWorkUnit($user->getSecondCompanyName());
                }elseif(input::post('user_name')){
                    $owner->setSubject(input::post('user_name'));
                    $owner->setUserIdcard(input::post('user_idcard'));
                    $owner->setWorkUnit(input::post('work_unit'));
                }
            }
            //检查是否重复添加
            if($owner->isRepeat()) $this->error($owner->getSubject().'已存在，不能重复添加！',site_url('user/patent/edit_owner/id/'.$this->patent->getPatentId().'/mid/'.$owner->getId()));

            if($owner->isNew()) $owner->setCreatedAt(date('Y-m-d H:i:s'));
            $owner->save();
            $this->refresh();
        }
		view::set("patent",$this->patent);
		view::set("owner",$owner);
		View::apply('inc_body','user/patent/edit_owner');
		View::display('page_blank');
	}

    /**
     * 删除专利权人
     */
    function delOwner()
    {
        if($this->patent->isNew()){
            $this->error('找不到该专利');
        }
        $owner = $this->patent->getOwnerById(input::getInput('mix.mid'));
        if($owner->isNew()){
            $this->error('找不到该专利权人');
        }
        if($owner->getPatentId()!=$this->patent->getPatentId()){
            $this->error('没有权限删除');
        }
        $owner->delete();
        $this->success('删除成功',getFromUrl());
    }

	/**
	 * 人员信息
	 */
	function member()
	{
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $mid=>$sort){
                $member = $this->patent->getMemberById($mid);
                if($member->isNew()) continue;
                $member->setSort($sort);
                $member->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
		view::set("patent",$this->patent);
		View::apply('inc_body','user/patent/member');
		View::display('page_main');
	}

    function project()
    {
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $pid=>$sort){
                $project = $this->patent->getProjectById($pid);
                if($project->isNew()) continue;
                $project->setSort($sort);
                $project->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
        view::set("patent",$this->patent);
        View::apply('inc_body','user/patent/project');
        View::display('page_main');
    }

    function product()
    {
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $pid=>$sort){
                $project = $this->patent->getProductById($pid);
                if($project->isNew()) continue;
                $project->setSort($sort);
                $project->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
        view::set("patent",$this->patent);
        View::apply('inc_body','user/patent/product');
        View::display('page_main');
    }

	function attachment()
    {
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        if(input::post()){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success('保存成功！',getFromUrl());
        }
        view::set("itemType",'patent');
        view::set("patent",$this->patent);
        View::apply('inc_body','user/patent/attachment');
        View::display('page_main');
    }

	/**
	 * 添加人员
	 */
	function addMember()
	{
		$patent = sf::getModel('Patents')->selectByPatentId(input::post('id'));
        if($patent->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该专利']);exit;
        }
        $user = sf::getModel('Declarers')->selectByUserId(input::post('userid'));
        if($user->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该人员']);exit;
        }
        $member = sf::getModel("FruitMembers")->selectByUserId(input::post('userid'),$patent->getPatentId(),'patent');
        if(!$member->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该人员已添加，不能重复添加']);exit;
        }
        $member->setUserName($user->getUserName());
        $member->setCompanyId($user->getCorporationId());
        $member->setCompanyName($user->getCorporationName());
        $member->save();
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
	}

    /**
     * 添加发明/设计人
     */
    function edit_member()
    {
        if($this->patent->isNew()) $this->error('请先填写基本信息',site_url('user/patent/edit/id/'.$this->patent->getPatentId()));
        $member = $this->patent->getMemberById(input::getInput('mix.mid'));
        if($member->isNew()) {
            $member->setItemId($this->patent->getPatentId());
            $member->setItemType('patent');
        }
        if(input::post()){
            if(input::post('user_type')=='inner' && empty(input::post('user_id'))) $this->error('请选择人员',site_url('user/patent/edit_member/id/'.$this->patent->getPatentId().'/mid/'.$member->getId()));
            if(input::post('user_type')=='outer' && empty(input::post('user_name'))) $this->error('请填写外单位人员姓名',site_url('user/patent/edit_member/id/'.$this->patent->getPatentId().'/mid/'.$member->getId()));

            $member->setUserType(input::post('user_type'));
            $member->setSort(input::post('sort'));
            if(input::post('user_type')=='inner' && input::post('user_id')){
                $user = sf::getModel('Declarers')->selectByUserId(input::post('user_id'));
                if($user->isNew()) $this->error('找不到该人员',site_url('user/patent/edit_member/id/'.$this->patent->getPatentId().'/mid/'.$member->getId()));
                $company = sf::getModel('Corporations')->selectByUserId(input::post('company_id'));
                $member->setUserId($user->getUserId());
                $member->setUserName($user->getPersonname());
                $member->setUserIdcard($user->getUserIdcard());
                $member->setCompanyId($company->getUserId());
                $member->setCorporationId($user->getCorporationId());
                $member->setCompanyName($company->getSubject());
            }elseif(input::post('user_name')){
                $member->setUserName(input::post('user_name'));
                $member->setCompanyName(input::post('company_name'));
                $member->setUserIdcard(input::post('user_idcard'));
            }
            //检查是否重复添加
            if($member->isRepeat()) $this->error($member->getUserName().'已存在，不能重复添加！',site_url('user/patent/edit_member/id/'.$this->patent->getPatentId().'/mid/'.$member->getId()));

            if($member->isNew()) $member->setCreatedAt(date('Y-m-d H:i:s'));
            $member->save();
            $member->updateSort();
            $this->refresh();
        }
        view::set("patent",$this->patent);
        view::set("member",$member);
        View::apply('inc_body','user/patent/edit_member');
        View::display('page_blank');
    }

	/**
	 * 删除人员
	 */
	function delMember()
	{
        if($this->patent->isNew()){
            $this->error('找不到该专利');
        }
        $member = $this->patent->getMemberById(input::getInput('mix.mid'));
        if($member->isNew()){
            $this->error('找不到该人员');
        }
        if($member->getItemId()!=$this->patent->getPatentId() && $member->getItemType()!='patent'){
            $this->error('没有权限删除');
        }
        $member->delete();
        $this->success('删除成功',getFromUrl());
	}

	/**
	 * 添加项目
	 */
	function addProject()
	{
        if($this->patent->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该专利']);exit;
        }
        $project = sf::getModel('Projects')->selectByProjectId(input::post('project_id'));
        if($project->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该项目']);exit;
        }
        if($this->patent->selectProjects()->getTotal()>0){
            echo json_encode(['code'=>1,'msg'=>'专利只允许关联一个项目']);exit;
        }
        $fruitProject = sf::getModel("FruitProjects")->selectByProjectId(input::post('project_id'),$this->patent->getPatentId(),'patent');
        if($fruitProject->isNew()){
            $sort = $fruitProject->getNewSort();
            $fruitProject->setSort($sort);
        }
        if(!$fruitProject->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该项目已添加，不能重复添加']);exit;
        }
        $fruitProject->save();
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
	}

	/**
	 * 删除项目
	 */
	function delProject()
	{
        if($this->patent->isNew()){
            $this->error('找不到该专利');
        }
        $fruitProject = $this->patent->getProjectById(input::get('pid'));
        if($fruitProject->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitProject->getItemId()!=$this->patent->getPatentId() && $fruitProject->getItemType()!='patent'){
            $this->error('没有权限删除');
        }
        $fruitProject->delete();
        $this->success('删除成功',getFromUrl());
	}

	/**
	 * 添加产品
	 */
	function addProduct()
	{
        if($this->patent->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该专利']);exit;
        }
        $product = sf::getModel('Products')->selectByProductId(input::post('product_id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $fruitProduct = sf::getModel("FruitProducts")->selectByProductId(input::post('product_id'),$this->patent->getPatentId(),'patent');
        if($fruitProduct->isNew()){
            $sort = $fruitProduct->getNewSort();
            $fruitProduct->setSort($sort);
        }
        if(!$fruitProduct->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该产品已添加，不能重复添加']);exit;
        }
        $fruitProduct->save();
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
	}

	/**
	 * 删除产品
	 */
	function delProduct()
	{
        if($this->patent->isNew()){
            $this->error('找不到该专利');
        }
        $fruitProduct = sf::getModel('FruitProducts',input::get('pid'));
        if($fruitProduct->isNew()){
            $this->error('找不到该产品');
        }
        if($fruitProduct->getItemId()!=$this->patent->getPatentId() && $fruitProduct->getItemType()!='patent'){
            $this->error('没有权限删除');
        }
        $fruitProduct->delete();
        $this->success('删除成功',getFromUrl());
	}
	/**
	 * 查看专利
	 */
	public function show()
	{
		if($this->patent->isNew()){
			$this->error('未找到该专利信息！',getFromUrl());
		}
        if (!$this->patent->hasShowAuth()) $this->error("你没有权限执行该操作！", getFromUrl());
		view::set("patent",$this->patent);
		View::apply('inc_body','user/patent/show');
		View::display('page_main');
	}
	/**
	 * 查看附件
	 */
	public function showFile()
	{
		if($this->patent->isNew()){
			$this->error('未找到该专利信息！',getFromUrl());
		}
		view::set("patent",$this->patent);
		View::apply('inc_body','user/patent/show_file');
		View::display('page_blank');
	}
	/**
	 * 导出专利
	 */
	public function download()
	{
		$this->show();
	}

    /**
     * 上报
     */
    function submit()
    {
        if($this->patent->isNew()){
            $this->error('未找到该专利信息！',getFromUrl());
        }
        if(input::getInput("post.id"))
        {
            //判断验证是否通过
            if($message = $this->patent->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->patent->setStatement(4);       //待单位/部门助理审核
            $this->patent->save();
            $this->patent->sendMessage();
            sf::getModel("historys")->addHistory($this->patent->getPatentId(),'上报专利成果！','patent');
            $this->success('上报成功！',site_url('user/patent/submit_list'));
        }

        $data['msg'] = $this->patent->validate();
        $data['patent'] = $this->patent;
        view::set($data);
        view::apply("inc_body","user/patent/submit");
        view::display("page_main");
    }


	/**
	 * 删除专利
	 */
	public function doDelete()
	{
        if (!$this->patent->hasEditAuth()) $this->error("你没有权限执行该操作！", getFromUrl());
		$patent = sf::getModel("Patents")->selectByPatentId(input::getInput("get.id"));
		if($patent->getUserId() != input::getInput("session.roleuserid")) 
			$this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
		if($patent->delete()){
			sf::getModel("historys")->addHistory($patent->getPatentId(),sprintf('删除专利成果！',$patent->getSubject()),'patent');
			$this->success(lang::get('Has been deleted!'),getFromUrl());
		}else $this->error("Error has happened!",getFromUrl());
	}

	public function patentGrid($tpl = 'user/patent/index',$addWhere = '1',$showMax=15,$page='page_main')
	{
        //处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

		//将搜索条件保存以备打印或者导出
//		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
//		{
//			//保存标记
//			$_SESSION['hash'] = $this->hash;
//			$_SESSION['patents']['baseSql'] = base64_encode($addWhere);
//			//打印
//			$_SESSION['patents']['sqlStr'] = base64_encode($addWhere);
//			$_SESSION['patents']['orderStr'] = base64_encode($addSql);
//		}else{
//			$_SESSION['queryOptions'] = '';
//			$_SESSION['url_str'];
//			$addWhere = base64_decode($_SESSION['patents']['sqlStr']);
//		}

		$form_vars = array('field','search','subject','user_id','patent_id');
		view::set("pager",sf::getModel('Patents')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}

    public function file_delete()
    {
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        if($file->getItemId()!=$this->patent->getPatentId()){
            $data['msg'] = '不能删除';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }

    /**
     * 撤回
     */
    function reBack()
    {
        if($this->patent->isNew()){
            $this->error('未找到该专利信息！',getFromUrl());
        }
        if(input::getInput("post.content")){
            $this->patent->setStatement(1);
            $this->patent->save();
            sf::getModel("Historys")->addHistory($this->patent->getPatentId(),'已撤回！<br/>'.input::getInput("post.content"),'patent');
            exit("<script>top.location.href='".site_url('user/patent/edit/id/'.$this->patent->getPatentId())."';</script>");
        }

        //原因表单
        $form = Form::load('user/patent/reBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'专利名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($this->patent->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('撤回修改！'))
            ->addItem(Form::hidden('id',$this->patent->getPatentId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }
}