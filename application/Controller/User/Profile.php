<?php
namespace App\Controller\User;
use App\Controller\BaseController;
use App\Facades\Button;
use App\models\Guide as guideModel;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
use App\Facades\PDF;

class profile extends BaseController
{
	private $tabs = array();

	public function load()
	{
	    if(input::get('unlock')=='yes'){
            $_SESSION['unlock'] = 'yes';
        }
		$this->tabs = [
		['method'=>'base','text'=>'基本资料','url'=>site_url('user/profile/base')],
		['method'=>'bank','text'=>'银行账号','url'=>site_url('user/profile/bank')],
        //['method'=>'figure','text'=>'创新能力画像','url'=>site_url('user/profile/figure')],
		['method'=>'project','text'=>'科研项目','url'=>site_url('user/profile/project')],
		['method'=>'product','text'=>'产品','url'=>site_url('user/profile/product')],
        ['method'=>'patent','text'=>'专利','url'=>site_url('user/profile/patent')],
		['method'=>'award','text'=>'奖励荣誉','url'=>site_url('user/profile/award')],
		//['method'=>'methods','text'=>'工法','url'=>site_url('user/profile/methods')],
//        ['method'=>'soft','text'=>'软著','url'=>site_url('user/profile/soft')],
        ['method'=>'paper','text'=>'论文','url'=>site_url('user/profile/paper')],
        //['method'=>'works','text'=>'著作','url'=>site_url('user/profile/works')],
        ['method'=>'education','text'=>'教育经历','url'=>site_url('user/profile/education')],
		['method'=>'work','text'=>'工作经历','url'=>site_url('user/profile/work')],
		['method'=>'results','text'=>'其它信息','url'=>site_url('user/profile/results')],
		['method'=>'attachment','text'=>'附件','url'=>site_url('user/profile/attachment')]
		];
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput('session.roleuserid'));

        $htmlStr = Button::setName('查看记录')->setUrl(site_url("admin/history/index/type/declarers/id/".$declarer->getUserId()))->setWidth('550px')->setHeight('80%')->setIcon('time')->window();
//		$htmlStr .= Button::setUrl(site_url("user/profile/submit/userid/".$declarer->getUserId()))->setClass('btn-alt-warning')->setIcon('send')->link("申请认证");
		view::set('method', input::getInput("mix.method"));
		view::set('tabs', $this->tabs);
		view::set('htmlStr', $htmlStr);
	}
	
	function index()
	{
		$this->base();	
	}
	
	/**
	 * 账号信息
	 */
	public function account()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('账号不存在！',getFromUrl());
		if (Input::getInput("post")) {
			//验证数据
//			if(input::getInput("session.safe.sms1") != input::getInput("post.safe_code"))//安全性判断
//				$this->page_debug("安全校验码不正确",getFromUrl());

            if(input::post('empno')){
                if(strlen(input::post('empno'))!=6){
                    $this->error("员工号格式不正确！",getFromUrl());
                }
                $_user = sf::getModel("Users")->selectByEmpno(input::post('empno'));
                if(!$_user->isNew() && $_user->getUserId()!=$user->getUserId()){
                    if($_user->isEmpty()){
                        $_user->delete();
                    }else{
                        $this->error("该员工号已被".$_user->getUserUsername()."(".$_user->getId().")绑定！",getFromUrl());
                    }
                }
                $user->setEmpno(input::post('empno'));
            }
			
			if(input::getInput("post.user_name") && $user->hasByUserName(trim(input::getInput("post.user_name"))))
				$this->page_debug("登录账号已被占用！",getFromUrl());
			
			if(input::getInput("post.user_idcard") && !isIdcard(input::getInput("post.user_idcard")))
				$this->page_debug("填写内容不是有效的身份证号码！",getFromUrl());
			if(input::getInput("post.user_idcard") && $user->hasByCardId(trim(input::getInput("post.user_idcard"))))
				$this->page_debug("身份证号码已经注册！",getFromUrl());
			
			if(input::getInput("post.user_email") && !isEmail(input::getInput("post.user_email")))
				$this->page_debug("填写内容不是有效的电子信箱！",getFromUrl());
			if(input::getInput("post.user_email") && $user->hasByEmail(trim(input::getInput("post.user_email"))))
				$this->page_debug("填写的电子邮箱已经注册！",getFromUrl());
			
			if(input::getInput("post.user_mobile") && !isMobile(input::getInput("post.user_mobile")))
				$this->page_debug("填写内容不是有效的手机号码！",getFromUrl());
			if(input::getInput("post.user_mobile") && $user->hasByMobile(input::getInput("post.user_mobile")))
				$this->page_debug("填写的手机号码已经注册！",getFromUrl());
			
			if(input::getInput("post.user_username") && $user->getUserUsername() == '')//姓名为空可以修改
			$user->setUserUsername(input::getInput("post.user_username"));	
			if(input::getInput("post.user_idcard") && $user->isBadIdCard())//错误的身份证可以修改
			$user->setUserIdcard(input::getInput("post.user_idcard"));
			input::getInput("post.user_name") && $user->setUserName(input::getInput("post.user_name"));
			input::getInput("post.user_mobile") && $user->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.user_phone") && $user->setUserPhone(input::getInput("post.user_phone"));
			input::getInput("post.user_email") && $user->setUserEmail(input::getInput("post.user_email"));
			$userrole = sf::getModel('UserRoles')->selectByRole(input::getInput("session.userid"),2);
			if(!$userrole->isNew() && input::getInput("post.user_email")){
                //更新项目负责人中的邮箱
				$declarer = sf::getModel('Declarers')->selectByUserId(input::getInput("session.userid"));
				$declarer->setUserEmail(input::getInput("post.user_email"));
				$declarer->save();
			}
			if(!$userrole->isNew() && input::getInput("post.user_mobile")){
                //更新项目负责人中的手机
				$declarer = sf::getModel('Declarers')->selectByUserId(input::getInput("session.userid"));
				$declarer->setUserMobile(input::getInput("post.user_mobile"));
				$declarer->save();
			}
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'修改登录账号信息！','users');
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/account');
		view::display('page_main');
	}

	/**
     * 银行账号
     */
    function bank()
	{
        $user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('账号不存在！',getFromUrl());
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $mid=>$sort){
                $bank = $user->getBankById($mid);
                if($bank->isNew()) continue;
                $bank->setSort($sort);
                $bank->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
		view::set("user",$user);
		View::apply('inc_body','user/profile/bank');
		View::display('page_main');
	}

    /**
     * 添加著作权人
     */
    function editBank()
    {
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('账号不存在！',getFromUrl());
        $bank = $user->getBankById(input::getInput('mix.id'));
        if($bank->isNew()){
            $bank->setUserId($user->getUserId());
        }
        if(input::post()){
            //检查是否重复添加
            $userBank = sf::getModel('UserBanks')->selectByUserIdAndCard($user->getUserId(),input::getInput("post.card"));
            if(!$userBank->isNew() && $userBank->getId()!=$bank->getId()){
                $this->error('该卡号已存在，不能重复添加！');
            }

            $bank->setSort(input::post('sort'));
			$bank->setUserId($user->getUserId());
            if($bank->isNew()) $bank->setCreatedAt(date('Y-m-d H:i:s'));

			$bank->setName(input::getInput("post.name"));
			$bank->setBranch(input::getInput("post.branch"));
			$bank->setNo(input::getInput("post.no"));
			$bank->setCard(input::getInput("post.card"));

            $bank->save();
            $bank->updateSort();
            $this->refresh();
        }
        view::set("user",$user);
        view::set("bank",$bank);
        View::apply('inc_body','user/profile/edit_bank');
        View::display('page_blank');
    }

	function defaultBank(){
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('账号不存在！',getFromUrl());
		$bank = sf::getModel('UserBanks',input::get('id'));
        if($bank->isNew()){
            $this->error('找不到该银行账户');
        }
		if($bank->getUserId()!=$user->getUserId()){
            $this->error('没有权限设置');
        }
		$banks=$user->selectBanks();
		while($bankT=$banks->getObject()){
			$bankT->setIsDefault(0);
			$bankT->save();
		}
        $bank = sf::getModel('UserBanks',input::get('id'));
		$bank->setIsDefault(1);
		$bank->save();
		$this->success('设置成功',getFromUrl());
	}

	/**
	 * 删除人员
	 */
	function delBank()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()) $this->page_debug('账号不存在！',getFromUrl());
        $bank = sf::getModel('UserBanks',input::get('id'));
        if($bank->isNew()){
            $this->error('找不到该银行账户');
        }
        if($bank->getUserId()!=$user->getUserId()){
            $this->error('没有权限删除');
        }
        $bank->delete();
        $this->success('删除成功',getFromUrl());
	}
	
	function edit()
	{
		$_SESSION['navigation_id'] = 677;
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew())
			$this->page_debug('请先登录！',site_url("login/index"));
		if(changeRole(2)) $this->jump(site_url("user/profile/base"));
		else $this->page_debug("请先激活",site_url("register/researcher"));
	}

	/**
	 * 基本资料
	 */
	public function base()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($declarer->getIsLock()==0 && input::session('unlock')!='yes'){
//            $this->jump(site_url('user/profile/show/userid/'.$declarer->getUserId()));
        }
		if($declarer->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$declarer->getUserId()));
		if (Input::getInput("post")) {

			$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
			if(!$declarer->getPersonname() && $user->getUserUsername()) $declarer->setPersonname($user->getUserUsername());
			$user->getUserIdcard() && $declarer->setUserIdcard($user->getUserIdcard());
			$user->getUserMobile() && $declarer->setUserMobile($user->getUserMobile());
			$user->getUserEmail()  && $declarer->setUserEmail($user->getUserEmail());

			$declarer->setWorkDepartment(input::getInput("post.work_department"));
			$declarer->setUserSex(input::getInput("post.user_sex"));
			$declarer->setPolitical(input::getInput("post.political"));
			$declarer->setUserBirthday(input::getInput("post.user_birthday"));
			$declarer->setUserDegree(input::getInput("post.user_degree"));
			$declarer->setEducation(input::getInput("post.education"));
			$declarer->setUserGraduate(input::getInput("post.user_graduate"));
			$declarer->setUserHonor(input::getInput("post.user_honor"));
			$declarer->setUserWork(input::getInput("post.user_work"));
			$declarer->setUserGraduateAt(input::getInput("post.user_graduate_at"));
			$declarer->setNation(input::getInput("post.nation"));
			$declarer->setNative(input::getInput("post.native"));
            //保存部门信息
			$company = sf::getModel("Corporations")->selectByUserId(input::getInput("post.corporation_id"));
			if(!$company->isNew() && $declarer->getCorporationId() == '')
			{
				$declarer->setCorporationId($company->getUserId());
				$declarer->setCorporationName($company->getSubject());
			}
			if(input::post('multiple_id')){
                $declarer->addMultiple(input::post('multiple_id'));
            }

			$declarer->setUserSummary(input::getInput("post.user_summary"));
			$declarer->setBranch(input::getInput("post.branch"));
			$declarer->setUserOccupation(input::getInput("post.user_occupation"));

			$declarer->setUserPhone(input::getInput("post.user_phone"));
			$declarer->setUserMobile(input::getInput("post.user_mobile"));
			$declarer->setUserEmail(input::getInput("post.user_email"));
			$declarer->setUserHomeAddress(input::getInput("post.user_home_address"));

			$declarer->setResults(input::getInput("post.results"));
			$declarer->setJobQualification(input::getInput("post.job_qualification"));
			$declarer->setJobQualificationOther(input::getInput("post.job_qualification_other"));
			$declarer->setUserAcademichonor(input::getInput("post.user_academichonor"));
			$declarer->setUserAcademichonorOther(input::getInput("post.user_academichonor_other"));

//			$declarer->setIsLock(9);
			$declarer->setIsLock(0);
			$declarer->setUpdatedAt(date('Y-m-d'));
			$declarer->save();
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $declarer);
		view::apply('inc_body', 'user/profile/base');
		view::display('page_main');
	}

    public function figure()
    {
        $declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $declarer);
        view::apply('inc_body', 'user/profile/figure');
        view::display('page_main');
    }

	private function setOption1(){
		$userid=input::getMix("id");
		$declarer = sf::getModel("Declarers")->selectByUserId($userid);
		view::set('title1','基本资料');
		view::set('option1',[
			[
				['name'=>'姓名','value'=>$declarer->getPersonname()?:'无'],
				['name'=>'性别','value'=>$declarer->getUserSex()?:'无'],
			],
			[
				['name'=>'职称','value'=>$declarer->getUserHonor()?:'无'],
				['name'=>'学历','value'=>$declarer->getUserDegree()?:'无'],
			],
			['name'=>'学术/荣誉头衔','value'=>implode(' | ',$declarer->getUserAcademichonor())?:'无'],
			['name'=>'职务','value'=>$declarer->getUserOccupation()?:'无'],
		]);
	}

	private function first($sql){
		$db = sf::getLib("db");
        return $db->fetch_first($sql);
	}
	private function select($sql){
		$db = sf::getLib("db");
        $query = $db->query($sql);
        $result=[];
        if($db->num_rows($query)) $result=$db->result_array($query);
        return $result;
	}
	private function setOption2(){
		$userid=input::getMix("id");
		$res1=$this->first("SELECT COUNT(*) AS total FROM `projects` WHERE `project_id` IN (SELECT `project_id` from `project_members` where user_id = '{$userid}') AND `cat_id`='16'");
		$res2=$this->first("SELECT COUNT(*) AS total FROM `projects` WHERE `project_id` IN (SELECT `project_id` from `project_members` where user_id = '{$userid}') AND `cat_id`='17'");
		$res3=$this->first("SELECT COUNT(*) AS total FROM `projects` WHERE `project_id` IN (SELECT `project_id` from `project_members` where user_id = '{$userid}') AND `cat_id`='15'");
		$res4=$this->first("SELECT SUM(`total_money`) AS total FROM `projects` WHERE user_id = '{$userid}' AND `statement` in (29,30)");
		view::set('title2','科研项目');
		view::set('option2',[
			['name'=>'纵向项目','value'=>"{$res1['total']}项"],
			['name'=>'横向项目','value'=>"{$res2['total']}项"],
			['name'=>'所立项目','value'=>"{$res3['total']}项"],
			['name'=>'主持项目经费','value'=>(isset($res4['total'])?getDecimals($res4['total']/10000):'0')."万元"],
		]);
	}
	private function setOption3(){
		$userid=input::getMix("id");
		$res1=$this->first("SELECT COUNT(*) AS total FROM `rewards` WHERE reward_id in (select item_id from fruit_members where item_type = 'reward' and user_id = '{$userid}')");
		$res2=$this->first("SELECT COUNT(*) AS total FROM `products` WHERE product_id in (select item_id from fruit_members where item_type = 'product' and user_id = '{$userid}')");
		$res3=$this->first("SELECT COUNT(*) AS total FROM `papers` WHERE paper_id in (select item_id from fruit_members where item_type = 'paper' and user_id = '{$userid}')");
		$res4=$this->first("SELECT COUNT(*) AS total FROM `patents` WHERE patent_id in (select item_id from fruit_members where item_type = 'patent' and user_id = '{$userid}')");
		$res5=$this->first("SELECT COUNT(*) AS total FROM `works` WHERE work_id in (select item_id from fruit_members where item_type = 'work' and user_id = '{$userid}')");
		$res6=$this->first("SELECT COUNT(*) AS total FROM `softs` WHERE soft_id in (select item_id from fruit_members where item_type = 'soft' and user_id = '{$userid}')");
		view::set('title3','科研成果');
		view::set('option3',[
			[
				['name'=>'奖励数','value'=>"{$res1['total']}个"],
				['name'=>'产品数','value'=>"{$res2['total']}个"],
			],
			[
				['name'=>'论文数','value'=>"{$res3['total']}个"],
				['name'=>'专利数','value'=>"{$res4['total']}个"],
			],
			[
				['name'=>'著作数','value'=>"{$res5['total']}个"],
				['name'=>'软著数','value'=>"{$res6['total']}个"],
			],
		]);
	}
	private function setOption4(){
		$userid=input::getMix("id");
		$res1=$this->select("SELECT * FROM `platforms` WHERE `platform_id` IN (SELECT `platform_id` FROM `platform_members` WHERE `user_id` = '$userid')");
		$res2=$this->select("SELECT * FROM `teams` WHERE `team_id` IN (SELECT `team_id` FROM `team_members` WHERE `user_id` = '$userid')");
		view::set('title4','创新平台');
		view::set('option4',[
			['name'=>'创新平台','value'=>count($res1)?implode(' | ',array_column($res1,'subject')):'无'],
			['name'=>'科研团队','value'=>count($res2)?implode(' | ',array_column($res2,'subject')):'无'],
		]);
	}
    public function figure_single()
    {
		$this->setOption1();
		$this->setOption2();
		$this->setOption3();
		$this->setOption4();
        view::display('user/profile/figure_single');
    }

	/**
	 * 成果奖励
	 */
	public function award()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/award');
		view::display('page_main');
	}

	public function methods()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/methods');
		view::display('page_main');
	}

	public function product()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/product');
		view::display('page_main');
	}


	public function patent()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/patent');
		view::display('page_main');
	}

	public function soft()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/soft');
		view::display('page_main');
	}

	/**
	 * 论文
	 */
	public function paper()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/paper');
		view::display('page_main');
	}
	/**
	 * 论著
	 */
	public function works()
	{
        $user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/works');
		view::display('page_main');
	}
	/**
	 * 教育经历
	 */
	public function education()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(Input::getInput("post")) {
			$ids   = input::getInput("post.ids");
			$start_at  = input::getInput("post.start_at");
			$end_at  = input::getInput("post.end_at");
			$school = input::getInput("post.school");
			$work = input::getInput("post.work");
			$degree = input::getInput("post.degree");
			$education = input::getInput("post.education");
			//删除前台删除的
			sf::getModel("UserEducations")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
			for($i=0,$n=count($school);$i<$n;$i++){
				if(!$school[$i]) continue;
				$edu = sf::getModel("UserEducations",$ids[$i]);
				$edu->setStartAt($start_at[$i]);
				$edu->setEndAt($end_at[$i]);
				$edu->setSchool($school[$i]);
				$edu->setWork($work[$i]);
				$edu->setDegree($degree[$i]);
				$edu->setEducation($education[$i]);
				$edu->setUserId(input::getInput("session.roleuserid"));
				$edu->save();
			}
			$user->setIsLock(0);
			$user->save();
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/education');
		view::display('page_main');
	}

	/**
	 * 工作经历
	 */
	public function work()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(Input::getInput("post")) {
			$ids   = input::getInput("post.ids");
			$start_at  = input::getInput("post.start_at");
			$end_at  = input::getInput("post.end_at");
			$place = input::getInput("post.place");
			$unit = input::getInput("post.unit");
			$department = input::getInput("post.department");
			$position = input::getInput("post.position");
			//删除前台删除的
			sf::getModel("UserWorks")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
			for($i=0,$n=count($unit);$i<$n;$i++){
				if(!$unit[$i]) continue;
				$work = sf::getModel("UserWorks",$ids[$i]);
				$work->setStartAt($start_at[$i]);
				$work->setEndAt($end_at[$i]);
				$work->setPlace($place[$i]);
				$work->setUnit($unit[$i]);
				$work->setDepartment($department[$i]);
				$work->setPosition($position[$i]);
				$work->setUserId(input::getInput("session.roleuserid"));
				$work->save();
			}
			$user->setIsLock(0);
			$user->save();
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/work');
		view::display('page_main');
	}

	/**
	 * 成果信息
	 */
	public function results()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($declarer->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$declarer->getUserId()));
		if (Input::getInput("post")) {
			$declarer->setZyzc(input::getInput("post.zyzc"));
			$declarer->setXscg(input::getInput("post.xscg"));
			$declarer->setCxcg(input::getInput("post.cxcg"));
			$declarer->setIsLock(9);
			$declarer->save();
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $declarer);
		view::apply('inc_body', 'user/profile/results');
		view::display('page_main');
	}
	/**
	 * 参与国家、省部级评估、评审活动简介
	 */
	public function project()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(Input::getInput("post")) {
			$ids   = input::getInput("post.ids");
			$start  = input::getInput("post.start");
			$end = input::getInput("post.end");
			$level = input::getInput("post.level");
			$role = input::getInput("post.role");
			$subject = input::getInput("post.subject");
			$brief = input::getInput("post.brief");
			//删除前台删除的
			sf::getModel("UserTechplans")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
			for($i=0,$n=count($subject);$i<$n;$i++){
				if(!$start[$i] || !$subject[$i]) continue;
				$project = sf::getModel("UserTechplans",$ids[$i]);
				$project->setStartAt($start[$i]);
				$project->setEndAt($end[$i]);
				$project->setSubject($subject[$i]);
				$project->setLevel($level[$i]);
				$project->setRole($role[$i]);
				$project->setBrief($brief[$i]);
				$project->setUserId(input::getInput("session.roleuserid"));
				$project->save();
			}
			$user->setIsLock(0);
			$user->save();
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/project');
		view::display('page_main');
	}

	/**
	 * 社会兼职、聘任信息
	 */
	public function employ()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(Input::getInput("post")) {
			$ids   = input::getInput("post.ids");
			$start_at  = input::getInput("post.start_at");
			$end_at = input::getInput("post.end_at");
			$subject = input::getInput("post.subject");
			$brief = input::getInput("post.brief");
			//删除前台删除的
			sf::getModel("UserEmploys")->remove("id NOT IN ('".implode("','",$ids)."') AND user_id = '".input::getInput("session.roleuserid")."'");
			for($i=0,$n=count($subject);$i<$n;$i++){
				if(!$subject[$i] || !$start_at[$i]) continue;
				$employ = sf::getModel("UserEmploys",$ids[$i]);
				$employ->setStartAt($start_at[$i]);
				$employ->setEndAt($end_at[$i]);
				$employ->setSubject($subject[$i]);
				$employ->setBrief($brief[$i]);
				$employ->setUserId(input::getInput("session.roleuserid"));
				$employ->save();
			}
			$this->success("保存成功！",getFromUrl());
		}
		view::set('user', $user);
		view::apply('inc_body', 'user/profile/employ');
		view::display('page');
	}
	
	/**
	 * 附件信息
	 */
	public function attachment()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(input::post()){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
		view::set('user', $user);
		view::set('itemType', 'user');
		view::apply('inc_body', 'user/profile/attachment');
		view::display('page_main');
	}
	
	/**
	 * 项目负责人信息查看
	 */
	public function show()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('指定项目负责人不存在！',getFromUrl());
        if (!$user->hasShowAuth()) {
            $this->error("你没有权限执行该操作！", getFromUrl());
        }
        view::set('user', $user);
		view::apply('inc_body', 'user/profile/show');
		view::display('page_main');
	}
	
	/**
	 * 申请表
	 */
	public function download()
	{
		// $this->page_debug('功能关闭中...',getFromUrl());
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('人员不存在！',getFromUrl());
		view::set('user', $user);

		// view::apply('inc_body', 'user/profile/apply');
		// view::display('page');

		$html = view::getContent("user/profile/apply");
		$pdf = PDF::setContent($html)
		->setHeader('<p style="text-align:left;border-bottom:1px solid #808080;color:#808080;">科学技术协会会员申请表</p>')
		// ->setFooter(['center'=>'- {PAGENO} -','right'=>'<img src="'.PDF::getQRcode($user->getUserId()).'" height="50" />'])
		->setWaterMark('科学技术协会会员申请表')
		->download('科学技术协会会员申请表（'.$user->getPersonname().'）.pdf');
	}

	/**
	 * 上报资料
	 */
	public function submit()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		if($user->hasLock()) $this->page_debug('申报材料正在审核中，目前只能进行查看操作！',site_url("user/profile/show/userid/".$user->getUserId()));
		if(Input::getInput("post")) {
			if(count($user->validate())) $this->page_debug("申报材料有误，请修改后再次上报！",getFromUrl());
			$user->setIsLock(1);//待单位审核
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'资料维护上报！','declarers');
			$this->success("上报成功！",site_url('user/profile/show/userid/'.$user->getUserId()));
		}
		view::set('user', $user);
		view::set('msg', $user->validate());
		view::apply('inc_body', 'user/profile/submit');
		view::display('page_main');
	}
	
	function valid()
	{
		@header('Content-type: application/json');
		$message = '';
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		if($user->isNew()){
			$valid = false;
			$message = '请重新登录后再试试！';
		}else if(input::getInput("post.user_idcard")){//验证身份证
			if(!isIdcard(input::getInput("post.user_idcard"))){
				$valid = false;
				$message = '输入内容不是有效的身份证号码';
			}elseif($user->hasByCardId(input::getInput("post.user_idcard"))){
				$message = '身份证号码已经注册不能重复注册';
				$valid = false;
			}else $valid = true;
		}else if(input::getInput("post.safe_code")){//验证验证码
			if(input::getInput("session.safe.sms1") != input::getInput("post.safe_code"))
			{
				$valid = false;
				$message = '安全校验码不正确请重新输入';	
			}else $valid = true;	
		}else if(input::getInput("post.user_name")){//登录名
			if($user->hasByUserName(input::getInput("post.user_name"))){
				$valid = false;
				$message = '登录账号已经被占用，请更换一个登录账号试试';	
			}else $valid = true;	
		}else if(input::getInput("post.user_email")){
			if(!isEmail(input::getInput("post.user_email"))){
				$valid = false;
				$message = '输入内容不是有效的电子邮箱地址';
			}elseif($user->hasByEmail(input::getInput("post.user_email"))){
				$valid = false;
				$message = '登录账号已经被占用，请更换一个登录账号再试试';	
			}else $valid = true;	
		}else if(input::getInput("post.user_mobile")){
			if(!isMobile(input::getInput("post.user_mobile"))){
				$valid = false;
				$message = '手机号码无效，请重新输入';			
			}else if($user->hasByMobile(input::getInput("post.user_mobile"))){
				$message = '该手机号码已经注册，如果忘记密码请使用找回密码功能！';
				$valid = false;
			}else $valid = true;
		}
		
		exit(json_encode(array(
			'valid'   => $valid,
			'message' => $message
			)));	
	}
	
	function safeCode()
	{
		@header('Content-type: application/json');
		$mobile = input::getInput("post.user_mobile");//将来验证新的手机号码
		$user = sf::getModel("Users")->selectByUserId(input::getInput("session.userid"));
		$_SESSION['safe']['sms1'] = rand(1000,9999);
		if(isMobile($user->getUserMobile())){
			//发给老手机验证用户有效性
			//$user->sendMessage('尊敬的'.$user->getUserUsername().'，你好！你正在科协办项目管理平台上修改账号信息，你的验证码为：'.$_SESSION['safe']['sms1'].'，如果不是你本人在操作，请拨打02885249950咨询。');
			$msg = "你的手机". getMask($user->getUserMobile(),4,7) ."将收到一条短信息，请注意查收！";
		}elseif(isMobile($mobile)){
			//sf::getModel("ShortMessages")->sendSms($mobile,'尊敬的'.$user->getUserUsername().'，你好！你正在科协办项目管理平台上修改账号信息，你的验证码为：'.$_SESSION['safe']['sms1'].'，如果不是你本人在操作，请拨打02885249950咨询。',input::getInput("session.userid"),'users',date("Y-m-d H:i:s"));
			$msg = "你的手机". getMask($mobile,4,7) ."将收到一条短信息，请注意查收！";	
		}
		exit(json_encode(["state"=>true,"msg"=>$msg]));	
	}

	public function upload()
	{
		if($_FILES){
			$upload = sf::getLib("Upload",'file',config::get("upload_path","./up_files/"),2097152,array('jpg','bmp','pdf','png'));
			if($upload->upload())
			{
				$result = $upload->getSaveFileInfo();
				foreach($result as $files)
				{
					$filemanager = sf::getModel("filemanager");
					$filemanager->setFileName($files['name']);
					$filemanager->setFileSavename($files['savename']);
					$filemanager->setFilePath($files['path']);
					$filemanager->setFileSize($files['size']);
					$filemanager->setFileExt($files['type']);
					$filemanager->setFileMinetype($files['minetype']);
					$filemanager->setUserId(input::session('userid'));
					$filemanager->setUserName(input::session('username'));
					$filemanager->setCreatedAt(date("Y-m-d H:i:s"));
					$filemanager->setFileNote(input::getInput("post.file_note"));
					$filemanager->setItemId(input::getInput("post.item_id")?:$_SESSION['roleuserid']);
					$filemanager->setItemType(input::getInput("post.item_type")?:'default');
					$filemanager->save();
				}
				$this->jump(site_url('user/profile/attachment'));
			}
		}
		$this->jump(getFromUrl());
	}
	// 证明材料
	public function upFile(){
		$item_type = input::getInput('mix.item_type');
		$item_id = input::getInput('mix.id');
		if(input::getInput('post') && $_FILES) {
			$upload = sf::getLib("upload","attachment",config::get("upload_path","./up_files/"),204800,array('jpg','bmp','png','jpeg'));
			if($upload->upload())
			{
				$result = $upload->getSaveFileInfo();
				foreach($result as $files)
				{
					$attachementModel = sf::getModel('Filemanager');
					$attachementModel->setFileName($files['name']);
					$attachementModel->setFileSavename($files['savename']);
					$attachementModel->setFilePath($files['path']);
					$attachementModel->setFileSize($files['size']);
					$attachementModel->setFileExt($files['type']);
					$attachementModel->setFileMinetype($files['minetype']);
					$attachementModel->setUserId(input::session('roleuserid'));
					$attachementModel->setUserName(input::session('username'));
					$attachementModel->setItemId(input::post('item_id'));
					$attachementModel->setFileNote(input::post('file_note'));
					$attachementModel->setItemType(input::post('item_type'));
					$attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
					$id = $attachementModel->save();
					$result['id'][] = $id;
					$result['name'][] = $files['name'];
					$result['path'][] = $files['path'];
					// exit("<script>parent.location.reload();</script>");
				}
			}else{
				$this->page_debug($upload->getError(),getFromUrl());
			}
		}
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		view::set('user', $user);
		view::set('item_type', $item_type);
		view::set('item_id', $item_id);
		view::apply("inc_body","user/profile/up_file");
		view::display("page_blank");
	}
	// 博士人才认定
	function doctor()
	{
		$data['user'] = sf::getModel("Declarers")->selectByUserId(input::getInput("session.roleuserid"));
		$data['guidelist'] = guideModel::where('id',11)->where('is_show',1)->where('parent_id',0)->where('show_at','>=',date("Y-m-d H:i:s"))->orderBy('is_lastnode')->orderBy('id')->get(['id','subject','guideid','is_lastnode']);
        //指南id不存在则
		if(Input::get('guideid'))
		{
			$guide = guideModel::where('guideid',Input::get('guideid'))->first();
            //if($guide->parent_id!=0) $this->page_debug('指南不存在！');
			$data['guide'] = $guide;
		}
		view::set($data);
		view::apply("inc_body","user/guide/guide_list");
		view::display("page");
	}

    public function file_delete()
    {
        $user = sf::getModel("Declarers")->selectByUserId(input::getMix("id"));
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        if($file->getItemId()!=$user->getUserId()){
            $data['msg'] = '不能删除';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }
	
}