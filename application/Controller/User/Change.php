<?php
namespace App\Controller\User;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use Sofast\Core\Sf;
use App\Lib\Upload;

class Change extends BaseController
{	
	/**
	 * 综合查询
	 */
	function index()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("(user_id = '".input::getInput("session.roleuserid")."' or `assistant_id` like '%".input::getInput("session.roleuserid")."%')  "));
		view::apply("inc_body","user/change/index");
		view::display("page_main");
	}
	
	/**
	 * 填写中的变更申请
	 */
	public function wait_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("(user_id = '".input::getInput("session.roleuserid")."' or `assistant_id` like '%".input::getInput("session.roleuserid")."%') AND statement IN (1,3,5,7,12,13,17,18) ","order by id desc"));
		view::apply("inc_body","user/change/wait_list");
		view::display("page_main");
	}

	/**
	 * 待项目负责人审核的变更申请
	 */
	public function wait_accept_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("user_id = '".input::getInput("session.roleuserid")."' AND statement = 2","order by id desc"));
		view::apply("inc_body","user/change/wait_accept_list");
		view::display("page_main");
	}

	/**
	 * 【加签】待审批人审核的变更申请
	 */
	public function wait_sign_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("id in (select item_id from signs where user_id = '".input::getInput("session.roleuserid")."' and statement = 1)","order by id desc"));
		view::apply("inc_body","user/change/wait_sign_list");
		view::display("page_main");
	}
	
	/**
	 * 退回的申请
	 */
	public function back_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("(user_id = '".input::getInput("session.roleuserid")."' or `assistant_id` like '%".input::getInput("session.roleuserid")."%') AND statement IN (3,5,7,12,13,17,18) "));
		view::apply("inc_body","user/change/back_list");
		view::display("page_main");
	}
	
	/**
	 * 已经上报的申请
	 */
	public function submit_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("(user_id = '".input::getInput("session.roleuserid")."' or `assistant_id` like '%".input::getInput("session.roleuserid")."%') AND statement IN (2,4,6,8,9,11,14,15,16) ","order by updated_at desc"));
		view::apply("inc_body","user/change/submit_list");
		view::display("page_main");
	}

	/**
	 * 已审核的申请
	 */
	public function accept_list()
	{
		view::set("pager",sf::getModel("ProjectChanges")->getPager("(user_id = '".input::getInput("session.roleuserid")."' or `assistant_id` like '%".input::getInput("session.roleuserid")."%') AND statement = 20 "));
		view::apply("inc_body","user/change/index");
		view::display("page_main");
	}
	
	/**
	 * 上报项目
	 */
	function doSubmit()
	{
		$_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
		if(input::post())
		{
			//判断项目验证是否通过
			if($message = $_change->validate()) $this->page_debug(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());

            if($_change->isAssistant()){
                //待项目负责人审核
                $_change->setStatement(2);
            }else{
                //待单位/部门助理审核
                $company = $_change->getCorporation();
                $_change->setCompanyLevel($company->getLevel());
                $_change->setWaitForCompany($company->getLevel());
                $_change->setStatement(4);
            }
			$_change->save();
			$_change->addHistory("申请变更材料上报成功！");
			$_change->sendMessage();
			$this->success(lang::get("Has been submit!"),site_url('user/change/submit_list'));
		}
		
		$data['change'] = $_change;
		$data['msg'] = $_change->validate();
		view::set($data);
		view::apply("inc_body","user/change/submit_do");
		view::display("page_main");
	}

	/**
	 * 撤回
	 */
	function doUndo()
	{
		$_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
		if($_change->getStatement() >= 8 || $_change->isNew()) $this->page_debug("没有权限执行该操作",getFromUrl());
		$_change->setStatement(1);//直接到填写中
		$_change->save();
		$_change->addHistory("变更申请材料撤回成功！");
        exit("<script>parent.location.reload();</script>");
	}


    /**
     * 审核项目
     */
    function doAccept()
    {
        $_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
        if(input::post())
        {
            //待部门助理审核
            $_change->setStatement(4);
            $_change->save();
            $_change->sendMessage();
            $_change->addHistory("同意申请材料中列示的调整内容！<br />处理备注为：".input::post("note"));
            exit("<script>parent.location.reload();</script>");
        }

        $data['change'] = $_change;
        //原因表单
        $form = Form::load('user/change/doAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($_change->getProjectName())
            )
            ->addItem(Form::Textarea(['name'=>'note','label'=>'处理意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('同意'))
            ->addItem(Form::hidden('id',$_change->getId()))
            ->render();

        view::set("inc_body",$form);
        view::set($data);
        view::display("page_blank");
    }

    /**
     * 退回
     */
    function doBack()
    {
        $_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
        if(input::post())
        {
            $_change->setStatement(3);
            $_change->save();
            $_change->sendMessage();
            $_change->addHistory("退回项目调整申请！<br />处理理由为：".input::post("note"));
            exit("<script>top.location.reload();</script>");
        }

        $data['change'] = $_change;
        //原因表单
        $form = Form::load('user/change/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($_change->getProjectName())
            )
            ->addItem(Form::Textarea(['name'=>'note','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$_change->getId()))
            ->render();

        view::set("inc_body",$form);
        view::set($data);
        view::display("page_blank");
    }


    /**
     * 审核项目
     */
    function doSignAccept()
    {
        $_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
        if(input::post())
        {
            $sign = $_change->getSign(input::session('userid'));
            $sign->setStatement(10);
            $sign->setIdea(input::post("note"));
            $sign->setSignAt(date('Y-m-d H:i:s'));
            $sign->save();
            $sign->sendMessage();
            $_change->addHistory("同意申请材料中列示的调整内容！<br />处理备注为：".input::post("note"));
            exit("<script>parent.location.reload();</script>");
        }

        $data['change'] = $_change;
        //原因表单
        $form = Form::load('user/change/doSignAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($_change->getProjectName())
            )
            ->addItem(Form::Textarea(['name'=>'note','label'=>'处理意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('同意'))
            ->addItem(Form::hidden('id',$_change->getId()))
            ->render();

        view::set("inc_body",$form);
        view::set($data);
        view::display("page_blank");
    }

    /**
     * 退回
     */
    function doSignBack()
    {
        $_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
        if(input::post())
        {
            $sign = $_change->getSign(input::session('userid'));
            $sign->setStatement(12);
            $sign->setIdea(input::post("note"));
            $sign->setSignAt(date('Y-m-d H:i:s'));
            $sign->save();
            $sign->sendMessage();
            $_change->addHistory("退回项目调整申请！<br />处理理由为：".input::post("note"));
            exit("<script>top.location.reload();</script>");
        }

        $data['change'] = $_change;
        //原因表单
        $form = Form::load('user/change/doSignBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($_change->getProjectName())
            )
            ->addItem(Form::Textarea(['name'=>'note','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$_change->getId()))
            ->render();

        view::set("inc_body",$form);
        view::set($data);
        view::display("page_blank");
    }

	/**
	 * 删除变更申请
	 */
	function delete()
	{
		$_change = sf::getModel("ProjectChanges",input::getInput("mix.id"));
		if($_change->getStatement() > 10 || $_change->isNew()) $this->page_debug("没有权限执行该操作",getFromUrl());
		$_change->delete();
		$_change->addHistory("变更申材料删除成功！");
		$this->page_debug('变更申材料删除成功！',getFromUrl());
	}

}