<?php
namespace App\Controller\AwardAssess;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class history extends BaseController
{	
	
	function index()
	{
		$addWhere = $addSql = '';
		$addWhere .= "project_id = '".input::getInput("mix.id")."' AND is_lock = 1 ";
		
		switch(input::getInput("session.userlevel"))
		{
			case 2:
			case 3:
				$addWhere .= " AND `is_back` = 1 ";
			break;
			case 40://复核 
			case 41://复审 
			case 42://初审
			case 43://初审
				$addWhere .= " AND `is_back` <= 3 ";
			break;
			default:
				$addWhere .= " AND `is_back` = 1 ";
			break;
		}
		$addSql = "ORDER BY updated_at DESC";

		view::set("pager",sf::getModel("Assess")->getPager($addWhere,$addSql,4));
		view::apply("inc_body","awardassess/history/index");
		view::display("page");
	}
	
	function add()
	{
		$project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
		//判断权限
		if(input::getInput("session.userlevel") == 41 && $project->getStateForBudgetBook() >= 25) exit("<script>alert('你目前没有权限提建议！');parent.location.reload();</script>");
		if(input::getInput("session.userlevel") == 42 && $project->getStateForBudgetBook() >= 20) exit("<script>alert('你目前没有权限提建议！');parent.location.reload();</script>");
		
		if(input::getInput("post")){
			input::getInput("post.toFile") && createFile($project->getProjectId());//是否创建历史版本
			//是否锁定
			if(input::getInput("post.is_lock")) $is_lock = 1;
			else $is_lock = 0;
			//设置建议状态
			if(input::getInput("post.is_back")) $is_back = 1;
			else $is_back = 0;
			
			if(input::getInput("session.userlevel") == 41) $is_back = 3;
			if(input::getInput("session.userlevel") == 42){
				if(input::getInput("mix.is_back")) $is_back =  1;//退回到用户
				else $is_back =  2;
			}
			//保存修改建议
			addAssess($project->getProjectId(),input::getInput("post.note"),$is_back,$is_lock);
			//设置项目审核状态
			if(input::getInput("post.is_lock")){
				switch(input::getInput("session.userlevel"))
				{
					case 40:
						$is_state = input::getInput("mix.is_back") ? 5 : 30;
						//转交给条财处审定
						if(input::getInput("mix.is_submit")) $is_state = 28;
					break;
					case 41:
						$is_state = input::getInput("mix.is_back") ? 16 : 25;
					break;
					case 42:
						$is_state = input::getInput("mix.is_back") ? 3 : 20;
					break;
					default:
						$is_state = 3;//默认是初审退回
					break;	
				}
				
				$project->setStateForBudgetBook($is_state);
				$project->setUpdatedAt(date("Y-m-d H:i:s"));
				$project->save();
				if($is_state == 30){
					sf::getModel("historys")->addHistory($project->getProjectId(),lang::get("The budget book has been accept!"),'budget');
					$project->sendMessage(sprintf(lang::get('MSG.P202'),$project->getSubject()));
				}else{
					sf::getModel("historys")->addHistory($project->getProjectId(),'预算书被处理!','budget',1);
				}
			}
			exit("<script>parent.location.reload();</script>");
		}
		
		view::set("assess",sf::getModel("Assess")->getByProjectId($project->getProjectId()));
		view::set("project",$project);
		view::apply("inc_body","awardassess/history/add");
		view::display("page");
	}
	
	/**
	 * 增加备注
	 */
	function doNote()
	{
		$project = sf::getModel("projects")->selectByProjectId(input::getInput("mix.id"));		
		if($project->isNew()) $this->page_debug("指定项目不存在！",getFromUrl());
		if(input::getInput("post.note"))
		{
			sf::getModel("historys")->addHistory($project->getProjectId(),'项目备注：'.input::getInput("post.note"),'project',9);
			exit("<script>parent.location.reload();</script>");	
		}
		view::set("project",$project);
		view::apply("inc_body","awardassess/history/note");
		view::display("page");
	}
	
}