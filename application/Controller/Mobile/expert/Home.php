<?php
namespace App\Controller\Mobile\Expert;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class home extends BaseController
{	
	/**
	 * 数据列表
	 */
	function index()
	{
		view::display("expert/home");
	}
	
	function left()
	{
		switch(input::getInput("session.userlevel"))
		{
			case 10:
				$left = 'expert/left';
			break;
			case 11:
				$left = 'expert/financial_left';
			break;
			default:
				$left = 'expert/left';
			break;
		}
		view::display($left);
	}
	
	function top()
	{
		$data['user'] = sf::getModel("ex_users")->selectByUserId(input::getInput("session.userid"));
		view::set($data);
		view::display("expert/top");
	}
	
	function center()
	{
		view::display("expert/center");
	}
	
	function buttom()
	{
		view::display("expert/buttom");
	}
	
	function main()
	{
		$data['user'] = sf::getModel("ExUsers")->selectByUserId(input::getInput("session.userid"));
		view::set($data);
		view::apply("inc_body","expert/main");
		view::display("page");
	}
	
}
?>