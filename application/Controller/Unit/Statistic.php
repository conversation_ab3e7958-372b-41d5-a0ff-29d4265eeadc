<?php
namespace App\Controller\unit;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Core\lang;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;

class statistic extends BaseController
{

    private $tabs = array();

    public function load()
    {
        if (input::getInput('get.year')) {
            $this->tabs = [
            ['method' => 'base', 'text' => '基本信息', 'url' => site_url('unit/statistic/base/year/' . input::getInput('get.year'))],
            ['method' => 'person', 'text' => '在职人员', 'url' => site_url('unit/statistic/person/year/' . input::getInput('get.year'))],
            ['method' => 'research', 'text' => '科研机构', 'url' => site_url('unit/statistic/research/year/' . input::getInput('get.year'))],
            ['method' => 'project', 'text' => '在研项目', 'url' => site_url('unit/statistic/project/year/' . input::getInput('get.year'))],
            ['method' => 'science', 'text' => '科技活动', 'url' => site_url('unit/statistic/science/year/' . input::getInput('get.year'))],
            ['method' => 'finance', 'text' => '财务', 'url' => site_url('unit/statistic/finance/year/' . input::getInput('get.year'))],
            ];
        } else {
            $this->tabs = [
            ['method' => 'base', 'text' => '基本信息', 'url' => site_url('unit/statistic/base')],
            ['method' => 'person', 'text' => '在职人员', 'url' => site_url('unit/statistic/person')],
            ['method' => 'research', 'text' => '科研机构', 'url' => site_url('unit/statistic/research')],
            ['method' => 'project', 'text' => '在研项目', 'url' => site_url('unit/statistic/project')],
            ['method' => 'science', 'text' => '科技活动', 'url' => site_url('unit/statistic/science')],
            ['method' => 'finance', 'text' => '财务', 'url' => site_url('unit/statistic/finance')],
            ];
        }

        view::set('method', input::getInput("mix.method"));
        view::set('tabs', $this->tabs);
    }

    function index()
    {
        $pager = sf::getModel('CorporationStatistics')->getPager("`corporation_id` = '".input::getInput("session.roleuserid")."'","ORDER BY `year` DESC");
        view::set('pager', $pager);
        view::apply('inc_body', 'unit/statistic/index');
        view::display('page');
    }

    /**
     * 企业基本资料
     */
    public function base11()
    {
        $user_id = input::getInput('session.roleuserid');
        $base = sf::getModel('CorporationBases')->selectByUserId($user_id);
        if ($data = input::getInput("post")) {
            $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
            $base->setUserId($user_id);
            $base->setSubject($corporation->getSubject());
            $base->setProperty($data['property']);
            $base->setYear(date('Y'));
            $base->setType($data['type']);
            $base->setCode(trim($data['code']));
            $corporation->setCode(trim($data['code']));
            $corporation->save();
            $base->setJgdm(trim($data['jgdm']));
            $base->setXzdm(trim($data['xzdm']));
            $base->setAddress(trim($data['address']));
            $base->setZcze(trim($data['zcze']));
            $base->setJzc(trim($data['jzc']));
            $base->setYysr(trim($data['yysr']));
            $base->setLrze(trim($data['lrze']));
            $base->setJlr(trim($data['jlr']));
            $base->setYylrl(trim($data['yylrl']));
            $base->setZcfzl(trim($data['zcfzl']));
            $base->setYftrqd(trim($data['yftrqd']));
            $base->setLdscl(trim($data['ldscl']));
            $base->setJscpsr(trim($data['jscpsr']));
            $base->setKjcxsr(trim($data['kjcxsr']));
            $base->setJjkc(trim($data['jjkc']));
            $base->setKjcxlr(trim($data['kjcxlr']));
            $base->setKjsrzb(trim($data['kjsrzb']));
            $base->setKjlrzb(trim($data['kjlrzb']));
            $base->save();
            $this->page_debug('保存成功！',getFromUrl());
        }
        view::set('base', $base);
        view::apply('inc_body', 'unit/statistic/base11');
        view::display('page');
    }
    //企业库——基本信息
    public function base(){
        $user_id = input::getInput('session.roleuserid');
        $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
        if(in_array($corporation->getStatement(),['11','10'])){
            $this->page_debug('基本信息锁定中，只能进行查看操作！',site_url('unit/statistic/showBase/id/'.$user_id));
        }
        if ($data = input::getInput("post")){
            $corporation->setCreatedAt($data['created_at']);
            $corporation->setAddress(trim($data['address']));
            $corporation->setMain($data['main']);
            $corporation->setManageLevel($data['manage_level']);
            $corporation->setIsHightech($data['is_hightech']);
            $corporation->setDepartment(trim($data['department']));
            $corporation->setConfirmAt($data['confirm_at']);
            $corporation->setLinkman(trim($data['linkman']));
            $corporation->setMobile(trim($data['mobile']));
            $corporation->setDlfr($data['dlfr']);
            $corporation->setProperty($data['property']);
            $corporation->setFmzl(trim($data['fmzl']));
            $corporation->setSyxx(trim($data['syxx']));
            $corporation->setWgsj(trim($data['wgsj']));
            $corporation->setRjzzq(trim($data['rjzzq']));
            $corporation->setStatement(1);
            $corporation->save();
            sf::getModel("CorporationBases")->remove("year IN (".date('Y').",".(date('Y')-1).") AND user_id = '".$user_id."'");
        // for ($i=1; $i < 5; $i++){
        //     for ($j = date('Y')-1; $j < date('Y')+1; $j++) { 
        //         $base->setUserId($user_id);
        //         $base->setSubject($corporation->getSubject());
        //         $base->setYear($j);
        //         $base->setQuarter($i);
        //         $base->setZcze($data[$j]['zcze'][$i]);
        //         $base->setJzc($data[$j]['jzc'][$i]);
        //         $base->setYysr($data[$j]['yysr'][$i]);
        //         $base->setLrze($data[$j]['lrze'][$i]);
        //         $base->setJlr($data[$j]['jlr'][$i]);
        //         $base->setYylrl($data[$j]['yylrl'][$i]);
        //         $base->setZcfzl($data[$j]['zcfzl'][$i]);
        //         $base->setYftrqd($data[$j]['yftrqd'][$i]);
        //         $base->setLdscl($data[$j]['ldscl'][$i]);
        //         $base->setJscpsr($data[$j]['jscpsr'][$i]);
        //         $base->setKjcxsr($data[$j]['kjcxsr'][$i]);
        //         $base->setJjkc($data[$j]['jjkc'][$i]);
        //         $base->setKjcxlr($data[$j]['kjcxlr'][$i]);
        //         $base->setKjsrzb($data[$j]['kjsrzb'][$i]);
        //         $base->setkjlrzb($data[$j]['kjlrzb'][$i]);
        //         $base->setCreatedAt(date("Y-m-d H:i:s"));
        //         $base->save();
        //     }
        // }
        }
        for ($i=1; $i < 5; $i++){
            $result1[$i] = sf::getModel("CorporationBases")->selectByYearQuarter(date('Y')-1,$i);
            $result2[$i] = sf::getModel("CorporationBases")->selectByYearQuarter(date('Y'),$i);
        }
        view::set('result1', $result1);
        view::set('result2', $result2);
        view::set('corporation', $corporation);
        view::apply('inc_body', 'unit/statistic/base');
        view::display('page_main');
    }
    function showBase(){
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        view::set('corporation',$corporation);
        view::apply('inc_body', 'unit/statistic/base_show');
        view::display('page');
    }
    //上报基本信息
    function doSubmitBase(){
        $this->validate();
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        $corporation->setStateForMoney(11);
        $corporation->setStateForPerson(11);
        $corporation->setStatement(11);
        $corporation->save();
        $this->page_debug('上报成功！',site_url('unit/statistic/showBase/id/'.input::getInput('mix.id')));
    }
    /*
    科技型企业
    */
    public function technology()
    {
        $user_id = input::getInput('session.roleuserid');
        $technology = sf::getModel('CorporationTechnologys')->selectByUserId($user_id);
        if ($data = input::getInput("post")) {
            $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
            $technology->setUserId($user_id);
            $technology->setSubject($corporation->getSubject());
            $technology->setYear(date('Y'));
            $technology->setType($data['type']);
            $technology->setLevel(trim($data['level']));
            $technology->setZcze(trim($data['zcze']));
            $technology->setJzc(trim($data['jzc']));
            $technology->setSndyysr(trim($data['sndyysr']));
            $technology->setSndlrze(trim($data['sndlrze']));
            $technology->setMain(trim($data['main']));
            $technology->setDepartment(trim($data['department']));
            $technology->setConfirmAt(trim($data['confirm_at']));
            $technology->setNote(trim($data['note']));
            $technology->save();
            $this->page_debug('保存成功！',getFromUrl());
        }
        view::set('technology', $technology);
        view::apply('inc_body', 'unit/statistic/technology');
        view::display('page');
    }
    /*
    高新技术企业培育
    */
    public function hightech()
    {
        $user_id = input::getInput('session.roleuserid');
        $hightech = sf::getModel('CorporationHightechs')->selectByUserId($user_id);
        if ($data = input::getInput("post")) {
            $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
            $hightech->setUserId($user_id);
            $hightech->setSubject($corporation->getSubject());
            $hightech->setYear(date('Y'));
            $hightech->setRegisterAt($data['register_at']);
            $corporation->setCreatedAt($data['register_at']);
            $corporation->save();
            $hightech->setLevel(trim($data['level']));
            $hightech->setDepartment(trim($data['department']));
            $hightech->setMain(trim($data['main']));
            $hightech->setZcze1(trim($data['zcze1']));
            $hightech->setZcze2(trim($data['zcze2']));
            $hightech->setZcze3(trim($data['zcze3']));
            $hightech->setZcze4(trim($data['zcze4']));
            $hightech->setJzc1(trim($data['jzc1']));
            $hightech->setJzc2(trim($data['jzc2']));
            $hightech->setJzc3(trim($data['jzc3']));
            $hightech->setJzc4(trim($data['jzc4']));
            $hightech->setYysr1(trim($data['yysr1']));
            $hightech->setYysr2(trim($data['yysr2']));
            $hightech->setYysr3(trim($data['yysr3']));
            $hightech->setYysr4(trim($data['yysr4']));
            $hightech->setLrze1(trim($data['lrze1']));
            $hightech->setLrze2(trim($data['lrze2']));
            $hightech->setLrze3(trim($data['lrze3']));
            $hightech->setLrze4(trim($data['lrze4']));
            $hightech->setYftr1(trim($data['yftr1']));
            $hightech->setYftr2(trim($data['yftr2']));
            $hightech->setYftr3(trim($data['yftr3']));
            $hightech->setYftr4(trim($data['yftr4']));
            $hightech->setFmzl(trim($data['fmzl']));
            $hightech->setSyxx(trim($data['syxx']));
            $hightech->setWgsj(trim($data['wgsj']));
            $hightech->setRjzzq(trim($data['rjzzq']));
            $hightech->setPass(trim($data['pass']));
            $hightech->setConfirmAt(trim($data['confirm_at']));
            $hightech->setLinkman(trim($data['linkman']));
            $hightech->setPhone(trim($data['phone']));
            $hightech->save();
            $this->page_debug('保存成功！',getFromUrl());
        }
        view::set('hightech', $hightech);
        view::apply('inc_body', 'unit/statistic/hightech');
        view::display('page');
    }
    /*
    科研经费
    */
    public function money()
    {
        $user_id = input::getInput('session.roleuserid');
        $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
        $moneys = sf::getModel('CorporationMoneys')->selectAll("user_id = '".$user_id."'");
        if(in_array($corporation->getStateForMoney(),['11','10'])){
            $this->page_debug('经费信息锁定中，只能进行查看操作！',site_url('unit/statistic/showMoney/id/'.$user_id));
        }
        if ($data = input::getInput("post")) {
            for ($i=1; $i < 6; $i++) { 
                $y = date('Y')-5+$i;
                $money = sf::getModel('CorporationMoneys')->selectByUserId($user_id,$y);
                $money->setYear($y);
                $money->setZcze($data['zcze'][$i]?:0);
                $money->setJzc($data['jzc'][$i]?:0);
                $money->setYysr($data['yysr'][$i]?:0);
                $money->setLrze($data['lrze'][$i]?:0);
                $money->setJlr($data['jlr'][$i]?:0);
                $money->setJlrl($data['jlrl'][$i]?:0);
                $money->setTrze($data['trze'][$i]?:0);
                $money->setYhdk($data['yhdk'][$i]?:0);
                $money->setZfzj($data['zfzj'][$i]?:0);
                $money->setSbjzj($data['sbjzj'][$i]?:0);
                $money->setSqzj($data['sqzj'][$i]?:0);
                $money->setTqjf($data['tqjf'][$i]?:0);
                $money->setQtzj($data['qtzj'][$i]?:0);
                $money->setYftrqd($data['yftrqd'][$i]?:0);
                $money->setJfzc($data['jfzc'][$i]?:0);
                $money->setYxzc($data['yxzc'][$i]?:0);
                $money->setQyzc($data['qyzc'][$i]?:0);
                $money->setJjkcfy($data['jjkcfy'][$i]?:0);
                $money->setJjkcjs($data['jjkcjs'][$i]?:0);
                $money->save();
            }
            $this->page_debug('保存成功！',getFromUrl());
        }
        while ($money = $moneys->getObject()) {
            $mons[5-date('Y')+$money->getYear()] = $money->toArray();
        }
        view::set('user_id',$user_id);
        view::set('moneys',$moneys);
        view::set('mons',$mons);
        view::apply('inc_body', 'unit/statistic/money');
        view::display('page_main');
    }
    function showMoney(){
        $user_id = input::getInput('mix.id');
        $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
        $moneys = sf::getModel('CorporationMoneys')->selectAll("user_id = '".$user_id."'");
        while ($money = $moneys->getObject()) {
            $mons[5-date('Y')+$money->getYear()] = $money->toArray();
        }
        view::set('corporation',$corporation);
        view::set('moneys',$moneys);
        view::set('mons',$mons);
        view::apply('inc_body', 'unit/statistic/money_show');
        view::display('page_blank');
    }
    //上报经费信息
    function doSubmitMoney(){
        $this->validate();
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        $corporation->setStateForMoney(11);
        $corporation->setStateForPerson(11);
        $corporation->setStatement(11);
        $corporation->save();
        $this->page_debug('上报成功！',site_url('unit/statistic/showMoney/id/'.input::getInput('mix.id')));
    }
    /*
    工法信息--未使用
    */
    public function method()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'method' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('工法信息统计表');
                        $attachementModel->setItemType('method');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationMethods")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $method = sf::getModel('CorporationMethods');
                        $method->setUserId($user_id);
                        $method->setSubject($v[2]);
                        $method->setType($v[3]);
                        $method->setCode($v[4]);
                        $method->setSqr($v[5]);
                        $method->setDate(gmdate('Y-m-d', ($v[6]-25569)*(24 * 60 * 60)));
                        $method->setIndustry($v[7]);
                        $method->setField($v[8]);
                        $method->setFileId($id);
                        $method->setUpdatedAt(date('Y-m-d H:i:s'));
                        $method->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/method');
        view::display('page');
    }
    /*
    协同创新——列表
    */
    public function innovateList()
    {
        $user_id = input::getInput('session.roleuserid');
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = "user_id = '".$user_id."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        $form_vars = array('subject','user_id');
        view::set("pager",sf::getModel('CorporationInnovates')->getPager($addWhere,$addSql,20,'','',$form_vars));
        view::apply("inc_body",'unit/statistic/innovate_list');
        view::display('page_main');
    }
    /*
    协同创新——添加
    */
    public function addInnovate()
    {
        $innovate = sf::getModel("CorporationInnovates",input::getInput('mix.id'));
        if($post = input::getInput('post')){
            $innovate->setUserId(input::getInput('session.roleuserid'));
            $innovate->setType($post['type']);
            $innovate->setSubject($post['subject']);
            $innovate->setUnit($post['unit']);
            $innovate->setCooperation($post['cooperation']);
            $innovate->setDate($post['date']);
            $innovate->setMoney($post['money']);
            $innovate->setStatus($post['status']);
            $innovate->setNote($post['note']);
            $innovate->setUpdatedAt(date('Y-m-d H:i:s'));
            $innovate->setStatement(1);
            $innovate->save();
            exit("<script>parent.location.reload();</script>"); 
        }
        view::set('innovate',$innovate);
        view::apply("inc_body","unit/statistic/add_innovate");
        view::display("page_blank");
    }
    /*
    协同创新——上报
    */
    public function submitInnovate(){
        $innovate = sf::getModel("CorporationInnovates",input::getInput('mix.id'));
        if($innovate->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        $innovate->setStatement(11);
        $innovate->save();
        $this->page_debug('上报成功！',getFromUrl());
    }
    /*
    协同创新——删除
    */
    public function delInnovate(){
        $innovate = sf::getModel("CorporationInnovates",input::getInput('mix.id'));
        if($innovate->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        if($innovate->delete()){
            sf::getModel("historys")->addHistory($innovate->getId(),'删除协同创新情况。任务名称：'.$innovate->getSubject(),'corporation_innovates');
            $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
        }else{
            $this->page_debug("Error has happened!",getFromUrl());
        }
    }
    /*
    协同创新——上传
    */
    public function innovate()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'innovate' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('协同创新情况统计表');
                        $attachementModel->setItemType('innovate');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationInnovates")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $innovate = sf::getModel('CorporationInnovates');
                        $innovate->setUserId($user_id);
                        $innovate->setType($v[2]);
                        $innovate->setSubject($v[3]);
                        $innovate->setUnit($v[4]);
                        $innovate->setCooperation($v[5]);
                        if(strlen($v[6]) == 10){
                            $innovate->setDate($v[6]);
                        }elseif(strlen($v[6]) == 5){
                            $innovate->setDate(gmdate('Y-m-d', ($v[6]-25569)*(24 * 60 * 60)));
                        }else{
                            $innovate->setDate('');
                        }
                        $innovate->setMoney($v[7]);
                        $innovate->setStatus($v[8]);
                        $innovate->setNote($v[9]);
                        $innovate->setFileId($id);
                        $innovate->setUpdatedAt(date('Y-m-d H:i:s'));
                        $innovate->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/innovate');
        view::display('page');
    }
    /**
    协同创新——查看
    */
    function showInnovate()
    {
        $addWhere = $addSql = '';
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " user_id = '".input::getInput('session.roleuserid')."' and file_id = ".input::getInput('mix.file_id');
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        input::getInput('mix.type') && $addWhere .= " and type = '".input::getInput('mix.type')."'";
        $from_vars = array('search','field');
        view::set("pager",sf::getModel("CorporationInnovates")->getPager($addWhere,$addSql,10,'','',$from_vars));
        view::apply("inc_body","unit/statistic/show_innovate");
        view::display("page_blank");
    }
    /*
    科学技术奖励——没使用
    */
    public function award()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'award' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('科学技术奖励统计表');
                        $attachementModel->setItemType('award');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationAwards")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $award = sf::getModel('CorporationAwards');
                        $award->setUserId($user_id);
                        $award->setSubject($v[2]);
                        $award->setUnit($v[3]);
                        $award->setPerson($v[4]);
                        $award->setDepartment($v[5]);
                        $award->setType($v[6]);
                        $award->setLevel($v[7]);
                        $award->setGrade($v[8]);
                        $award->setCode($v[9]);
                        $award->setFileId($id);
                        $award->setUpdatedAt(date('Y-m-d H:i:s'));
                        $award->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/award');
        view::display('page');
    }
    /*
    科技成果鉴定及评价——列表
    */
    public function fruitList()
    {
        $user_id = input::getInput('session.roleuserid');
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = "user_id = '".$user_id."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        input::getInput("mix.source") && $addWhere .= " AND `source` = '".input::getInput("mix.source")."' ";
        input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
        $form_vars = array('subject','user_id');
        view::set("pager",sf::getModel('CorporationFruits')->getPager($addWhere,$addSql,20,'','',$form_vars));
        view::apply("inc_body",'unit/statistic/fruit_list');
        view::display('page');
    }
    /*
    科技成果鉴定及评价——添加
    */
    public function addFruit()
    {
        if($post = input::getInput('post')){
            $fruit = sf::getModel("CorporationFruits");
            $fruit->setUserId(input::getInput('session.roleuserid'));
            $fruit->setSubject($post['subject']);
            $fruit->setSource($post['source']);
            $fruit->setUnit($post['unit']);
            $fruit->setDate($post['date']);
            $fruit->setDepartment($post['department']);
            $fruit->setLevel($post['level']);
            $fruit->setIndustry($post['industry']);
            $fruit->setUpdatedAt(date('Y-m-d H:i:s'));
            $fruit->save();
            exit("<script>parent.location.reload();</script>"); 
        }
        view::apply("inc_body","unit/statistic/add_fruit");
        view::display("page_blank");
    }
    /*
    科技成果鉴定及评价——删除
    */
    public function delFruit(){
        $fruit = sf::getModel("CorporationFruits",input::getInput('mix.id'));
        if($fruit->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        if($fruit->delete()){
            sf::getModel("historys")->addHistory($fruit->getId(),'删除科技成果鉴定及评价。成果名称：'.$fruit->getSubject(),'corporation_fruits');
            $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
        }else{
            $this->page_debug("Error has happened!",getFromUrl());
        }
    }
    /*
    科技成果鉴定及评价——上传
    */
    public function fruit()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'fruit' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('科技成果鉴定及评价汇总表');
                        $attachementModel->setItemType('fruit');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationFruits")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $fruit = sf::getModel('CorporationFruits');
                        $fruit->setUserId($user_id);
                        $fruit->setSubject($v[2]);
                        $fruit->setSource($v[3]);
                        $fruit->setUnit($v[4]);
                        if(strlen($v[5]) == 10){
                            $fruit->setDate($v[5]);
                        }elseif(strlen($v[5]) == 5){
                            $fruit->setDate(gmdate('Y-m-d', ($v[5]-25569)*(24 * 60 * 60)));
                        }else{
                            $fruit->setDate('');
                        }
                        $fruit->setDepartment($v[6]);
                        $fruit->setLevel($v[7]);
                        $fruit->setIndustry($v[8]);
                        $fruit->setFileId($id);
                        $fruit->setUpdatedAt(date('Y-m-d H:i:s'));
                        $fruit->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/fruit');
        view::display('page');
    }
    /**
    科技成果鉴定及评价——查看
    */
    function showFruit()
    {
        $addWhere = $addSql = '';
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " user_id = '".input::getInput('session.roleuserid')."' and file_id = ".input::getInput('mix.file_id');
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        input::getInput('mix.source') && $addWhere .= " and source = '".input::getInput('mix.source')."'";
        $from_vars = array('search','field');
        view::set("pager",sf::getModel("CorporationFruits")->getPager($addWhere,$addSql,10,'','',$from_vars));
        view::apply("inc_body","unit/statistic/show_fruit");
        view::display("page_blank");
    }
    /*
    科技成果鉴定及评价——列表
    */
    public function platformList()
    {
        $user_id = input::getInput('session.roleuserid');
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = "user_id = '".$user_id."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        $form_vars = array('subject','user_id');
        view::set("pager",sf::getModel('CorporationPlatforms')->getPager($addWhere,$addSql,20,'','',$form_vars));
        view::apply("inc_body",'unit/statistic/platform_list');
        view::display('page_main');
    }
    /*
    科技成果鉴定及评价——添加
    */
    public function addPlatform()
    {
        $platform = sf::getModel("CorporationPlatforms",input::getInput('mix.id'));
        if($post = input::getInput('post')){
            $platform->setUserId(input::getInput('session.roleuserid'));
            $platform->setSubject($post['subject']);
            $platform->setUnit($post['unit']);
            $platform->setCooperation($post['cooperation']);
            $platform->setDate($post['date']);
            $platform->setDepartment($post['department']);
            $platform->setLevel($post['level']);
            $platform->setPrincipal($post['principal']);
            $platform->setEngine($post['engine']);
            $platform->setNote($post['note']);
            $platform->setUpdatedAt(date('Y-m-d H:i:s'));
            $platform->save();
            exit("<script>parent.location.reload();</script>"); 
        }
        view::set('platform',$platform);
        view::apply("inc_body","unit/statistic/add_platform");
        view::display("page_blank");
    }
    /*
    科技创新基地（平台）——上报
    */
    public function submitPlatform(){
        $platform = sf::getModel("CorporationPlatforms",input::getInput('mix.id'));
        if($platform->getUserId() != input::getInput("session.roleuserid")){
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        $platform->setStatement(11);
        $platform->save();
        $this->page_debug('上报成功！',getFromUrl());
    }
    /*
    科技创新基地（平台）——删除
    */
    public function delPlatform(){
        $platform = sf::getModel("CorporationPlatforms",input::getInput('mix.id'));
        if($platform->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        if($platform->delete()){
            sf::getModel("historys")->addHistory($platform->getId(),'删除科技创新基地（平台）。平台名称：'.$platform->getSubject(),'corporation_platforms');
            $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
        }else{
            $this->page_debug("Error has happened!",getFromUrl());
        }
    }
    /*
    科技创新基地（平台）
    */
    public function platform()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'platform' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('科技创新基地（平台）统计表');
                        $attachementModel->setItemType('platform');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationPlatforms")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $platform = sf::getModel('CorporationPlatforms');
                        $platform->setUserId($user_id);
                        $platform->setSubject($v[2]);
                        $platform->setUnit($v[3]);
                        $platform->setCooperation($v[4]);
                        if(strlen($v[5]) == 10){
                            $platform->setDate($v[5]);
                        }elseif(strlen($v[5]) == 5){
                            $platform->setDate(gmdate('Y-m-d', ($v[5]-25569)*(24 * 60 * 60)));
                        }else{
                            $platform->setDate('');
                        }
                        // $platform->setDate(gmdate('Y-m-d', ($v[5]-25569)*(24 * 60 * 60)));
                        $platform->setDepartment($v[6]);
                        $platform->setLevel($v[7]);
                        $platform->setNote($v[8]);
                        $platform->setFileId($id);
                        $platform->setUpdatedAt(date('Y-m-d H:i:s'));
                        $platform->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/platform');
        view::display('page');
    }
    /**
    科技创新基地（平台）——查看
    */
    function showPlatform()
    {
        $addWhere = $addSql = '';
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " user_id = '".input::getInput('session.roleuserid')."' and file_id = ".input::getInput('mix.file_id');
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        input::getInput('mix.level') && $addWhere .= " and level = '".input::getInput('mix.level')."'";
        $from_vars = array('search','field');
        view::set("pager",sf::getModel("CorporationPlatforms")->getPager($addWhere,$addSql,10,'','',$from_vars));
        view::apply("inc_body","unit/statistic/show_platform");
        view::display("page_blank");
    }
    /*
    参与标准制定情况——列表
    */
    public function standardList()
    {
        $user_id = input::getInput('session.roleuserid');
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = "user_id = '".$user_id."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        input::getInput("mix.type") && $addWhere .= " AND `type` = '".input::getInput("mix.type")."' ";
        $form_vars = array('subject','user_id');
        view::set("pager",sf::getModel('CorporationStandards')->getPager($addWhere,$addSql,20,'','',$form_vars));
        view::apply("inc_body",'unit/statistic/standard_list');
        view::display('page');
    }
    /*
    参与标准制定情况——添加
    */
    public function addStandard()
    {
        if($post = input::getInput('post')){
            $standard = sf::getModel("CorporationStandards");
            $standard->setUserId(input::getInput('session.roleuserid'));
            $standard->setSubject($post['subject']);
            $standard->setType($post['type']);
            $standard->setDate($post['date']);
            $standard->setUnit($post['unit']);
            $standard->setUpdatedAt(date('Y-m-d H:i:s'));
            $standard->save();
            exit("<script>parent.location.reload();</script>"); 
        }
        view::apply("inc_body","unit/statistic/add_standard");
        view::display("page_blank");
    }
    /*
    参与标准制定情况——删除
    */
    public function delStandard(){
        $standard = sf::getModel("CorporationStandards",input::getInput('mix.id'));
        if($standard->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        if($standard->delete()){
            sf::getModel("historys")->addHistory($standard->getId(),'删除参与标准制定情况。标准名称：'.$standard->getSubject(),'corporation_standards');
            $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
        }else{
            $this->page_debug("Error has happened!",getFromUrl());
        }
    }
    /*
    参与标准制定情况
    */
    public function standard()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'standard' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('参与标准制定情况统计表');
                        $attachementModel->setItemType('standard');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationStandards")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $standard = sf::getModel('CorporationStandards');
                        $standard->setUserId($user_id);
                        $standard->setSubject($v[2]);
                        $standard->setType($v[3]);
                        if(strlen($v[4]) == 10){
                            $standard->setDate($v[4]);
                        }elseif(strlen($v[4]) == 5){
                            $standard->setDate(gmdate('Y-m-d', ($v[4]-25569)*(24 * 60 * 60)));
                        }else{
                            $standard->setDate('');
                        }
                        $standard->setUnit($v[5]);
                        $standard->setFileId($id);
                        $standard->setUpdatedAt(date('Y-m-d H:i:s'));
                        $standard->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/standard');
        view::display('page');
    }
    /**
    参与标准制定情况——查看
    */
    function showStandard()
    {
        $addWhere = $addSql = '';
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " user_id = '".input::getInput('session.roleuserid')."' and file_id = ".input::getInput('mix.file_id');
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        input::getInput('mix.type') && $addWhere .= " and type = '".input::getInput('mix.type')."'";
        $from_vars = array('search','field');
        view::set("pager",sf::getModel("CorporationStandards")->getPager($addWhere,$addSql,10,'','',$from_vars));
        view::apply("inc_body","unit/statistic/show_standard");
        view::display("page_blank");
    }
    /*
    成果转化情况——列表
    */
    public function transformList()
    {
        $user_id = input::getInput('session.roleuserid');
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = "user_id = '".$user_id."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        input::getInput("mix.type") && $addWhere .= " AND `type` = '".input::getInput("mix.type")."' ";
        $form_vars = array('subject','user_id');
        view::set("pager",sf::getModel('CorporationTransforms')->getPager($addWhere,$addSql,20,'','',$form_vars));
        view::apply("inc_body",'unit/statistic/transform_list');
        view::display('page');
    }
    /*
    成果转化情况——添加
    */
    public function addTransform()
    {
        if($post = input::getInput('post')){
            $transform = sf::getModel("CorporationTransforms");
            $transform->setUserId(input::getInput('session.roleuserid'));
            $transform->setSubject($post['subject']);
            $transform->setUnit($post['unit']);
            $transform->setType($post['type']);
            $transform->setDate($post['date']);
            $transform->setIndustry($post['industry']);
            $transform->setMoney($post['money']);
            $transform->setNote($post['note']);
            $transform->setUpdatedAt(date('Y-m-d H:i:s'));
            $transform->save();
            exit("<script>parent.location.reload();</script>"); 
        }
        view::apply("inc_body","unit/statistic/add_transform");
        view::display("page_blank");
    }
    /*
    成果转化情况——删除
    */
    public function delTransform(){
        $transform = sf::getModel("CorporationTransforms",input::getInput('mix.id'));
        if($transform->getUserId() != input::getInput("session.roleuserid")){ 
            $this->page_debug(lang::get("You do not have permission to do it!"),getFromUrl());
        }
        if($transform->delete()){
            sf::getModel("historys")->addHistory($transform->getId(),'删除成果转化情况。成果名称：'.$transform->getSubject(),'corporation_transforms');
            $this->page_debug(lang::get('Has been deleted!'),getFromUrl());
        }else{
            $this->page_debug("Error has happened!",getFromUrl());
        }
    }
    /*
    成果转化情况
    */
    public function transform()
    {
        $db = sf::getLib("db");
        $query = $db->fetch_first("SELECT * FROM `filemanager` WHERE `user_id` = '".input::getInput('session.roleuserid')."' and item_type = 'transform' order by id desc");
        if(input::getInput('post')){
            if($_FILES['file']['name']) {
                $upload = sf::getLib("upload","file",config::get("upload_path","./up_files/"),20971520,array('xls'));
                if($upload->upload())
                {
                    $result = $upload->getSaveFileInfo();
                    foreach($result as $files)
                    {
                        $attachementModel = sf::getModel('Filemanager');
                        $attachementModel->setFileName($files['name']);
                        $attachementModel->setFileSavename($files['savename']);
                        $attachementModel->setFilePath($files['path']);
                        $attachementModel->setFileSize($files['size']);
                        $attachementModel->setFileExt($files['type']);
                        $attachementModel->setFileMinetype($files['minetype']);
                        $attachementModel->setUserId(input::session('roleuserid'));
                        $attachementModel->setUserName(input::session('username'));
                        $attachementModel->setItemId(input::session('roleuserid'));
                        $attachementModel->setFileNote('成果转化情况统计表');
                        $attachementModel->setItemType('transform');
                        $attachementModel->setCreatedAt(date('Y-m-d H:i:s'));
                        $id = $attachementModel->save();
                    }
                }else{
                    $this->page_debug('上传失败！',getFromUrl());
                }
                //上传后的文件名
                $filename = $_FILES['file']['name'][0];
                $uploadfile = $_FILES['file']['tmp_name'][0];
                $result = $_FILES['file']['tmp_name'][0];
                //如果上传文件成功，就执行导入excel操作
                if ($result) 
                {
                    sf::getModel("CorporationTransforms")->remove("user_id = '".input::getInput("session.roleuserid")."'");
                    $excelreader = sf::getLib('ExcelReader');
                    $excelreader->setOutputEncoding('UTF-8');
                    $excelreader->read($uploadfile);
                    $arr = $excelreader->sheets[0];
                    $this->totalRow = count($arr['cells'])-1;
                    $user_id = input::getInput('session.roleuserid');
                    foreach ($arr['cells'] as $k=>$v){
                        if($k<3) continue;
                        foreach ($v as &$item){
                            $item = str_replace('  ','',trim($item));
                            $item = str_replace(' ','',trim($item));
                        }
                        $transform = sf::getModel('CorporationTransforms');
                        $transform->setUserId($user_id);
                        $transform->setSubject($v[2]);
                        $transform->setUnit($v[3]);
                        $transform->setType($v[4]);
                        if(strlen($v[5]) == 10){
                            $transform->setDate($v[5]);
                        }elseif(strlen($v[5]) == 5){
                            $transform->setDate(gmdate('Y-m-d', ($v[5]-25569)*(24 * 60 * 60)));
                        }else{
                            $transform->setDate('');
                        }
                        $transform->setIndustry($v[6]);
                        $transform->setMoney($v[7]);
                        $transform->setNote($v[8]);
                        $transform->setFileId($id);
                        $transform->setUpdatedAt(date('Y-m-d H:i:s'));
                        $transform->save();
                    }
                    $this->page_debug('保存成功！',getFromUrl());
                } else {
                    $this->page_debug('保存失败！',getFromUrl());
                }
            }else{
                $this->page_debug('未选择任何文件！',getFromUrl());
            }
        }
        view::set('query',$query);
        view::apply('inc_body', 'unit/statistic/transform');
        view::display('page');
    }
    /**
    成果转化情况——查看
    */
    function showTransform()
    {
        $addWhere = $addSql = '';
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'id';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'ASC';
        $addSql .= 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " user_id = '".input::getInput('session.roleuserid')."' and file_id = ".input::getInput('mix.file_id');
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
        input::getInput('mix.type') && $addWhere .= " and type = '".input::getInput('mix.type')."'";
        $from_vars = array('search','field');
        view::set("pager",sf::getModel("CorporationTransforms")->getPager($addWhere,$addSql,10,'','',$from_vars));
        view::apply("inc_body","unit/statistic/show_transform");
        view::display("page_blank");
    }
    /*
    企业科技活动人员
    */
    public function person()
    {
        $user_id = input::getInput('session.roleuserid');
        $person = sf::getModel('CorporationPersons')->selectByUserId($user_id); 
        $corporation = sf::getModel('Corporations')->selectByUserId($user_id); 
        if(in_array($corporation->getStateForPerson(),['11','10'])){
            $this->page_debug('人才信息锁定中，只能进行查看操作！',site_url('unit/statistic/showPerson/id/'.$user_id));
        }
        if ($data = input::getInput("post")) {
            $corporation = sf::getModel("Corporations")->selectByUserId($user_id);
            $person->setUserId($user_id);
            $person->setSubject($corporation->getSubject());
            $person->setYear(date('Y'));
            $person->setZz(trim($data['zz']));
            $person->setBs(trim($data['bs']));
            $person->setSs(trim($data['ss']));
            $person->setXs(trim($data['xs']));
            $person->setGj(trim($data['gj']));
            $person->setZj(trim($data['zj']));
            $person->setYf(trim($data['yf']));
            $person->setGl(trim($data['gl']));
            $person->setKjbl(trim($data['kjbl']));
            $person->setRych(trim($data['rych']));
            $person->setWp(trim($data['wp']));
            $person->setTotal(trim($data['total']));
            $person->save();
            $this->page_debug('保存成功！',getFromUrl());
        }
        view::set('user_id', $user_id);
        view::set('person', $person);
        view::apply('inc_body', 'unit/statistic/person');
        view::display('page');
    }

    function showPerson(){
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        $person = sf::getModel('CorporationPersons')->selectByUserId(input::getInput('mix.id'));
        view::set('corporation',$corporation);
        view::set('person',$person);
        view::apply('inc_body', 'unit/statistic/person_show');
        view::display('page_blank');
    }
    //上报人才信息
    function doSubmitPerson(){
        $this->validate();
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        $corporation->setStateForMoney(11);
        $corporation->setStateForPerson(11);
        $corporation->setStatement(11);
        $corporation->save();
        $this->page_debug('上报成功！',site_url('unit/statistic/showPerson/id/'.input::getInput('mix.id')));
    }
    //上报验证：基本信息+经费信息+人才信息
    function validate(){
        $corporation = sf::getModel("Corporations")->selectByUserId(input::getInput('mix.id'));
        if(!$corporation->getCreatedAt() || !$corporation->getAddress() || !$corporation->getManageLevel() || !$corporation->getLinkman() || !$corporation->getMobile() || !$corporation->getDlfr()){
            $this->page_debug('【基本信息】未完善，请完善后上报！',getFromUrl());
        }
        $money1 = $corporation->getMoneyByYear(date('Y')-4);
        $money2 = $corporation->getMoneyByYear(date('Y')-3);
        $money3 = $corporation->getMoneyByYear(date('Y')-2);
        $money4 = $corporation->getMoneyByYear(date('Y')-1);
        $money5 = $corporation->getMoneyByYear(date('Y'));
        if(!intval($money5->getZcze())){
            $this->page_debug('【经费情况】未完善，请完善后上报！',site_url('unit/statistic/money'));

        }
        $person = sf::getModel("CorporationPersons")->selectByUserId(input::getInput('mix.id'));
        if(!$person->getZz() || !$person->getTotal()){
            $this->page_debug('【人才情况】未完善，请完善后上报！',site_url('unit/statistic/person'));
        }
    }

    //科技创新数据统计
    function sjtj(){
        view::apply("inc_body","unit/statistic/sjtj");
        view::display("page_main");
    }

    function getNumberMoney($addWhere='')
    {
        $projects = sf::getModel("Projects")->selectAll($addWhere);
        $data['money'] = 0.00;
        $data['money_dq'] = 0.00;
        $data['number'] = 0;
        $data['number_dq'] = 0;
        while ($project = $projects->getObject()) {
            if($project->getDeclareYear() == date('Y')){
                $data['money_dq'] += $project->getTotalMoney();
                $data['number_dq']++;
            }
            $data['money'] += $project->getTotalMoney();
            $data['number']++;
        }
        $data['money'] = round($data['money'],2);
        $data['money_dq'] = round($data['money_dq'],2);
        return $data;
    }
    function getNumberMoneyLevel($addWhere='')
    {
        $projects = sf::getModel("Projects")->selectAll($addWhere);
        $data['money'] = 0.00;
        $data['money_dq'] = 0.00;
        $data['number'] = 0;
        $data['number_dq'] = 0;
        while ($project = $projects->getObject()) {
            if($project->getDeclareYear() == date('Y')){
                $data['money_dq'] += $project->getTotalMoney();
                $data['number_dq']++;
            }
            $data['money'] += $project->getTotalMoney();
            $data['number']++;
        }
        $data['money'] = round($data['money'],2);
        $data['money_dq'] = round($data['money_dq'],2);
        return $data;
    }

    function getFruits($tables='',$addWhere=''){
        $addWhere .= " and `".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10";
        $fruits = sf::getModel($tables)->selectAll($addWhere);
        $data[1]=$data[2]=$data[3]=$data[4]=0;
        while ($fruit = $fruits->getObject()) {
            if($fruit->getYear() == date('Y')){
                $data[2]++;
            }
            $data[1]++;
        }
        return $data;
    }

    /**
     * 导出——科技创新统计PDF
     */
    public function download()
    {
        ini_set('max_execution_time', 600);
        $company = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'));
        if($company->isNew()) $this->error('找不到该单位');
        $currentYear = date('Y');
        $lastYear = date('Y')-1;
        view::set('currentYear', $currentYear);
        view::set('lastYear', $lastYear);

        // 1.1 全部科研项目情况
        $years = "(".(date('Y')).",".(date('Y')-1).",".(date('Y')-2).")";
        $data['A1'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and declare_year in ".$years);
        $data['A2'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 16 and declare_year in ".$years);
        $data['A3'] = $this->getNumberMoneyLevel("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and project_level = '国家级' and declare_year in ".$years);
        $data['A4'] = $this->getNumberMoneyLevel("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and project_level = '省部级' and declare_year in ".$years);
        $data['A5'] = $this->getNumberMoneyLevel("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and project_level = '企业级' and declare_year in ".$years);

        // 1.2 政府科研项目清单
        $data['B1'] = sf::getModel("Projects")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 16","order by company_level asc,corporation_id asc");

        // 1.3 集团公司科研项目情况
        $data['C1'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3782 and declare_year in ".$years);
        $data['C2'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3783 and declare_year in ".$years);
        $data['C3'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3784 and declare_year in ".$years);
        $data['C4'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3785 and declare_year in ".$years);
        $data['C5'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3786 and declare_year in ".$years);
        $data['C6'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and project_type = 3787 and declare_year in ".$years);
        $data['C7'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and type_id = 2 and declare_year in ".$years);
        $data['C8'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and type_id = 3 and declare_year in ".$years);
        $data['C9'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and type_id = 4 and declare_year in ".$years);
        $data['C10'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 29 and cat_id = 15 and declare_year in ".$years);
        $data['C11'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 29 and cat_id = 15 and real_end_at < '".date("Y-m-d",time() + 30*24*60*60)."' and declare_year in ".$years);
        $data['C12'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 30 and cat_id = 15 and declare_year in ".$years);
        $data['C13'] = $this->getNumberMoney("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement IN (29,30) and cat_id = 15 and declare_year in ".$years);

        // 2.1 科技创新成果情况
        $data['E1'] = $this->getFruits("Patents","status in (1,2,3,5)");
        $data['E2'] = $this->getFruits("Patents","status in (1,2,3,5) and type = 1");
        $data['E3'] = $this->getFruits("Patents","status in (1,2,3,5) and type = 2");
        $data['E4'] = $this->getFruits("Patents","status in (1,2,3,5) and type = 3");
        $data['E41'] = $this->getFruits("Patents","status in (1,2,3,5) and type = 5");
        $data['E5'] = $this->getFruits("Patents","status in (4) and type in (1,2,3)");
        $data['E6'] = $this->getFruits("Patents","status in (4) and type = 1");
        $data['E7'] = $this->getFruits("Patents","status in (4) and type = 2");
        $data['E8'] = $this->getFruits("Patents","status in (4) and type = 3");
        $data['E81'] = $this->getFruits("Patents","status in (4) and type = 5");
        $data['E9'] = $this->getFruits("Softs","1");
        $data['E10'] = $this->getFruits("Methods","1");
        $data['E11'] = $this->getFruits("Methods","type = 3");
        $data['E12'] = $this->getFruits("Methods","type = 2");
        $data['E13'] = $this->getFruits("Methods","type = 1");
        $data['E14'] = $this->getFruits("Works","1");
        $data['E15'] = $this->getFruits("Standards","1");
        $data['E16'] = $this->getFruits("Standards","type = '国家标准'");
        $data['E17'] = $this->getFruits("Standards","type = '地方标准'");
        $data['E18'] = $this->getFruits("Standards","type = '行业标准'");
        $data['E19'] = $this->getFruits("Fruits","1");
        $data['E20'] = $this->getFruits("Fruits","level = '国际领先'");
        $data['E21'] = $this->getFruits("Fruits","level = '国际先进'");
        $data['E22'] = $this->getFruits("Fruits","level = '国内领先'");
        $data['E23'] = $this->getFruits("Fruits","level = '国内先进'");
        $data['E24'] = $this->getFruits("Fruits","level = '省内领先'");
        $data['E25'] = $this->getFruits("Rewards","level = '国家级' and grade = '一等奖'");
        $data['E26'] = $this->getFruits("Rewards","level = '国家级' and grade = '二等奖'");
        $data['E27'] = $this->getFruits("Rewards","level = '省级' and grade = '一等奖'");
        $data['E28'] = $this->getFruits("Rewards","level = '省级' and grade = '二等奖'");
        $data['E29'] = $this->getFruits("Rewards","level = '省级' and grade = '优秀奖'");
        $data['E30'] = $this->getFruits("Papers","1");
        $data['E31'] = $this->getFruits("Papers","level in (5,6)");
        $data['E32'] = $this->getFruits("Papers","level in (2,3)");
        $data['E33'] = $this->getFruits("Papers","level not in (2,3,5,6)");
        $data['E34'] = $this->getFruits("Products","1");
        $data['E35'] = $this->getFruits("ScienceArticles","1");

        // 2.2 科技成果技术领域分布情况
        $domains = get_select_data('skills',1);
        foreach ($domains as $key => $value) {
            $data['F1'][$value][1] = sf::getModel("Patents")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and fruit_domain = '".$value."'")->getTotal();
            $data['F1'][$value][2] = sf::getModel("Methods")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and fruit_domain = '".$value."'")->getTotal();
            $data['F1'][$value][3] = sf::getModel("Papers")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and fruit_domain = '".$value."'")->getTotal();
            $data['F1'][$value][4] = sf::getModel("Rewards")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and fruit_domain = '".$value."'")->getTotal();
        }

        // 2.3 科技成果转化情况
        $data['G1'][2] = sf::getModel("Patents")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '已经转化'")->getTotal();
        $data['G1'][3] = sf::getModel("Methods")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '已经转化'")->getTotal();
        $data['G1'][4] = sf::getModel("Rewards")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '已经转化'")->getTotal();
        $data['G1'][5] = sf::getModel("Papers")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '已经转化'")->getTotal();
        $data['G2'][2] = sf::getModel("Patents")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'")->getTotal();
        $data['G2'][3] = sf::getModel("Methods")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'")->getTotal();
        $data['G2'][4] = sf::getModel("Rewards")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'")->getTotal();
        $data['G2'][5] = sf::getModel("Papers")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'")->getTotal();

        // 2.4 科技成果已转化项目清单
        $data['H1'] = sf::getModel("Transforms")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10","order by company_level asc,corporation_id asc");

        // 2.5 科技成果具有转化潜力项目清单
        $data['zls']  = sf::getModel("Patents")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'","order by company_level asc,corporation_id asc");
        $data['gfs']  = sf::getModel("Methods")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'","order by company_level asc,corporation_id asc");
        $data['jls']  = sf::getModel("Rewards")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'","order by company_level asc,corporation_id asc");
        $data['jss']  = sf::getModel("Papers")->selectAll("`".input::session('companyfield')."` = '".input::session('roleuserid')."' and statement = 10 and transform = '具有转化潜力'","order by company_level asc,corporation_id asc");

        // 3.1 博士人才情况
        $data['bsrcs'] = sf::getModel("CorporationTalents")->selectAll("`company_id` = '".input::session('roleuserid')."' and type = 'docter' and total>0","order by total desc");

        // 3.2 创新人才情况
        $data['cxrcs'] = sf::getModel("CorporationTalents")->selectAll("`company_id` = '".input::session('roleuserid')."' and type = 'innovate' and total>0","order by total desc");

        // 3.3 集团专家库已认证情况
        $gcl = ['建筑类','交通类','环保类','化工类','电子信息类','机械类','纺织类'];
        $jrgl = ['统计学类','金融类','管理类','经济类','法学类','财务类'];
//        $qt = ['材料类','食品类','科技服务类','能源类','农业类','医卫类','其他类'];
//        $all = sf::getModel("Experts")->selectAll()->getTotal();
        $experts = sf::getModel("Experts")->selectAll("`corporation_id` = '".input::session('roleuserid')."' and is_lock = 0");
        while($expert =$experts->getObject()){
            if($expert->getTag() == '内部专家'){
                if($expert->getHonorLevel()=='正高'){
                    $data['Q1'][2]++;
                }else{
                    $data['Q1'][3]++;
                }
                if(in_array($expert->getUserType(),$gcl)){
                    $data['Q1'][4]++;
                }elseif(in_array($expert->getUserType(),$jrgl)) {
                    $data['Q1'][5]++;
                }else{
                    $data['Q1'][6]++;
                }
                $data['Q1'][1]++;
            }else{
                if($expert->getHonorLevel()=='正高'){
                    $data['Q2'][2]++;
                }else{
                    $data['Q2'][3]++;
                }
                if(in_array($expert->getUserType(),$gcl)){
                    $data['Q2'][4]++;
                }elseif(in_array($expert->getUserType(),$jrgl)) {
                    $data['Q2'][5]++;
                }else{
                    $data['Q2'][6]++;
                }
                $data['Q2'][1]++;
            }
            if($expert->getHonorLevel()=='正高'){
                $data['Q3'][2]++;
            }else{
                $data['Q3'][3]++;
            }
            if(in_array($expert->getUserType(),$gcl)){
                $data['Q3'][4]++;
            }elseif(in_array($expert->getUserType(),$jrgl)) {
                $data['Q3'][5]++;
            }else{
                $data['Q3'][6]++;
            }
            $data['Q3'][1]++;
        }
        $data['company'] = $company;
        view::set($data);
        $html = view::getContent("unit/statistic/download");
        $pdf = PDF::setContent($html)
            ->setHeader('<p style="text-align:left;border-bottom:1px solid #808080;color:#808080;">科技创新报告</p>')
            // ->setFooter(['center'=>'- {PAGENO} -','right'=>'<img src="'.PDF::getQRcode($expert->getUserId()).'" height="50" />'])
            ->setWaterMark('科技创新报告')
//        ->download('科技创新统计.pdf');
            ->show('科技创新报告.pdf');
    }
}