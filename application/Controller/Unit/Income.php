<?php
namespace App\Controller\Unit;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Core\lang;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class Income extends BaseController
{
	
	/**
	 * 支出综合列表
	 */
	public function index()
	{
		$this->mygrid("unit/income/index");
	}
	/**
	 * 待审核的支出列表
	 */
	public function wait_list()
	{
        $addwhere = "is_lock = 4";
        if(input::session('userlevel')==4){
            $addwhere = "is_lock = 6";
        }
        $addwhere .= " and company_level = '".input::session('companylevel')."'";
		$this->mygrid("unit/income/wait_list",$addwhere);
	}
	/**
	 * 已审核的支出列表
	 */
	public function submit_list()
	{
        $this->mygrid("unit/income/submit_list","is_lock = 20 ");
	}
	/**
	 * 已退回的支出列表
	 */
	public function back_list()
	{
        $addwhere = "is_lock = 5";
        if(input::session('userlevel')==4){
            $addwhere = "is_lock = 7";
        }
        $addwhere .= " and company_level = '".input::session('companylevel')."'";
		$this->mygrid("unit/income/back_list",$addwhere);
	}
	/**
	 * 已审定的支出列表
	 */
	public function confirm_list()
	{
		$this->mygrid("unit/income/confirm_list","is_lock = 20 ");
	}
	/**
	 * 审核通过
	 */
	function doSubmitOnlyOne()
	{
		$income = sf::getModel("ProjectIncomes")->selectByIncomeId(input::getInput("mix.id"));
		if($income->isNew()) $this->error('找不到该记录！');
		if(input::getInput("post.content")){
//            $income->setIsLock(6);
//            if(input::session('userlevel')==4){
////                $income->setIsLock(9);
//                $income->setIsLock(20);
//            }
            $income->setIsLock(20);
			$income->save();
            $income->addIncome();
            $income->sendMessage();

			sf::getModel("historys")->addHistory($income->getIncomeId(),'审核通过！<br/>'.input::getInput("post.content"),'income');
			exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
		}
		view::set("income",$income);
		view::apply("inc_body","unit/income/note_submit");
		view::display("page_blank");
	}
	/**
	 * 审核通过
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个科目！',getFromUrl());
		for($i=0,$n=count($ids);$i<$n;$i++){
			$income = sf::getModel("ProjectIncomes")->selectByIncomeId($ids[$i]);
            $income->setIsLock(20);
//            $income->setIsLock(6);
//            if(input::session('userlevel')==4){
//                $income->setIsLock(20);
//            }
			$income->save();
            $income->addIncome();
            $income->sendMessage();
			sf::getModel("historys")->addHistory($income->getIncomeId(),'审核通过！','income');
		}		
		$this->success('审核成功！',getFromUrl());				
	}
	/**
	 * 审核退回
	 */
	function doRejectedOnlyOne()
	{
		$income = sf::getModel("ProjectIncomes")->selectByIncomeId(input::getInput("mix.id"));
        if($income->isNew()){
            $this->error('没有找到该支出记录');
        }
		if(input::getInput("post.content")){
            $income->setIsLock(5);
            if(input::session('userlevel')==4){
                $income->setIsLock(7);
            }
			$income->save();
            $income->removeIncome();
            $income->sendMessage();
			sf::getModel("historys")->addHistory($income->getIncomeId(),'审核退回！<br/>'.input::getInput("post.content"),'income');
			exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/退回成功！';</script>");
		}
		view::set("income",$income);
		view::apply("inc_body","unit/income/note_back");
		view::display("page_blank");
	}
	/**
	 * 审核退回
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个科目！',getFromUrl());
		for($i=0,$n=count($ids);$i<$n;$i++){
			$income = sf::getModel("ProjectIncomes")->selectByIncomeId($ids[$i]);
            $income->setIsLock(5);
            if(input::session('userlevel')==4){
                $income->setIsLock(7);
            }
			$income->save();
            $income->removeIncome();
            $income->sendMessage();
			sf::getModel("historys")->addHistory($income->getIncomeId(),'审核退回！','income');
		}
		$this->page_debug('退回成功！',getFromUrl());				
	}
	public function mygrid($tpl = 'unit/income/index',$addWhere = '1',$showMax=16)
	{
		//处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		//处理搜索
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		input::getInput("mix.is_lock") && $addWhere .= " AND is_lock = ".input::getInput("mix.is_lock");
        if(input::getInput("mix.corporation_id")){
            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
        }else{
            $parentCompanyId = getParentCompanyId(input::session('roleuserid'));
        }
        $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";
		//显示页面
		$form_vars = array('search','field','corporation_id');
		view::set("pager",sf::getModel("ProjectIncomes")->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display("page_main");
	}

	/**
	 * 项目综合列表
	 */
	public function project_list()
	{
		$addWhere = "statement = 29 AND `corporation_id` like '".input::getInput('session.companyid')."%' ";
		if(input::getInput("post.declare_year")) $addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";
		if(input::getInput("post.type_current_group")) $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";
		$this->grid('unit/income/project_list',$addWhere);
	}

    /**
     * 经费支出台账
     */
    public function payment_list()
    {
        $addWhere = "statement = 29 and `".input::session('companyfield')."` = '".input::getInput('session.roleuserid')."' ";
        if(input::getInput("post.declare_year")) $addWhere .= "AND declare_year = '".input::getInput("post.declare_year")."' ";
        if(input::getInput("post.type_current_group")) $addWhere .= "AND type_current_group = '".input::getInput("post.type_current_group")."' ";
        $this->grid('unit/income/payment_list',$addWhere);
    }

    /**
     * 经费支出台账
     */
    function showDetail()
    {
        $project = sf::getModel("Projects")->selectByProjectId(input::getInput("mix.id"));
        view::set("project",$project);
        view::apply("inc_body","unit/income/detail");
        view::display("page_blank");
    }
}