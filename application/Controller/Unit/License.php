<?php
namespace App\Controller\Unit;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class License extends BaseController
{	
	/**
	 * 专利综合搜索
	 * @return [type] [description]
	 */
	function index()
	{
        $addwhere = " statement > 0 AND `company_id` like '".input::session('companyid')."%'";
		$this->licenseGrid('unit/license/index',$addwhere);
	}
	/**
	 * 待审核专利
	 */
	function waitList()
	{
		$addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->licenseGrid('unit/license/wait_list',$addwhere);

	}
	/**
	 * 已审核专利
	 */
	function acceptList()
	{
		$addwhere = "statement IN (6,9,15)";
        if(input::session('userlevel')==4){
            $addwhere = "statement IN (9,15)";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->licenseGrid('unit/license/accept_list',$addwhere);

	}
	/**
	 * 被退回专利
	 */
	function backList()
	{
		$addwhere = "statement = 5";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 7";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->licenseGrid('unit/license/back_list',$addwhere);

	}

	/**
	 * 审核专利
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");	
		}else{
			$ids[] = input::getInput("get.id");	
		}
		if(!$ids[0])
			$this->page_debug('请至少选择一个！',getFromUrl());
		for($i=0,$n=count($ids);$i<$n;$i++){
			$license = sf::getModel("Licenses")->selectByLicenseId($ids[$i]);
			if($license->isNew()) continue;
			$license->setStatement(6);
            if(input::session('userlevel')==4){
                $license->setStatement(9);
            }
			$license->save();
			$license->sendMessage();
			sf::getModel("historys")->addHistory($license->getLicenseId(),"单位审核通过！",'license');//保存历史记录
		}
		// $this->page_debug(lang::get("Has been accepted!")."受理编号为：".$project->getAcceptId(),getFromUrl());
		$this->success('审核成功！',getFromUrl());
	}	
	/**
	 * 驳回
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");
		}else{
			$this->page_debug('请至少选择一个！',getFromUrl());	
		}
		for($i=0,$n=count($ids);$i<$n;$i++){
			$license = sf::getModel("Licenses")->selectByLicenseId($ids[$i]);
			$license->setStatement(5);
            if(input::session('userlevel')==4){
                $license->setStatement(7);
            }
			$license->save();
			$license->sendMessage();
			sf::getModel("historys")->addHistory($license->getLicenseId(),'退回许可！<br/>'.input::getInput("post.content"),'license');
		}
		$this->page_debug('退回成功！',getFromUrl());	
	}	
	/**
	 * 驳回
	 */
	function doRejectedOnlyOne()
	{
		$license = sf::getModel('Licenses')->selectByLicenseId(input::getInput('mix.id'));
		if(input::getInput("post.content")){
			$license->setStatement(5);
            if(input::session('userlevel')==4){
                $license->setStatement(7);
            }
			$license->save();
			$license->sendMessage();
			sf::getModel("historys")->addHistory($license->getLicenseId(),'退回许可！<br/>'.input::getInput("post.content"),'license');
			// $this->page_debug('退回成功！',getFromUrl());
			exit("<script>parent.location.reload();</script>");
		}
		view::set("license",$license);
		view::set("msg","退回修改");
		view::apply("inc_body","unit/license/note");
		view::display("page_blank");
	}
    /**
     * 审核通过
     */
    function doSubmitOne()
    {
        $license = sf::getModel('Licenses')->selectByLicenseId(input::getInput('mix.id'));
		if($license->isNew()) $this->error('没有找到该行业许可');
		if(input::getInput("post.content")){
			$license->setStatement(6);
			if(input::session('userlevel')==4){
				$license->setStatement(9);
			}
			$license->save();
			$license->sendMessage();
			sf::getModel("historys")->addHistory($license->getLicenseId(),'单位审核通过！','license');
			exit("<script>parent.location.reload();</script>");
		}
        view::set("license",$license);
		view::set("msg","审核通过");
        view::apply("inc_body","unit/license/note");
        view::display("page_blank");
    }
    public function licenseGrid($tpl = 'unit/license/index',$addWhere = '1',$showMax=25,$page='page_main')
	{
        //处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getMix('company_id')) $addWhere .= " AND company_id = '".input::getMix('company_id')."' ";
		//$addWhere.=" AND `".input::session('companyfield')."` = '".input::session('roleuserid')."'";
//		if(input::getInput("mix.corporation_id")){
//            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
//        }else{
//            $parentCompanyId = getParentCompanyId(input::session('roleuserid'));
//        }
//        $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";

		//将搜索条件保存以备打印或者导出
		 if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		 {
		 	//保存标记
		 	$_SESSION['hash'] = $this->hash;
		 	$_SESSION['licenses']['baseSql'] = base64_encode($addWhere);
		 	//打印
		 	$_SESSION['licenses']['sqlStr'] = base64_encode($addWhere);
		 	$_SESSION['licenses']['orderStr'] = base64_encode($addSql);
		 }else{
		 	$_SESSION['queryOptions'] = '';
		 	$addWhere = base64_decode($_SESSION['licenses']['sqlStr']);
		 }
		
		$form_vars = array('field','search','subject','user_id','license_id','statement','company_id');
		view::set("pager",sf::getModel('Licenses')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}
}