<?php
namespace App\Controller\Unit;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class fruit extends BaseController
{	
	/**
	 * 待审核成果评价
	 */
	function waitList()
	{
		$this->fruitGrid('unit/fruit/wait_list',"statement = 2 and company_level = '".input::session('companylevel')."' ");
	}
	/**
	 * 已审核
	 */
	function acceptList()
	{
		$this->fruitGrid('unit/fruit/accept_list',"statement IN (9,10) and company_level = '".input::session('companylevel')."' ");

	}
	/**
	 * 被退回
	 */
	function backList()
	{
		$this->fruitGrid('unit/fruit/back_list',"statement = 3  and company_level = '".input::session('companylevel')."' ");

	}
	/**
	 * 论文综合搜索
	 * @return [type] [description]
	 */
	function index()
	{
		$this->fruitGrid('unit/fruit/index','statement > 0');
	}
	/**
	 * 审核
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");	
		}else{
			$ids[] = input::getInput("get.id");	
		}
		if(!$ids[0])
			$this->page_debug('请至少选择一个！',getFromUrl());
		for($i=0,$n=count($ids);$i<$n;$i++){
			$fruit = sf::getModel("fruits")->selectByFruitId($ids[$i]);
			if($fruit->isNew()) continue;
			$fruit->setStatement(9);
			$fruit->save();
			sf::getModel("historys")->addHistory($fruit->getFruitId(),"单位审核通过！",'fruit');
		}
		$this->page_debug('审核成功！',getFromUrl());
	}

    /**
     * 审核通过
     */
    function doSubmitOne()
    {
        $fruit = sf::getModel('Fruits')->selectByFruitId(input::getInput('mix.id'));
        if($fruit_domain = input::getInput("post.fruit_domain")){
            if($fruit->isNew()) $this->error('没有找到该标准');
            $fruit->setFruitDomain($fruit_domain);
            $fruit->setTransform(input::getInput("post.transform"));
            $fruit->setBelong(input::getInput("post.belong"));
            $fruit->setStatement(9);
            $fruit->save();
            sf::getModel("historys")->addHistory($fruit->getFruitId(),'单位审核通过！','fruit');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/审核通过！';</script>");
        }
        view::set("fruit",$fruit);
        view::apply("inc_body","unit/fruit/note_submit");
        view::display("page_blank");
    }

	/**
	 * 驳回
	 */
	function doRejectedOnlyOne()
	{
        $fruit = sf::getModel('Fruits')->selectByFruitId(input::getInput('mix.id'));
		if(input::getInput("post.content")){
			$fruit->setStatement(3);
			$fruit->save();
			sf::getModel("historys")->addHistory($fruit->getFruitId(),'退回成果评价！<br/>'.input::getInput("post.content"),'fruit');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/退回成功！';</script>");
		}
		view::set("fruit",$fruit);
		view::apply("inc_body","unit/fruit/note");
		view::display("page_blank");
	}		
	public function fruitGrid($tpl = 'unit/fruit/index',$addWhere = '1',$showMax=25,$page='page_main')
	{
        //处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere.=" AND `".input::session('companyfield')."` = '".input::session('roleuserid')."'";
		input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
		input::getInput("mix.source") && $addWhere .= " AND `source` = '".input::getInput("mix.source")."' ";
		input::getInput("mix.level") && $addWhere .= " AND `level` = '".input::getInput("mix.level")."' ";
		input::getInput("mix.statement") && $addWhere .= " AND `statement` = '".input::getInput("mix.statement")."' ";
        $addWhere.=" AND `".input::session('companyfield')."` = '".input::getInput('session.roleuserid')."'";
        if(input::getInput("mix.first_id")){
            $addWhere .= " AND `first_id` = '".input::getInput("mix.first_id")."'";
        }
        if(input::getInput("mix.second_id") && input::getInput("mix.second_id")==input::getInput("mix.first_id")){
            $addWhere .= " AND `second_id` = '0'";
        }elseif(input::getInput("mix.second_id")){
            $addWhere .= " AND `second_id` = '".input::getInput("mix.second_id")."'";
        }
        if(input::getInput("mix.third_id") && input::getInput("mix.third_id")==input::getInput("mix.second_id")){
            $addWhere .= " AND `third_id` = '0'";
        }elseif(input::getInput("mix.third_id")){
            $addWhere .= " AND `third_id` = '".input::getInput("mix.third_id")."'";
        }
        if(input::getInput("mix.fourth_id") && input::getInput("mix.fourth_id")==input::getInput("mix.third_id")){
            $addWhere .= " AND `fourth_id` = '0'";
        }elseif(input::getInput("mix.fourth_id")){
            $addWhere .= " AND `fourth_id` = '".input::getInput("mix.fourth_id")."'";
        }

		//将搜索条件保存以备打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			//保存标记
			$_SESSION['hash'] = $this->hash;
			$_SESSION['fruits']['baseSql'] = base64_encode($addWhere);
			//打印
			$_SESSION['fruits']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['fruits']['orderStr'] = base64_encode($addSql);
		}else{
			$_SESSION['queryOptions'] = '';
			$addWhere = base64_decode($_SESSION['fruits']['sqlStr']);
		}

		$form_vars = array('subject','user_id');
		view::set("pager",sf::getModel('fruits')->getPager($addWhere,$addSql,20,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}
}