<?php
namespace App\Controller\Unit;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Soft extends BaseController
{	
	/**
	 * 专利综合搜索
	 * @return [type] [description]
	 */
	function index()
	{
        $addwhere = " statement > 0 AND (`company_id` like '".input::session('companyid')."%' or soft_id in (SELECT soft_id FROM `soft_owners` where owner_type = 'user' and user_type = 'inner' and company_id = '".input::getInput('session.roleuserid')."'))";
		$this->softGrid('unit/soft/index',$addwhere);
	}
	/**
	 * 待审核专利
	 */
	function waitList()
	{
		$addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->softGrid('unit/soft/wait_list',$addwhere);

	}
	/**
	 * 已审核专利
	 */
	function acceptList()
	{
		$addwhere = "statement IN (6,9,15)";
        if(input::session('userlevel')==4){
            $addwhere = "statement IN (9,15)";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->softGrid('unit/soft/accept_list',$addwhere);

	}
	/**
	 * 被退回专利
	 */
	function backList()
	{
		$addwhere = "statement = 5";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 7";
        }
        $addwhere .= " AND `company_id` = '".input::session('roleuserid')."' ";
		$this->softGrid('unit/soft/back_list',$addwhere);

	}

    /**
     * 导入的软著
     */
    function import_list()
    {
        $addwhere = "is_import = 1 and role in (3,4)";
        $this->softGrid('unit/soft/import_list',$addwhere);
    }

    /**
     * 软著库
     */
    function store_list()
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere = 'statement = 10';
        input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        input::getInput("mix.getway") && $addWhere .= " AND getway = '".input::getInput("mix.getway")."'";
        input::getInput("mix.right_range") && $addWhere .= " AND right_range = '".input::getInput("mix.right_range")."'";
        input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getMix('start_at') && input::getMix('end_at')){
            $addWhere .= " AND (`register_at` between '".input::getMix("start_at")."' and '".input::getMix("end_at")."')";
        }
        if(input::getInput("mix.first_id")){
            $addWhere .= " AND `first_id` = '".input::getInput("mix.first_id")."'";
        }else{
            $addWhere .= " AND `first_id` = '9B581D83-B2BA-7337-0678-A875B156B111'";
        }
        if(input::getInput("mix.second_id") && input::getInput("mix.second_id")==input::getInput("mix.first_id")){
            $addWhere .= " AND `second_id` = '0'";
        }elseif(input::getInput("mix.second_id")){
            $addWhere .= " AND `second_id` = '".input::getInput("mix.second_id")."'";
        }
        if(input::getInput("mix.third_id") && input::getInput("mix.third_id")==input::getInput("mix.second_id")){
            $addWhere .= " AND `third_id` = '0'";
        }elseif(input::getInput("mix.third_id")){
            $addWhere .= " AND `third_id` = '".input::getInput("mix.third_id")."'";
        }
        input::getInput("mix.fourth_id") && $addWhere .= " AND `fourth_id` = '".input::getInput("mix.fourth_id")."'";
        //将搜索条件保存以备打印或者导出
        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['softs']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['softs']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['softs']['orderStr'] = base64_encode($addSql);
        }else{
            $_SESSION['queryOptions'] = '';
            $_SESSION['url_str'];
            $addWhere = base64_decode($_SESSION['softs']['sqlStr']);
        }
        $form_vars = array('field','search','subject','user_id','soft_id','start_at','end_at','first_id','second_id','third_id','fourth_id','getway','right_range','statement');
        view::set("pager",sf::getModel('Softs')->getPager($addWhere,$addSql,25,'','',$form_vars));
        view::apply("inc_body",'unit/soft/store_list');
        view::display('page_main');
    }
	/**
	 * 审核专利
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");	
		}else{
			$ids[] = input::getInput("get.id");	
		}
		if(!$ids[0])
			$this->page_debug('请至少选择一个！',getFromUrl());
		for($i=0,$n=count($ids);$i<$n;$i++){
			$soft = sf::getModel("Softs")->selectBySoftId($ids[$i]);
			if($soft->isNew()) continue;
			$soft->setStatement(6);
            if(input::session('userlevel')==4){
                $soft->setStatement(9);
            }
			$soft->save();
			$soft->sendMessage();
			sf::getModel("historys")->addHistory($soft->getSoftId(),"审核成功！",'soft');//保存历史记录
		}
		// $this->page_debug(lang::get("Has been accepted!")."受理编号为：".$project->getAcceptId(),getFromUrl());
		$this->success('审核成功！',getFromUrl());
	}	
	/**
	 * 驳回
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")){
			$ids = input::getInput("post.select_id");
		}else{
			$this->page_debug('请至少选择一个！',getFromUrl());	
		}
		for($i=0,$n=count($ids);$i<$n;$i++){
			$soft = sf::getModel("Softs")->selectBySoftId($ids[$i]);
            if($soft->isNew()) continue;
			$soft->setStatement(5);
            if(input::session('userlevel')==4){
                $soft->setStatement(7);
            }
			$soft->save();
			$soft->sendMessage();
			sf::getModel("historys")->addHistory($soft->getSoftId(),'退回软著！<br/>'.input::getInput("post.content"),'soft');
		}
		$this->page_debug('退回成功！',getFromUrl());	
	}	
	/**
	 * 驳回
	 */
	function doRejectedOnlyOne()
	{
		$soft = sf::getModel('Softs')->selectBySoftId(input::getInput('mix.id'));
        if($soft->isNew()) $this->error('没有找到该软著');
		if(input::getInput("post.content")){
			$soft->setStatement(5);
            if(input::session('userlevel')==4){
                $soft->setStatement(7);
            }
			$soft->save();
			$soft->sendMessage();
			sf::getModel("historys")->addHistory($soft->getSoftId(),'退回专利！<br/>'.input::getInput("post.content"),'soft');
			// $this->page_debug('退回成功！',getFromUrl());
			exit("<script>parent.location.reload();</script>");
		}
		view::set("soft",$soft);
		view::apply("inc_body","unit/soft/note");
		view::display("page_blank");
	}
    /**
     * 审核通过
     */
    function doSubmitOne()
    {
        $soft = sf::getModel('Softs')->selectBySoftId(input::getInput('mix.id'));
        if($soft->isNew()) $this->error('没有找到该软著');
        if(input::post()){
            $soft->setStatement(6);
            if(input::session('userlevel')==4){
                $soft->setStatement(9);
            }
    		$soft->save();
			$soft->sendMessage();
            sf::getModel("historys")->addHistory($soft->getSoftId(),'审核通过！','soft');
            exit("<script>parent.location.reload();</script>");
        }
        view::set("soft",$soft);
        view::set("msg",'审核通过');
        view::apply("inc_body","unit/soft/note");
        view::display("page_blank");
    }
	public function softGrid($tpl = 'unit/soft/index',$addWhere = '1',$showMax=25,$page='page_main')
	{
        //处理排序
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if(input::getInput("mix.search") && input::getInput("mix.field")==="tag"){
            $itemIds=sf::getModel("Tags")->getItemIdsByTypeAndTag("soft",input::getInput("mix.search"));
            foreach($itemIds as $key=>$itemId){
                $itemIds[$key]="'$itemId'";
            }
            $inSql=count($itemIds)?implode(",",$itemIds):"''";
            $addWhere.=" AND `soft_id` IN ($inSql)";
        }elseif(input::getInput("mix.search") && input::getInput("mix.field")==="user_name"){
            $addWhere .= " AND `soft_id` IN (SELECT soft_id FROM `soft_owners` where subject like '%".input::getInput("mix.search")."%')";
        }elseif(input::getInput("mix.search") && input::getInput("mix.field")){
            $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        }
        input::getInput("mix.fruit_domain") && $addWhere .= " AND `fruit_domain` like '%".input::getInput("mix.fruit_domain")."%'";
		input::getInput("mix.getway") && $addWhere .= " AND getway = '".input::getInput("mix.getway")."'";
		input::getInput("mix.right_range") && $addWhere .= " AND right_range = '".input::getInput("mix.right_range")."'";
		input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getMix('company_id')) $addWhere .= " AND company_id = '".input::getMix('company_id')."' ";
		//$addWhere.=" AND `".input::session('companyfield')."` = '".input::session('roleuserid')."'";
//		if(input::getInput("mix.corporation_id")){
//            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
//        }else{
//            $parentCompanyId = getParentCompanyId(input::session('roleuserid'));
//        }
//        $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";
//        $addWhere .= " AND (corporation_id like '".$parentCompanyId."%' or soft_id in (SELECT soft_id FROM `soft_owners` where owner_type = 'user' and user_type = 'inner' and company_id = '".input::getInput('session.roleuserid')."')) ";
        if(input::getMix('start_at') && input::getMix('end_at')){
            $addWhere .= " AND (`register_at` between '".input::getMix("start_at")."' and '".input::getMix("end_at")."')";
        }
		//将搜索条件保存以备打印或者导出
		 if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		 {
		 	//保存标记
		 	$_SESSION['hash'] = $this->hash;
		 	$_SESSION['softs']['baseSql'] = base64_encode($addWhere);
		 	//打印
		 	$_SESSION['softs']['sqlStr'] = base64_encode($addWhere);
		 	$_SESSION['softs']['orderStr'] = base64_encode($addSql);
		 }else{
		 	$_SESSION['queryOptions'] = '';
		 	$addWhere = base64_decode($_SESSION['softs']['sqlStr']);
		 }
		$form_vars = array('field','search','subject','user_id','soft_id','start_at','end_at','fruit_domain','getway','right_range','statement','company_id');
		view::set("pager",sf::getModel('Softs')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
		view::apply("inc_body",$tpl);
		view::display($page);
	}
}