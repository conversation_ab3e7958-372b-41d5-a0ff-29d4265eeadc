<?php
namespace App\Controller;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class idea extends BaseController
{	
	function index()
	{
		view::set("pager",sf::getModel("Ideas")->getPager("user_id= '".input::getInput("session.userid")."' ","ORDER BY created_at DESC",4));
		view::apply("inc_body","idea/index");
		view::display("page");
	}
	
	function edit()
	{
		$idea = sf::getModel("Ideas",input::getInput("mix.id"));
		
		if(input::getInput("post.content"))
		{
			input::getInput("post.content") && $idea->setContent(input::getInput("post.content"));
			
			$idea->setUserId(input::getInput("session.userid"));
			$idea->setUserName(input::getInput("session.nickname"));
			$idea->setCreatedAt(date("Y-m-d H:i:s"));
			$idea->save();
			exit("<script>parent.location.reload();</script>");
		}
		
		view::set("idea",$idea);
		view::apply("inc_body","idea/edit");
		view::display("page");
	}
	
}
?>