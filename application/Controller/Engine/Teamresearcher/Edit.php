<?php
namespace App\Controller\Engine\Teamresearcher;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
use Sofast\Core\Config;

class edit extends BaseController
{	
    private $project = null;
    private $configs = [];
    /**
     * 部件模式
     */
    private $type = 'award';

    function load(){
        $this->project = sf::getModel("Awards")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("type",$this->type);
        $this->view->set("configs",$this->project->getWidgetConfigs('researcher',$this->type));
    }

    function index(){
        $this->edit();
    }
	/**
	 * 附件列表
	 */
	function edit(){
        if($data = input::getInput('post')){
            $researcher = $this->project->researcher($this->type);
            $data['subject'] && $researcher->setSubject($data['subject']);
            $data['sex'] && $researcher->setSex($data['sex']);
            $data['birthday'] && $researcher->setBirthday($data['birthday']);
            $data['degree'] && $researcher->setDegree($data['degree']);
            $data['degree_time'] && $researcher->setDegreeTime($data['degree_time']);
            $data['title'] && $researcher->setTitle($data['title']);
            $data['birtharea'] && $researcher->setBirtharea($data['birtharea']);
            $data['nationality'] && $researcher->setNationality($data['nationality']);
            $data['nation'] && $researcher->setnation($data['nation']);
            $data['political_climate'] && $researcher->setPoliticalClimate($data['political_climate']);
            $data['age'] && $researcher->setAge($data['age']);
            $data['education'] && $researcher->setEducation($data['education']);
            $data['final_degree_area'] && $researcher->setFinalDegreeArea($data['final_degree_area']);
            $data['phone'] && $researcher->setPhone($data['phone']);
            $data['mobile'] && $researcher->setMobile($data['mobile']);
            $data['email'] && $researcher->setEmail($data['email']);
            $data['address'] && $researcher->setAddress($data['address']);
            $data['administrative_post'] && $researcher->setAdministrativePost($data['administrative_post']);
            $data['professional'] && $researcher->setProfessional($data['professional']);
            $data['certificate_type'] && $researcher->setCertificateType($data['certificate_type']);
            $data['identification_number'] && $researcher->setIdentificationNumber($data['identification_number']);
            $data['is_returnee'] && $researcher->setIsReturnee($data['is_returnee']);
            $data['back_time'] && $researcher->setBackTime($data['back_time']);
            $data['is_technology_owner'] && $researcher->setIsTechnologyOwner($data['is_technology_owner']);
            $data['type'] && $researcher->setType($data['type']);
            $data['capital_input'] && $researcher->setCapitalInput($data['capital_input']);
            $data['learning_experience'] && $researcher->setLearningExperience($data['learning_experience']);
            $data['work_experience'] && $researcher->setWorkExperience($data['work_experience']);
            $data['fax'] && $researcher->setFax($data['fax']);
            $data['subject_id1'] && $researcher->setSubjectId1($data['subject_id1']);
            $data['subject_id2'] && $researcher->setSubjectId2($data['subject_id2']);
            $data['industrial'] && $researcher->setIndustrial($data['industrial']);
            $data['honour'] && $researcher->setHonour($data['honour']);
            $data['talent_plan'] && $researcher->setTalentPlan($data['talent_plan']);
            $data['major_industries'] && $researcher->setMajorIndustries($data['major_industries']);
            $data['work_year'] && $researcher->setWorkYear($data['work_year']);
            $data['research_cat'] && $researcher->setResearchCat($data['research_cat']);
            $data['work_company'] && $researcher->setWorkCompany($data['work_company']);
            $data['research_direction'] && $researcher->setResearchDirection($data['research_direction']);
            $data['achievement'] && $researcher->setAchievement($data['achievement']);
            $data['instructor'] && $researcher->setInstructor($data['instructor']);
			$data['more'] && $researcher->setMore($data['more']);
            $researcher->save();
            if($data['linkman']){
                $baseModel = $this->project->getBaseinfo();
                $baseModel->setLinkmanTel($data['linkman']['linkman_tel']);
                $baseModel->setLinkmanMobile($data['linkman']['linkman_mobile']);
                $baseModel->save();
            }
            $this->closeWindow();
        }

        $this->view->set($data);
        $this->view->apply("inc_body","Edit/edit");
        $this->view->display('page_blank');
	}	
}
?>