<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <?=btn("back")?>
    <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存资料</a>
    <p style="clear:both;"></p>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption>
        预期实施成效
        </caption>
        <?php $ayqcx = $target->getYqcx();?>
        <?php $yqcx = $target->getYqcxTask();?>
        <tr>
          <td><textarea name="yqcx" rows="20" style="width:99.9%;"<?php if(strlen($ayqcx) > 10):?> readonly="readonly"<?php endif;?>><?php if($yqcx != ''):?><?=$yqcx?><?php else:?><?=$ayqcx?><?php endif;?></textarea></td>
        </tr>
      </table>
      <div class="ex_tools">
        <input type="submit" name="button2" id="button2" value="保存资料" />
      </div>
    </form>
  </div>
</div>
