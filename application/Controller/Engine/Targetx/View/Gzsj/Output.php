<table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap">
  <caption>
  项目负责人工作时间 <?php if($configs['action']['edit']):?><span style="float:right">
  <?=btn("widget","编辑",site_url("engine/targetx/edit/gzsj/id/".$project->getProjectId()),"edit",$project->getProjectId(),$widget_name,site_url("engine/".($configs['engine']?:'tasker')."/reloadWidget"))?>
  </span><?php endif;?>
  </caption>
  <?php $gzsj = $target->getGzsjTask();?>
  <tr>
    <td width="49%" align="center"><strong>时间</strong></td>
    <td width="51%" align="center"><strong>项目管理期内每年全职在川工作时间（月）</strong></td>
  </tr>
  <?php for($i=0,$n=count($gzsj['sj']['yq']);$i<$n;$i++):?>
  <tr>
    <td height="35"><?=$gzsj['sj']['yq'][$i]?></td>
    <td width="51%"><?=$gzsj['sl']['yq'][$i]?></td>
  </tr>
  <?php endfor;?>
</table>
