<!DOCTYPE HTML>
<html>
<head>
<meta name="renderer" content="webkit">
<meta http-equiv="X-UA-Compatible" content="IE=edge,Chrome=1">
<meta charset="utf-8">
<title>
<?=$project->getSubject()?>
--任务书</title>
<link rel="stylesheet" href="<?=site_path("css/template.css")?>">
<?php if($download != 'yes'):?>
<meta name="Description" content="<?=$project->getSubject()?>任务书">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
<!-- bootstrap & fontawesome -->
<link rel="stylesheet" href="<?=site_path("assets/css/bootstrap.min.css")?>">
<link rel="stylesheet" href="<?=site_path("assets/css/font-awesome.min.css")?>">

<!--[if lte IE 9]>
<link rel="stylesheet" href="<?=site_path("assets/css/ace-ie.min.css")?>" />
<![endif]-->
<!-- ace settings handler -->
<script src="<?=site_path("assets/js/ace-extra.min.js")?>"></script>
<!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->
<!--[if lte IE 8]>
<script src="<?=site_path("assets/js/html5shiv.min.js")?>"></script>
<script src="<?=site_path("assets/js/respond.min.js")?>"></script>
<![endif]-->
<!-- basic scripts -->
<!--[if !IE]> -->
<script type="text/javascript">window.jQuery || document.write("<script src='<?=site_path("assets/js/jquery.min.js")?>'>"+"<"+"/script>");</script>
<!-- <![endif]-->

<!--[if IE]>
<script type="text/javascript">
 window.jQuery || document.write("<script src='<?=site_path("assets/js/jquery1x.min.js")?>'>"+"<"+"/script>");
</script>
<![endif]-->
<?php endif;?>
<base href="http://*************/">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>

<body class="project_review">
<?php if($download != 'yes'):?>
<?php include("Navbar.php");?>
<?php endif;?>
<div class="main">
  <bookmark content="封面" />
  <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11" style="border:none;">
    <tr>
      <td height="5" align="right" width="16%" class="def11" ><span class="STYLE8">申报编号：</span></td>
      <td class="def2" align="left" width="20%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getAcceptId()?></td>
      <td valign="bottom" class="def11" align="right" width="14%"></td>
      <td valign="bottom" class="def11" align="left" width="14%"></td>
      <td valign="bottom" class="def11" align="right" width="14%"><span class="STYLE8">立项编号：</span></td>
      <td class="def2"  align="left" width="22%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getRadicateId()?></td>
    </tr>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <p align="center"><span class="STYLE4"><strong>四川省科技计划项目 <br>
    任务合同书<br>
    </strong>
    (重点研发项目)</span></p>
  <p align="center"><br>
  </p>
  <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def" style="border:none;">
    <tr>
      <td width="29%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">项目名称：</span></td>
      <td width="50%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getSubject()?>
      </span></td>
      <td rowspan="9" align="right" class="def1" width="10%"></td>
    </tr>
    <tr>
      <td width="29%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">承担单位：</span></td>
      <td class="def" align="left" width="50%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getCorporationName()?>
        </span>&nbsp;<strong>（盖章）</strong></td>
    </tr>
    <tr>
      <td width="29%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">项目负责人：</span></td>
      <td class="def" align="right" width="50%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><strong>（签字）</strong></td>
    </tr>
    <tr>
      <td width="29%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">推荐单位：</span></td>
      <td width="50%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getDepartmentName()?>
      </span></td>
    </tr>
    <tr>
      <td height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">立项经费：</span></td>
      <td width="50%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
        <?=$project->getRadicateMoney()?>
      </span>&nbsp;<strong>（万元）</strong></td>
    </tr>
    <tr>
      <td width="29%" height="60" align="right" valign="bottom" class="def1"><span class="STYLE8">项目起止年限：</span></td>
      <td class="def" align="left" width="50%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
        <?=$project->getStartAt()?>
        至
        <?=$project->getEndAt()?>
      </span></td>
    </tr>
  </table>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <p>&nbsp;</p>
  <p align="center"><span class="STYLE5">四川省科学技术厅制</span></p>
  <p align="center">&nbsp;</p>
  <pagebreak resetpagenum="1"/>
  <bookmark content="填写和打印说明" />
  <p style="font-size:25px; text-align:center;"><strong>填 报 说 明</strong></p>
  <p align="center">&nbsp;</p>
  <p style="text-indent:2em;">1.填写任务合同书各项内容应实事求是，认真填写，表述明确。外来语要同时用原文和中文表达，第一次出现的缩略词，须注明全称。</p>
  <p style="text-indent:2em;">2.任务合同书的各个部分都必须填写，原则上不能有空白；确实无法填写的内容，请填"无"或"0"。</p>
  <p style="text-indent:2em;">3.任务合同书是项目经费拨付、中期检查、绩效评价（验收）的依据。任务合同书的内容应参照项目申报书填写，各项指标不能调减，可以调增。</p>
  <p style="text-indent:2em;">4.任务合同书必须通过四川省科技信息管理系统在线填写、上报，并经承担单位、推荐单位和四川省科技厅审核通过后签订，必须确保网上的四川省科技信息管理系统的电子文档与最终打印稿一致。
  <p style="text-indent:2em;">5.项目负责人将任务合同书打印一式四份纸质文档， A4纸，左侧装订，不得加用塑料等额外装订材料。由承担单位和推荐单位审核签署意见并加盖公章后，报送四川省科技厅相关处室进行纸质文档和网上的审核签署。纸质文档盖"四川省科学技术厅科研项目合同专用章"后，四川省科技厅存档一份，另三份返项目单位归档（推荐单位一份、承担单位一份、项目负责人一份）。</p>
  <p style="text-indent:2em;">6.任务合同书是四川省科技厅与项目承担各方的约束性文本，具有合同效力，其中四川省科技厅为甲方，项目承担单位为乙方，推荐单位为丙方。任务合同书受《中华人民共和国合同法》、《四川省科技计划项目管理办法》等相关法律法规和管理制度保护，由四川省科技厅负责解释。</p>
  <pagebreak />
  <bookmark  content="一、项目信息表" />
  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">一、项目信息表</div>
  <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="abc" style="overflow:wrap">
    <tr>
      <td width="90" height="40" align="center">项目名称</td>
      <td colspan="3"><?=$project->getSubject()?></td>
    </tr>
    <tr>
      <td width="90" height="40" align="center">起始时间</td>
      <td width="270"><?=$project->getStartAt()?></td>
      <td width="108" align="center">终止时间</td>
      <td width="202"><?=$project->getEndAt()?></td>
    </tr>
     <tr>
      <td width="90" height="40" align="center">知识产权</td>
      <td colspan="3"><?=getCheckedStr(['申报单位独占', '相关单位共享'], $project->getBaseinfo()->getData('patent_desc')) ?></td>
    </tr>
    </table>
    <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="abc" style="overflow:wrap">
    <caption>第一承担单位</caption>
	<?php $company = $project->getCompany(true);?>
    <tr>
      <td width="90" height="40" align="center">单位名称</td>
      <td width="270"><?= $project->getCorporationName() ?></td>
      <td width="112" align="center">社会信用代码</td>
      <td width="197"><?= $company->getCode() ?></td>
    </tr>
    <tr>
      <td height="40" align="center">单位地址</td>
      <td><?= $company->getAddress() ?></td>
      <td width="112" align="center">邮编</td>
      <td><?= $company->getPostcode() ?></td>
    </tr>
    <tr>
      <td height="40" align="center">职工人数</td>
      <td><?= $company->getStaff('count') ? intval($company->getStaff('count')) : $project->getCorporation()->getPersoncount() ?> 人</td>
      <td align="center">单位性质</td>
      <td><?= $company->getData('property') ?></td>
    </tr>
    <tr>
      <td height="40" align="center">单位负责人</td>
      <td><?=$project->getCorporation()->getPrincipal()?></td>
      <td align="center">推荐单位</td>
      <td><?= $company->getDepartmentName() ?></td>
    </tr>
    <tr>
      <td height="40" align="center">联系人</td>
      <td><?= $company->getLinkman('name') ?></td>
      <td align="center">联系部门</td>
      <td><?= $company->getPrincipal('department') ?></td>
    </tr>
    <tr>
      <td height="40" align="center">联系人手机</td>
      <td><?=$company->getLinkman('mobile') ?></td>
      <td align="center">联系人电话</td>
      <td><?= $company->getLinkman('tel') ?></td>
    </tr>
  </table>
  <?php if(!$project->getBudgetBook(true)->isNew()):?>
  <?=$project->getBudgetBook()->getPage('cooperation')?>
  <?php endif;?>
  <table width="680" border="0" align="center" cellpadding="1" cellspacing="0" class="abc" style="overflow:wrap">
    <caption>项目负责人</caption>
    <tr>
      <td width="90" height="42" align="center">姓名</td>
      <td width="165"><?=$project->researcher()->getSubject()?></td>
      <td width="67" align="center">性别</td>
      <td width="111"><?=$project->researcher()->getSex()?></td>
      <td width="89" align="center">出生年月</td>
      <td width="133"><?=$project->researcher()->getBirthday()?></td>
    </tr>
    <tr>
      <td width="90" height="40" align="center">学历/学位</td>
      <td><?=$project->researcher()->getEducation()?>/
      <?=$project->researcher()->getDegree()?></td>
      <td align="center">职称</td>
      <td><?=$project->researcher()->getTitle()?></td>
      <td align="center">手机</td>
      <td><?=$project->researcher()->getMobile()?></td>
    </tr>
    <tr>
      <td width="90" height="40" align="center">从事专业</td>
      <td colspan="5"><?=$project->researcher()->getProfessional()?></td>
    </tr>
  </table>
  <?=$project->widgets('team','task')?>
  <div class="div_table" style="width: 680px;margin: 0 auto">
	<p class="div_table_title"><strong>项目概述</strong></p>
	<p class="div_table_content"><?= showText($project->getSummary()) ?></p>
  </div>
  <?php if($download != 'yes'):?>
  <pagebreak />
  <?php endif;?>
  <bookmark content="二、项目研究主要目标、研究内容、技术关键、技术路线和应用方案" />
  <?=$project->widgets('summary','task')?>
  <?php if($download == 'yes'):?>
  <pagebreak />
  <?php endif;?>
  <?=$project->widgets('summary_more','task')?>
  <pagebreak />
  <bookmark content="三、项目的考核内容和考核指标" />
  <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">三、项目的考核内容和考核指标</div>
  <?=$project->widgets('target','task')?>
  <br />
  <bookmark content="四、计划进度和阶段目标" />
  <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">四、计划进度和阶段目标<small>（以半年为单位，叙述项目的进度安排和阶段目标任务。）</small></div>
  <?=$project->widgets('schedule','task')?>
  <pagebreak />
  <bookmark content="五、项目固定研究人员基本情况表" />
  <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">五、项目固定研究人员基本情况表</div>
  <?=$project->widgets('member','task')?>
  <pagebreak />
  <bookmark content="六、项目经费预算" />
  <p style="font-size:20px; font-weight:bold;font-family:'黑体';">六、项目经费预算</p>
  <?=$project->widgets('budget','task')?>
  <pagebreak />
  <bookmark content="七、项目承担单位承诺书" />
  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">七、项目承担单位承诺书</div>
  <div style=" font-size:16px;">
  <p>&nbsp;</p>
    <p style="text-indent:2em;">1．我单位保证在项目实施（包括项目申请、评估评审、检查、项目执行、资源汇交、验收等过程）中所提交的材料真实、准确、有效。</p>
    <p style="text-indent:2em;">2．我单位将严格履行《四川省科技计划项目管理办法》、《四川省科技计划项目专项资金管理办法》等项目及经费管理办法文件规定，组织实施管理机构的职责和《项目任务合同书》中的各项约定，承诺项目经费专款专用、单独核算，为项目实施提供必要的条件和进行有效的管理与监督。</p>
    <p style="text-indent:2em;">3．我单位已按照《国家科技计划（专项、基金等）严重失信行为记录暂行规定》的规定建立了规范科研行为、调查处理科研不端行为的相关制度。</p>
    <p style="text-indent:2em;">4．我单位保证严肃调查处理或配合相关调查机构调查处理在实施项目过程中发现的科研不端行为，并及时向推荐单位和四川省科技厅报告相关调查处理结果。</p>
    <p style="text-indent:2em;">5．我单位已对任务合同书的内容和密级进行了审核，项目所属密级符合《中华人民共和国保守国家秘密法》、《科学技术保密规范》及《对外科技交流保密提醒制度》中的密级要求和条件，保证严格遵守国家有关保密规定，在科研活动和对外合作中不泄露国家秘密。</p>
    <p style="text-indent:2em;">6、我单位保证在项目执行期间及时做好科技报告的呈交工作，在项目完成后1年内做好项目验收工作，如项目通过验收或通过科技成果鉴定，及时做好项目的科技成果登记工作。</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p style="text-align:right;">项目承担单位盖章：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
    <p>&nbsp;</p>
    <p style="text-align:right"> 年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;</p>
  </div>
  <pagebreak />
  <bookmark content="八、项目研究人员承诺书" />
  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">八、项目研究人员承诺书</div>
  <div style=" font-size:16px;">
  <p>&nbsp;</p>
    <p style="text-indent:2em;">1．本人承诺在项目实施（包括项目评估评审、检查、项目执行、资源汇交、验收等过程）中，遵守科学道德和诚信要求，严格执行《四川省科技计划项目管理办法》、《四川省科技计划项目专项资金管理办法》等相关科技计划管理及经费管理办法规定和《项目任务合同书》中的约定，不发生下列科研不端行为：</p>
    <p style="text-indent:2em;">（1）在职称、简历以及研究基础等方面提供虚假信息；</p>
    <p style="text-indent:2em;">（2）抄袭、剽窃他人科研成果；</p>
    <p style="text-indent:2em;">（3）捏造或篡改科研数据；</p>
    <p style="text-indent:2em;">（4）在涉及人体研究中，违反知情同意、保护隐私等规定；</p>
    <p style="text-indent:2em;">（5）违反医学伦理和实验动物管理规范；</p>
    <p style="text-indent:2em;">（6）其他科研不端行为。</p>
    <p style="text-indent:2em;">2．如本人被举报在项目实施中存在科研不端行为，将积极配合相关调查机构组织开展的调查。</p>
    <p style="text-indent:2em;">3、本人承诺严格遵守《中华人民共和国保守国家秘密法》、《科学技术保密规范》及《对外科技交流保密提醒制度》，在科研活动和对外合作中不泄露国家秘密。</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>项目负责人签字：</p>
    <p>项目参与人签字：</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p style="text-align:right"> 年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p style="text-align:right;">项目承担单位盖章：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
    <p>&nbsp;</p>
    <p style="text-align:right"> 年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;</p>
  </div>
  <pagebreak />
  <bookmark content="九、任务合同书签订各方盖章及意见" />
  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">九、任务合同书签订各方盖章及意见</div>
  <p>&nbsp;</p>
  <?php if($project->getIsShift() == 2):?>
  <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap; font-size:16px;">
    <tr>
      <td width="37" rowspan="2" align="center">甲方</td>
      <td width="171" height="50" align="center">单位名称</td>
      <td width="244"><?=$project->getDepartmentName()?></td>
      <td width="197" align="center" valign="middle" style="border-bottom:none;">（单位公章）</td>
    </tr>
    <tr>
      <td height="50" align="center">电话及传真</td>
      <td>&nbsp;</td>
      <td align="right" valign="middle" style="border-top:none;">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;</td>
    </tr>
    <tr>
      <td rowspan="7" align="center">乙方</td>
      <td height="50" align="center">承担单位名称</td>
      <td><?=$project->getCorporationName()?></td>
      <td valign="middle" style="border-bottom:none">&nbsp;</td>
    </tr>
    <tr>
      <td height="50" align="center">地址及邮编</td>
      <td><?=$project->getCorporation()->getAddress()?>
        ,
        <?=$project->getCorporation()->getPostalcode()?></td>
      <td valign="bottom" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="50" align="center">电话及传真</td>
      <td>&nbsp;</td>
      <td align="center" valign="middle" style="border-bottom:none; border-top:none;">（单位公章）</td>
    </tr>
    <tr>
      <td height="50" align="center">开户银行</td>
      <td><?=$project->getCorporation()->getBankName()?></td>
      <td align="center" valign="middle" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="50" align="center">帐&nbsp;&nbsp;号</td>
      <td><?=$project->getCorporation()->getBankId()?></td>
      <td align="center" valign="middle" style="border-bottom:none; border-top:none;">（合作单位公章）</td>
    </tr>
    <tr>
      <td rowspan="2" align="center"><span style="border-bottom:none; border-top:none;">合作单位名称</span></td>
      <td rowspan="2" valign="top"><?=$project->getCooperation(true,'<br />',true)?></td>
      <td height="149" valign="bottom" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="50" align="right" valign="middle" style="border-top:none;">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;</td>
    </tr>
  </table>
  <?php else:?>
  <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="abc" style="overflow:wrap; font-size:16px;">
    <tr>
      <td width="37" rowspan="5" align="center">甲方</td>
      <td width="170" height="40" align="center">单位名称</td>
      <td width="246">四川省科学技术厅</td>
      <td width="196" valign="middle" style="border-bottom:none">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" align="center">分管厅领导</td>
      <td width="246" align="right">（签章）</td>
      <td width="196" rowspan="2" align="right" valign="top" style="border-bottom:none; border-top:none;"><p align="center">（项目合同章）&nbsp;&nbsp;&nbsp;&nbsp;</p>
        <p align="right">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;</p></td>
    </tr>
    <tr>
      <td height="40" align="center">分管处室负责人</td>
      <td width="246" align="right">&nbsp;（签章）</td>
    </tr>
    <?php $manager = $project->manager(true);?>
    <tr>
      <td height="40" align="center">项目管理人</td>
      <td width="246"><?=$manager->getUserFullName()?>&nbsp;</td>
      <td width="196" align="right" valign="bottom" style="border-bottom:none; border-top:none;"><p align="center">&nbsp;</p></td>
    </tr>
    <tr>
      <td height="58" align="center">电话及传真</td>
      <td width="246"><?=$manager->getUserPhone()?>&nbsp;</td>
      <td width="196" align="right" valign="bottom" style="border-top:none;"><p align="center">（预算合同章）&nbsp;&nbsp;&nbsp;&nbsp;</p>
        <p align="right">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;</p></td>
    </tr>
    <tr>
      <td rowspan="8" align="center">乙方</td>
      <td height="40" align="center">承担单位名称</td>
      <td><?=$project->getCorporationName()?></td>
      <td valign="middle" style="border-bottom:none">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" align="center">地址及邮编</td>
      <td><?=$project->getCorporation()->getAddress()?>
        ,
        <?=$project->getCorporation()->getPostalcode()?></td>
      <td align="center" valign="bottom" style="border-bottom:none; border-top:none;">（承担单位公章）</td>
    </tr>
    <tr>
      <td height="40" align="center">电话及传真</td>
      <td><?=$project->getCorporation()->getPhone()?></td>
      <td rowspan="2" align="center" valign="middle" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" align="center">开户银行</td>
      <td><?=$project->getCorporation()->getBankName()?></td>
    </tr>
    <tr>
      <td height="40" align="center">帐&nbsp;&nbsp;号</td>
      <td><?=$project->getCorporation()->getBankId()?></td>
      <td align="center" valign="middle" style="border-bottom:none; border-top:none;">（合作单位公章）</td>
    </tr>
    <tr>
      <td rowspan="3" align="center"><span style="border-bottom:none;">合作单位名称</span></td>
      <td rowspan="3" valign="top"><?=$project->getCooperation(true,'<br />',true)?></td>
      <td height="43" align="center" valign="middle" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="47" valign="bottom" style="border-bottom:none; border-top:none;">&nbsp;</td>
    </tr>
    <tr>
      <td height="33" align="right" valign="middle" style="border-top:none;">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;</td>
    </tr>
    <tr>
      <td rowspan="2" align="center">丙方</td>
      <td height="40" align="center">推荐单位名称</td>
      <td><?=$project->getDepartmentName()?></td>
      <td align="center" valign="middle" style="border-bottom:none;">（单位公章）</td>
    </tr>
    <tr>
      <td height="40" align="center">电话及传真</td>
      <td>&nbsp;</td>
      <td align="right" valign="middle" style="border-top:none;">年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;</td>
    </tr>
  </table>
  <?php endif;?>
  <pagebreak  />
  <bookmark content="十、附加条款" />
  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">十、附加条款</div>
  <div style="font-size:13px;">
    <?php if($project->getIsShift() == 2):?>
    <p>&nbsp;</p>
    <p style="text-indent: 2em;">1.任务各方共同遵守《四川省科技计划项目管理办法》、《四川省科技计划项目专项资金管理办法》等相关管理办法，以下简称《办法》，并自愿接受其约束。</p>
    <p style="text-indent: 2em;">2.任务合同书下达后，项目负责人全面负责项目的实施工作，各成员必须严格履行相应职责。</p>
    <p style="text-indent: 2em;">3.项目实施过程中，项目的研究计划、主要研究人员、研究任务、经费预算等需要调整时，项目负责人应根据《办法》中有关规定，向甲方或乙方提出变更理由及其内容的申请报告，经甲方或乙方审查通过后实施。未经批准，项目负责人必须按原任务合同书履行。</p>
    <p style="text-indent: 2em;">4.乙方必须接受甲方对项目进度及经费使用的监督和检查，并按甲方要求及时提供相关执行情况报告和相关统计报表，逾期不报，甲方有权暂停资助或终止项目。</p>
    <p style="text-indent: 2em;">5.乙方因某种原因致使无法按计划执行而主动要求结题时，乙方应在规定时限内提出申请；如乙方未主动提出申请，甲方有权根据调查情况终止任务。</p>
    <p style="text-indent: 2em;">6.任务执行过程中，若甲方无故终止任务，甲方无权追回拨给乙方的经费和乙方所购置的物资，甲方并承担善后处理所发生的费用。</p>
    <p style="text-indent: 2em;">7.乙方应遵守任务合同书的约定，及时呈送符合撰写标准的科技报告，并获得科技报告收录证明。乙方可根据项目具体情况提出科技报告的保密和解密期限要求。乙方应在项目验收后按规定进行成果登记。</p>
    <p style="text-indent: 2em;">8.任务到期完成后，乙方必须在三个月内完成验收准备，主动提交验收材料，并在任务到期后1年内完成项目验收手续。</p>
    <p style="text-indent: 2em;">9.项目研究成果及其形成的知识产权归项目承担单位所有。在特定情况下，国家根据需要保留无偿使用、开发、使之有效利用和获取收益的权利。乙方申报成果、专利、发表论文时需注明由"四川省科技计划资助"（英文标注："Supported by Sichuan Science and Technology Program"）。乙方因实施本项目而引起的各种知识产权纠纷由乙方负全部责任。</p>
    <p style="text-indent: 2em;">10.乙方对项目执行过程中产生的研究成果须及时采取知识产权保护措施，依法取得相关知识产权，并予以有效管理和充分使用。</p>
    <p style="text-indent: 2em;">11.乙方指定项目组成员___________________为本项目档案员，负责本项目档案的收集、积累和保存工作，要做到随时收集、编号登记、入袋保管，归档的重点是项目各个阶段形成的不同载体的文件材料和技术资料，特别是研究实验阶段形成的作为成果依据的原始材料。</p>
    <p style="text-indent: 2em;">12.乙方在项目实施过程中应建立相应的规章制度，加强安全管理，确保人员及设备安全，并对科研安全负全部责任。</p>
    <p style="text-indent: 2em;">13.乙方在项目实施过程中，应遵守科研诚信、科技行为廉洁的有关规定，不得向甲方、丙方工作人员行贿或报销应由个人支付的任何费用，被纪检监察机关或司法机关查证属实的，甲方有权终止项目实施并追缴拨付的全部科研经费。</p>
    <p style="text-indent: 2em;">14.任务合同书是对签订各方都有法定约束力的协议，自各方签字盖章之日起正式生效，若有争议或纠纷，按《办法》有关条款处理。</p>
    <p style="text-indent: 2em;">15.任务合同书未尽事宜，由甲乙双方协商解决。协商不成的，可向仲裁机构申请仲裁或向人民法院起诉，但在有关司法、仲裁结果生效之前，乙方应按照甲方要求继续履行或终止履行本任务合同书。</p>
    <?php else:?>
    <p>&nbsp;</p>
    <p style="text-indent: 2em;">1.任务各方共同遵守《四川省科技计划项目管理办法》、《四川省科技计划项目专项资金管理办法》等相关管理办法，以下简称《办法》，并自愿接受其约束。</p>
    <p style="text-indent: 2em;">2.任务合同书下达后，项目负责人全面负责项目的实施工作，各成员必须严格履行相应职责。</p>
    <p style="text-indent: 2em;">3.项目实施过程中，项目的研究计划、主要研究人员、研究任务、经费预算等需要调整时，项目负责人应根据《办法》中有关规定，向甲方或乙方提出变更理由及其内容的申请报告，经甲方或乙方审查通过后实施。未经批准，项目负责人必须按原任务合同书履行。</p>
    <p style="text-indent: 2em;">4.乙方必须接受甲方对项目进度及经费使用的监督和检查，并按甲方要求及时提供相关执行情况报告和相关统计报表，逾期不报，甲方有权暂停资助或终止项目。</p>
    <p style="text-indent: 2em;">5.乙方因某种原因致使无法按计划执行而主动要求结题时，乙方应在规定时限内提出申请；如乙方未主动提出申请，甲方有权根据调查情况终止任务。</p>
    <p style="text-indent: 2em;">6.任务执行过程中，若甲方无故终止任务，甲方无权追回拨给乙方的经费和乙方所购置的物资，甲方并承担善后处理所发生的费用。</p>
    <p style="text-indent: 2em;">7.乙方应遵守任务合同书的约定，及时呈送符合撰写标准的科技报告，并获得科技报告收录证明。乙方可根据项目具体情况提出科技报告的保密和解密期限要求。乙方应在项目验收后按规定进行成果登记。</p>
    <p style="text-indent: 2em;">8.任务到期完成后，乙方必须在三个月内完成验收准备，主动提交验收材料，并在任务到期后1年内完成项目验收手续。</p>
    <p style="text-indent: 2em;">9.推荐单位作为任务合同书中的丙方加盖公章，负责协调项目的组织实施、经费使用及监督检查中出现的有关问题。</p>
    <p style="text-indent: 2em;">
    10.项目研究成果及其形成的知识产权归项目承担单位所有。在特定情况下，国家根据需要保留无偿使用、开发、使之有效利用和获取收益的权利。乙方申报成果、专利、发表论文时需注明由"四川省科技计划资助"（英文标注："Supported by Sichuan Science and Technology Program"）。乙方因实施本项目而引起的各种知识产权纠纷由乙方负全部责任。</p>
    <p style="text-indent: 2em;">11.乙方对项目执行过程中产生的研究成果须及时采取知识产权保护措施，依法取得相关知识产权，并予以有效管理和充分使用。</p>
    <p style="text-indent: 2em;">12.乙方指定项目组成员___________________为本项目档案员，负责本项目档案的收集、积累和保存工作，要做到随时收集、编号登记、入袋保管，归档的重点是项目各个阶段形成的不同载体的文件材料和技术资料，特别是研究实验阶段形成的作为成果依据的原始材料。</p>
    <p style="text-indent: 2em;">13.乙方在项目实施过程中应建立相应的规章制度，加强安全管理，确保人员及设备安全，并对科研安全负全部责任。</p>
    <p style="text-indent: 2em;">14.乙方在项目实施过程中，应遵守科研诚信、科技行为廉洁的有关规定，不得向甲方、丙方工作人员行贿或报销应由个人支付的任何费用，被纪检监察机关或司法机关查证属实的，甲方有权终止项目实施并追缴拨付的全部科研经费。</p>
    <p style="text-indent: 2em;">15.任务合同书是对签订各方都有法定约束力的协议，自各方签字盖章之日起正式生效，若有争议或纠纷，按《办法》有关条款处理。</p>
    <p style="text-indent: 2em;">16.任务合同书未尽事宜，由甲乙双方协商解决。协商不成的，可向仲裁机构申请仲裁或向人民法院起诉，但在有关司法、仲裁结果生效之前，乙方应按照甲方要求继续履行或终止履行本任务合同书。</p>
    <?php endif;?>
  </div>
  <?php if($download != 'yes'):?>
  <pagebreak />
  	  <div align="center" style="font-size:20px; font-weight:bold;font-family:'黑体';">十、相关附件</div>
      <?=$project->widgets('attachements','task')?>
    <?php endif;?>
  <div class="clearfix"></div>
</div>
<?php if($download != 'yes'):?>
<script src="<?=site_path("assets/js/bootstrap.min.js")?>"></script> 
<!-- page specific plugin scripts --> 
<!-- ace scripts --> 
<script src="<?=site_path('js/layer/layer.js') ?>"></script> 
<script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script> 
<script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script> 
<script type="text/javascript">
      $(function(){
        $('pagebreak').before('<div class="pagebreak"></div>');
      });

    </script>
<?php endif;?>
</body>
</html>