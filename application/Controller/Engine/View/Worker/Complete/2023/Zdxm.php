<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><?=$project->getSubject()?>--验收书</title>
    <meta name="robots" content="noindex, nofollow">
    <link rel="stylesheet" href="<?=site_path("css/template.css")?>?v=1">
    <?php if($download != 'yes'):?>
    <link rel="icon" type="image/png" sizes="192x192" href="<?=site_path('assets/media/favicons/favicon-192x192.png')?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?=site_path('assets/media/favicons/apple-touch-icon-180x180.png')?>">
    <link rel="stylesheet" id="css-main" href="<?=site_path('assets/css/compress.min.css')?>?v=<?=config::get('css.main_version')?>">
    <script src="<?=site_path('assets/js/compress.core.min.js')?>"></script>
    <script src="<?=site_path('assets/js/compress.app.min.js')?>"></script>
    <style>
        .project_review{width: 800px;margin: 100px auto;padding: 0;background: none}
        .project_review .main{margin: 80px auto;padding:60px !important;;box-shadow:0 .5rem 2rem #d4dcec;-webkit-transform:translateY(-2px);transform:translateY(-2px);opacity:1}
        .content .block{
            margin: 0;padding: 0
        }
        .content .block .block-content{
            margin-top: 0
        }
    </style>
    <?php endif;?>
</head>
<body>
<script>
if (document.addEventListener) {
    window.addEventListener('pageshow', function (event) {
        if (event.persisted || window.performance &&
            window.performance.navigation.type == 2) {
            $("#preloader").hide();
        }
    },false);
}
</script>
<div id="preloader" style="display: none;">
    <div id="ctn-preloader" class="ctn-preloader">
        <div class="round_spinner">
            <div class="spinner"></div>
            <div class="text">
                <img src="<?=site_url('assets/images/pdf.png')?>" alt="">
                <h4>
                    <span>文档生成中...</span>
                </h4>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="baseUrl" value="<?=site_url('/')?>" />
<div id="page-container" class="page-header-dark main-content-boxed">
    <?php if($download != 'yes'):?>
        <?php include(__DIR__."/../Navbar.php");?>
    <?php endif;?>
    <!-- Main Container -->
    <main id="main-container">
        <!-- Page Content -->
        <div class="content project_review" style="">
            <div class="block block-rounded">
                <div class="block-content main">
                    <bookmark content="封面" />
                    <?=$project->widgets('applyform', 'complete')?>
                    <pagebreak>
                    <bookmark content="封面" />
                    <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11" style="border:none;">
                        <tr>
                            <td height="5" align="right" width="17%" class="def11" ></td>
                            <td class="def11" align="left" width="21%" style="margin-bottom:0px;margin-top:20px" valign="bottom"></td>
                            <td valign="bottom" class="def11" align="right" width="15%"></td>
                            <td valign="bottom" class="def11" align="left" width="14%"></td>
                            <td valign="bottom" class="def11" align="left" width="15%"><span class="STYLE8">项目编号：</span></td>
                            <td class="def2" align="left" width="22%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><?=$project->getRadicateId()?></td>
                        </tr>
                    </table>
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                    <p align="center"><span class="STYLE4"><strong>民航二所"十四五"集智攻关重大项目<br>验收报告<br></strong></span></p>
                    <p align="center"><br></p>
                    <?=$project->widgets('cover','complete')?>
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                    <p>&nbsp;</p>
                    <p align="center"><span class="STYLE5">中国民用航空总局第二研究所</span></p>
                    <p align="center">&nbsp;</p>
                    <pagebreak>
                    <bookmark content="验收报告填写和打印说明" />
                    <p style="font-size:25px; text-align:center;"><strong>验收报告填写和打印说明</strong></p>
                    <p align="center">&nbsp;</p>
                    <p style="text-indent:2em;">1.填写验收报告各项内容应实事求是，认真填写，表述明确。第一次出现的缩略词，须注明全称。 </p>
                    <p style="text-indent:2em;">2.验收报告的各个部分都必须填写，原则上不能有空白；确实无法填写的内容，请一律用“—”表示，项目编号科技管理部门统一填写。 </p>
                    <p style="text-indent:2em;">3.验收报告经项目承担单位或部门、合作单位或部门、科技管理部门、财务管理部门审批合格后，项目负责人将验收报告用A4纸打印一式三份。由所在单位或部门及合作单位或部门审核签署意见并加盖公章后，报送科技管理部门备案。 </p>

                    <pagebreak resetpagenum="1">                  
                    <bookmark  content="一、项目验收信息表" />
                    <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">一、项目验收信息表</div>
                    <?=$project->widgets('baseinfo','complete')?>
                    <?php if($download != 'yes'):?>
                    <pagebreak></pagebreak>
                    <?php endif;?>
                    <bookmark content="二、项目总体完成情况综述。" />
                        <?=$project->widgets('complete_content1','complete')?>
                    <pagebreak></pagebreak>
                    <bookmark content="四、经费决算表" />
                    <?=$project->widgets('money','complete')?>
                    <pagebreak />
                    <bookmark content="五、通过本项目资助获得的专利、发表的论文及专著情况" />
                    <?=$project->widgets('complete_content2','complete')?>
                    <pagebreak></pagebreak>
                    <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">七、验收专家组意见</div>
                    <?=$project->widgets('completeidea','complete')?>
                    <pagebreak></pagebreak>
                    <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';">八、验收专家组人员</div>
                    <?=$project->widgets('completeexpert','complete')?>
                    <pagebreak></pagebreak>
                    <widget type="printer">
                        <bookmark content="九、验收审批意见" />
                        <div align="left" style="font-size:20px; font-weight:bold;font-family:'黑体';padding-bottom: 10px;">九、验收审批意见</div>

                        <table width="680" border="1" align="center" cellpadding="5" cellspacing="0" class="table table-bordered table-striped table-hover">
                          <tr align="center">
                            <td colspan="2" height="40" align="center" valign="middle"><strong>科技管理部门意见</strong></td>
                          </tr>
                            <tr>
                                <td colspan="2" height="300" align="left" valign="top" style="border-bottom:none;"></strong></td>
                            </tr>
                          <tr>
                            <td height="69" align="center" valign="middle" style="border-top:none;border-right:none;width: 50%"><p>负责人（签字）：</p></td>
                            <td height="69" align="center" valign="middle" style="border-top:none;border-left:none;width: 50%"><p>（章）</p></td>
                          </tr>
                          <tr>
                            <td colspan="2" height="69" align="right" valign="middle" style="border-top:none;"><p>年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日</p></td>
                          </tr>
                          <tr align="center">
                            <td colspan="2" height="40" align="center"><strong>财务管理部门意见</strong></td>
                          </tr>
                          <tr>
                            <td colspan="2" height="300" align="left" valign="top" style="border-bottom:none;"></strong></td>
                          </tr>
                            <tr>
                                <td height="69" align="center" valign="middle" style="border-top:none;border-right:none;width: 50%"><p>负责人（签字）：</p></td>
                                <td height="69" align="center" valign="middle" style="border-top:none;border-left:none;width: 50%"><p>（章）</p></td>
                            </tr>
                            <tr>
                                <td colspan="2" height="69" align="right" valign="middle" style="border-top:none;"><p>年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日</p></td>
                            </tr>
                        </table>
                    </widget>
                    <?php if($download != 'yes'):?>
                        <pagebreak></pagebreak>
                        <?=$project->widgets('attachement','complete')?>
                    <?php endif;?>
                    <div class="clearfix"></div>
                </div>
            </div>

        </div><!-- END Page Content -->
    </main><!-- END Main Container -->

</div><!-- END Page Container -->
<?php if($download != 'yes'):?>
<script>
    jQuery(function () { Dashmix.helpers('sparkline'); });
</script>
<script src="<?=site_path('js/layer/layer.js') ?>"></script>
<script src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
<script src="<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
<script type="text/javascript">
    $(function(){
        $('pagebreak').before('<div class="pagebreak"></div>');
    });
</script>
<?php endif;?>
</body>
</html>