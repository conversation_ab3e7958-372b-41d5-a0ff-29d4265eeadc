<?php
namespace App\Controller\Engine\Cover;
use App\Controller\BaseController;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class edit extends BaseController
{	
	private $project = NULL;
	private $configs = array();
    private $type = 'apply';

	function load()
	{
		$this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
		if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
        $this->type =  (input::mix("worker") == 'tasker') ? 'task' : 'apply';
		$this->view = new Template(realpath(dirname(__FILE__)).'/View/');
		$this->configs = $this->project->getWidgetConfigs('cover',$this->type);
		$this->view->set("project",$this->project);
		$this->view->set("configs",$this->configs);
	}
	
	/**
	 * 封面
	 */
	function index()
	{
		$guide = $this->project->getGuide(true);
		if($guide->isNew()) $this->page_debug("指南丢失！",getFromUrl());
		if(input::post())
		{
			$this->checkCsrf();
            if(!input::post("user_id")) $this->error('请选择项目负责人');
            if(!input::post("top_corporation_id")) $this->error('请选择所在单位');
            if(!input::post("company_id")) $this->error('请选择所在部门');
            if(input::post("start_at") >= input::post("end_at")) $this->error('起止时间不合法');
            if(input::post("company_role")=='B' && !input::post('lead_corporation_name')) $this->error('请填写牵头单位名称');
            if(input::post("user_id")!==input::session('roleuserid') && !in_array(input::session("roleuserid"),input::post("assistant_id"))) $this->error('当前登录账号必须作为项目负责人或项目助理');
            $this->project->setProjectLevel(input::post('project_level'));
            $this->project->setProjectLevel2(input::post('project_source'));
            $this->project->setProjectSource(input::post('project_source'));
            if(input::post('project_source2') && !in_array(input::post('project_source2'),['课题','项目'])){
                $this->project->setProjectLevel3(input::post('project_source2'));
                $this->project->setProjectSource(input::post('project_source').'-'.input::post('project_source2'));
            }
            if($this->project->getProjectLevel()=='其他'){
                $this->project->setProjectSource(input::post('project_source_qt'));
            }
            if(input::post('project_source')=='其他'){
                $this->project->setProjectSource('其他-'.input::post('project_source_qt'));
            }
            if(input::post('project_source2')=='其他'){
                $this->project->setProjectSource(input::post('project_source').'-其他-'.input::post('project_source_qt'));
            }
            $this->project->setPattern('项目');
            if(in_array(input::post('project_source2'),['课题','项目'])){
                $this->project->setPattern(input::post('project_source2'));
            }
            if(in_array(input::post('project_source3'),['课题','项目'])){
                $this->project->setPattern(input::post('project_source3'));
            }
			//项目名称
			if(input::post("subject")){
				$this->project->setSubject(input::post("subject"));
				$this->project->setSubtitle(input::post("subject"));
			}
			//课题名称
			if(input::post("subtitle") && input::post("subtitle")!='无' && $this->project->getPattern()=='课题'){
				$this->project->setSubject(input::post("subtitle"));
			}
            //专项名称
            $this->project->setSpecialSubject(input::post('special_subject'));
            //项目负责人
            if(input::post("user_id")!== $this->project->getUserId()){
                $researcher = sf::getModel("Declarers")->selectByUserId(input::post("user_id"));
                if($researcher->isNew()){
                    $this->error('未找到该项目负责人');
                }
                $this->project->setUserId($researcher->getUserId());
                $this->project->setUserName($researcher->getPersonname());
                $researcherModel = $this->project->researcher($this->type);
                $researcherModel->setSubject($researcher->getPersonname());
                $researcherModel->setSex($researcher->getUserSex());
                $researcherModel->setBirthday($researcher->getUserBirthday());
                $researcherModel->setDegree($researcher->getUserDegree());
                $researcherModel->setTitle($researcher->getUserHonor());
                $researcherModel->setAge($researcher->getAge());
                $researcherModel->setIdentificationNumber($researcher->getUserIdcard());
                $researcherModel->setMobile($researcher->getUserMobile());
                $researcherModel->setEducation($researcher->getEducation());
                $researcherModel->setProfessional($researcher->getUserWork());
                $researcherModel->save();
            }
            //项目助理
            if($assistantIds = input::post("assistant_id")){
                $assistantIds = array_filter($assistantIds);
                $assistantIds = array_unique($assistantIds);
                $assistantIdArr = [];
                $assistantNameArr = [];
                foreach ($assistantIds as $k=>$assistantId){
                    if($assistantId===$this->project->getUserId()){
                        $this->error('不能添加项目负责人作为科研助理');
                    }
                    $assistant = sf::getModel("Declarers")->selectByUserId($assistantId);
                    if($assistant->isNew()){
                        $this->error('未找到该项目助理'.($k+1));
                    }

                    $assistantIdArr[] = $assistant->getUserId();
                    $assistantNameArr[] = $assistant->getPersonname();
                }
                $this->project->setAssistantId($assistantIdArr);
                $this->project->setAssistantName($assistantNameArr);
            }
            //申报单位
            if(input::post("top_corporation_id")!== $this->project->getTopCorporationId()){
                $company = sf::getModel("Corporations")->selectByUserId(input::post("top_corporation_id"));
                if($company->isNew()){
                    $this->error('未找到该单位');
                }
                $this->project->setTopCorporationId($company->getUserId());
            }
            //申报部门
            if(input::post("company_id")!== $this->project->getCorporationId()){
                $company = sf::getModel("Corporations")->selectByUserId(input::post("company_id"));
                if($company->isNew()){
                    $this->error('未找到该单位');
                }
                $this->project->setCorporationId($company->getUserId());
                $this->project->setCorporationName($company->getSubject());
            }
            //牵头/参与
            if(input::post("company_role")){
                $this->project->setCompanyRole(input::post("company_role"));
                $this->project->setLeadCorporationName('');
                if(input::post("company_role")=='B'){
                    $this->project->setLeadCorporationName(input::post("lead_corporation_name"));
                }
            }

			//研究领域
			if(input::post("subject_id")){
				$_subject = sf::getModel("Subjects")->selectByCode(input::post("subject_id"),1);
				$this->project->setSubjectId($_subject->getCode());
				$this->project->setSubjectName($_subject->getSubject());
			}
			if(input::post("subject_ids")){
                $subjectId = array_pop (input::post("subject_ids"));
                $this->project->setSubjectIds(input::post("subject_ids"));
                $this->project->setSubjectId($subjectId);
                $_subject = sf::getModel("Subjects")->selectByCode($subjectId,1);
                $this->project->setSubjectName($_subject->getSubject());
			}
			if(input::post("subject_ids2")){
                $subjectId = array_pop (input::post("subject_ids2"));
                $this->project->setSubjectIds2(input::post("subject_ids2"));
                $this->project->setSubjectId2($subjectId);
                $_subject = sf::getModel("Subjects")->selectByCode($subjectId,1);
                $this->project->setSubjectName2($_subject->getSubject());
			}
			//项目类别
			if(input::post("project_type")){
				$this->project->setProjectType(input::post("project_type"));
                $this->project->setProjectTypeSubject(sf::getModel('Categorys',input::post("project_type"))->getSubject());
			}

			//技术领域
			if(input::post("skill_id")){
				$_skill = sf::getModel("Subjects")->selectByCode(input::post("skill_id"),4);
				$this->project->setSkillId($_skill->getCode());
				$this->project->setSkillName($_skill->getSubject());
			}
			//行业
			if(input::post("industry")){
			    $industryId = array_pop (input::post("industry"));
                $this->project->setIndustry(input::post("industry"));
                $this->project->setIndustryId($industryId);
                $this->project->setIndustryName(sf::getModel('Industrys')->selectByCode($industryId)->getSubject());
			}

			//指南有时间，直接使用指南中的时间
			$this->project->setStartAt(input::post("start_at"));
			$this->project->setRealStartAt(input::post("start_at"));
            $this->project->setEndAt(input::post("end_at"));
            $this->project->setRealEndAt(input::post("end_at"));

            $this->project->setUpdatedAt(date('Y-m-d H:i:s'));
			$this->project->save();
            if($baseinfo = input::post('baseinfo')){
                $baseModel = $this->project->getBaseinfo($this->type);
                $baseModel->setData($baseinfo['data']);
                $baseModel->save();
            }
            $this->project->setFundTpl();//设置经费模板
            $this->closeWindow();
		}

		$this->view->set("project",$this->project);
		$this->view->set("guide",$guide);
		$this->view->apply("inc_body","Edit");
		$this->view->display("page_blank");
	}
	
}
?>