<div class="main">
    <div class="btn-group btn-group-sm" role="group">
        <?= btn("back") ?>
        <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i
                    class="ace-icon fa fa-save"></i>保存资料</a>
        <p style="clear:both;"></p>
    </div>
    <div class="box">
        <form id="validateForm" name="validateForm" method="post" action="">
            <table width="100%" border="0" cellpadding="3" cellspacing="1"
                   class="tb_data table table-hover table-striped">
                <caption>
                    预期成果
                </caption>
                <?php $yqcg = $target->getYqcg(); ?>
                <tr>
                    <th colspan="2">预期成果类型</th>
                    <th width="65%">具体描述（每项不超过50字）</th>
                </tr>
                <?php if ($configs['yqcg']['xcp']): ?>
                    <tr>
                        <th>新产品</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[xcp][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['xcp']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[xcp][sm][yq]" type="text"
                                   value="<?= $yqcg['xcp']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['xjs']): ?>
                    <tr>
                        <th>新技术</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[xjs][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['xjs']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[xjs][sm][yq]" type="text"
                                   value="<?= $yqcg['xjs']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['xff']): ?>
                    <tr>
                        <th>新方法</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[xff][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['xff']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[xff][sm][yq]" type="text"
                                   value="<?= $yqcg['xff']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['syzz']): ?>
                    <tr>
                        <th>实验装置/系统</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[syzz][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['syzz']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[syzz][sm][yq]" type="text"
                                   value="<?= $yqcg['syzz']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['bz']): ?>
                    <tr>
                        <th>标准</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[bz][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['bz']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[bz][sm][yq]" type="text"
                                   value="<?= $yqcg['bz']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['lw']): ?>
                    <tr>
                        <th>论文</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[lw][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['lw']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">篇</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[lw][sm][yq]" type="text"
                                   value="<?= $yqcg['lw']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['zl']): ?>
                    <tr>
                        <th>发明专利</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[zl][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['zl']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[zl][sm][yq]" type="text"
                                   value="<?= $yqcg['zl']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['jsbg']): ?>
                    <tr>
                        <th>技术报告</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[jsbg][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['jsbg']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">篇</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[jsbg][sm][yq]" type="text"
                                   value="<?= $yqcg['jsbg']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
                <?php if ($configs['yqcg']['qt']): ?>
                    <tr>
                        <th>其他</th>
                        <td>
                            <div class="input-group">
                                <input name="yqcg[qt][sl][yq]" type="text" class="form-control int"
                                       value="<?= $yqcg['qt']['sl']['yq'] ?>" size="5"/>
                                <div class="input-group-append">
                                    <span class="input-group-text">项</span>
                                </div>
                            </div>
                        </td>
                        <td><input class="form-control" name="yqcg[qt][sm][yq]" type="text"
                                   value="<?= $yqcg['qt']['sm']['yq'] ?>" size="5"/></td>
                    </tr>
                <?php endif; ?>
            </table>
            <div class="ex_tools">
                <?=btn('button','保存资料','submit','save')?>
                <?=btn('button','关闭窗口','button','close','closeWindow()','btn-sm','btn-alt-danger')?>
            </div>
        </form>
    </div>
</div>
<script src="<?=site_path('js/math.js')?>"></script>