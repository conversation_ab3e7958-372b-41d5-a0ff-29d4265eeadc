<?php
namespace App\Controller\Engine\RandD;
use App\Controller\BaseController;
use Sofast\Core\lang;
use Sofast\Core\router;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Sf;

class edit extends BaseController
{
    private $project = NULL;
    private $randd = NULL;
	private $configs = array();

    function load()
    {
        $this->project = sf::getModel("Projects")->selectByProjectId(input::mix("id"));
        if($this->project->isNew()) $this->page_debug("项目不存在！",getFromUrl());
		if($this->project->getUserId() != input::session("roleuserid")) $this->page_debug("你没有权限执行该操作！",getFromUrl());
        $this->randd = $this->project->getRandd();

        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->view->set("project",$this->project);
        $this->view->set("randd",$this->randd);
		$this->configs = $this->project->getWidgetConfigs('randd');
        $this->view->set("configs",$this->configs);
    }

    /**
     * 债权融资情况
     */
    function index()
    {
        if(input::post())
		{
            $this->randd->setCompanyInfo(input::post('company_info'));
			$this->randd->setIsScale(input::post('is_scale'));
			$this->randd->setApplyInput(input::post('apply_input'));
			$this->randd->setConfirmInput(input::post('confirm_input'));
			//保存冗余信息
			$this->randd->setYear($this->project->getDeclareYear());
			$this->randd->setCompanyId($this->project->getCorporationId());
			if($this->configs['tpl'] != 'Gather ') $this->randd->setCompanyType($this->project->getCorporation()->getProperty());
			$this->randd->save();
			//保存申报经费
			if($this->configs['tpl'] != 'Gather'){
				$this->project->setDeclareMoney($this->randd->getApplyInput('subsidy_money'));
				$this->project->setTotalMoney($this->randd->getApplyInput('subsidy_money'));
			}else{
				$company_info = input::post('company_info');
				$this->project->setDeclareMoney($company_info['money']);
				$this->project->setTotalMoney($company_info['money']);	
			}
			$this->project->save();
            $this->page_debug(lang::get('Has been saved!'),getFromUrl());
        }
        $this->view->apply("inc_body", $this->configs['tpl']."/Edit");
        $this->view->display("page_blank");
    }
	
}