<?php
namespace App\Controller\Income\Standard;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Core\lang;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class apply extends BaseController
{
    private $income = NULL;
    private $standard = NULL;
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
        $this->income = sf::getModel('Incomes')->selectByIncomeId(input::getMix('id'));
        if($this->income->isNew()){
            $this->standard = sf::getModel('Standards')->selectByStandardId(input::getMix('pid'));
            if($this->standard->isNew()) $this->error('没有找到该项目');
            $this->income->setType('standard');
            $this->income->setProjectId($this->standard->getStandardId());
            $this->income->setCompanyId($this->standard->getCompanyId());
            $this->income->setCompanyName($this->standard->getCompanyName());
            $this->income->setSubject($this->standard->getSubject());
            $this->income->setUserId(input::getInput('session.roleuserid'));
            $this->income->setUserName(input::getInput('session.nickname'));
        }else{
            $this->standard = $this->income->getProject();
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('income/standard/apply/edit/id/' . $this->income->getIncomeId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('income/standard/apply/attachment/id/' . $this->income->getIncomeId())
            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }


    public function edit()
    {
        if(input::post()){
            if(empty(input::post("money"))) $this->error('请填写本次到账经费',getFromUrl());
            if(empty(input::post("income_at"))) $this->error('请填写到账日期',getFromUrl());
            $this->income->setSubject(input::post('subject'));
            $this->income->setIncomeAt(input::post('income_at'));
            $this->income->setMoney(input::post('money')*10000);
            $this->income->setNote(input::post('note'));
            $this->income->setUpdatedAt(date('Y-m-d H:i:s'));
            $this->income->save();
            $this->income->updateIncomeMoney();
            $this->success('保存成功！',site_url('income/standard/apply/edit/id/'.$this->income->getIncomeId()));
        }
        $this->view->set("income",$this->income);
        $this->view->set("standard",$this->standard);
        $this->view->apply("inc_body",'standard/apply/edit');
        $this->view->display("page");
    }

    public function attachment()
    {
        if($this->income->isNew()) $this->error('请先填写基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set("income",$this->income);
        $this->view->set('itemType','income');
        $this->view->apply("inc_body",'standard/apply/attachment');
        $this->view->display("page");
    }

    function show()
    {
        if($this->income->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->view->set("income",$this->income);
        $this->view->set("itemType",'income');
        $this->view->apply('inc_body','standard/apply/show');
        $this->view->display('page');
    }

    public function gird($tpl = 'reward/apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `department_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array('field','search','corporation_id','department_id','subject','user_name','corporation_name','department_name');
        $this->view->set("pagers",sf::getModel('Incomes')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    function dodelete()
    {
        if($this->income->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->income->delete();
        $this->income->updateIncomeMoney();
        addHistory($this->income->getProjectId(),'删除经费到账记录');
        $this->success(lang::get('Has been deleted!'),getFromUrl());
    }

}