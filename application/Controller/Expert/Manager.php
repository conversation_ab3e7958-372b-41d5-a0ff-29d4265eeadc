<?php
namespace App\Controller\expert;
use App\Controller\BaseController;
use App\Models\User;
use App\Models\UserInfoTemplate;
use App\Module\Form\FormFactory;
use Sofast\Core\config;
use Sofast\Core\router;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class Manager extends BaseController
{
	private $tab;
	public function __construct()
	{
		parent::__construct();
		//以下数组调整tab选项卡顺序
		$id = input::getInput('get.id');
		$this->tab = [
			['text'=>'账号信息','url'=>site_url('expert/manager/show/id/'.$id)],
			['text'=>'基本资料','url'=>site_url('expert/manager/baseinfo/id/'.$id)],
			['text'=>'研究领域','url'=>site_url('expert/manager/researchArea/id/'.$id)],
			['text'=>'单位信息','url'=>site_url('expert/manager/unitinfo/id/'.$id)],
			['text'=>'奖励荣誉','url'=>site_url('expert/manager/honor/id/'.$id)],
			['text'=>'成果奖励','url'=>site_url('expert/manager/awards/id/'.$id)],
			['text'=>'专利信息','url'=>site_url('expert/manager/patents/id/'.$id)],
			['text'=>'论文信息','url'=>site_url('expert/manager/papers/id/'.$id)],
			['text'=>'附件上传','url'=>site_url('expert/manager/attachment/id/'.$id)],
		];
		$currentAct = router::getMethod();
		view::set('readonly', true);
		view::set('currentAct', $currentAct);
		view::set('tab', $this->tab);
	}

	/**
	 * 数据列表
	 */
	function index()
	{
		$this->gridForExpert('expert/manager/index');
	}
	
	
	/**
	 * 等待审核的
	 */
	function wait_list()
	{
		$this->gridForExpert('expert/manager/wait_list','is_lock = 1');
	}
	
	/**
	 * 已经审核的
	 */
	function submit_list()
	{
		$this->gridForExpert('expert/manager/submit_list','is_lock = 0');
	}
	
	/**
	 * 实名认证退回
	 */
	function back_list()
	{
		$this->gridForExpert('expert/manager/back_list','is_lock = 3');
	}
	
	/**
	 * 黑名单
	 */
	function black_list()
	{
		$this->gridForExpert('expert/manager/black_list','is_lock = 4');
	}
	
	/**
	 * 注册中的账号
	 */
	function reg_list()
	{
		$this->gridForExpert('expert/manager/reg_list','is_lock = 9');
	}
	
	/**
	 * 更新证件待核
	 */
	public function paperwork_list()
	{
		$this->gridForExpert('expert/manager/paperwork_list','is_lock = 6');
	}
	
	
	
	/**
	 * 显示用户信息
	 */
	function show()
	{
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		//用户信息
		$userinfo = User::where('user_id',$userid)->first();
		//附件信息
		$filemanager = sf::getModel("filemanager");
		$addWhere = $addSql = '';
		$addSql = 'order by created_at DESC';
		$addWhere = "`user_id` = '{$userid}' and `item_type` = 'expert'";
		input::getInput("post.search") && $addWhere .= "AND `".input::getInput("post.filed")."` like '%".input::getInput("post.search")."%' ";
		$fileinfo['pager'] = $filemanager->getPager($addWhere ,$addSql ,20);
		$fileinfo['num'] = $filemanager;

		view::set('fileinfo', $fileinfo);
		view::set('value', $userinfo);
		view::apply('inc_body', 'expert/manager/show');
		view::display('page');
	}
	/**
	 * 显示用户信息
	 */
	function show2()
	{
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$value = User::getFormCache($userid);	//准备回显数据
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/show2');
		view::display('page');
	}
	/**
	 * 显示用户信息
	 */
//	function showOld()
//	{
//		$model = UserInfoTemplate::find(1);
//		if (!$model) exit('project_model not found');
//		$layout = FormFactory::loadLayout('UserTab', $model->getConfig());
//		$layout->setReadonly(true);
////		dd($layout);
//		view::set('html', $layout->render());
//		view::apply('inc_body', 'expert/manager/show');
//		view::display('page');
//	}
	/**
	 * 保存用户基本资料
	 */
	public function baseinfo()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$value = $user->experts;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/baseinfo');
		view::display('page');
	}

	/**
	 * 研究领域
	 */
	public function researchArea()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$experts = $user->experts;
		$experts->gb_code1_data = $experts->getCodeName($experts->gb_code1);
		$experts->gb_code2_data = $experts->getCodeName($experts->gb_code2);
		$experts->hy_code1_data = $experts->getCodeName($experts->hy_code1,3);
		$experts->hy_code2_data = $experts->getCodeName($experts->hy_code2,3);
		$value = $experts;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/researchArea');
		view::display('page');
	}

	/**
	 * 单位信息
	 */
	public function unitinfo()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$unitInfo = $user->unitInfo;
		$unitInfo->property = $unitInfo->corporations ? $unitInfo->corporations->property : '';
		$unitInfo->unit_area = $unitInfo->corporations ? $unitInfo->corporations->unit_area : '';
		$unitInfo->address = $unitInfo->corporations ? $unitInfo->corporations->address : '';
		$value = $unitInfo;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/unitinfo');
		view::display('page');
	}

	/**
	 * 奖励荣誉
	 */
	public function honor()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$honorInfo = $user->honorInfo;
		$value = $honorInfo;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/honor');
		view::display('page');
	}

	/**
	 * 成果奖励
	 */
	public function awards()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$awardInfo = $user->awardInfo()->get();
		$value = $awardInfo;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/awards');
		view::display('page');
	}


	/**
	 * 专利
	 */
	public function patents()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$patentInfo = $user->patentInfo()->get();
		$value = $patentInfo;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/patents');
		view::display('page');
	}


	/**
	 * 论文
	 */
	public function papers()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$user = User::where('user_id',$userid)->first();
		$paperInfo = $user->paperInfo()->get();
		$value = $paperInfo;
		view::set('value', $value);
		view::apply('inc_body', 'expert/manager/papers');
		view::display('page');
	}

	/**
	 * 附件信息
	 */
	public function attachment()
	{
		//准备回显数据
		if(Input::getInput("get.id")){
			$userid = Input::getInput("get.id");
		}else{
			$userid = $_SESSION['userid'];
		}
		$filemanager = sf::getModel("filemanager");
		$addWhere = $addSql = '';
		$addSql = 'order by created_at DESC';
		$addWhere = "`user_id` = '{$userid}' and `item_type` = 'expert'";
		input::getInput("post.search") && $addWhere .= "AND `".input::getInput("post.filed")."` like '%".input::getInput("post.search")."%' ";
		$fileinfo['pager'] = $filemanager->getPager($addWhere ,$addSql ,20);
		$fileinfo['num'] = $filemanager;
		view::set('value', $fileinfo);
		view::apply('inc_body', 'expert/manager/attachment');
		view::display('page');
	}


	/**
	 * 删除数据
	 */
	function delete()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.userid");
		sf::getModel("experts")->remove($ids);
		sf::getModel("historys")->addHistory('system',lang::get("Delete expert for the system!"));
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 审核评审专家
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("mix.userid"); 
		
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("experts")->selectByUserId($ids[$i]);
			if($user->isNew()) continue;
			$user->setIslock(0);//审核通过
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'通过实名认证！');
			$user->sendMessage(sprintf(lang::get('MSG.E001'),$user->getUserName()),$user->getUserId(),'experts');
		}
		$this->page_debug(lang::get("Has been validated!"),getFromUrl());
	}
	
	/**
	 * 退回
	 */
	function doBack()
	{
		$user = sf::getModel("experts")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->setIslock(3);//平台锁定
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'平台认证退回！其原因是：<br />'.input::getInput("post.content"));
			$user->sendMessage(sprintf(lang::get('MSG.E002'),$user->getUserName()),$user->getUserId(),'experts');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","expert/manager/note");
		view::display("page");
	}
	
	/**
	 * 黑名单
	 */
	function doBlack()
	{
		$user = sf::getModel("experts")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->setIslock(4);//黑名单
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'账号被列入黑名单！其原因是：<br />'.input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","expert/manager/note");
		view::display("page");
	}
	
	
	/**
	 * 验证项目负责人
	 */
	function validate()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids = input::getInput("get.id"); 
		
		sf::getModel("experts")->validate($ids);
		sf::getModel("historys")->addHistory('system',sprintf(lang::get('The state has been change,which ID is "%s"!'),htmlspecialchars($ids,ENT_QUOTES)));
		$this->page_debug(lang::get("Has been validated!"),getFromUrl());
	}
	
	function gridForExpert($tpl='expert/manager/index',$addWhere='1',$showMax=20)
	{
		$experts = sf::getModel("experts");

		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
		input::getInput("mix.user_type") && $addWhere .= " AND `user_type` LIKE '".input::getInput("mix.user_type")."' ";

		if(input::getInput("mix.is_lock") == 2) $addWhere .= " AND `is_lock` = 0 ";
		elseif(input::getInput("mix.is_lock")) $addWhere .= " AND `is_lock` = '".input::getInput("mix.is_lock")."' ";

		input::getInput("mix.user_honor") && $addWhere .= " AND `user_honor` = '".input::getInput("mix.user_honor")."' ";
		input::getInput("mix.user_degree") && $addWhere .= " AND `user_degree` = '".input::getInput("mix.user_degree")."' ";
		input::getInput("mix.user_sex") && $addWhere .= " AND `user_sex` = '".input::getInput("mix.user_sex")."' ";
		input::getInput("mix.subject_names") && $addWhere .= " AND `subject_names` LIKE '%".input::getInput("mix.subject_names")."%' ";

		//将搜索条件保存一边打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			$_SESSION['hash'] = $this->hash;
			$_SESSION['experts']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['experts']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['experts']['sqlStr']);
			//$addSql = 	base64_decode($_SESSION['orderStr']);
		}

		$from_vars = array('search','user_type','field','user_honor','user_degree','user_sex','subject_names','is_lock','id');
		view::set("pager",$experts->getPager($addWhere ,$addSql ,$showMax,'','',$from_vars));
		view::apply("inc_body",$tpl);
		view::display("page");
	}

	function gridForExpert_bak($tpl='expert/manager/index',$addWhere='1',$showMax=20)
	{
		$experts = sf::getModel("experts");

		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

		input::getInput("mix.search") && $addWhere .= " AND `".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";
		input::getInput("mix.user_type") && $addWhere .= " AND `user_type` LIKE '".input::getInput("mix.user_type")."' ";

		if(input::getInput("mix.is_lock") == 2) $addWhere .= " AND `is_lock` = 0 ";
		elseif(input::getInput("mix.is_lock")) $addWhere .= " AND `is_lock` = '".input::getInput("mix.is_lock")."' ";

		input::getInput("mix.user_honor") && $addWhere .= " AND `user_honor` = '".input::getInput("mix.user_honor")."' ";
		input::getInput("mix.user_degree") && $addWhere .= " AND `user_degree` = '".input::getInput("mix.user_degree")."' ";
		input::getInput("mix.user_sex") && $addWhere .= " AND `user_sex` = '".input::getInput("mix.user_sex")."' ";
		input::getInput("mix.subject_names") && $addWhere .= " AND `subject_names` LIKE '%".input::getInput("mix.subject_names")."%' ";

		//将搜索条件保存一边打印或者导出
		if(input::getInput("post") || $this->hash != $_SESSION['hash'])
		{
			$_SESSION['hash'] = $this->hash;
			$_SESSION['experts']['sqlStr'] = base64_encode($addWhere);
			$_SESSION['experts']['orderStr'] = base64_encode($addSql);
		}else{
			$addWhere = base64_decode($_SESSION['experts']['sqlStr']);
			//$addSql = 	base64_decode($_SESSION['orderStr']);
		}

		$from_vars = array('search','user_type','field','user_honor','user_degree','user_sex','subject_names','is_lock','id');
		view::set("pager",$experts->getPager($addWhere ,$addSql ,$showMax,'','',$from_vars));
		view::apply("inc_body",$tpl);
		view::display("page");
	}
	
	function download()
	{
		$expert = sf::getModel("experts")->selectByUserId(input::getInput("get.id"));
		header("Content-type:application");
		header("Content-Disposition: attachment; filename=".$expert->getUserId().'.doc');
		$data['expert'] = $expert;
		view::set($data);
		$output = view::getContent("expert/manager/output");
		exit($output);	
	}

	/**
	 * 编辑专家等级
	 */
	function edit()
	{
		$experts = sf::getModel("experts")->selectByUserId(input::getInput("mix.userid"));		
		if(input::getInput("post.user_grade"))
		{
			input::getInput("post.user_name") && $experts->setUserName(input::getInput("post.user_name"));
			input::getInput("post.user_birthday") && $experts->setUserBirthday(input::getInput("post.user_birthday"));
			input::getInput("post.card_name") && $experts->setCardName(input::getInput("post.card_name"));
			input::getInput("post.card_id") && $experts->setCardId(input::getInput("post.card_id"));
			input::getInput("post.user_mobile") && $experts->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.unit_phone") && $experts->setUnitPhone(input::getInput("post.unit_phone"));
			input::getInput("post.user_grade") && $experts->setUserGrade(input::getInput("post.user_grade"));
			$experts->setUpdatedAt(date("Y-m-d H:i:s"));
			if($experts->save())
			sf::getModel("historys")->addHistory("system",lang::get('Modify').($experts->getUserName()).lang::get('expert grade').input::getInput("post.user_grade"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$experts);
		view::apply("inc_body","expert/manager/edit");
		view::display("page");
	}
	
	function create()
	{
		$expert = sf::getModel("experts")->selectByUserId(input::getInput("get.userid"));
		$_user = $expert->getUser(true);
		if($expert->isNew() || $_user->isNew()) $this->page_debug('专家账号不存在！',getFromUrl());
		$password = 'a'.rand(100000,999999);
		$_user->setUserPassword($password);
		$_user->setUpdatedAt(date("Y-m-d H:i:s"));
		$_user->save();
		$expert->sendMessage('尊敬的评审专家您好！您在中国民航局第二研究所科管平台上进行了取回密码操作！您的账号为："'.$_user->getUserName().'"，密码为："'.$password.'"。请及时登录系统修改密码！',$_user->getUserId(),'experts');
		sf::getModel("historys")->addHistory($expert->getUserId(),lang::get("The password has been change!"),'experts');
		$this->page_debug(sprintf(lang::get("The new password is:%s!"),$password),getFromUrl());
	}
	
	/**
	 * 显示用户更多信息
	 */
	function more()
	{
		$expert = sf::getModel("experts")->selectByUserId(input::getInput("mix.userid"));	
		view::set("user",$expert);
		view::apply("inc_body","expert/manager/more");
		view::display("page");
	}
	
	/**
	 * 合并项目负责人
	 */
	function join()
	{
		if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());
		
		//被合并的项目负责人
		$expert = sf::getModel("Experts")->selectByUserId(input::getInput("post.userid"));
		if($expert->isNew()) $this->page_debug("被合并专家不存在！",getFromUrl());
		
		$_userid  = $expert->getUserId();
		$_username = $expert->getUserName();
		
		$ids = $_ids = array();
		$db = sf::getLib("db");
		//合并并删除专家
		if(is_array(input::getInput("post.select_id"))) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("post.userid");
		
		for($i=0,$n=count($ids);$i<$n;$i++)
		{
			$_user = sf::getModel("Experts")->selectByUserId($ids[$i]);
			if($_user->isNew()) continue;
			//转移项目评审
			$db->query("UPDATE project_grade SET expert_id = '".$_userid."' WHERE expert_id = '".$_user->getUserId()."' ");
			//转移预算评审
			$db->query("UPDATE assess_grade SET expert_id = '".$_userid."' WHERE expert_id = '".$_user->getUserId()."' ");
			//删除帐号
			$_user->delete();
			sf::getModel("historys")->addHistory($_userid,sprintf("将专家“%s(%s)”的相关信息附加到该帐号！",$_user->getUserName(),$_user->getCardId()),'experts');
		}
		
		$this->page_debug("专家合并成功！",getFromUrl());
	}
	
}
?>