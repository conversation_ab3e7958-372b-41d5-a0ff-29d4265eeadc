<?php
namespace App\Controller\Expert;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class budgetreview extends BaseController
{	
	function index()
	{
		$this->wait_list();
	}
	
	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
		$data['pager'] = sf::getModel("BudgetGrades")->getPager("`expert_id` = '".input::getInput("session.userid")."' AND `is_submit` = 0 ","ORDER BY updated_at DESC");
		view::set($data);
		view::apply("inc_body","expert/budgetreview/wait_list");
		view::display("page");
	}
	
	/**
	 * 审评后等待上报的项目
	 */
	public function appraise_list()
	{
		$data['pager'] = sf::getModel("BudgetGrades")->getPager("`expert_id` = '".input::getInput("session.userid")."' AND `is_submit` = 1 ","ORDER BY updated_at DESC");
		view::set($data);
		view::apply("inc_body","expert/budgetreview/appraise_list");
		view::display("page");
	}
	
	/**
	 * 成功评审项目列表
	 */
	public function submit_list()
	{
		$data['pager'] = sf::getModel("BudgetGrades")->getPager("`expert_id` = '".input::getInput("session.userid")."' AND `is_submit` = 2 ","ORDER BY updated_at DESC");
		view::set($data);
		view::apply("inc_body","expert/budgetreview/submit_list");
		view::display("page");
	}
    
    /**
	 * 成功评审项目列表
	 */
	public function modification_list()
	{
		$data['pager'] = sf::getModel("Projects")->getPager("state_for_budget_book = 23 AND project_id IN (SELECT project_id FROM `assess_grade` WHERE `expert_id` = '".input::getInput("session.userid")."' AND `is_submit` = 2 AND role LIKE '%主审%') ","ORDER BY updated_at DESC");
		view::set($data);
		view::apply("inc_body","expert/budgetreview/modification_list");
		view::display("page");
	}
	
	/**
	 * 放弃评审项目列表
	 */
	public function nonuser_list()
	{
		$data['pager'] = sf::getModel("BudgetGrades")->getPager("`expert_id` = '".input::getInput("session.userid")."' AND `is_submit` = 3 ","ORDER BY updated_at DESC");
		view::set($data);
		view::apply("inc_body","expert/budgetreview/nonuser_list");
		view::display("page");
	}
		
	/**
	 * 上报评审的项目
	 */
	public function doSubmit()
	{
		if(input::getInput("post.select_id"))
		{
			foreach(input::getInput("post.select_id") as $id)
			{
				$grade = sf::getModel("BudgetGrades",$id);
				if($grade->isNew()) continue;
				$grade->submit();
			}
			$msg = lang::get("They has been submit!");	
		}else $msg = lang::get("Must select a project!");
		$this->page_debug($msg,getFromUrl());
	}
	
	/**
	 * 放弃评审
	 */
	public function doNonuser()
	{
		$grade = sf::getModel("BudgetGrades",input::getInput("mix.id"));
		if($grade->isNew()) $this->page_debug("The project do not exist!",getFromUrl());
		//设置为弃权
		$grade->nonuser(3);
		$this->page_debug(lang::get("Has been nonuser!"),getFromUrl());
	}
	
}