<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    已申报的项目
                </h3>
            </div>
            <div class="block-content">
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>
                            <?=getColumnStr('项目名称','subject')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('承担单位','corporation_id')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('项目负责人','user_name')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('项目状态','statement')?>
                        </th>
                        <th class="text-center" style="width: 240px;">
                            项目资料
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($project = $projects->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$projects->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <a href="<?=site_url('apply/project/show/id/'.$project->getProjectId())?>"><?=$project->getSubject()?></a>
                            <p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$project->getCorporationName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$project->getUserName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$project->getState()?>
                        </td>
                        <td>
                            <table class="snote" style="border: none">
                                <tr>
                                    <td>申报书附件</td>
                                    <td><?=$project->attachments()->getTotal() ? Button::setTarget('_blank')->setUrl(site_url('apply/project/show/id/'.$project->getProjectId()))->link('查看') : '<i class="text-danger">未上传</i>'?></td>
                                </tr>
                                <?php
                                if(in_array($project->getStatement(),[29,30])):
                                    ?>
                                    <tr>
                                        <td>年度执行报告</td>
                                        <td><?php
                                            $stages = $project->stages();
                                            if($stages->getTotal()==0) echo '<i class="text-danger">未填报</i>';
                                            else {
                                                $stage = $stages->getObject();
                                                echo Button::setUrl(site_url('Archive/project/stage/id/'.$project->getProjectId()))->window($stage->getYear().'已填报');
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>季度执行报告</td>
                                        <td><?php
                                            $quarters = $project->quarters();
                                            if($quarters->getTotal()==0) echo '<i class="text-danger">未填报</i>';
                                            else {
                                                $quarter = $quarters->getObject();
                                                echo Button::setUrl(site_url('Archive/project/quarter/id/'.$project->getProjectId()))->window($quarter->getYear().'/'.$quarter->getQuarter().'已填报');
                                            }
                                            ?></td>
                                    </tr>
                                    <tr>
                                        <td>经费支出</td>
                                        <td><?php
                                            $payments = $project->payments();
                                            if($payments->getTotal()==0) echo '<i class="text-danger">未填报</i>';
                                            else {
                                                echo Button::setUrl(site_url('office/payment/showDetail/id/'.$project->getProjectId()))->window($project->getPaymentTotal().'/'.$project->getApplyTotal());
                                            }
                                            ?></td>
                                    </tr>
                                    <tr>
                                        <td>阶段目标</td>
                                        <td><?php
                                            $schedules = $project->schedules('apply',true);
                                            if($schedules->getTotal()==0) echo '<i class="text-danger">未填报</i>';
                                            else {
                                                echo Button::setUrl(site_url('Archive/project/target/id/'.$project->getProjectId()))->window('查看');
                                            }
                                            ?></td>
                                    </tr>
                                <?php endif;?>
                            </table>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
