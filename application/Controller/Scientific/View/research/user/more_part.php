<table border="0" cellpadding="3" cellspacing="1" class="show_more tb_data table-sm">
    <tr>
        <th colspan="4" class="text-center">详细信息</th>
    </tr>
    <tr>
        <th width="20%" class="text-center">名称</th>
        <td width="30%"><?=$plan->getSubject()?></td>
        <th width="20%" class="text-center">提交单位及提交人员</th>
        <td width="30%"><?=$plan->getSubmitUnitPerson()?></td>
    </tr>
    <tr>
        <th class="text-center">背景</th>
        <td><?=$plan->getBackground()?></td>
        <th class="text-center">国内外现状</th>
        <td><?=$plan->getHomeAndAboard()?></td>
    </tr>
    <tr>
        <th class="text-center">技术简介</th>
        <td><?=$plan->getTechIntro()?></td>
        <th class="text-center">市场前景分析</th>
        <td><?=$plan->getMarket()?></td>
    </tr>
    <tr>
        <th class="text-center">资料来源</th>
        <td><?=$plan->getSource()?></td>
        <th class="text-center">提交日期</th>
        <td><?=$plan->getSubmitDate()?></td>
    </tr>
    <tr>
        <th class="text-center">状态</th>
        <td><?=$plan->getState()?></td>
        <th></th>
        <td></td>
    </tr>
    <tr>
        <th class="text-center">填写日期</th>
        <td><?=$plan->getCreatedAt()?></td>
        <th class="text-center">历史记录</th>
        <td><?=Button::setName('查看记录')->setUrl(site_url("admin/history/index/id/".$plan->getResearchId().'/type/scientific_research'))->setWidth('550px')->setheight('80%')->window()?></td>
    </tr>
    <tr></tr>
</table>
