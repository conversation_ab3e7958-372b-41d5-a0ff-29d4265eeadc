<script type="text/javascript" src="<?=site_url('js/jquery.webupload.js')?>"></script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑项目计划
        </h2>
        <div class="content-options">
            <?=Button::setUrl(site_url('scientific/research/user/wait_list'))->setIcon('back')->link('返回')?>
            <?php
            if(!$plan->isNew()):
                ?>
                <?=Button::setUrl(site_url("scientific/research/user/show/id/".$plan->getResearchId()))->setIcon('show')->link('查看预览')?>
                <?=Button::setUrl(site_url("scientific/research/user/submit/id/".$plan->getResearchId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
            <?php endif;?>
        </div>
    </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="block block-rounded">
                    <div class="page-body">
                        <ul class="nav nav-tabs nav-tabs-block">
                            <?php foreach($tabs as $tab):?>
                                <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                        <?=$tab['text']?>
                                    </a></li>
                            <?php endforeach;?>
                            
                        </ul>
                        <div class="block-content tab-content">
                            <div class="tab-pane active" id="tab1" role="tabpanel">
                            <div class="clearfix"></div>
                              <div class="form-group">
                                  <div id="uploader" class="wu-example dropzone">
                                      <div class="queueList">
                                          <div id="dndArea" class="placeholder">
                                              <div id="upload-<?=$itemType?>"></div>
                                              <?=Button::setIcon('fa fa-upload')->button('点击上传文件')?>
                                              <p>或者将文件拖到此处，上传文件应为pdf格式，体积不超过10MB</p>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                              <div class="clearfix"></div>
                              <div class="header no-margin-top">已上传文件列表 <small>(点击文件名可 查看/下载)</small></div>

                                <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="item_id" value="<?=$plan->getResearchId()?>">
                                    <input type="hidden" name="table" value="filemanager">
                                    <input type="hidden" name="upload_size" value="20480000">
                              <table class="table">
                                  <thead>
                                  <tr>
                                      <th class="text-center">序号</th>
                                      <th class="text-center">文件名</th>
                                      <th class="text-center">文件格式</th>
                                      <th class="text-center">文件大小</th>
                                      <th class="text-center">上传时间</th>
                                      <th class="text-center">文件说明</th>
                                      <th class="text-center" style="width:70px;">操作</th>
                                  </tr>
                                  </thead>
                                  <tbody id="file-list-<?=$itemType?>">
                                  <?php if($plan->getAttachment()->getTotal()>0):
                                      $attachments = $plan->getAttachment();
                                      while($file = $attachments->getObject()):
                                          ?>
                                          <tr id="file<?=$file->getId()?>">
                                              <td class="text-center" style="width: 70px;">
                                                  <input type="hidden" name="attachment_id[]" value="<?=$file->getId()?>">
                                                  <input class="form-control" style="text-align: center" type="text" name="no[<?=$file->getId()?>]" value="<?=$file->getNo()?>">
                                              </td>
                                              <td><i class="fa fa-caret-right orange"></i> <a href="<?=site_path('up_files/'.$file->getFilePath())?>" target="_blank"><?=$file->getFileName()?></a></td>
                                              <td class="text-center"><?=$file->getFileExt()?></td>
                                              <td class="text-center"><?=$file->getFileSize()?></td>
                                              <td class="text-center"><?=$file->getCreatedAt()?></td>
                                              <td class="text-center" style="width: 300px">
                                                  <input class="form-control" type="text" name="filenote[<?=$file->getId()?>]" value="<?=$file->getFileNote()?>">
                                              </td>
                                              <td class="text-center">
                                                  <a href="javascript:delete_file('<?=$file->getId()?>','')" onClick="return confirm('确定要删除该文件吗？')" class="btn btn-danger btn-sm">删除</a>
                                              </td>
                                          </tr>
                                      <?php endwhile;?>
                                  <?php endif;?>
                                  </tbody>
                                  <tr>
                              </table>

                             <div class="clearfix"></div>

                              <div class="form-group col-xs-12 col-sm-12 col-md-12">
                                  <div style="width:100px;margin:0 auto;text-align:center">
                                      <a href="javascript:void(0);" onclick="form1.submit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                                  </div>
                              </div>
                                </form>
                              <div class="clearfix"></div>
                        </div>
                      </div>
                    </div>
                    <div class="page-footer">
                      <!--右下角浮动-->

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script>
    var baseurl = $('#baseUrl').val();
    var fileSizeLimit = '100';   //单位：MB
    var fileSingleSizeLimit = '10';   //单位：MB
    var fileNumLimit = 10;   //允许上传的文件数量
    $(function(){
        var uploadFileNum = '<?=$plan->getAttachment()->getTotal()?>';   //已上传的文件数量
        $('#upload-<?=$itemType?>').webupload({
            formData:{item_id:'<?=$plan->getResearchId()?>',item_type:'<?=$itemType?>'},
            accept:{
                title: 'PDF',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            },
            dnd: '#uploader .queueList',
            paste: document.body,
            uploadFileNum:uploadFileNum,
            fileNumLimit:fileNumLimit,
            fileSizeLimit:fileSizeLimit*1024*1024,
            fileSingleSizeLimit:fileSingleSizeLimit*1024*1024,
        },{
            success:function(file, response){
                this.options.uploadFileNum++;
                $(".upload-<?=$itemType?>").removeClass('hidden');
                var data = eval(response._raw);
                var id = data[0].id;
                var file_name = data[0].file_name;
                var index1=file_name.lastIndexOf(".");
                var index2=file_name.length;
                var file_ext=file_name.substring(index1,index2);//后缀名
                var file_savepath = data[0].path;
                var file_size = _sizeFormat(data[0].size);
                var upload_time = data[0].time;
                var fid = file.id;

                var trhtml = '<tr id="file'+id+'">\n' +
                    '    <td class="text-center" style="width: 70px"><input type="hidden" name="attachment_id[]" value="'+id+'"><input class="form-control" style="text-align: center" type="text" name="no['+id+']" value="50" /></td>\n' +
                    '    <td><i class="fa fa-caret-right orange"></i> <a href="'+baseurl+'up_files/'+file_savepath+'" target="_blank">'+file_name+'</a></td>\n' +
                    '    <td class="text-center">'+file_ext+'</td>\n' +
                    '    <td class="text-center">'+file_size+'</td>\n' +
                    '    <td class="text-center">'+upload_time+'</td>\n' +
                    '    <td class="text-center" style="width: 300px"><input class="form-control" type="text" name="filenote['+id+']" value="" /></td>\n' +
                    '    <td class="text-center">\n' +
                    '\t<a href="javascript:delete_file('+id+',\''+fid+'\')" class="btn btn-danger btn-sm" onClick="return confirm(\'确定要删除该文件吗？\')">删除</a>\n' +
                    '    </td>\n' +
                    '</tr>';
                $(".ant-table-placeholder").remove();
                $("#file-list-<?=$itemType?>").append(trhtml);
            }
        });
    });

    function delete_file(id,fid) {
        var url = "<?=site_url('common/file_delete')?>";
        $.getJSON(url,{fid:id,id:'<?=$plan->getResearchId()?>'},function (data) {
            if(data.code==1){
                showSuccess('删除成功！');
                $("#file"+id).remove();
                $("#attachment_"+id).remove();
                if(fid) $('#PDF_'+fid).remove();
            }else{
                showError(data.msg);
            }
        });
    }
</script>