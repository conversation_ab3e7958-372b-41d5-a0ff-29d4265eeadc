<?php
namespace App\Controller\Spider;

use App\Controller\Spider\Http\Request;
use App\Controller\Spider\Http\Response;
use Illuminate\Support\Arr; 
use Config, Log;
use App\Controller\Spider\Model\Group;
use App\Controller\Spider\Model\Content;
use App\Controller\Spider\Model\Task as TaskModel;
use Sofast\Support\Template;
use Sofast\Core\Sf;

class Com{

    function index(){
        $name = Request::get('name');
        $dir = realpath(dirname(__FILE__)).'/components/';
        $view = Request::get('view');

        $file = $dir.$name;
        if(is_file($file)) exit(file_get_contents($file));
        else exit('');
    }
}