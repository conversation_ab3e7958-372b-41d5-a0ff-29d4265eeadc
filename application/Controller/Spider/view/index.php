<div id="app">
    <div class="p-1">
        <div class="px-2 py-1 flex items-center bg-gray-100">
            <span class="animate-ping inline-flex rounded-full h-2 w-2 bg-green-500 opacity-90 " v-if="state.socket"></span>
            <span class="inline-flex rounded-full h-2 w-2 bg-red-500 opacity-90 " v-else></span>
            <span class="px-1">服务</span>
        </div>
    </div>
    <div class="bg-white-500" id="taskChart" style="width:calc(100vw - 300px);height:200px;">

    </div>

    <div class="bg-gray-100 h-12 flex justify-between items-center">
        <div>
            <a-select
                v-model:value="group_name"
                placeholder="选择"
                style="width: 120px"
                @change="getTasks"
            >
                <a-select-option
                v-for="group in groups"
                :key="group.id"
                :label="group.subject"
                :value="group.subject"
                ></a-select-option>
            </a-select>
            <a-button @click="logModal">日志</a-button>
        </div>
        <div>
            <a-button type="primary" @click="add" :disabled="!group_name">添加任务</a-button>
        </div>
    </div>
    
    <table-grid :columns="columns" :data="tasks" :pagination="pagination" @change="onChange" v-loading="loading" >
        <template #body:task_status={record}>
            <template v-if="record.isStart">
                <a-button type="primary" :loading="state.stop" @click="stop(record)" class="flex items-center" :disabled="!state.socket">
                    <template #icon>
                        <i class="text-red-400 iconoir-xmark-circle text-xl pr-1" ></i>
                    </template>
                    停止任务
                </a-button>
            </template>
            <template v-else>
                <a-button type="primary" :loading="state.start" @click="start(record)" class="flex items-center" :disabled="!state.socket">
                    <template #icon>
                        <i class="text-green-400 iconoir-play text-xl pr-1" ></i>
                    </template>
                    开始任务
                </a-button>
            </template>
        </template>

        <template #body:status='{record, value}'>
            <a-switch v-model:checked="record.status" checked-children="1" un-checked-children="0" @change="switchStatus(record)"></a-switch>
        </template>

        <template #body:subject='{record, value}'>
        {{value}} <span class="text-gray-300 text-sm">/ {{record.task_id}}</span>
        </template>

        <template #body:data_count='{record, value}'>
            <a-button @click="contentModal(record)">
            {{value || 0}} 查看
            </a-button>
        </template>

        <template #body:action={record} >
            <a-space >
                <a-button type="primary" @click="testModal(record)" :disabled="!state.socket || record.isStart">
                    测试
                </a-button>
                <a-button type="primary" @click="edit(record)" :disabled="record.isStart">
                    配置
                </a-button>
                <a-popconfirm
                    title="确认要删除此任务?"
                    ok-text="确认"
                    cancel-text="否"
                    @confirm="del(record)"
                >
                    <a-button type="primary" danger :loading="state.del" :disabled="record.isStart">
                        删除
                    </a-button>
                </a-popconfirm>
                
            </a-space>
        </template>
    </table-grid>

    <a-modal v-model:open="state.log" title="测试日志" :footer="null" width="60%">
        <a-empty v-if="!server_log.length"></a-empty>
        <div style="overflow:auto;">
        <div v-for="msg in server_log">{{msg}}</div>
        </div>
    </a-modal>

    <a-modal v-model:open="state.content" :title="current_task.subject+'数据'" :footer="null" width="60%">
        <content-list :task_id="current_task.task_id"></content-list>
    </a-modal>

    <modal-base v-model:url="modal.url" v-model:content="modal.content" :title="modal.title" :height="modal.height" :width="modal.width"></modal-base>
</div>

<script src="<?=site_path('assets/echarts/echarts.min.js')?>"></script>
<script type="text/javascript">
let app = createApp({
    components: {
        'content-list': VueModule('spider/com/index/name/content-list.vue'),
    },
    setup() {
        const ws = ref()
        const loading = ref(false)
        const columns = ref([])
        const groups = ref([])
        const tasks = ref([])
        const info = ref([])
        const group_name = ref('民航二所')
        const server_log = ref([])
        const task_status = ref([])
        const current_task = ref({})
        const running_task = ref({})
        const running_chart = ref()
        const running_chart_data = ref([])

        const modal = reactive({
            url: '',
            content: '',
            title: '',
            height: '80vh',
            width: '60vw',
        })

        const pagination = reactive({
            total: 0,
            current: 1,
            pageSize: 20,
        })

        const state = reactive({
            edit: false,
            log: false,
            socket:false,
            start:false,
            stop:false,
            del:false,
            content:false
        })

        watch(()=>task_status.value, (val)=>{
            tasks.value.forEach(task=>{
                if(task_status.value.length == 0) task.isStart = false
                else{
                    val.forEach(item=>{
                        if(task.task_id==item.task_id){
                            task.isStart = item.status?true:false
                        }
                    })
                }
            })
            
        }, {deep:true})

        onMounted(function(){
            connectWebSocket();
            setTimeout(_=>{
                initEcharts();
            },2000)
            columns.value = [
                {'name': 'id', 'title': 'ID', 'width':'50px'},
                {'name': 'status', 'title': '状态', 'width':'150px'},
                {'name': 'task_status', 'title': '任务状态', 'width':'150px'},
                {'name': 'subject', 'title': '任务名称'},
                {'name': 'updated_at', 'title': '更新时间'},
                {'name': 'data_count', 'title': '已采集数据', 'width':'150px'},
                {'name': 'action', 'title': '功能', 'width':'200px'},
            ]

            getGroups()
            if(group_name.value){
                getTasks()
            }
        })

        const getTaskDataCount = _=>{
            task_ids = []
            is_start = []
            
            tasks.value.forEach(item=>{
                task_ids.push(item.task_id)
                if(item.isStart) is_start.push(item.task_id)
            })
            
            if(task_ids.length > 0){
                http('post',baseUrl + 'spider/index/dataCount', {task_ids:task_ids}).then(res=>{
                    let count = res.data || {}
                    tasks.value.forEach(item=>item.data_count = count[item.task_id])

                    if(is_start.length > 0) setTimeout(_=>getTaskDataCount(), 60000)
                    else{
                        setTimeout(_=>getTaskDataCount(), 60000)
                    }
                })
            }else{
                setTimeout(_=>getTaskDataCount(), 5000)
            }
        }

        const sendWsMessage = (channel, data)=>{
            if(ws.value) ws.value.send(JSON.stringify({
                "channel":channel,
                "data":data
            }))
        }

        const getGroups = _=>{
            http('post',baseUrl + 'spider/index/groups').then(res=>{
                groups.value = res.data || []
            })
        }

        const getTasks = _=>{
            http('post',baseUrl + 'spider/index/tasks', {group_name:group_name.value}).then(res=>{
                tasks.value = res.data || []
                tasks.value.forEach(item=>{
                    item.status = item.status?true:false
                })

                getTaskDataCount()
            })
        }

        const openWindow = menu=>{
            modal.title = menu.subject
            modal.url = menu.url
        }

        const add = _=>{
            let menu = {
                subject:'新增任务',
                url:baseUrl+'spider/task/add/group_name/'+group_name.value
            }
            openWindow(menu);
            // location.href = baseUrl + 'spider/task/add/'+group_name.value
        }

        const edit = record=>{
            let menu = {
                subject:record.subject,
                url:baseUrl+'spider/task/edit/task_id/'+record.task_id
            }
            openWindow(menu);
            // location.href = baseUrl + 'spider/task/edit/'+record.task_id
        }

        const testModal = record=>{
            server_log.value = []
            state.log = true
            server_log.value.unshift('正在建立连接...')

            http('post',baseUrl + 'spider/task/test/task_id/', {task_id:record.task_id}).then(res=>{
                if(res.code == 200){
                    server_log.value.unshift(res.message)
                }
                else{
                    message.error(res.message)
                    state.log = false
                }
            })                
        }

        const logModal = record=>{
            state.log = true
        }

        const contentModal = record=>{
            state.content = true
            current_task.value = record
        }

        const appendLog = text=>{
            server_log.value.unshift(text)
        }

        // 尝试连接WebSocket服务器的函数
        const connectWebSocket = ()=>{
            try {
                const send = (ws, channel, data)=>{
                    ws.send(JSON.stringify({
                        "channel":channel,
                        "data":data
                    }))
                }

                const loopRunningTask = _=>{
                    send(ws.value, 'loop_redis_list', {})
                    setTimeout(_=>{
                        loopRunningTask()
                    }, 1000)
                }

                const loopTaskStatus = _=>{
                    if(group_name.value)
                     send(ws.value, 'getTaskStatus', {group_name:group_name.value})
                    setTimeout(_=>{
                        loopTaskStatus()
                    }, 1000)
                }

                ws.value = new WebSocket("<?=$config['socket_server']?>");

                // 连接成功建立时触发
                ws.value.onopen = function(event) {
                    console.log('websocket已连接')
                    
                    <?php $time = time();?>
                    data = {"channel":"bind", "data":{"token":"<?=md5($config['secret_key'].$time)?>", "time":'<?=$time?>'}}
                    ws.value.send(JSON.stringify(data))

                    loopRunningTask()
                    loopTaskStatus()
                };

                // 收到服务器消息时触发
                ws.value.onmessage = function(event) {
                    // console.log('收到服务端回复的消息：'+event.data);
                    data = JSON.parse(event.data)
                    // data = event.data
                    if(data.channel == 'log'){
                        app.appendLog(data.data)
                    }

                    if(data.channel == 'bind'){
                        app.state.socket = true
                    }

                    if(data.channel == 'task_status'){
                        app.task_status = data.data
                    }

                    if(data.channel == 'running_task'){
                        item = data.data
                        if(item.hold){
                            let last_value = {}
                            if(running_chart_data.value.length > 0)
                                last_value = running_chart_data.value[running_chart_data.value.length - 1]
                            else{
                                last_value = {value:[new Date().getTime(), 0]}
                            }
                            
                            running_chart_data.value.push({
                                value:[new Date().getTime(), last_value['value'][1]]
                            })
                        }
                        else{
                            running_task.value[item.task_id] = item
                            count = 0
                            for(let i in running_task.value){
                                count += parseInt(running_task.value[i]['count'])
                            }
                            running_chart_data.value.push({
                                value:[new Date().getTime(), count]
                            })
                        }
                        if(running_chart.value){
                            if(running_chart.value.length > 10)
                                running_chart.value = running_chart.value.slice(-10);

                            running_chart.value.setOption({
                                series: [{
                                    data: running_chart_data.value
                                }
                                ]
                            });
                        }
                        
                    }
                };

                // 连接关闭时触发
                ws.value.onclose = function(event) {
                    // console.log('WebSocket 连接已关闭');
                    app.state.socket = false
                    setTimeout(connectWebSocket, 5000);
                };

                // 处理错误，并尝试重新连接
                ws.value.onerror = function(error) {
                    // console.error('WebSocket 出错');
                    // console.error('WebSocket 出错', error);
                    // 2秒后尝试重新连接
                };
            } catch (error) {
            }
            
        }

        const start = record=>{
            state.start = true
            http('post',baseUrl + 'spider/task/start', {task_id:record.task_id}).then(res=>{
                state.start = false
                message.info(res.message)
            })
        }

        const stop = record=>{
            state.stop = true
            http('post',baseUrl + 'spider/task/stop', {task_id:record.task_id}).then(res=>{
                state.stop = false
                message.info(res.message)
            })
        }

        const switchStatus = record=>{
            http('post',baseUrl + 'spider/task/switchStatus', {task_id:record.task_id, status:record.status}).then(res=>{
                message.info(res.message)
            })
        }

        const del = record=>{
            state.del = true
            http('post',baseUrl + 'spider/task/delete', {task_id:record.task_id}).then(res=>{
                state.del = false
                if(res.code == 200){
                    message.success(res.message)
                    tasks.value = tasks.value.filter(item=>item.task_id!=record.task_id)
                }
                else message.error(res.message)
            })
        }

        const initEcharts = _=>{
            var chartDom = document.getElementById('taskChart');
            if(!chartDom) return
            running_chart.value = echarts.init(chartDom);
            option = {
                grid:[
                    {
                    show: true,
                    z: 0,
                    left: 40,
                    top: 40,
                    right: 0,
                    bottom: 40,
                    containLabel: false,
                    borderWidth: 0,
                    borderColor: '#ccc',
                    }
                ],
                title: {
                    text: ''
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    animation: true
                    }
                },
                xAxis: {
                    type: 'time',
                    splitLine: {
                    show: false
                    },
                    boundaryGap: false,
                },
                yAxis: {
                    splitNumber: 1,
                    type: 'value',
                    boundaryGap: [0, '100%'],
                    splitLine: {
                    show: false
                    }
                },
                series: [
                    {
                    name: 'Fake Data',
                    type: 'line',
                    showSymbol: false,
                    smooth: true,
                    data: [],
                    areaStyle: {}
                    }
                ]
            };
            option && running_chart.value.setOption(option);
        }

        return {
            current_task,
            task_status,
            state,
            server_log,
            columns,
            groups,
            tasks,
            pagination,
            add,
            edit,
            group_name,
            getTasks,
            info,
            testModal,
            logModal,
            appendLog,
            start,
            stop,
            del,
            contentModal,
            switchStatus,
            modal
        }
    }
}).use(ElementPlus, {
    locale: ElementPlusLocaleZhCn,
}).use(antd).use(coms).use(funcs).mount('#app');
</script>