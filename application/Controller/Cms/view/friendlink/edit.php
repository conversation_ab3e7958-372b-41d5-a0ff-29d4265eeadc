<div class=" padded">
<div class="block">
<div class="block-content">

<form id="validateForm" name="validateForm" class="form-horizontal" method="post" action="<?=site_url("cms/friendlink/edit")?>">

  <div class="col-md-8">
    <div class="page-header">基本内容</div>

    <div class="form-group">
      <label class="col-md-2 control-label">主题</label>
      <div class="col-md-10">
      <input type="text" name="data[title]" class="form-control" value="<?=$friendlink->getTitle()?>"/>
      <small class="help-block"></small>
      </div>
    </div>

    <div class="form-group">
      <label class="col-md-2 control-label">链接</label>
      <div class="col-md-10">
      <input type="text" name="data[url]" class="form-control" value="<?=$friendlink->getUrl()?:'http://'?>"/>
      <small class="help-block"></small>
      </div>
    </div>

    <div class="form-group">
      <label class="col-md-2 control-label">缩略图</label>
      <div class="col-md-10" id="upload_thumb">
      <input type="hidden" name="data[thumb]" class="form-control file_data" value="<?=$friendlink->getThumb()?>"/>
      <input type="button" name="button3" class="btn btn-primary file_button" value="上传" onclick="return $.window('上传','<?=site_path('common/webupload/item_type/cms_thumb/type/jpg,png')?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = JSON.parse($.getCache('uploaddata',true));if(uploaddata) $('#upload_thumb').find('input.file_data').val(uploaddata[0].path);addToPage('upload_thumb');}})">

      <?php if($friendlink->getThumb()): ?>
        <a href="#" data-href="<?=site_path('up_files/'.$friendlink->getThumb())?>" class="btn btn-info file_preview" onClick="return $.window('查看图片',$(this).attr('data-href'));" title="点击查看大图"><?=$friendlink->getThumb()?></a>
        <button type="button" class="btn btn-danger file_del" onclick="thumb_del('upload_thumb')">删除</button>
      <?php endif; ?>
      <small class="help-block"></small>
      </div>
    </div>

  </div>

  <input type="hidden" name="id" value="<?=$friendlink->getId()?>">
  <input type="hidden" name="getFromUrl" id="getFromUrl" value="">
</form>
</div>

</div>

<div class="row">
  <div class="col-xs-12 col-sm-12 col-md-12 text-center">
<a href="javascript:void(0);" onclick="return saveAndJump('<?=site_url('cms/friendlink/index')?>');" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存并返回</a>
<a href="javascript:void(0);" onclick="return saveAndJump('<?=site_url('cms/friendlink/edit')?>');" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存并继续</a>
  </div>
</div>
</div>
<div class="clearfix"></div>

<script type="text/javascript">
function thumb_del(obj){
  $('#'+obj).find('.file_preview').remove();
  $('#'+obj).find('.file_del').remove();
  $('#'+obj).find('.file_data').val('');
}

function saveAndJump(url){
  $('#getFromUrl').val(url);
  $('#validateForm').submit();
  return false;
}

function addToPage(containId){
  var file = $('#'+containId).find('.file_data').val();
  var button = '<a href="#" data-href="<?=site_path('up_files/')?>'+'/'+file+'" class="btn btn-info file_preview" onClick="return $.window(\'查看图片\',$(this).attr(\'data-href\'));" title="点击查看大图">'+file+'</a><button type="button" class="btn btn-danger file_del" onclick="thumb_del(\''+containId+'\')">删除</button>';

  $('#'+containId).find('.file_button').after(button);
}
</script>