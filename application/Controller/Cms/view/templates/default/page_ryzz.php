<?php include('header.php');?>
<link rel="stylesheet" href="<?=cms_path()?>/css/content.css" />
<div class="banner_inner">
    <!-- <p><a href="##" ><img src="<?=cms_thumb(cms_cat(97,'thumb'))?>" /></a></p> -->
</div>

<div class="box">
<div class="titleblock">
    <div class="bg"></div>
    <div class="text">
        <p><?=cms_cat(97,'subject')?></p>
        <!-- <p><?=strtoupper(cms_cat(97,'dirname'))?></p> -->
    </div>
</div>
</div>

<div class="box">
    <ul class="nav_inner">
        <?php foreach(cms_list_category(97) as $index=>$category): ?>
            <li class="<?=$category['id']==$id?'active':''?>" >
                <a href="<?=$category['url']?>">
                    <h3><?=$category['subject']?></h3>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>

    <div class="contents ">
        <div class="items items_ld">
            <ul class="list-unstyled">
            <?php foreach(cms_list($cats['id'],20,'order by displayorder ASC') as $item):?>
              <li >
                <a href="javascript:void(0);" style="display: block;border:1px solid #e5e5e5;text-align: center;box-shadow: 0 .5em 1em #DCDCDC;">
                    <div style="width: 100%;padding:0;height:250px;text-align: center;"><img style="height: 100%;width: 100%;" src="<?=cms_thumb($item['thumb'])?>" ></div>
                    <div style="padding:10px;">
                        <h3 align="left" style="color:#3f80db;"><?=cms_strcut($item['subject'],'50')?></h3>
                        <br/>
                        <p align="left">
                            <?=$item['description']?>
                        </p>
                    </div>
                </a>
              </li>
              <?php endforeach; ?>
            </ul>
        </div>
    </div>
    </div>
    

    <div id="myModal">
    <div class="modal">
    <div class="modal-header">
    <p id="title"></p>
    <span class="close">&times;</span>
    </div>
    <!--弹窗文本-->
    <div class="modal-content">
    <div id="content"></div>
    </div>
    </div>
</div>
<script type="text/javascript">
$(function(){
    $('.nav_inner li').mouseenter(function(){
        $(this).css('background','#DCDCDC').siblings('li').css('background','#FFF');
    }).mouseleave(function(){
        $(this).css('background','#FFF');
    });
});
</script>

<style type="text/css">
#myModal{
 display: none;
 position: fixed;
 z-index:1;
 left:0;
 top:0;
 width: 100%;
 height:100%;
 overflow: auto;
 background:rgba(0,0,0,0.5);
}
#myModal .modal{
 width: 900px;
 height:300px;
 position: relative;
 top:50%;
 left:50%;
 margin-top: -150px;
 margin-left: -450px;
 animation:animate 1s;
}
.modal .modal-header{
 height:50px;
 background:white;
 color: #000;
 line-height:50px;
 border-bottom: 1px solid #000000;
}
.modal .modal-header p{
 display: inline-block;
 margin:0;
 position: absolute;
 left: 20px;
}
.modal .modal-header .close{
 position: absolute;
 right:20px;
 font-size: 20px;
 cursor:pointer;
}
.modal .modal-content{
 background: white;
 height:300px;
 text-align: center;
 overflow-y: auto;
}
.modal .modal-content #content{
text-align: left;
 padding:5px;
 
}
.modal .modal-footer{
 position: relative;
 height:50px;
 background: white;
}
/*添加动画*/
@keyframes animate{
 from{top:0;opacity:0}
 to{top:50%;opacity:1}
}
</style>

<script type="text/javascript">
function showModal(k,val){
    var close=document.getElementsByClassName("close")[0];
    var myModal=document.getElementById("myModal");
    var title=document.getElementById("title");
    var content=document.getElementById("content");
    title.innerHTML = k
    content.innerHTML = val || '无'
    myModal.style.display="block";
    close.onclick=function () {
        myModal.style.display="none";
        title.innerHTML = ''
        content.innerHTML = ''
    }
        //用户点击其他地方关闭弹窗
    window.onclick=function (event) {
        if(event.target ==myModal){
            myModal.style.display="none";
            title.innerHTML = ''
            content.innerHTML = ''
        }
    }
}
</script>
<?php include('footer.php');?>