<?php
namespace App\Controller\Unitassistant;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\config;
use Sofast\Core\Lang;
class Award extends BaseController
{
	
	/**
	 * 综合搜索
	 */
	public function index()
	{
		$addWhere = "`corporation_id` like '".input::session("companyid")."%'";
		$this->awardgrid('unitassistant/award/index',$addWhere,20);
	}

	/**
	 * 等待审核的项目列表
	 */
	public function wait_list()
	{
		$addWhere = "statement = 4 and `corporation_id` like '".input::session("companyid")."%' and wait_for_company = '".input::session("companylevel")."'";
		$this->awardgrid('unitassistant/award/wait_list',$addWhere,20);
	}
	
	/**
	 * 上报的项目列表
	 */
	public function submit_list()
	{
//		$addWhere = "`corporation_id` like '".input::session("companyid")."%'  AND ((statement = 2 and wait_for_company < '".$_SESSION['companylevel']."') or (statement IN (5,9,10)))";
		$addWhere = "`corporation_id` like '".input::session("companyid")."%' and statement IN (6,8)";
		$this->awardgrid('unitassistant/award/submit_list',$addWhere,20);
	}
	/**
	 * 上报的申请列表
	 */
	public function submit_out()
	{
		$addWhere = "`corporation_id` = '".input::session("roleuserid")."'  AND ((state_for_out = 2 and wait_for_company < '".$_SESSION['companylevel']."') or (state_for_out IN (9,10)))";
		$this->awardgrid('unitassistant/award/submit_out',$addWhere,20);
	}
	/**
	 * 已经立项的项目列表
	 */
	public function radicate_list()
	{
		$this->awardgrid('unitassistant/award/radicate_list',"corporation_id = '".input::getInput("session.roleuserid")."' and statement >= '".config::get("HAS_RADICATE")."' ",20);
	}

	/**
	 * 已退回的项目列表
	 */
	public function rejected_list()
	{
		$addWhere = "statement IN (5,7,12,13,17,18) and `corporation_id` = '".input::getInput("session.roleuserid")."'";
		$this->awardgrid('unitassistant/award/rejected_list',$addWhere,20);
	}
	/**
	 * 已退回的申请列表
	 */
	public function rejected_out()
	{
		$addWhere = " state_for_out = 3 and corporation_id = '".input::getInput("session.roleuserid")."' ";
		$this->awardgrid('unitassistant/award/rejected_out',$addWhere,20);
	}	

	/**
	 * 已经结题的项目列表
	 */
	public function complete_list()
	{
		$this->awardgrid('unitassistant/award/index',"corporation_id = '".input::getInput("session.roleuserid")."' and statement >= '".config::get("HAS_COMPLETE")."' ",20);
	}
	/**
	 * 等待审核的申请列表
	 */
	public function wait_out()
	{
		$addWhere = "`corporation_id` = '".input::session("roleuserid")."'  AND state_for_out = 2 and wait_for_company = '".$_SESSION['companylevel']."'";
		$this->awardgrid('unitassistant/award/wait_out',$addWhere,20);
	}
	/**
	 * 审核项目
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("Awards")->selectByProjectId($ids[$i]);
			
			//检查单位帐号是否可以上报
			// if($msg = $project->getCorporation(true)->validate()) 
			// 	$this->page_debug(sprintf(lang::get("You can not operate, because the:%s!"),'<br />'.implode('<br />',$msg)),getFromUrl());
			if($project->getCorporation()->getIsLock())  $this->page_debug("单位账号还没有获得实名认证！",getFromUrl());
			//检查项目是否可以上报
			if($msg = $project->validate()) 
				$this->page_debug(sprintf(lang::get("You can not operate, because the:%s!"),'<br />'.implode('<br />',$msg)),getFromUrl());

			if($project->getWaitForCompany()<=2){
                //单位已审核
				$project->setStatement(7);
			}else{
			    //待二级单位审核
				$project->setWaitForCompany(2);
			}
			if($project->save())
				$msg =  '单位审核成功！';
			else
				$msg =  lang::get("Error has happened!");
			sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		}		
		$this->success($msg,site_url('unitassistant/award/wait_list'));
	}

    /**
     * 审核项目
     */
    function doAcceptOnlyOne()
    {
    	$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
    	if($project->isNew()) $this->page_debug('没有找到该项目！');
    	if(!$project->getAcceptId()){
    		$accept_id = sf::getModel('Types',$project->getTypeId())->getSerialNumber($project->getDeclareYear());
    		$project->setAcceptId($accept_id);
    	}
        //分管部门
    	$office_id = $project->getGuide(true)->getOfficeId();
    	if($office_id){
    		$project->setOfficeId($office_id);
    		$project->setOfficeSubject(sf::getModel("categorys",$office_id,'office')->getSubject());
    	}
    	$project->setStatement(5);
    	$project->setStateForAssess(0);
    	$project->save();
    	$msg = lang::get("Has been accepted!");
    	sf::getModel("historys")->addHistory($project->getProjectId(),$msg);
    	$this->success('审核通过！申报编号为：'.$project->getAcceptId(),getFromUrl());
    }

	/**
	 * 审核项目
	 */
	function doSubmitOnlyOne()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if($project->isNew()) $this->page_debug('没有找到该项目！');
		if(input::post("content")){
            $project->setStatement(6);
			$project->save();
			$project->sendMessage();
			sf::getModel("Historys")->addHistory($project->getProjectId(),input::post("content"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::set("msg",'审核通过！');
		view::apply("inc_body","unitassistant/award/note_submit");
		view::display("page_blank");
	}

    /**
     * 审核项目外协申请表
     */
    function doSubmitByOut()
    {
    	if(input::getInput("post.select_id")) 
    		$ids = input::getInput("post.select_id");
    	else 
    		$ids[] = input::getInput("mix.id");
    	if(!$ids[0])
    		$this->page_debug('请至少选择一个项目！',getFromUrl());	
    	for($i=0,$n=count($ids);$i<$n;$i++){
    		$project = sf::getModel("Awards")->selectByProjectId($ids[$i]);
            $project->setStateForOut(9);
    		$project->save();
    		sf::getModel("Historys")->addHistory($project->getProjectId(),'单位审核通过项目外协申请表！');
    	}
    	$this->success('审核通过！',getFromUrl());
    }
	/**
	 * 返至待审核
	 */
	function doWait()
	{

		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("get.id"));
		if($project->getCorporationId() != input::getInput("session.roleuserid") || $project->getStatement() > 6) 
			$this->page_debug(lang::get("You are't the owner!"),getFromUrl());	
		$project->setStatement(config::get("UNIT_WAIT"));
		if($project->save()) $msg =  "此项目已成功返回至“待审核”状态！";
		else $msg =  lang::get("Error has happened!");
		sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		$this->page_debug($msg,getFromUrl());					
	}	
	/**
	 * 驳回项目
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("get.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("Awards")->selectByProjectId($ids[$i]);
			$project->setStatement(12);
			if($project->save()) $msg =  lang::get("Has been rejected!");
			else $msg =  lang::get("Error has happened!");
			sf::getModel("Historys")->addHistory($project->getProjectId(),$msg);
		}
		$this->page_debug($msg,getFromUrl());	
	}
	/**
	 * 驳回项目
	 */
	function doRejectedOnlyOne()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
			if($project->getStatement() == '4'){
				$project->setStatement(7);//上级单位退回
			}else{
				$project->setStatement(3);//申报单位退回
			}			
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'单位退回！<br/>'.input::getInput("post.content"));
			pushMsgToResearcher('你的项目申请书已被退回','user/project/deformity_list',$project->getUserId());
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unitassistant/award/note");
		view::display("page_blank");
	}
	/**
	 * 驳回申请
	 */
	function rejectOutOnlyOne()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
			$project->setStateForOut(3);
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'单位退回项目外协申请表！<br/>'.input::getInput("post.content"));
			exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/退回成功！';</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unitassistant/award/note_out");
		view::display("page_blank");
	}	
	function rejectOutAll(){
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("mix.id");
		if(!$ids[0])
			$this->page_debug('请至少选择一个项目！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$project = sf::getModel("Awards")->selectByProjectId($ids[$i]);
			$project->setStateForOut(3);
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'单位退回项目外协申请表！');
		}
		$this->page_debug('批量退回成功！',getFromUrl());	
	}
	/**
	 * 发回重填
	 */
	function doBack()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
			$project->setStatement(5);
			$project->save();
            $project->sendMessage();
			sf::getModel("Historys")->addHistory($project->getProjectId(),lang::get("Has been rejected!")."<br />".input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unitassistant/award/back");
		view::display("page_blank");
	}

	public function doRadicate()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if(input::getInput("post.content")){
			$project->setStatement(5);
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),"同意立项！"."<br />".input::getInput("post.content"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("project",$project);
		view::apply("inc_body","unitassistant/award/radicate");
		view::display("page_blank");
	}

	public function reBack()
	{
		$project = sf::getModel("Awards")->selectByProjectId(input::getInput("mix.id"));
		if($project->isNew()) $this->error('没有找到该项目',getFromUrl());
		if(input::getInput("post.content")){
			$project->setStatement(5);
			$project->save();
			sf::getModel("Historys")->addHistory($project->getProjectId(),'单位/部门助理退回！<br/>'.input::getInput("post.content"));
			$this->success('退回成功！','javascript:top.location.reload();');
		}

        //原因表单
		$form = Form::load('unitassistant/award/reBack')
		->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($project->getSubject())
			)
		->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
		->addItem(Form::hidden('id',$project->getProjectId()))
		->render();

		view::set("inc_body",$form);
		view::display("page_blank");
	}

}