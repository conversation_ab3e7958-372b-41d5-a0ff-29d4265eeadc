<?php
namespace App\Controller\Milestone;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class Office extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/view/');
    }

    /**
     * 综合查询
     */
    function index()
    {
        $this->gird("office/index");
    }

    /**
     * 填写中的
     */
    public function wait_list()
    {
        $addWhere = "`statement` = 9 ";
        $this->gird("office/wait_list",$addWhere);
    }

    /**
     * 已上报的
     */
    public function accept_list()
    {
        $addWhere = "`statement` = 20 ";
        $this->gird("office/accept_list",$addWhere);
    }


    /**
     * 被退回的
     */
    public function back_list()
    {
        $addWhere = "`statement` = 12 ";
        $this->gird("office/back_list",$addWhere);
    }

    /**
     * 审核
     */
    function doAccept()
    {
        $milestone = sf::getModel("ProjectMilestones")->selectByMilestoneId(input::getInput("mix.id"));
        if($milestone->isNew()) $this->page_debug('没有找到该项内容！');
        if(input::post("content")){
            $milestone->setStatement(20);
            $milestone->save();
            $milestone->sendMessage();
            sf::getModel("Historys")->addHistory($milestone->getMilestoneId(),input::post("content"),'milestone');
            $this->refresh();
        }
        //原因表单
        $form = Form::load('milestone/office/doAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($milestone->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'审核意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('审核通过！'))
            ->addItem(Form::hidden('id',$milestone->getMilestoneId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 驳回
     */
    function doBack()
    {
        $milestone = sf::getModel("ProjectMilestones")->selectByMilestoneId(input::getInput("mix.id"));
        if($milestone->isNew()) $this->page_debug('没有找到该项内容！');
        if(input::getInput("post.content")){
            $milestone->setStatement(12);
            $milestone->save();
            $milestone->sendMessage();
            sf::getModel("Historys")->addHistory($milestone->getMilestoneId(),'已退回！<br/>'.input::getInput("post.content"),'milestone');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('milestone/office/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($milestone->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',$milestone->getMilestoneId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    public function gird($tpl = 'apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";
        if(input::getMix('parent_id')){
            $addWhere .= " AND `corporation_id` like '".rtrim(input::getMix("parent_id"),'0')."%' ";
        }
        if(input::getMix('corporation_id')){
            $addWhere .= " AND `corporation_id` like '".getParentCompanyId(input::getMix('corporation_id'))."%'";
        }
        $form_vars = array('field','search','corporation_id','parent_id','subject','statement','user_name','corporation_name','department_name');
        $this->view->set("pagers",sf::getModel('ProjectMilestones')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

}