<?php
namespace App\Controller\Bonus\Rewardgr\Assign;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Assign extends BaseController
{
    private $view = NULL;
    
    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../../view/');
    }


    public function index()
    {
        $assignReward = sf::getModel('BonusAssignRewards',input::getMix('id'));
        if($assignReward->isNew()) $this->error('找不到该项内容');
        if(input::session('userlevel')==2 && $assignReward->getManagerId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        if(input::post()){
            $moneys = input::post('money');
            $ownMoneys = input::post('own_money');
            $sorts = input::post('sort');
            $editMoney = false;
            foreach ($moneys as $userId=>$money){
                $assignUser = sf::getModel('BonusAssignUsers',$userId);
                $assignUser->setItemId($assignReward->getRewardId());
                $assignUser->setBonusItemId($assignReward->getId());
                $assignUser->setMoney($money);
                $assignUser->setOwnMoney($ownMoneys[$userId]);
                $assignUser->setSort($sorts[$userId]);
                $assignUser->save();
            }
            $totalMoney = array_sum($moneys);
            $totalOwnMoney = array_sum($ownMoneys);
            if($totalMoney>$assignReward->getMoney()) {
                $assignReward->setStateForAssign(1);
                $assignReward->save();
                $this->error('分配的授奖单位奖金不能大于'.$assignReward->getMoney().'元');
            }
            if($totalOwnMoney>$assignReward->getOwnMoney()) {
                $assignReward->setStateForAssign(1);
                $assignReward->save();
                $this->error('分配的二所配套奖金不能大于'.$assignReward->getOwnMoney().'元');
            }
            if($assignReward->waitAssignMoney()>0) {
                $assignReward->setStateForAssign(1);
                $assignReward->save();
                $this->error('授奖单位奖金还有'.$assignReward->waitAssignMoney().'元还未分配');
            }
            if($assignReward->waitAssignOwnMoney()>0) {
                $assignReward->setStateForAssign(1);
                $assignReward->save();
                $this->error('二所配套奖金还有'.$assignReward->waitAssignOwnMoney().'元还未分配');
            }
            $assignReward->setStateForAssign(9);
            if($assignReward->isConfirmAll()){
                $assignReward->setStateForAssign(10);
            }
            $assignReward->save();
            $url = site_url('bonus/rewardgr/assign/assign/index/id/'.$assignReward->getId());
            if(input::session('userlevel')==2) $url = site_url('bonus/rewardgr/assign/user/assign_list');
            $this->success('分配成功！',$url);
        }
        if(input::session('userlevel')!=2){
            $this->view->set("backurl",site_url('bonus/rewardgr/assign/company/reward_list/assign_company_id/'.$assignReward->getAssignCompany()->getId()));
        }
        $this->view->set("assignReward",$assignReward);
        $this->view->set("bonus",$assignReward->getBonus());
        $this->view->apply("inc_body",'rewardgr/assign/assign/index');
        $this->view->display('page_main');
    }


    public function show()
    {
        $assignReward = sf::getModel('BonusAssignRewards',input::getMix('id'));
        if($assignReward->isNew()) $this->error('找不到该项内容');
        if(input::session('userlevel')==2 && $assignReward->getManagerId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $this->view->set("assignReward",$assignReward);
        $this->view->set("bonus",$assignReward->getBonus());
        $this->view->apply("inc_body",'rewardgr/assign/assign/show');
        $this->view->display('page_blank');
    }

    /**
     * 添加人员
     */
    function edit_user()
    {
        $assignReward = sf::getModel('BonusAssignRewards',input::getMix('id'));
        if($assignReward->isNew()) $this->error('找不到该项内容');
        if(input::session('userlevel')==2 && $assignReward->getManagerId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $user = $assignReward->getAssignUserById(input::getMix('mid'));
        if($user->isNew()) {
            $user->setBonusType('rewardgr');
            $user->setItemId($assignReward->getRewardId());
            $user->setBonusItemId($assignReward->getId());
            $user->setBonusId($assignReward->getBonusId());
            $user->setCompanyId($assignReward->getCompanyId());
            $user->setCompanyName($assignReward->getCompanyName());
        }
        if(input::post()){
            if(empty(input::post('user_id'))) $this->error('请选择人员');
            $user->setSort(input::post('sort'));
            $researcher = sf::getModel('Declarers')->selectByUserId(input::post('user_id'));
            if($researcher->isNew()) $this->error('找不到该人员');
            $needConfirm = $user->getNeedConfirm();
            $user->setNeedConfirm((int)input::post('need_confirm'));
            $user->setUserId($researcher->getUserId());
            $user->setUserName($researcher->getPersonname());
            $user->setCorporationId($researcher->getSecondCompanyId());
            $user->setCorporationName($researcher->getSecondCompanyName());
            $user->setMoney((int)input::post('money'));
            $user->setOwnMoney((int)input::post('own_money'));
            if($user->getNeedConfirm()==0){
                if(!input::post('note')) $this->error('请填写分配依据');
                if(!input::post('bank_id')) $this->error('请选择银行卡');
                $bank = sf::getModel('UserBanks',input::post('bank_id'));
                $data['bank_id'] = $bank->getId();
                $data['bank_name'] = $bank->getName();
                $data['bank_branch'] = $bank->getBranch();
                $data['bank_no'] = $bank->getCard();
                $data['note'] =input::post('bank_note');
                $user->setNote(input::post('note'));
                $user->setData($data);
                $user->setConfirmAt(date('Y-m-d H:i:s'));
                $user->setStateForConfirm(10);
            }
            if($needConfirm==0 && $user->getStateForConfirm()==10 && $user->getNeedConfirm()==1){
                $user->setStateForConfirm(0);
                $user->setNote('');
                $user->setData([]);
                $user->setConfirmAt('');
            }
            if($user->isNew()) $user->setCreatedAt(date('Y-m-d H:i:s'));
            $user->save();
            $this->refresh();
        }
        $this->view->set("assignReward",$assignReward);
        $this->view->set("user",$user);
        $this->view->apply('inc_body','rewardgr/assign/assign/edit_user');
        $this->view->display('page_blank');
    }

    /**
     * 删除人员
     */
    function remove_user()
    {
        $assignReward = sf::getModel('BonusAssignRewards',input::getMix('id'));
        if($assignReward->isNew()) $this->error('找不到该项内容');
        if(input::session('userlevel')==2 && $assignReward->getManagerId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $user = $assignReward->getAssignUserById(input::getInput('mix.mid'));
        if($user->isNew()){
            $this->error('找不到该人员');
        }
        if($user->getBonusId()!=$assignReward->getBonusId()){
            $this->error('没有权限删除');
        }
        if(!$assignReward->getAssignCompany()->allowEdit()){
            $this->error('当前状态禁止删除');
        }
        $user->delete();
        if($assignReward->waitAssignMoney()>0) {
            $assignReward->setStateForAssign(1);
            $assignReward->save();
        }
        $this->success('删除成功',getFromUrl());
    }

}