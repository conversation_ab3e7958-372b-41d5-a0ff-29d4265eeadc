<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    [<?=$assignCompany->getCompanyName()?>]奖金分配情况
                </h3>
                <div class="block-options">
                    <?=Button::back();?>
                    <?php
                    if(input::session('userlevel')!=2):
                        ?>
                        <?=Button::setUrl(site_url('bonus/rewardrkx/assign/download/department/id/'.$assignCompany->getId()))->setIcon('download')->link('奖金分配部门汇总表')?>
                        <?=Button::setUrl(site_url('bonus/rewardrkx/assign/download/bank/id/'.$assignCompany->getId()))->setIcon('download')->link('奖金分配明细表')?>
                    <?php endif;?>
                </div>
            </div>
            <div class="block-content">

                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>奖励名称</th>
                        <th>奖金领取人</th>
                        <th class="d-none d-sm-table-cell">
                            奖金(元)
                        </th>
                        <th class="d-none d-sm-table-cell">
                            经办人
                        </th>
                        <th class="d-none d-sm-table-cell">
                            状态
                        </th>
                        <th class="text-center" style="width: 330px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    while($assignReward = $assignRewards->getObject()):
                        $reward = $assignReward->getReward(true);
                        ?>
                    <tr>
                        <td class="text-center">
                            <?=$assignRewards->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <a href="<?=site_url('user/reward/show/id/'.$reward->getRewardId())?>" target="_blank"><?=$reward->getSubject()?></a>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignReward->getAssignUserNames()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignReward->getMoney()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignReward->getManagerName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignReward->getAssignState()?>
                        </td>
                        <td class="text-center">
                            <?=Button::setUrl(site_url("bonus/rewardrkx/assign/assign/show/id/".$assignReward->getId()))->setIcon('show')->window('查看')?>
                            <?=Button::setUrl(site_url('bonus/rewardrkx/assign/download/reward/id/'.$assignReward->getId()))->setIcon('download')->link('分配明细表')?>
                            <?php
                            if(in_array($assignReward->getStateForAssign(),[0,1]) && in_array($assignCompany->getStatement(),[0,1,12])):
                            ?>
                            <?=Button::setUrl(site_url("bonus/rewardrkx/assign/assign/index/id/".$assignReward->getId()))->setIcon('edit')->link('分配')?>
                            <?php elseif(in_array($assignReward->getStateForAssign(),[9,10]) && in_array($assignCompany->getStatement(),[0,1,12])):?>
                            <?=Button::setUrl(site_url("bonus/rewardrkx/assign/assign/index/id/".$assignReward->getId()))->setIcon('edit')->setClass('btn-alt-danger')->link('更改')?>
                            <?php endif;?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                </table>
                <div class="clearfix"></div>
                <div class="header no-margin-top">附件列表</div>
                <table class="table">
                    <thead>
                    <tr>
                        <th class="text-center">序号</th>
                        <th class="text-center">文件名</th>
                        <th class="text-center">文件格式</th>
                        <th class="text-center">文件大小</th>
                        <th class="text-center">上传时间</th>
                        <th class="text-center">文件说明</th>
                        <th class="text-center" style="width:70px;">操作</th>
                    </tr>
                    </thead>
                    <tbody id="file-list">
                    <?php if($assignCompany->getAttachment('bonus_rewardrkx')->getTotal()>0):
                        $attachments = $assignCompany->getAttachment('bonus_rewardrkx');
                        while($file = $attachments->getObject()):
                            ?>
                            <tr id="file<?=$file->getId()?>">
                                <td class="text-center" style="width: 70px;">
                                    <?=$attachments->getIndex()?>
                                </td>
                                <td><i class="fa fa-caret-right orange"></i> <a href="<?=site_path('up_files/'.$file->getFilePath())?>" target="_blank"><?=$file->getFileName()?></a></td>
                                <td class="text-center"><?=$file->getFileExt()?></td>
                                <td class="text-center"><?=$file->getFileSize()?></td>
                                <td class="text-center"><?=$file->getCreatedAt()?></td>
                                <td class="text-center" style="width: 300px">
                                    <?=$file->getFileNote()?>
                                </td>
                                <td class="text-center">
                                    <?=Button::setUrl(site_path('up_files/'.$file->getFilePath()))->link('查看')?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                    <?php endif;?>
                    </tbody>
                    <tr>
                </table>
                <div class="clearfix"></div>
            </div>
        </div>
        </div>
    </div>
</div>
