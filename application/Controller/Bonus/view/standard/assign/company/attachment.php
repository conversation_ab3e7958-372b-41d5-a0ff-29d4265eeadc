<script type="text/javascript" src="<?=site_url('js/jquery.webupload.js')?>"></script>
<div class="content">
        <div class="row">
            <div class="col-lg-12">
                <div class="block block-rounded">
                    <div class="page-body">
                        <div class="block-content tab-content">
                            <div class="tab-pane active" id="tab1" role="tabpanel">
                                <?php
                                    if(!$isShow):
                                ?>
                            <div class="alert alert-warning alert-dismissable" role="alert">
                                <h3 class="alert-heading font-size-h6 my-2">
                                    附件上传说明
                                </h3>
                                <p>1.下载<?=Button::setUrl(site_url('bonus/standard/assign/download/department/id/'.$assignCompany->getId()))->link('部门汇总表')?>、<?=Button::setUrl(site_url('bonus/standard/assign/download/bank/id/'.$assignCompany->getId()))->link('奖金分配明细表')?>、<?=Button::setUrl(site_url('bonus/standard/assign/company/standard_list/assign_company_id/'.$assignCompany->getId()))->link('分配明细表')?>并打印盖章<br>2.将盖章件扫描为pdf文件并上传</p>
                            </div>

                            <div class="clearfix"></div>
                              <div class="form-group">
                                  <div id="uploader" class="wu-example dropzone">
                                      <div class="queueList">
                                          <div id="dndArea" class="placeholder">
                                              <div id="upload-<?=$itemType?>"></div>
                                              <?=Button::setIcon('fa fa-upload')->button('点击上传文件')?>
                                              <p>或者将文件拖到此处，上传文件应为pdf格式，体积不超过10MB</p>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                                    <?php endif;?>
                              <div class="clearfix"></div>
                              <div class="header no-margin-top">已上传文件列表 <small>(点击文件名可 查看/下载)</small></div>

                                <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="item_id" value="<?=$assignCompany->getId()?>">
                                    <input type="hidden" name="table" value="filemanager">
                                    <input type="hidden" name="upload_size" value="20480000">
                              <table class="table">
                                  <thead>
                                  <tr>
                                      <th class="text-center">序号</th>
                                      <th class="text-center">文件名</th>
                                      <th class="text-center">文件格式</th>
                                      <th class="text-center">文件大小</th>
                                      <th class="text-center">上传时间</th>
                                      <th class="text-center">文件说明</th>
                                      <th class="text-center" style="width:70px;">操作</th>
                                  </tr>
                                  </thead>
                                  <tbody id="file-list-<?=$itemType?>">
                                  <?php if($assignCompany->getAttachment($itemType)->getTotal()>0):
                                      $attachments = $assignCompany->getAttachment($itemType);
                                      while($file = $attachments->getObject()):
                                          ?>
                                          <tr id="file<?=$file->getId()?>">
                                              <td class="text-center" style="width: 70px;">
                                                  <input type="hidden" name="attachment_id[]" value="<?=$file->getId()?>">
                                                  <?php
                                                  if(!$isShow):
                                                  ?>
                                                  <input class="form-control" style="text-align: center" type="text" name="no[<?=$file->getId()?>]" value="<?=$attachments->getIndex()?>">
                                                  <?php else:?>
                                                      <?=$attachments->getIndex()?>
                                                  <?php endif;?>
                                              </td>
                                              <td><i class="fa fa-caret-right orange"></i> <a href="<?=site_path('up_files/'.$file->getFilePath())?>" target="_blank"><?=$file->getFileName()?></a></td>
                                              <td class="text-center"><?=$file->getFileExt()?></td>
                                              <td class="text-center"><?=$file->getFileSize()?></td>
                                              <td class="text-center"><?=$file->getCreatedAt()?></td>
                                              <td class="text-center" style="width: 300px">
                                                  <?php
                                                  if(!$isShow):
                                                  ?>
                                                  <input class="form-control" type="text" name="filenote[<?=$file->getId()?>]" value="<?=$file->getFileNote()?>">
                                                  <?php else:?>
                                                  <?=$file->getFileNote()?>
                                                  <?php endif;?>
                                              </td>
                                              <td class="text-center">
                                                  <?php
                                                  if(!$isShow):
                                                  ?>
                                                  <a href="javascript:delete_file('<?=$file->getId()?>','')" onClick="return confirm('确定要删除该文件吗？')" class="btn btn-danger btn-sm">删除</a>
                                                  <?php else:?>
                                                      <a href="<?=site_path('up_files/'.$file->getFilePath())?>" class="btn btn-alt-primary btn-sm">查看</a>
                                                  <?php endif;?>
                                              </td>
                                          </tr>
                                      <?php endwhile;?>
                                  <?php endif;?>
                                  </tbody>
                                  <tr>
                              </table>

                             <div class="clearfix"></div>

                                    <?php
                                    if(!$isShow):
                                    ?>
                              <div class="form-group col-xs-12 col-sm-12 col-md-12">
                                  <div style="width:100px;margin:0 auto;text-align:center">
                                      <a href="javascript:void(0);" onclick="form1.submit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                                  </div>
                              </div>
                                    <?php endif;?>
                                </form>
                              <div class="clearfix"></div>
                        </div>
                      </div>
                    </div>
                    <div class="page-footer">
                      <!--右下角浮动-->

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<script>
    var baseurl = $('#baseUrl').val();
    var fileSizeLimit = '100';   //单位：MB
    var fileSingleSizeLimit = '10';   //单位：MB
    var fileNumLimit = 10;   //允许上传的文件数量
    $(function(){
        var uploadFileNum = '<?=$assignCompany->getAttachment($itemType)->getTotal()?>';   //已上传的文件数量
        $('#upload-<?=$itemType?>').webupload({
            formData:{item_id:'<?=$assignCompany->getId()?>',item_type:'<?=$itemType?>'},
            accept:{
                title: 'PDF',
                extensions: 'pdf',
                mimeTypes: 'application/pdf'
            },
            dnd: '#uploader .queueList',
            paste: document.body,
            uploadFileNum:uploadFileNum,
            fileNumLimit:fileNumLimit,
            fileSizeLimit:fileSizeLimit*1024*1024,
            fileSingleSizeLimit:fileSingleSizeLimit*1024*1024,
        },{
            success:function(file, response){
                this.options.uploadFileNum++;
                $(".upload-<?=$itemType?>").removeClass('hidden');
                var data = eval(response._raw);
                var id = data[0].id;
                var file_name = data[0].file_name;
                var index1=file_name.lastIndexOf(".");
                var index2=file_name.length;
                var file_ext=file_name.substring(index1,index2);//后缀名
                var file_savepath = data[0].path;
                var file_size = _sizeFormat(data[0].size);
                var upload_time = data[0].time;
                var fid = file.id;

                var trhtml = '<tr id="file'+id+'">\n' +
                    '    <td class="text-center" style="width: 70px"><input type="hidden" name="attachment_id[]" value="'+id+'"><input class="form-control" style="text-align: center" type="text" name="no['+id+']" value="50" /></td>\n' +
                    '    <td><i class="fa fa-caret-right orange"></i> <a href="'+baseurl+'up_files/'+file_savepath+'" target="_blank">'+file_name+'</a></td>\n' +
                    '    <td class="text-center">'+file_ext+'</td>\n' +
                    '    <td class="text-center">'+file_size+'</td>\n' +
                    '    <td class="text-center">'+upload_time+'</td>\n' +
                    '    <td class="text-center" style="width: 300px"><input class="form-control" type="text" name="filenote['+id+']" value="" /></td>\n' +
                    '    <td class="text-center">\n' +
                    '\t<a href="javascript:delete_file('+id+',\''+fid+'\')" class="btn btn-danger btn-sm" onClick="return confirm(\'确定要删除该文件吗？\')">删除</a>\n' +
                    '    </td>\n' +
                    '</tr>';
                $(".ant-table-placeholder").remove();
                $("#file-list-<?=$itemType?>").append(trhtml);
            }
        });
    });

    function delete_file(id,fid) {
        var url = "<?=site_url('common/file_delete')?>";
        $.getJSON(url,{fid:id,id:'<?=$assignCompany->getId()?>'},function (data) {
            if(data.code==1){
                showSuccess('删除成功！');
                $("#file"+id).remove();
                $("#attachment_"+id).remove();
                if(fid) $('#PDF_'+fid).remove();
            }else{
                showError(data.msg);
            }
        });
    }
</script>