<div class="block-content">
    <div class="main">
        <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
            <input type="hidden" name="id" value="<?=$bonusPatent->getApplyId()?>">
            <table class="table table-bordered table-sm">
                <thead>
                <tr>
                    <th style="width: 50px" class="text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                    <th >专利名称</th>
                    <th style="width: 100px">类别</th>
                    <th style="width: 10%">发明人</th>
                    <th style="width: 100px">所属单位</th>
                    <th style="width: 110px">授权日期</th>
                </tr>
                </thead>
                <tbody>
                <?php
                while($patent = $pagers->getObject()):
                    ?>
                    <tr>
                        <td class="text-center"><input name="select_id[]" type="checkbox" value="<?=$patent->getPatentId()?>" /></td>
                        <td><?=$patent->getSubject()?></td>
                        <td><?=$patent->getType()?></td>
                        <td><?=$patent->getUserNames()?></td>
                        <td><?=$patent->getCompanyName()?></td>
                        <td><?=$patent->getDate()?></td>
                    </tr>
                <?php endwhile;?>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pagers->fromto().$pagers->navbar(6).$pagers->pagejump()?></span></td>
                </tr>
                </tfoot>
            </table>
        </form>
        <div class="form-group row">
            <div style="width: 130px;margin: 0 auto">
                <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('save')->setClass('btn-alt-primary btn-save')->button('保存')?>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            form1.submit();
        }
    }
</script>
