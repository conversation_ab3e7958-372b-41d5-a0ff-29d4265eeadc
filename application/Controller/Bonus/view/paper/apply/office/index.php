<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    论文奖金申请综合查询
                </h3>
            </div>
            <div class="block-content">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>
                            <?=getColumnStr('名称','subject')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('填报单位','company_id')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('联系人','linkman')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('状态','statement')?>
                        </th>
                        <th class="text-center" style="width: 100px;">
                            允许添加范围外论文
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($bonu = $pagers->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <a href="<?=site_url('bonus/paper/apply/apply/show/id/'.$bonu->getApplyId())?>"><?=$bonu->getSubject()?></a>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$bonu->getCompanyName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$bonu->getLinkman()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$bonu->getState()?>
                        </td>
                        <td class="text-center">
                            <div class="custom-control custom-switch custom-control-lg mb-2">
                                <input type="checkbox" class="custom-control-input extra_switch" id="task_<?=$bonu->getApplyId()?>" <?=$bonu->getExtraOpen()==1?'checked':''?> value="<?=$bonu->getApplyId()?>"> <label class="custom-control-label" for="task_<?=$bonu->getApplyId()?>"></label>
                            </div>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
<script>
    $(function(){
        $(".extra_switch").change(function(){
            var checked = $(this).is(':checked');
            var item_id = $(this).val();
            if(checked){
                extraOpen(item_id);
            }else{
                extraClose(item_id);
            }
        });
    });

    function extraOpen(id)
    {
        var url = '<?=site_url('bonus/paper/apply/office/extraOpen')?>';
        $.post(url,{id:id});
    }

    function extraClose(id)
    {
        var url = '<?=site_url('bonus/paper/apply/office/extraClose')?>';
        $.post(url,{id:id});
    }
</script>