<script type="text/javascript">
    function todo(json)
    {
        $.post('<?=site_url("bonus/paper/apply/apply/addExtraPaper")?>',{id:"<?=$bonusPaper->getApplyId()?>",paper_id:json.paper_id},function(json){
            if(json.code==1){
                location.href=location.href+'/_save/no/_msg/'+json.msg;
            }else{
                location.href=location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            论文奖励申请
        </h2>
        <div class="content-options">
            <?=Button::setUrl(site_url('bonus/paper/apply/apply/wait_list'))->setIcon('back')->link('返回')?>
            <?php
            if(!$bonusPaper->isNew()):
                ?>
                <?=Button::setUrl(site_url("bonus/paper/apply/apply/show/id/".$bonusPaper->getApplyId()))->setIcon('show')->link('查看预览')?>
                <?=Button::setUrl(site_url("bonus/paper/apply/apply/submit/id/".$bonusPaper->getApplyId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
            <?php endif;?>
        </div>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <table class="table table-bordered table-sm">
                            <thead>
                            <tr>
                                <th class="text-center" style="width: 50px" >序号</th>
                                <th class="text-center">题目</th>
                                <th class="text-center" style="width: 200px">刊物/会议</th>
                                <th class="text-center" style="width: 200px">作者</th>
                                <th class="text-center" style="width: 120px">级别</th>
                                <th class="text-center" style="width: 120px">发表日期</th>
                                <th class="text-center" style="width: 70px">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                                while($paper = $papers->getObject()):
                            ?>
                                <tr class="<?=$paper->getIsExtra()?'text-danger':''?>">
                                    <td class="text-center"><?=$papers->getIndex()?></td>
                                    <td><?=$paper->getSubject()?></td>
                                    <td><?=$paper->getPublication()?></td>
                                    <td class="text-left" ><?=$paper->getUserNames()?></td>
                                    <td class="text-center" ><?=$paper->getLevels()?></td>
                                    <td class="text-center" ><?=$paper->getDate()?></td>
                                    <td class="text-center"><?=Button::setUrl(site_url('bonus/paper/apply/apply/delete_paper/id/'.$bonusPaper->getApplyId().'/paper_id/'.$paper->getPaperId()))->setClass('btn-alt-danger')->delete('删除')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <div class="clearfix"></div>
                        <div class="form-group row">
                            <div class="col-sm-8 ml-auto">
                                <?=Button::setUrl(site_url('bonus/paper/apply/apply/paper_list/id/'.$bonusPaper->getApplyId()))->setIcon('add')->window('选取')?>

                                <?php
                                if($bonusPaper->getExtraOpen()==1):
                                ?>
                                    <?=Button::setUrl(site_url('common/paper_search'))->setIcon('add')->setClass('btn-alt-danger')->window('添加范围外论文')?>
                                <?php endif;?>
                            </div>
                        </div>

                    </div>

            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm"></span> 正在保存...');
        validateForm.submit();
    }
</script>