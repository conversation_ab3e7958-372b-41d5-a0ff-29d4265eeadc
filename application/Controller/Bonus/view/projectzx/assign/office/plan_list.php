<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    纵向项目立项奖金发放计划
                </h3>
                <div class="block-options">
                    <?=Button::setName('新增计划')->setIcon('add')->setUrl(site_url('bonus/projectzx/assign/plan/edit'))->link()?>
                </div>
            </div>
            <div class="block-content">

                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>
                            <?=getColumnStr('名称','subject')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('截止时间','end_at')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            科研考核年度
                        </th>
                        <th class="d-none d-sm-table-cell">
                            奖金(万元)
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('状态','statement')?>
                        </th>
                        <th class="text-center" style="width: 170px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($pager = $pagers->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <?=$pager->getSubject()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getEndAt()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getBonusYear()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getMoney()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getState()?>
                        </td>
                        <td class="text-center">
                           <?=Button::setUrl(site_url("bonus/projectzx/assign/plan/edit/id/".$pager->getBonusId()))->setIcon('edit')->link('编辑')?>
                           <?=Button::setUrl(site_url("bonus/projectzx/assign/plan/delete/id/".$pager->getBonusId()))->setIcon('delete')->setClass('btn-alt-danger')->delete('删除')?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
