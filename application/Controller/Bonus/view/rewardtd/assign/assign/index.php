<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            科技创新先进团队奖金分配
        </h2>
        <div class="content-options">
            <?=Button::setUrl($backurl?:site_url('bonus/rewardtd/assign/user/assign_list'))->setIcon('back')->link('返回')?>
        </div>
    </div>

    <form name="validateForm" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">奖金事项<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <?=$bonus->getSubject()?>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">奖励名称<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <?=$assignReward->getReward()->getSubject()?>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">待分配金额<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                ￥<?=$assignReward->waitAssignMoney()?>元
                            </div>
                        </div>
                        <div class="header no-margin-top">奖金领取人员</div>
                        <table class="table table-hover datatable col-md-12">
                            <thead>
                            <tr>
                                <th class="text-center" style="width: 80px">排序</th>
                                <th class="text-center" style="width: 80px">序号</th>
                                <th style="width: 100px">姓名</th>
                                <th >单位/部门</th>
                                <th  style="width: 110px">奖金(元)</th>
                                <th>银行账号</th>
                                <th>开户银行</th>
                                <th>付款附言</th>
                                <th>状态</th>
                                <th class="text-center" style="width:130px;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="member-list">
                            <?php $users = $assignReward->selectAssignUsers(); ?>
                            <?php while ($user = $users->getObject()) :?>
                                <tr>
                                    <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                    <td class="text-center">
                                        <input type="hidden" name="sort[<?=$user->getId()?>]" class="sort" value="<?=$users->getIndex()?>">
                                        <span class="sort_text"><?=$users->getIndex()?></span>
                                    </td>
                                    <td>
                                        <?=$user->getUserName()?>
                                    </td>
                                    <td>
                                        <?=$user->getCorporationName()?>
                                    </td>
                                    <td>
                                        <input class="form-control int" type="text" name="money[<?=$user->getId()?>]" value="<?=$user->getMoney()?>">
                                    </td>
                                    <td><?=$user->getData('bank_no')?></td>
                                    <td><?=$user->getData('bank_name')?></td>
                                    <td><?=$user->getData('note')?></td>
                                    <td><?=$user->getState()?></td>
                                    <td class="text-center">
                                        <?=Button::setUrl(site_url('bonus/rewardtd/assign/assign/edit_user/id/'.$assignReward->getId().'/mid/'.$user->getId()))->window('编辑')?>
                                        <?=Button::setUrl(site_url('bonus/rewardtd/assign/assign/remove_user/id/'.$assignReward->getId().'/mid/'.$user->getId()))->setClass('btn-alt-danger')->delete('删除')?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                            </tbody>
                        </table>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 300px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('bonus/rewardtd/assign/assign/edit_user/id/'.$assignReward->getId()))->setIcon('add')->setClass('btn-alt-success')->window('添加人员')?>
                                <?=Button::setType('submit')->setIcon('check')->button('完成分配')?>
                            </div>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script src="<?=site_url('js/math.js')?>"></script>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
        validateForm.submit();
    }

    var filetable = document.getElementById('member-list');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#member-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
            })
        },

    });


</script>