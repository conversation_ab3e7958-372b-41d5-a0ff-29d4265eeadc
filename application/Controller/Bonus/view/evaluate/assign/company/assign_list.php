<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    奖金分配情况
                </h3>
            </div>
            <div class="block-content">

                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>事项名称</th>
                        <th class="d-none d-sm-table-cell">
                            奖金(元)
                        </th>
                        <th style="width: 170px;">
                            状态
                        </th>
                        <th class="text-center" style="width: 400px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($pager = $pagers->getObject()):
                        $bonus = $pager->getBonus(true);
                        ?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <?=$bonus->getSubject()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getMoney()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$pager->getState()?>
                        </td>
                        <td class="text-center">
                            <?=Button::setUrl(site_url("bonus/evaluate/assign/assign/show/id/".$pager->getId()))->setIcon('show')->window('查看')?>
                            <?php
                            if(in_array($pager->getStateForAssign(),[0,1]) && in_array($pager->getStatement(),[0,1,12])):
                                ?>
                                <?=Button::setUrl(site_url("bonus/evaluate/assign/assign/index/id/".$pager->getId()))->setIcon('edit')->link('分配')?>
                            <?php elseif(in_array($pager->getStateForAssign(),[9,10]) && in_array($pager->getStatement(),[0,1,12])):?>
                                <?=Button::setUrl(site_url("bonus/evaluate/assign/assign/index/id/".$pager->getId()))->setIcon('edit')->setClass('btn-alt-danger')->link('更改')?>
                            <?php endif;?>
                            <?php
                            if($pager->allowEdit()):
                                ?>
                                <?=Button::setUrl(site_url("bonus/evaluate/assign/company/attachment/id/".$pager->getId()))->setIcon('upload')->setEndEvent('location.reload()')->window('附件('.$pager->getAttachementCount().')')?>
                                <?=Button::setUrl(site_url("bonus/evaluate/assign/company/submit/id/".$pager->getId()))->setIcon('submit')->setClass('btn-alt-warning')->link('上报')?>
                            <?php else:?>
                                <?=Button::setUrl(site_url("bonus/evaluate/assign/company/attachment/is_show/1/id/".$pager->getId()))->setIcon('upload')->setEndEvent('location.reload()')->window('附件('.$pager->getAttachementCount().')')?>
                            <?php endif;?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
