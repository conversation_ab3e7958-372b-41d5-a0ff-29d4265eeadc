<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    奖金确认
                </h3>
            </div>
            <div class="block-content">
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序号
                        </th>
                        <th>奖励名称</th>
                        <th class="d-none d-sm-table-cell">
                            奖金(元)
                        </th>
                        <th class="d-none d-sm-table-cell">
                            银行账号
                        </th>
                        <th class="d-none d-sm-table-cell">
                            开户银行
                        </th>
                        <th class="d-none d-sm-table-cell">
                            付款附言
                        </th>
                        <th style="width: 170px;">
                            状态
                        </th>
                        <th class="text-center" style="width: 170px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($assignUser = $assignUsers->getObject()):
                        $bonus = $assignUser->getBonus(true);
                        $reward = $assignUser->getReward(true);
                        ?>
                    <tr>
                        <td class="text-center">
                            <?=$assignUsers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <?=$reward->getSubject()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignUser->getMoneyTotal()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignUser->getData('bank_no')?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignUser->getData('bank_name')?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignUser->getData('note')?:'科技奖(配套)奖金'?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$assignUser->getState()?>
                        </td>
                        <td class="text-center">
                            <?php
                                if($assignUser->getStateForConfirm()==0):
                            ?>
                            <?=Button::setUrl(site_url("bonus/reward/assign/user/doConfirm/id/".$assignUser->getId()))->setIcon('check')->window('确认')?>
                            <?php else:?>
                            <?=Button::setUrl(site_url("bonus/reward/assign/user/doConfirm/id/".$assignUser->getId()))->setIcon('check')->setClass('btn-alt-danger')->window('更改')?>
                            <?php endif;?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
