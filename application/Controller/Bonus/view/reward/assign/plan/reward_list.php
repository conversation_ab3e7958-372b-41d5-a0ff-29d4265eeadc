<div class="block-content">
    <div class="main">
        <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
            <input type="hidden" name="bonus_id" value="<?=$bonus->getBonusId()?>">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th style="width: 50px" class="text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                    <th>奖励荣誉名称</th>
                    <th class="text-center">奖项类别</th>
                    <th class="text-center">奖项级别</th>
                    <th class="text-center">获奖日期</th>
                    <th class="text-center">授奖单位奖金<br>(元)</th>
                    <th class="text-center">二所配套奖金<br>(元)</th>
                    <th class="text-center">研发单位奖金<br>(元)</th>
                    <th class="text-center">管理部门奖金<br>(元)</th>
                </tr>
                </thead>
                <tbody>
                <?php
                while($pager = $pagers->getObject()):
                    ?>
                    <tr>
                        <td class="text-center"><input name="select_id[]" type="checkbox" value="<?=$pager->getRewardId()?>" /></td>
                        <td><?=$pager->getSubject()?></td>
                        <td class="text-center"><?=$pager->getCategory()?></td>
                        <td class="text-center"><?=$pager->getLevel()?></td>
                        <td class="text-center"><?=$pager->getDate()?></td>
                        <td class="text-center"><?=$pager->getIncomeMoney()?></td>
                        <td class="text-center"><?=$pager->getOwnMoney()?></td>
                        <td><?=$pager->getReseachCompanyMoneyTable()?></td>
                        <td><?=$pager->getDepartmentCompanyMoneyTable()?></td>
                    </tr>
                <?php endwhile;?>
                </tbody>
                <tfoot>
                <tr>
                    <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pagers->fromto().$pagers->navbar(6).$pagers->pagejump()?></span></td>
                </tr>
                </tfoot>
            </table>
        </form>
        <div class="form-group row">
            <div style="width: 130px;margin: 0 auto">
                <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('save')->setClass('btn-alt-primary btn-save')->button('保存')?>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            form1.submit();
        }
    }
</script>
