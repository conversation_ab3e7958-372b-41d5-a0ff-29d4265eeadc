<?php
namespace App\Controller\Bonus\Performance\Assign;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Download extends BaseController
{
    public function projectList()
    {
        $bonus = sf::getModel('Bonus')->selectByBonusId(input::getMix('bonus_id'));
        if($bonus->isNew()) $this->error('找不到该奖金发放事项');
        $companys = getSecondCompanys();
        $companyIds = [];
        foreach ($companys as $cid=>$company){
            $companyIds[] = "'".$cid."'";
        }

        $template = WEBROOT . '/up_files/tpl/bonus_performance_office.xlsx'; //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();

        //先按项目类型排序、再按部门排序
        $body = [];
        $bonusPerformances = sf::getModel('BonusPerformances')->selectAll("bonus_id = '{$bonus->getBonusId()}' and state_for_assign > 0 order by field(`company_id`,".implode(',',$companyIds).")");
        $i=0;
        while($bonusPerformance = $bonusPerformances->getObject()){
            $project = $bonusPerformance->getProject(true);
            $body[$i][] = $bonusPerformances->getIndex();
            $body[$i][] = $project->getSubject();
            $body[$i][] = $project->getProjectSource();
            $body[$i][] = $project->getCorporationName();
            $body[$i][] = $project->getUserName();
            $body[$i][] = $bonusPerformance->getMoney();
            $i++;
        }

        $startRow = 2;
        $index='A';
        $objActSheet->setCellValue('A1',$bonus->getBonusYear().'年度科研项目绩效发放清单');
        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            if($key>0) $objPHPExcel->getActiveSheet()->insertNewRowBefore($startRow, 1);
            for($i=0;$i<count($value);$i++){
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                $index++;
            }
        }

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$bonus->getBonusYear().'年度科研项目绩效发放清单.xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        ob_end_clean();
        $objWriter->save('php://output');
    }

    public function project()
    {
        $bonusPerformance = sf::getModel('BonusPerformances',input::getMix('id'));
        if($bonusPerformance->isNew()) $this->error('找不到该项内容');
        if(input::session('userlevel')==2 && $bonusPerformance->getManagerId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $template = WEBROOT . '/up_files/tpl/bonus_project.xlsx'; //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();
        $body = [];
        $users = $bonusPerformance->selectAssignConfirmUsers();
        $i=0;
        while($user = $users->getObject()){
            $body[$i][] = $users->getIndex();
            $body[$i][] = filterName($user->getUserName());
            $body[$i][] = $user->getMoney();
            $body[$i][] = $user->getNote();
            $i++;
        }
        $startRow = 4;
        $index='A';
        $objActSheet->setCellValue('A1',$bonusPerformance->getBonus()->getSubject());
        $objActSheet->setCellValue('B2',$bonusPerformance->getCompanyName());
        $objActSheet->setCellValue('B3',$bonusPerformance->getProject()->getSubject());

        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            if($key>0) $objPHPExcel->getActiveSheet()->insertNewRowBefore($startRow, 1);
            for($i=0;$i<count($value);$i++){
                if($value[$i]=='BLANK') {
                    $index++;
                    continue;
                }
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                $index++;
            }
//            $mergeCells[] =  = implode(':',$_mergeCell);
//            if($mergeCells) mergeCells($this->objPHPExcel,$mergeCells);
            $objActSheet->mergeCells('D'.$startRow.':G'.$startRow);
            //分配依据居左
            $objPHPExcel->getActiveSheet()->getStyle('D'.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
            //分配依据自动换行
            $objPHPExcel->getActiveSheet()->getStyle('D'.$startRow)->getAlignment()->setWrapText(TRUE);
            //分配依据自动行高
//            $objPHPExcel->getActiveSheet()->getRowDimension($startRow)->setRowHeight(-1);
        }
        $objActSheet->setCellValue('C'.($startRow+1),'=SUM(C5:C'.($startRow).')');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$bonusPerformance->getBonus()->getSubject().'项目分配明细('.$bonusPerformance->getSubject().').xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        ob_end_clean();
        $objWriter->save('php://output');
    }

    public function department()
    {
        $assignCompany = sf::getModel('BonusAssignCompanys',input::getMix('id'));
        if($assignCompany->isNew()) $this->error('找不到该项内容');
        if(!in_array(input::session('userlevel'),[5,6]) && $assignCompany->getCompanyId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $template = WEBROOT . '/up_files/tpl/bonus_department.xlsx'; //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();

        $users = $assignCompany->getAssignConfirmUsersByGroup();
        $body = [];
        $i=0;
        foreach($users as $user){
            $body[$i][] = $i+1;
            $body[$i][] = filterName($user['user_name']);
            $body[$i][] = $user['money'];
            $i++;
        }

        $startRow = 3;
        $index='A';
        $objActSheet->setCellValue('A1',$assignCompany->getBonus()->getSubject().'分配汇总表');
        $objActSheet->setCellValue('B2',$assignCompany->getCompanyName());

        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            if($key>0) $objPHPExcel->getActiveSheet()->insertNewRowBefore($startRow, 1);
            for($i=0;$i<count($value);$i++){
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                $index++;
            }
        }
        $objActSheet->setCellValue('C'.($startRow+1),'=SUM(C3:C'.($startRow).')');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$assignCompany->getBonus()->getSubject().'分配汇总表('.$assignCompany->getCompanyName().').xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        ob_end_clean();
        $objWriter->save('php://output');
    }

    public function bank()
    {
        $assignCompany = sf::getModel('BonusAssignCompanys',input::getMix('id'));
        if($assignCompany->isNew()) $this->error('找不到该项内容');
        if(!in_array(input::session('userlevel'),[5,6]) && $assignCompany->getCompanyId()!=input::session('roleuserid')) $this->error('没有权限执行该操作');
        $template = WEBROOT . '/up_files/tpl/bonus_bank.xlsx'; //使用导出模板
        $objPHPExcel = \PHPExcel_IOFactory::load($template);     //加载excel文件,设置模板
        $objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);  //设置保存版本格式
        //设置默认字体
        $objPHPExcel->getDefaultStyle()->getFont()->setName('宋体');
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet = $objPHPExcel->getActiveSheet();

        $users = $assignCompany->getAssignConfirmUsersByGroup();
        $body = [];
        $i=0;
        foreach($users as $user){
            $assignUser = $assignCompany->getAssignUserById($user['id']);
            $_user = $assignUser->getUser(true);
            $_bank = sf::getModel('UserBanks',$assignUser->getData('bank_id'));
            $body[$i][] = $i+1;
            $body[$i][] = filterName($assignUser->getUserName());
            $body[$i][] = $user['money'];
            $body[$i][] = $assignUser->getData('bank_no');
            $body[$i][] = $assignUser->getData('bank_name').$assignUser->getData('bank_branch');
            $body[$i][] = $_bank->getNo();
            $body[$i][] = $_user->getUserIdcard();
            $body[$i][] = $_user->getUserMobile();
            $body[$i][] = $assignUser->getData('note');
            $i++;
        }

        $startRow = 4;
        $index='A';
        $objActSheet->setCellValue('A1',$assignCompany->getBonus()->getSubject().'奖金分配明细表('.$assignCompany->getCompanyName().')');
        $objActSheet->setCellValue('A2','填报单位/部门：'.$assignCompany->getCompanyName());

        foreach($body as $key=>$value){
            $startRow++;
            $index='A';
            if($key>0) $objPHPExcel->getActiveSheet()->insertNewRowBefore($startRow, 1);
            for($i=0;$i<count($value);$i++){
                $objActSheet->setCellValue($index.$startRow,$value[$i]);
                $objPHPExcel->getActiveSheet()->getStyle($index.$startRow)->getAlignment()->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
                if(in_array($i,[3,5,6])){
                    //银行卡、身份证 使用文本类型
                    $objPHPExcel->getActiveSheet()->setCellValueExplicit($index.$startRow,$value[$i],\PHPExcel_Cell_DataType::TYPE_STRING);
                }
                $index++;
            }
        }
        $objActSheet->setTitle('科研绩效');

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="'.$assignCompany->getBonus()->getSubject().'奖金分配明细表('.$assignCompany->getCompanyName().').xlsx"');
        header('Cache-Control: max-age=0');
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        ob_end_clean();
        $objWriter->save('php://output');
    }

}