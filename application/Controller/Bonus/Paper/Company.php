<?php
namespace App\Controller\Bonus\Paper;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{
    private $view = NULL;
    
    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
    }

    function guide_list()
    {
//        $pagers = sf::getModel('Bonus')->getPager("bonus_type = 'paper' and statement = 20 and unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') and unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."')","order by id desc");
        $pagers = sf::getModel('Bonus')->getPager("bonus_type = 'paper' and apply_statement = 20 and unix_timestamp(apply_start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') ","order by id desc");
        $this->view->set("pagers",$pagers);
        $this->view->apply('inc_body','paper/company/guide_list');
        $this->view->display('page');
    }

//    public function index()
//    {
//        $addWhere = "`company_id` = '".input::session("roleuserid")."'";
//        $this->gird('paper/company/index',$addWhere);
//    }

    public function write_list()
    {
        $addWhere = "`user_id` = '".input::session("roleuserid")."' and statement in (0,1,5,7,12,17)";
        $this->gird('paper/company/write_list',$addWhere);
    }

    public function submit_list()
    {
        $addWhere = "`user_id` = '".input::session("roleuserid")."'  AND statement in (6,9,15,20)";
        $this->gird('paper/company/submit_list',$addWhere);
    }


    public function bonus_list()
    {
        $pagers = sf::getModel('BonusAssignCompanys')->getPager("company_id = '".input::session("roleuserid")."' and bonus_id in (select bonus_id from bonus where bonus_type = 'paper' and statement = 20)","order by id desc");
        $this->view->set("pagers",$pagers);
        $this->view->apply('inc_body','paper/company/bonus_list');
        $this->view->display('page_main');
    }

    public function manager_list()
    {
        $assignCompanyId = input::getMix('assign_company_id');
        $assignCompany = sf::getModel('BonusAssignCompanys',$assignCompanyId);
        if ($assignCompany->isNew()) $this->error('没有找到该内容');
        $assignPapers = $assignCompany->selectAssignProjects();
        $this->view->set("assignPapers",$assignPapers);
        $this->view->apply('inc_body','paper/company/manager_list');
        $this->view->display('page_main');
    }

    public function assign_paper_list()
    {
        $assignCompany = sf::getModel('BonusAssignCompanys',input::getMix('assign_company_id'));
        if ($assignCompany->isNew()) $this->error('没有找到该内容');
        $assignPapers = $assignCompany->selectAssignProjects();
        $this->view->set("assignCompany",$assignCompany);
        $this->view->set("assignPapers",$assignPapers);
        $this->view->apply('inc_body','paper/company/assign_paper_list');
        $this->view->display('page_main');
    }

    public function doAssign()
    {
        $assignPaper = sf::getModel('BonusPaperDetails',input::getMix('id'));
        if($assignPaper->isNew()) $this->error('找不到该项内容');
        if(input::post()){
            $moneys = input::post('money');
            $sorts = input::post('sort');
            foreach ($moneys as $userId=>$money){
                $assignUser = sf::getModel('BonusAssignUsers',$userId);
                $assignUser->setBonusItemId($assignPaper->getId());
                $assignUser->setMoney($money);
                $assignUser->setSort($sorts[$userId]);
                $assignUser->save();
                if($assignUser->isConfirmAll()){
                    $assignPaper->setStateForAssign(10);
                }
            }
            $totalMoney = array_sum($moneys);
            if($totalMoney>$assignPaper->getMoney()) $this->error('分配的总金额不能大于'.$assignPaper->getMoney().'元');
            if($assignPaper->waitAssignMoney()>0) $this->error('还有'.$assignPaper->waitAssignMoney().'元还未分配');
            if($assignPaper->getStateForAssign()!=10){
                $assignPaper->setStateForAssign(9);
            }
            $assignPaper->save();
            $this->success('分配成功！',site_url('bonus/paper/company/doAssign/id/'.$assignPaper->getId()));
        }
        $this->view->set("backurl",site_url('bonus/paper/company/assign_paper_list/assign_company_id/'.$assignPaper->getAssignCompany()->getId()));
        $this->view->set("assignPaper",$assignPaper);
        $this->view->set("bonus",$assignPaper->getBonus());
        $this->view->apply("inc_body",'paper/user/assign');
        $this->view->display('page_main');
    }


    public function showAssign()
    {
        $assignPaper = sf::getModel('BonusPaperDetails',input::getMix('id'));
        if($assignPaper->isNew()) $this->error('找不到该项内容');
        $this->view->set("assignPaper",$assignPaper);
        $this->view->set("bonus",$assignPaper->getBonus());
        $this->view->apply("inc_body",'paper/user/assign_show');
        $this->view->display('page_blank');
    }

    public function assign_list()
    {
        $pagers = sf::getModel('BonusAssignCompanys')->getPager("company_id = '".input::session("roleuserid")."' and bonus_id in (select bonus_id from bonus where bonus_type = 'paper' and statement = 20) ","order by id desc");
        $this->view->set("pagers",$pagers);
        $this->view->apply('inc_body','paper/company/assign_list');
        $this->view->display('page_main');
    }

    public function attachment()
    {
        $assignCompany = sf::getModel("BonusAssignCompanys",input::getMix("id"));
        if($assignCompany->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('itemType','bonus_paper');
        $this->view->set('assignCompany',$assignCompany);
        $this->view->set('isShow',input::getMix('is_show'));
        $this->view->apply('inc_body','paper/company/attachment');
        $this->view->display('page_blank');
    }

    public function submit()
    {
        $assignCompany = sf::getModel("BonusAssignCompanys",input::getMix("id"));
        if($assignCompany->isNew()) $this->page_debug(lang::get('The project is not found!'));
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $assignCompany->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $assignCompany->setSubmitAt(date("Y-m-d H:i:s"));
            $assignCompany->setStatement(9);       //待科技处助理审核
            $assignCompany->save();
            sf::getModel('Historys')->addHistory($assignCompany->getId(),'论文奖金分配情况上报','bonus_paper');
            $this->success(lang::get("Has been submit!"),site_url("bonus/paper/company/assign_list"));
        }
        $this->view->set('assignCompany',$assignCompany);
        $this->view->set('msg',$assignCompany->validate());
        $this->view->apply("inc_body","paper/company/submit");
        $this->view->display("page");
    }




    public function setManager()
    {
        $assignPaper = sf::getModel('BonusPaperDetails',input::getMix('id'));
        if($assignPaper->isNew()) $this->error('找不到该项内容！',getFromUrl());
        if(input::post()){
            $user = sf::getModel("Declarers")->selectByUserId(input::post('manager_id'));
            if($user->isNew()) $this->error('找不到该人员');
            $assignPaper->setManagerId($user->getUserId());
            $assignPaper->setManagerName($user->getPersonname());
            $assignPaper->save();
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/设置成功！'</script>");
        }
        $this->view->set('assignPaper',$assignPaper);
        $this->view->apply("inc_body","paper/company/manager");
        $this->view->display("page_blank");
    }

    public function gird($tpl = 'paper/company/index',$addWhere = '1',$showMax=15,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";
        input::getInput("mix.bonus_year") && $addWhere .= " AND `bonus_year` = '".input::getInput("mix.bonus_year")."' ";

        $form_vars = array('field','search','company_id','subject','bonus_year','statement','user_name','company_name');
        $pagers = sf::getModel('BonusPapers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars);
        $this->view->set("pagers",$pagers);
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}