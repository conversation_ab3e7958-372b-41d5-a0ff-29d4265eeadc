<?php
namespace App\Controller\Bonus\Paper\Apply;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{
    private $view = NULL;
    
    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../../view/');
    }

    function guide_list()
    {
//        $pagers = sf::getModel('Bonus')->getPager("bonus_type = 'paper' and statement = 20 and unix_timestamp(start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') and unix_timestamp(end_at) > unix_timestamp('".date("Y-m-d H:i:s")."')","order by id desc");
        $pagers = sf::getModel('Bonus')->getPager("bonus_type = 'paper' and apply_statement = 20 and unix_timestamp(apply_start_at) < unix_timestamp('".date("Y-m-d H:i:s")."') ","order by id desc");
        $this->view->set("pagers",$pagers);
        $this->view->apply('inc_body','paper/apply/company/guide_list');
        $this->view->display('page');
    }


    public function write_list()
    {
        $addWhere = "`user_id` = '".input::session("roleuserid")."' and statement in (0,1,5,7,12,17)";
        $this->gird('paper/apply/company/write_list',$addWhere);
    }

    public function submit_list()
    {
        $addWhere = "`user_id` = '".input::session("roleuserid")."'  AND statement in (6,9,15,20)";
        $this->gird('paper/apply/company/submit_list',$addWhere);
    }

    /**
     * 待审核的申请
     * @return void
     */
    public function wait_list()
    {
        $addWhere = "`company_id` = '".input::session("roleuserid")."' and statement = 6";
        $this->gird('paper/apply/company/wait_list',$addWhere);
    }

    public function back_list()
    {
        $addWhere = "`company_id` = '".input::session("roleuserid")."' AND statement = 7";
        $this->gird('paper/apply/company/back_list',$addWhere);
    }

    public function accept_list()
    {
        $addWhere = "`company_id` = '".input::session("roleuserid")."' AND statement IN (9,20)";
        $this->gird('paper/apply/company/accept_list',$addWhere);
    }



    /**
     * 审核
     */
    function doAccept()
    {
        $bp = sf::getModel("BonusPapers")->selectByApplyId(input::getInput("mix.id"));
        if($bp->isNew()) $this->page_debug('没有找到该项内容！');
        if(input::post("content")){
            $bp->setStatement(9);
            $bp->save();
            sf::getModel("Historys")->addHistory($bp->getApplyId(),input::post("content"),'bonus_paper');
            $this->refresh();
        }
        //原因表单
        $form = Form::load('bonus/paper/apply/company/doAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($bp->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'审核意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('审核通过！'))
            ->addItem(Form::hidden('id',$bp->getApplyId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 驳回
     */
    function doBack()
    {
        $bp = sf::getModel('BonusPapers')->selectByApplyId(input::getMix('id'));
        if($bp->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $bp->setStatement(7);
            $bp->save();
            sf::getModel("Historys")->addHistory($bp->getApplyId(),'已退回！<br/>'.input::getInput("post.content"),'bonus_paper');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('bonus/paper/apply/company/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($bp->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',$bp->getApplyId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 撤回
     */
    function reBack()
    {
        $bp = sf::getModel('BonusPapers')->selectByApplyId(input::getMix('id'));
        if($bp->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $bp->setStatement(1);
            $bp->save();
            sf::getModel("Historys")->addHistory($bp->getApplyId(),'已撤回！<br/>'.input::getInput("post.content"),'bonus_paper');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('bonus/paper/apply/company/reBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($bp->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'撤回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('撤回修改！'))
            ->addItem(Form::hidden('id',$bp->getApplyId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    public function gird($tpl = 'paper/apply/company/index',$addWhere = '1',$showMax=15,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";
        input::getInput("mix.bonus_year") && $addWhere .= " AND `bonus_year` = '".input::getInput("mix.bonus_year")."' ";

        $form_vars = array('field','search','company_id','subject','bonus_year','statement','user_name','company_name');
        $pagers = sf::getModel('BonusPapers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars);
        $this->view->set("pagers",$pagers);
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}