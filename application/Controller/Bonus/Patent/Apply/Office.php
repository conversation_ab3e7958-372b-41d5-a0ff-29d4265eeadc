<?php
namespace App\Controller\Bonus\Patent\Apply;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Office extends BaseController
{
    private $view = NULL;
    
    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../../view/');
    }

    public function plan_list()
    {
        $pagers = sf::getModel('Bonus')->getPager("bonus_type = 'patent'","order by id desc");
        $this->view->set("pagers",$pagers);
        $this->view->apply("inc_body",'patent/apply/office/plan_list');
        $this->view->display('page');
    }

    /**
     * 申请综合查询
     * @return void
     */
    public function index()
    {
        $addWhere = "1";
        $this->gird('patent/apply/office/index',$addWhere);
    }

    /**
     * 待审核的申请
     * @return void
     */
    public function wait_list()
    {
        $addWhere = "statement = 9";
        $this->gird('patent/apply/office/wait_list',$addWhere);
    }

    /**
     * 已审核的申请
     * @return void
     */
    public function accept_list()
    {
        $addWhere = "statement = 20";
        $this->gird('patent/apply/office/accept_list',$addWhere);
    }

    /**
     * 已退回的申请
     * @return void
     */
    public function back_list()
    {
        $addWhere = "statement = 12";
        $this->gird('patent/apply/office/back_list',$addWhere);
    }

    /**
     * 审核
     */
    function doAccept()
    {
        $bp = sf::getModel("BonusPatents")->selectByApplyId(input::getInput("mix.id"));
        if($bp->isNew()) $this->page_debug('没有找到该项内容！');
        if(input::post("content")){
            $bp->setStatement(20);
            $bp->save();
//            $bp->changePatentBonusStatement(1);
            sf::getModel("Historys")->addHistory($bp->getApplyId(),input::post("content"),'bonus_patent');
            $this->refresh();
        }
        //原因表单
        $form = Form::load('bonus/patent/apply/office/doAccept')
            ->addItem(Form::Input(['name'=>'subject','label'=>'名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($bp->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'审核意见'])->setWidth(12)->setAttribute('class','no-margin')->setValue('审核通过！'))
            ->addItem(Form::hidden('id',$bp->getApplyId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    /**
     * 驳回
     */
    function doBack()
    {
        $bp = sf::getModel('BonusPatents')->selectByApplyId(input::getMix('id'));
        if($bp->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $bp->setStatement(12);
            $bp->save();
//            $bp->changePatentBonusStatement(0);
            sf::getModel("Historys")->addHistory($bp->getApplyId(),'已退回！<br/>'.input::getInput("post.content"),'bonus_patent');
            $this->refresh();
        }

        //原因表单
        $form = Form::load('bonus/patent/apply/office/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($bp->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改！'))
            ->addItem(Form::hidden('id',$bp->getApplyId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
    }

    public function gird($tpl = 'patent/apply/office/index',$addWhere = '1',$showMax=15,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";
        input::getInput("mix.bonus_year") && $addWhere .= " AND `bonus_year` = '".input::getInput("mix.bonus_year")."' ";

        if(input::getMix('parent_id')){
            $addWhere .= " AND `company_id` like '".rtrim(input::getMix("parent_id"),'0')."%' ";
        }
        if(input::getMix('corporation_id')){
            $addWhere .= " AND `company_id` like '".getParentCompanyId(input::getMix('corporation_id'))."%'";
        }
        if(input::getMix('company_id')){
            $addWhere .= " AND `company_id` = '".input::getMix('company_id')."'";
        }
        if(input::getMix('bonus_id')){
            $addWhere .= " AND `bonus_id` = '".input::getMix('bonus_id')."'";
        }

        $form_vars = array('field','search','bonus_id','company_id','parent_id','corporation_id','subject','bonus_year','statement','user_name','company_name');
        $pagers = sf::getModel('BonusPatents')->getPager($addWhere,$addSql,$showMax,'','',$form_vars);
        $this->view->set("pagers",$pagers);
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    public function extraOpen()
    {
        $item = sf::getModel('BonusPatents')->selectByApplyId(input::getMix("id"));
        if($item->isNew()) $this->error('没有找到该项目');
        $item->setExtraOpen(1);
        $item->save();
    }

    public function extraClose()
    {
        $item = sf::getModel('BonusPatents')->selectByApplyId(input::getMix("id"));
        if($item->isNew()) $this->error('没有找到该项目');
        $item->setExtraOpen(0);
        $item->save();
    }
}