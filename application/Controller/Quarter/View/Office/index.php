<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                报告综合查询
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="search">
              <?php include_once('search_part.php')?>
            </div>
              <div class="box">
                  <form id="validateForm" name="validateForm" method="post" action="">
                  <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-condensed">
                      <thead>
                  <tr>
                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                    <th><?=getColumnStr('立项编号','radicate_id')?></th>
                    <th><?=getColumnStr('项目名称','subject')?></th>
                    <th><?=getColumnStr('季度','year')?></th>
                    <th width="100"><?=getColumnStr('负责人','user_id')?></th>
                    <th><?=getColumnStr('承担单位','corporation_id')?></th>
                    <th class="text-center"><?=getColumnStr('状态','statement')?></th>
                  </tr>
                      </thead>
                      <tbody>
                  <?php while($quarter = $pager->getObject()):?>
                  <tr>
                    <td align="center"><input name="select_id[]" type="checkbox" value="<?=$quarter->getQuarterId()?>" /></td>
                    <td><?=$quarter->getRadicateId()?></td>
                    <td><?=link_to("quarter/user/show/id/".$quarter->getQuarterId(),$quarter->getSubject(),array('target'=>"_blank"))?></td>
                    <td><?=$quarter->getYear()?>年第<?=$quarter->getQuarter()?>季度</td>
                    <td><?=link_to("user/profile/show/userid/".$quarter->getUserId(),$quarter->getUserName())?></td>
                    <td><?=link_to("unit/profile/show/userid/".$quarter->getCompanyId(),$quarter->getCompanyName())?></td>
                    <td class="text-center"><?=$quarter->getState()?></td>
                  </tr>
                <?php endwhile;?>
                      </tbody>
                      <tfoot>
                <tr>
                  <td colspan="9" align="right">&nbsp;<span class="pager_bar">
                    <?=$pager->total().$pager->navbar(10)?>
                    </span></td>
                </tr>
                      </tfoot>
                </table>
                  </form>
              </div>
            </div>
        </div>
    </div>
</div>
