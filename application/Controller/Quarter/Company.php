<?php
namespace App\Controller\Quarter;
use App\Controller\BaseController;
use App\Facades\Form;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Config;
use Sofast\Core\Sf;
use App\Controller\Quarter\GridTrait;
use Sofast\Support\View;

class Company extends BaseController
{	
	use GridTrait;
	/**
	 * 综合查询
	 */
	function index()
	{
		$this->grid("Company/index","`company_id` like  '".input::session("companyid")."%' ");
	}
	
	/**
	 * 填写中的
	 */
	public function wait_list()
	{
        $addWhere = "`company_id` like  '".input::session("companyid")."%'  AND statement = 2 and wait_for_company = '".$_SESSION['companylevel']."'";
		$this->grid("Company/wait_list",$addWhere);
	}
	
	/**
	 * 已上报的
	 */
	public function submit_list()
	{
        $addWhere = "`company_id` like  '".input::session("companyid")."%'  AND ((statement = 2 and wait_for_company < '".$_SESSION['companylevel']."') or (statement IN (9,10)))";
		$this->grid("Company/submit_list",$addWhere);
	}
	
	/**
	 * 已签署的
	 */
	public function confirm_list()
	{
		$this->grid("Company/index","company_id = '".input::session("roleuserid")."' AND `statement` = 10 ");
	}
	
	/**
	 * 被退回的
	 */
	public function back_list()
	{
        $addWhere = "`company_id` like  '".input::session("companyid")."%'  AND statement = 3 and company_level = '".input::session('companylevel')."'";
		$this->grid("Company/back_list",$addWhere);
	}
	
	/**
	 * 上报项目
	 */
	function doSubmit()
	{
		$ids = array();
		if(is_array(input::post("select_id"))) $ids = input::post("select_id");
		else $ids[] = input::mix("id");
		
		for($i=0,$n=count($ids);$i<$n;$i++){
			$quarter = sf::getModel("ProjectQuarters")->selectByQuarterId($ids[$i]);
			if($quarter->isNew()) continue;
			if(!in_array($quarter->getStatement(),array(2,3))) continue;
            $quarter->setStatement(9);
//            $quarter->setStatement(2);
//            if($quarter->getWaitForCompany()<=2){
//                if($quarter->getProject()->isGeneralProject()){
//                    //计划内一般项目，直接审核通过
//                    $quarter->setStatement(10);
//                }else{
//                    //待科技创新部审核
//                    $quarter->setStatement(9);
//                    pushMsgToOffice('你有一个季度执行报告等待审核','quarter/office/wait_list');
//                }
//            }else{
//                $quarter->setWaitForCompany(input::session('companylevel')-1);   //待上级单位审核
//                pushMsgToCompanyManager('你有一个季度执行报告等待审核','quarter/company/wait_list',$quarter->getWaitForCompanyId());
//            }
			$quarter->save();
			sf::getModel("Historys")->addHistory($quarter->getProjectId(),'季度执行报告审核通过！');
		}
		$this->success("季度执行报告审核成功!",getFromUrl());
	}
	
	/**
	 * 上报项目
	 */
	function doBack()
	{
		$quarter = sf::getModel("ProjectQuarters")->selectByQuarterId(input::mix("id"));
		if(input::post())
		{
            $quarter->setStatement(3);
			$quarter->save();
			sf::getModel("Historys")->addHistory($quarter->getProjectId(),'季度执行报告被退回！其理由是：'.input::post("content"));
			exit("<script>parent.location.reload();</script>");
		}

        //原因表单
        $form = Form::load('quarter/company/doBack')
            ->addItem(Form::Input(['name'=>'subject','label'=>'项目名称'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($quarter->getSubject())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$quarter->getQuarterId()))
            ->render();

        $this->view->set("inc_body",$form);
        $this->view->display("page_blank");
	}

}