<?php
namespace App\controller\society\member;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class User extends BaseController
{
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../View/');
    }

    public function index()
    {
        $addWhere = "`user_id` = '".input::session("roleuserid")."'";
        $this->gird('member/user/index',$addWhere);
    }


    public function gird($tpl = 'member/user/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";

        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";


        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name','first_id','second_id','third_id','fourth_id','type','project_state');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}