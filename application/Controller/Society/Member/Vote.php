<?php
namespace App\Controller\Society\Member;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Vote extends BaseController
{
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
    }

    public function index()
    {
        $this->view->set("pager",sf::getModel('Filemanager')->getPager("item_type = 'member_vote'","order by id desc"));
        $this->view->apply("inc_body",'member/vote/index');
        $this->view->display('page');
    }

    public function member_list()
    {
        $addWhere = "statement IN (29,30)";
        $this->gird('member/vote/member_list',$addWhere);
    }

    public function upload()
    {
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setFileNote(input::getInput('post.radicate_year').'年学术委员会选举结果');
                $filemanager->save();
            }
            $this->refresh();
        }
        $this->view->set("item_type",'member_vote');
        $this->view->set("radicate_year",input::getMix('radicate_year')?:date('Y'));
        $this->view->apply('inc_body','member/vote/upload');
        $this->view->display('page_blank');
    }

    public function member_upload()
    {
        $filelink = site_path('up_files/tpl/society_member.xls');
        $filename = '委员名单导入模板';
        $this->view->set("filelink",$filelink);
        $this->view->set("filename",$filename);
        $this->view->set("radicate_year",input::getMix('radicate_year')?:date('Y'));
        $this->view->apply('inc_body','member/vote/member_upload');
        $this->view->display('page_blank');
    }

    public function gird($tpl = 'member/office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";

        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.is_join_academic") && $addWhere .= " AND `is_join_academic` = '".input::getInput("mix.is_join_academic")."' ";
        input::getInput("mix.user_level_rank") && $addWhere .= " AND `user_level_rank` = '".input::getInput("mix.user_level_rank")."' ";
        input::getInput("mix.education") && $addWhere .= " AND `education` = '".input::getInput("mix.education")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getInput("mix.corporation_id") && input::getInput('mix.fuzzy')=='no'){
            $addWhere .= " AND `corporation_id` = '".input::getInput("mix.corporation_id")."' ";
        }elseif(input::getInput("mix.corporation_id")){
            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
            $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";
        }else{
            $addWhere .= " AND `corporation_id` like '9B581D83-B2BA-7337-0678%' ";
        }

        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['SocietyMembers']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['SocietyMembers']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['SocietyMembers']['orderStr'] = base64_encode($addSql);
        }else{

        }

        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name','first_id','second_id','third_id','fourth_id','user_level_rank','education','is_join_academic');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

    public function doUploadMember()
    {
        if (!empty($_FILES)) {
            if(input::post('is_clear')==1){
                $this->clearMembers();
            }
            $msg = $this->importMembers();
            $this->view->set('msg',$msg);
            $this->view->set('error',$this->error);
            $this->view->set('totalRow',$this->totalRow);
            $this->view->apply("inc_body", "member/vote/upload_result");
            $this->view->display("page_blank");
        }
    }

    private function importMembers($startRow = 2)
    {
        //上传后的文件名
        $filename = $_FILES['file']['name'];
        $uploadfile = $_FILES['file']['tmp_name'];
        $fileinfo = pathinfo($filename);
        if(!in_array($fileinfo['extension'],['xls','xlsx'])){
            $this->error[] = "请上传excel文档";
            return false;
        }
        $result = $_FILES['file']['tmp_name'];
        if ($result) //如果上传文件成功，就执行导入excel操作
        {
            $arr['cells'] = excel_in($uploadfile);
            $this->totalRow = count($arr['cells'])-$startRow+1;
            $i=0;
            foreach ($arr['cells'] as $k=>$v){
                if($k<$startRow) continue;
                foreach ($v as &$item){
                    $item = $this->filterStr($item);
                }
                $name = $v[2];
                $idcard = $v[3];
                $companyName = $v[4];

                if(empty($name)){
                    $this->error[] = "第{$k}行的姓名没有录入，不能导入！";
                    continue;
                }

                if(empty($idcard) && empty($companyName)){
                    $this->error[] = "第{$k}行的身份证和部门都没有录入，不能导入！";
                    continue;
                }
                if($idcard){
                    $member = sf::getModel("SocietyMembers")->selectByUserIdcard($idcard);
                }else{
                    $member = sf::getModel("SocietyMembers")->selectByUserName($name);
                }
                if($member->isNew()){
                    $this->error[] = "第{$k}行的【{$name}】没有填写提名申请表，不能设置为委员！";
                    continue;
                }
                if($member->getStatement()<20){
                    $this->error[] = "第{$k}行的【{$name}】提名申请还为审核通过，不能设置为委员！";
                    continue;
                }
                $member->setStatement(29);
                $member->setUpdatedAt(date('Y-m-d H:i:s'));
                $member->save();
                sf::getModel("historys")->addHistory($member->getMemberId(),'设置为学术委员会委员','society_member');
                $i++;
            }
            return $i;
        } else {
            $this->error[] = "上传失败";
            return false;
        }
    }

    private function clearMembers()
    {
        sf::getLib('db')->exec("update society_members set statement = 31 where statement = 29");
    }

    /**
     * 删除附件
     */
    function delete()
    {
        $file = sf::getModel('Filemanager',input::getInput("mix.fid"));
        if($file->isNew()){
            $this->error('找不到该文档',getFromUrl());
        }
        if($file->getItemId()!=input::session("roleuserid")){
            $this->error('不能删除',getFromUrl());
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $this->success('删除成功！',getFromUrl());
        }else{
            $this->error('删除失败',getFromUrl());
        }
    }

    private function filterStr($str)
    {
        if(mb_detect_encoding($str, 'UTF-8', true)===false){
            $str = iconv('ISO-8859-1','UTF-8',$str);
        }
        $str = trim($str);
        $str = str_replace('　','',$str);
        $str = str_replace('  ','',$str);
        $str = str_replace(' ','',$str);
        $str = str_replace('\'','‘',$str);
        $str = str_replace("\r\n",'',$str);
        $str = str_replace("\n\r",'',$str);
        $str = str_replace("\r",'',$str);
        $str = str_replace("\n",'',$str);
        return $str;
    }
}