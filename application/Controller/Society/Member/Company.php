<?php
namespace App\Controller\Society\Member;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Company extends BaseController
{
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
    }

    public function index()
    {
        $addwhere = "`corporation_id` = '".input::session('roleuserid')."' ";
        $this->gird('member/company/index',$addwhere);
    }

    public function wait_list()
    {
        $addwhere = "statement = 4";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 6";
        }
        $addwhere .= " AND `corporation_id` = '".input::session('roleuserid')."' ";
        $this->gird('member/company/wait_list',$addwhere);
    }

    public function submit_list()
    {
        $addwhere = "statement IN (6,9,15)";
        if(input::session('userlevel')==4){
            $addwhere = "statement IN (9,15)";
        }
        $addwhere .= " AND `corporation_id` = '".input::session('roleuserid')."' ";
        $this->gird('member/company/submit_list',$addwhere);
    }

    public function back_list()
    {
        $addwhere = "statement = 5";
        if(input::session('userlevel')==4){
            $addwhere = "statement = 7";
        }
        $addwhere .= " AND `corporation_id` = '".input::session('roleuserid')."' ";
        $this->gird('member/company/back_list',$addwhere);
    }


    /**
     * 上报
     */
    public function doAcceptOnlyOne()
    {
        $member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        //判断验证是否通过
        if($message = $member->validate())
            $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
        $member->setStatement(6);
        if(input::session('userlevel')==4){
            $member->setStatement(9);
        }
        $member->save();
        sf::getModel('Historys')->addHistory($member->getMemberId(),'单位已推荐','society_member');
        $this->success(lang::get("Has been submit!"),site_url("society/member/company/wait_list"));
    }

    /**
     * 驳回
     */
    function doRejectedOnlyOne()
    {
        $member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $member->setStatement(5);
            if(input::session('userlevel')==4){
                $member->setStatement(7);
            }
            $member->save();
            sf::getModel("Historys")->addHistory($member->getMemberId(),'单位退回！<br/>'.input::getInput("post.content"),'society_member');
            exit("<script>parent.location.reload();</script>");
        }

        //原因表单
        $form = Form::load('society/member/company/doRejectedOnlyOne')
            ->addItem(Form::Input(['name'=>'subject','label'=>'姓名'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($member->getUserName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$member->getMemberId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    public function gird($tpl = 'member/user/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";

        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";


        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name','first_id','second_id','third_id','fourth_id');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}