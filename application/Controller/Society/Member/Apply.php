<?php
namespace App\Controller\Society\Member;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Apply extends BaseController
{

    private $tabs = array();
    private $member;
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)).'/../view/');
        $this->member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($this->member->isNew()){
            if(input::session('userlevel')==2){
                $user = sf::getModel('Declarers')->selectByUserId(input::getInput('session.roleuserid'));
                $company = $user->getCorporation();
                $this->member->setUserSex($user->getUserSex());
                $birthday = str_replace('-','.',$user->getUserBirthday());
                $birthday = substr($birthday,0,7);
                $this->member->setUserBirthday($birthday);
                $this->member->setUserMobile($user->getUserMobile());
                $this->member->setUserEmail($user->getUserEmail());
                $this->member->setUserIdcard($user->getUserIdcard());
            }else{
                $company = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'));
            }
            if($company->isNew()){
                $this->page_debug('请先完善你的单位信息',site_url('user/profile/base'));
            }
            $this->member->setCorporationId($company->getUserId());
            $this->member->setUserId(input::getInput('session.roleuserid'));
            $this->member->setUserName(input::getInput('session.nickname'));
            $this->member->setCorporationName($company->getSubject());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('society/member/apply/edit/id/' . $this->member->getMemberId())
            ],
//            [
//                'method' => 'attachment',
//                'text' => '相关附件',
//                'url' => site_url('society/member/apply/attachment/id/' . $this->member->getMemberId())
//            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }

    public function index()
    {
        $this->gird();
    }


    public function wait_list()
    {
        $this->gird('society/member/apply/wait_list','statement IN (1,3,6,12)');
    }

    public function submit_list()
    {
        $this->gird('society/member/apply/index','statement IN (2,5,9,10)');
    }

    public function back_list()
    {
        $this->gird('society/member/apply/back_list','statement IN (3,6,12)');
    }

    function edit()
    {
        if(input::session('userlevel')!=2){
            $this->error('请登录科研人员账号填写！',getFromUrl());
        }
        if(in_array($this->member->getStatement(),[2,9,10])){
            $this->error('当前状态不允许修改！',getFromUrl());
        }
        if($this->member->isRepeat()) $this->error('已存在你的提名申请表，不能重复填写！',site_url('society/member/user/index'));
        if(input::post()){
            $this->member->setDeclareYear(date('Y'));
            $this->member->setBirthplace(input::post('birthplace'));
            $this->member->setNation(input::post('nation'));
            $this->member->setParty(input::post('party'));
            $this->member->setSchool(input::post('school'));
            $this->member->setMajor(input::post('major'));
            $this->member->setEducation(input::post('education'));
            $this->member->setDepartment(input::post('department'));
            $data=['408'=>'正高','409'=>'副高','411'=>'中级','410'=>'初级','636'=>'其他'];
            $userLevelRank = $data[input::post('user_level_rank')]?:'';
            $this->member->setUserLevelRank($userLevelRank);
            $this->member->setUserLevel(input::post('user_level'));
            $this->member->setUserDuty(input::post('user_duty'));
            $this->member->setSkill(input::post('skill'));
            $this->member->setAddress(input::post('address'));
            $this->member->setPostcode(input::post('postcode'));
            $this->member->setContent1(input::post('content1'));
            $this->member->setIsResearchJob(input::post('is_research_job'));
            $this->member->setJobName(input::post('job_name'));
            $this->member->setIsJoinAcademic(input::post('is_join_academic'));
            $this->member->setAcademic(input::post('academic'));
            $this->member->setStatement(1);
//            $this->member->setWaitForCompany($this->member->getCorporation()->getLevel());
//            $this->member->setCompanyLevel($this->member->getCorporation()->getLevel());
            if($this->member->isNew()) $this->member->setCreatedAt(date('Y-m-d H:i:s'));
            $this->member->save();
            $this->success('保存成功！',site_url('society/member/apply/edit/id/'.$this->member->getMemberId()));
        }
        $this->view->set("member",$this->member);
        $this->view->apply('inc_body','member/apply/edit');
        $this->view->display('page');
    }

    function show()
    {
        if($this->member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->view->set("member",$this->member);
        $this->view->apply('inc_body','member/apply/show');
        $this->view->display('page');
    }

    /**
     * 上报
     */
    public function submit()
    {
        if($this->member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $this->member->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->member->setSubmitAt(date("Y-m-d H:i:s"));
            $this->member->setStatement(4);
            $this->member->save();
            sf::getModel('Historys')->addHistory($this->member->getMemberId(),'委员提名申请上报','society_member');
            $this->success(lang::get("Has been submit!"),site_url("society/member/user/index"));
        }
        $this->view->set('member',$this->member);
        $this->view->set('msg',$this->member->validate());
        $this->view->apply("inc_body","member/apply/submit");
        $this->view->display("page");
    }

    function dodelete()
    {
        if($this->member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(in_array($this->member->getStatement(),[2,9,10])){
            $this->error('当前状态不允许删除！',getFromUrl());
        }
        $this->member->delete();
        sf::getModel('Historys')->addHistory($this->member->getMemberId(),'删除科协会员登记表','society_member');
        $this->success(lang::get('Has been deleted!'),getFromUrl());
    }

    public function attachment()
    {
        if($this->member->isNew()) $this->error('请先填写基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('member',$this->member);
        $this->view->set('itemType','society_member');
        $this->view->apply('inc_body', 'member/apply/attachment');
        $this->view->display('page');
    }

    /**
     * 导出
     */
    public function export()
    {
        if ($this->member->isNew()) $this->error('没有找到该项内容');
        $pdf = $this->member->getConfigs('file.apply');
        if($pdf && !in_array($this->member->getStatement(),[1,3,6,12]) && file_exists(WEBROOT.'/up_files/'.$pdf)){
            $this->jump(site_path('up_files/'.$pdf));
        }else{
            $this->makepdf();
        }
    }

    public function makepdf()
    {
        if ($this->member->isNew()) $this->error('没有找到该项内容');
        $this->view->set("print",'yes');
        $this->view->set("member",$this->member);
        $htmlStr = $this->view->getContent("member/apply/show");
        //替换字体
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //提取打印内容
        //提取打印内容
        preg_match_all('/<printer>(.*?)<\/printer>/isx',$htmlStr,$matches);
        $htmlStr = $matches[1][0];
        $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="60%" style="border: 0px solid #fff;font-size: 12px">学术委员会委员提名申请表</td><td width="40%" style="text-align: right;border: 0px solid #fff;font-size: 12px">'.$this->member->getUserName().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;"></td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->member->getUserName())
            ->setSubject($this->member->getUserName())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->member->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('学术委员会委员提名申请表',0.1)
            ->show();
    }

    public function deleteAttachment()
    {
        $filemanager = sf::getModel("Filemanager",input::getInput('mix.file_id'));
        if($filemanager->isNew()) $this->error('未找到该附件',getFromUrl());
        if($filemanager->getItemType()!='society_member') $this->error('没有权限删除',getFromUrl());
        if($this->member->getMemberId()!=$filemanager->getItemId()) $this->error('没有权限删除',getFromUrl());
        $filemanager->delete();
        $this->success('删除成功',site_url('society/member/apply/attachment/id/'.input::getInput('mix.id')));
    }

    public function file_delete()
    {
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        if($file->getItemId()!=$this->member->getMemberId()){
            $data['msg'] = '不能删除';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }


    public function gird($tpl = 'member/apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `user_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}