<?php
namespace App\Controller\Society\Member;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Office extends BaseController
{
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
    }

    public function index()
    {
        $this->gird();
    }

    public function wait_list()
    {
        $addWhere = "statement = 9";
        $this->gird('member/office/wait_list',$addWhere);
    }

    public function accept_list()
    {
        $addWhere = "statement = 20";
        $this->gird('member/office/accept_list',$addWhere);
    }

    public function back_list()
    {
        $addWhere = "statement = 12";
        $this->gird('member/office/back_list',$addWhere);
    }


    /**
     * 上报
     */
    public function doAcceptOnlyOne()
    {
        $member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        //判断验证是否通过
        if($message = $member->validate())
            $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
        $member->setStatement(20);
        $member->save();
        sf::getModel('Historys')->addHistory($member->getMemberId(),'审核通过','society_member');
        $this->success('审核通过！',site_url("society/member/office/wait_list"));
    }

    /**
     * 驳回
     */
    function doRejectedOnlyOne()
    {
        $member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $member->setStatement(12);
            $member->save();
            sf::getModel("Historys")->addHistory($member->getMemberId(),'已退回！<br/>'.input::getInput("post.content"),'society_member');
            exit("<script>parent.location.reload();</script>");
        }

        //原因表单
        $form = Form::load('society/member/office/doRejectedOnlyOne')
            ->addItem(Form::Input(['name'=>'subject','label'=>'姓名'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($member->getUserName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'退回原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue('退回修改'))
            ->addItem(Form::hidden('id',$member->getMemberId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }

    function change(){
        $memberId = input::getInput("post.member_id");
        $cid = input::getInput("post.cid");

        if(!$memberId) exit('NO1');
        if(!$cid) exit('NO2');
        //修改单位
        $member = sf::getModel("SocietyMembers")->selectByMemberId($memberId);
        $old_name = $member->getCorporationName();
        $company = sf::getModel("Corporations")->selectByUserId($cid);
        if($company->isNew()) exit('company not exist');
        $member->setCorporationId($company->getUserId());
        $member->setCorporationName($company->getSubject());
        $member->save();
        sf::getModel("historys")->addHistory($member->getMemberId(),"工作单位由【".$old_name."】调整为【".$member->getCorporationName()."】。",'society_member');
        exit('OK');
    }

    public function gird($tpl = 'member/office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";

        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.is_join_academic") && $addWhere .= " AND `is_join_academic` = '".input::getInput("mix.is_join_academic")."' ";
        input::getInput("mix.user_level_rank") && $addWhere .= " AND `user_level_rank` = '".input::getInput("mix.user_level_rank")."' ";
        input::getInput("mix.education") && $addWhere .= " AND `education` = '".input::getInput("mix.education")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getInput("mix.corporation_id") && input::getInput('mix.fuzzy')=='no'){
            $addWhere .= " AND `corporation_id` = '".input::getInput("mix.corporation_id")."' ";
        }elseif(input::getInput("mix.corporation_id")){
            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
            $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";
        }else{
            $addWhere .= " AND `corporation_id` like '9B581D83-B2BA-7337-0678%' ";
        }

        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['SocietyMembers']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['SocietyMembers']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['SocietyMembers']['orderStr'] = base64_encode($addSql);
        }else{

        }

        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name','first_id','second_id','third_id','fourth_id','user_level_rank','education','is_join_academic');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}