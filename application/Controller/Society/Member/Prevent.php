<?php
namespace App\Controller\Society\Member;
use App\Controller\BaseController;
use App\Facades\Form;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Prevent extends BaseController
{
    private $view;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
    }


    public function index()
    {
        $addWhere = "statement  = 31";
        $this->gird('member/prevent/index',$addWhere);
    }

    function doRemove()
    {
        $member = sf::getModel('SocietyMembers')->selectByMemberId(input::getMix('id'));
        if($member->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput("post.content")){
            $member->setStatement(31);
            $member->save();
            sf::getModel("Historys")->addHistory($member->getMemberId(),'已免除！<br/>'.input::getInput("post.content"),'society_member');
            $url = site_url('society/member/vote/member_list/_save/yes/_msg/已免除！');
            exit("<script>top.location.href='{$url}';</script>");
        }

        //原因表单
        $form = Form::load('society/member/prevent/doRemove')
            ->addItem(Form::Input(['name'=>'subject','label'=>'姓名'])->setWidth(12)->setAttribute('class','no-margin')->setAttribute('disabled','disabled')->setValue($member->getUserName())
            )
            ->addItem(Form::Textarea(['name'=>'content','label'=>'免除原因'])->setWidth(12)->setAttribute('class','no-margin')->setValue(''))
            ->addItem(Form::hidden('id',$member->getMemberId()))
            ->render();

        view::set("inc_body",$form);
        view::display("page_blank");
    }


    public function gird($tpl = 'member/office/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';

        input::post("search") && $addWhere .= ' AND '.input::post("field")." LIKE '%".trim(input::post("search"))."%' ";

        input::getInput("mix.declare_year") && $addWhere .= " AND `declare_year` = '".input::getInput("mix.declare_year")."' ";
        input::getInput("mix.is_join_academic") && $addWhere .= " AND `is_join_academic` = '".input::getInput("mix.is_join_academic")."' ";
        input::getInput("mix.user_level_rank") && $addWhere .= " AND `user_level_rank` = '".input::getInput("mix.user_level_rank")."' ";
        input::getInput("mix.education") && $addWhere .= " AND `education` = '".input::getInput("mix.education")."' ";
        input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getInput("mix.corporation_id") && input::getInput('mix.fuzzy')=='no'){
            $addWhere .= " AND `corporation_id` = '".input::getInput("mix.corporation_id")."' ";
        }elseif(input::getInput("mix.corporation_id")){
            $parentCompanyId = getParentCompanyId(input::getInput("mix.corporation_id"));
            $addWhere .= " AND `corporation_id` like '".$parentCompanyId."%' ";
        }else{
            $addWhere .= " AND `corporation_id` like '9B581D83-B2BA-7337-0678%' ";
        }

        if(input::getInput("post") || $this->hash != $_SESSION['hash'])
        {
            //保存标记
            $_SESSION['hash'] = $this->hash;
            $_SESSION['SocietyMembers']['baseSql'] = base64_encode($addWhere);
            //打印
            $_SESSION['SocietyMembers']['sqlStr'] = base64_encode($addWhere);
            $_SESSION['SocietyMembers']['orderStr'] = base64_encode($addSql);
        }else{

        }

        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name','first_id','second_id','third_id','fourth_id','user_level_rank','education','is_join_academic');
        $this->view->set("pagers",sf::getModel('SocietyMembers')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

}