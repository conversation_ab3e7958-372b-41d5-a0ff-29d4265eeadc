<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    我的申请
                </h3>
            </div>
            <div class="block-content">
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序 号
                        </th>
                        <th>
                            <?=getColumnStr('姓名','user_name')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('工作单位','corporation_id')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('申请年度','declare_year')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('填表时间','created_at')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('状态','statement')?>
                        </th>
                        <th class="text-center" style="width: 100px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($member = $pagers->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <a href="<?=site_url('society/member/apply/show/id/'.$member->getMemberId())?>"><?=$member->getUserName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCorporationName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getDeclareYear()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCreatedAt()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getState()?>
                        </td>
                        <td class="text-center">
                            <?php
                                if($member->enableWrite()):
                            ?>
                            <?php
                            $urls = [
                                [
                                    'name'=>'编辑',
                                    'url'=>site_url("society/member/apply/edit/id/".$member->getMemberId()),
                                    'icon'=>'edit',
                                ],
                                [
                                    'name'=>'查看',
                                    'url'=>site_url("society/member/apply/show/id/".$member->getMemberId()),
                                    'icon'=>'show',
                                ],
                                [
                                    'name'=>'导出',
                                    'url'=>site_url("society/member/apply/export/id/".$member->getMemberId()),
                                    'icon'=>'export',
                                ],
                                [
                                    'name'=>'上报',
                                    'url'=>site_url("society/member/apply/submit/id/".$member->getMemberId()),
                                    'icon'=>'submit',
                                ],
                                [
                                    'name'=>'分割线',
                                    'url'=>'-',
                                ],
                                [
                                    'name'=>'删除',
                                    'url'=>site_url("society/member/apply/dodelete/id/".$member->getMemberId()),
                                    'icon'=>'trash',
                                    'class'=>'text-danger',
                                    'event'=>'return showConfirm(\'删除后将不可恢复，确定要删除吗？\',this);',
                                ]
                            ];
                            echo Button::setClass('btn-primary')->group('操作',$urls)?>
                            <?php else:?>
                                <?=Button::setClass('btn-primary')->setUrl(site_url("society/member/apply/show/id/".$member->getMemberId()))->link('查看')?>
                            <?php endif;?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
