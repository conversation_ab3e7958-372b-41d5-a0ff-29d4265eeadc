<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        科协会员申请
                    </h3>
                    <div class="block-options">
                        <?=Button::back()?>
                        <?=Button::setUrl(site_url("society/member/apply/edit/id/".$member->getMemberId()))->setIcon('edit')->link('编辑')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="page-body">
                        <div class="alert alert-warning" role="alert">
                            <h4>温馨提示：</h4>
                            <ul>
                                <li>上报前请仔细核对填报内容，一经上报将不可更改。</li>
                            </ul>
                        </div>
                        <?php if(count($msg)):?>
                            <div class="alert alert-danger" role="alert">
                                <h4>系统检查到你申请材料有错，请检查并修改这些错误：</h4>
                                <ul>
                                    <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
                                        <li>
                                            <?=($i+1)?>
                                            、
                                            <?=$msg[$i]?>
                                        </li>
                                    <?php endfor;?>
                                </ul>
                            </div>
                        <?php endif;?>
                        <form name="up" id="up" method="post" action="" class="mb-3">
                            <?php if(count($msg)):?>
                                <input type="submit" name="button" id="button" disabled="disabled"  value="申请有误，请返回修改后再上报！" class="btn btn-sm btn-alt-danger"/>
                            <?php else:?>
                                <input type="submit" name="button" id="button" value="上报资料" onclick="return confirm('一经上报将不可更改,你确定上报？')"  class="btn btn-alt-danger btn-lg" />
                            <?php endif;?>
                            <input name="id" type="hidden" id="id" value="<?=$member->getMemberId();?>" />
                            <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" />
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>







