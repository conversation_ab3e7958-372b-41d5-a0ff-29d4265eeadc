<script language="javascript" type="text/javascript" src="<?=site_path("js/DatePicker/WdatePicker.js")?>"></script>
<script language="javascript" type="text/javascript">
    $(function (){
        //选择职称级别
        $(document).on("change","#user_level_rank",function(){
            var id= $(this).val();
            if(id==0){
                $('#user_level').html('<option value="0">==请选择==</option>');
                return;
            }
            var url = baseurl+'common/HonorAjax';
            ajaxData(url,{id:id},'html',function(){},function(data){
                $('#user_level').html(data);
            });
        });
    });
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑会员登记表
        </h2>
        <?php
        if(!$member->isNew()):
            ?>
            <div>
                <?=Button::setSize('btn-xs')->setUrl(site_url('society/member/apply/show/id/'.$member->getMemberId()))->setClass('btn-alt-info my-2')->setIcon('far fa-sticky-note')->link('查看');?>
                <?=Button::setSize('btn-xs')->setUrl(site_url('society/member/apply/submit/id/'.$member->getMemberId()))->setClass('btn-alt-warning my-2')->setIcon('send')->link('上报');?>
            </div>
        <?php endif;?>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">

                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    <li class="nav-item ml-auto">
                        <button class="btn btn-link" type="submit" style="padding-top: .75rem;
    padding-bottom: .75rem;"><i class="fa fa-save"></i> 保存</button>
                    </li>
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">

                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">姓名<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="user_name" type="text" id="user_name" disabled value="<?=$member->getUserName()?>" required="required" class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">工作单位<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="corporation_name" id="corporation_name" value="<?=$member->getCorporationName()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">性别<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="user_sex" id="user_sex" value="<?=$member->getUserSex()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">出生年月<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="user_birthday" id="user_birthday" value="<?=$member->getUserBirthday()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">手机<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="user_mobile" id="user_mobile" value="<?=$member->getUserMobile()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">E-mail<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="user_email" id="user_email" value="<?=$member->getUserEmail()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">籍贯<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="birthplace" id="birthplace" value="<?=$member->getBirthplace()?>"  class="form-control" required/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">民族<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="nation" id="nation">
                                    <?=getSelectFromArray(get_select_data('nation'),$member->getNation())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">政治面貌<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select name="party" id="party" class="form-control" required>
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(['中共党员','中共预备党员','共青团员','民革党员','民盟盟员','民建会员','民进会员','农工党党员','致公党党员','九三学社社员','台盟盟员','无党派人士','群众'],$member->getParty())?>
                                </select>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">毕业院校<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="school" id="school" value="<?=$member->getSchool()?>"  class="form-control" required/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所学专业<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="major" id="major" value="<?=$member->getMajor()?>"  class="form-control" required/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">文化程度<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <select class="form-control" name="education" id="education" required>
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(['博士研究生','硕士研究生','大学本科','专科','中职/高中','初中','小学','其他'],$member->getEducation())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">所在部门<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" name="department" id="department" value="<?=$member->getDepartment()?>"  class="form-control" required/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">职务</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <input type="text" name="user_duty" id="user_duty" class="form-control" value="<?=$member->getUserDuty()?>" />
                                <p class="help-block"></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">职称</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <select name="user_level_rank" id="user_level_rank" class="form-control w-auto" style="float: left" >
                                    <?=$data=['0'=>'==请选择==','408'=>'正高','409'=>'副高','411'=>'中级','410'=>'初级','636'=>'其他']?>
                                    <?=getSelectFromArray($data,array_search($member->getUserLevelRank(),$data),false)?>
                                </select>
                                <select name="user_level" id="user_level" class="form-control w-auto" style="float: left" >
                                    <?=getOptionByParent(array_search($member->getUserLevelRank(),$data),$member->getUserLevel())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">技能等级</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <input type="text" name="skill" id="skill" class="form-control" value="<?=$member->getSkill()?>" />
                                <p class="help-block"></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">通讯地址</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <input type="text" name="address" id="address" class="form-control" value="<?=$member->getAddress()?>" />
                                <p class="help-block"></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">邮编</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <input type="text" name="postcode" id="postcode" class="form-control" value="<?=$member->getPostcode()?>" />
                                <p class="help-block"></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">是否分管或从事科创工作</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <select name="is_research_job" id="is_research_job" class="form-control">
                                    <?=getSelectFromArray(['是','否'],$member->getIsResearchJob())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row job_name" style="<?=$member->getIsResearchJob()=='否'?'display: none':''?>">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">工作岗位<em>*</em></label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <select name="job_name" id="job_name" class="form-control">
                                    <option value="">请选择</option>
                                    <?=getSelectFromArray(['分管领导','分管部门中干','科创工作负责人'],$member->getJobName())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">是否参加各级学术组织（学会、协会、研究会）</label>
                            <div class="col-xs-12 col-sm-5 col-md-5">
                                <select name="is_join_academic" id="is_join_academic" class="form-control">
                                    <?=getSelectFromArray(['是','否'],$member->getIsJoinAcademic())?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row academic" style="<?=$member->getIsJoinAcademic()=='否'?'display: none':''?>">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">参加学术组织情况<em>*</em></label>
                            <div class="col-md-8 col-sm-8 col-xs-8">
                                <table class="table table-bordered">
                                    <tr>
                                        <td align="center">学术组织级别</td>
                                        <td align="center">学术组织名称</td>
                                        <td align="center">职务</td>
                                        <td align="center">任期</td>
                                        <td align="center" style="width: 100px;text-align: center">操作</td>
                                    </tr>
                                    <?php
                                    $data = $member->getAcademic();
                                    if(empty($data)) $data['subject'][0] = '';
                                    $i=0;
                                    foreach ($data['subject'] as $k=>$item):
                                    $i++;
                                    ?>
                                    <tr data-row="<?=$k?>">
                                        <td align="center">
                                            <select name="academic[level][]" class="form-control">
                                                <option value="">请选择</option>
                                                <?=getSelectFromArray(['国家级','省级','市级'],$data['level'][$k])?>
                                            </select>
                                        </td>
                                        <td align="center"><input class="form-control" type="text" name="academic[subject][]" value="<?=$data['subject'][$k]?>"></td>
                                        <td align="center"><input class="form-control" type="text" name="academic[duty][]" value="<?=$data['duty'][$k]?>"></td>
                                        <td align="center"><input class="form-control" type="text" name="academic[worktime][]" value="<?=$data['worktime'][$k]?>"></td>
                                        <td style="width: 100px;text-align: center">
                                            <?php
                                            if($i==count($data['subject'])):
                                            ?>
                                                <button type="button" class="btn btn-primary add-row"><span class="fa fa-plus" aria-hidden="true"></span></button>
                                            <?php
                                            else:
                                            ?>
                                                <button type="button" class="btn btn-danger remove-row"><span class="fa fa-minus" aria-hidden="true"></span></button>
                                            <?php
                                            endif;
                                            ?>
                                        </td>
                                    </tr>
                                    <?php endforeach;?>
                                </table>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">工作简历<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <textarea name="content1" class="no-margin form-control" id="content1" rows='10' ><?=$member->getContent1()?></textarea>
                            </div>
                        </div>

                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> 保存</button>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    $(function (){
        $("#is_research_job").change(function (){
            if($(this).val()=='是'){
                $(".job_name").show();
            }else{
                $(".job_name").hide();
            }
        });
        $("#is_join_academic").change(function (){
            if($(this).val()=='是'){
                $(".academic").show();
            }else{
                $(".academic").hide();
            }
        });
    });

    $(document).on('click','.add-row',function(){
        //克隆行
        var rows = $(this).parents('#activeForm');
        var tr = $(this).parents('tr').clone();
        var line = rows.length;
        var row = tr.data('row');
        //替换行id
        var reg = '/\\['+row+'\\]/ig';
        var new_tr = tr.html().replace(eval(reg), '['+line+']');
        new_tr = "<tr data-row='"+line+"'>"+new_tr+"</tr>";
        //追加至表格
        $(this).parents('tbody').append(new_tr);
        //按钮变为减号
        $(this).removeClass('add-row').removeClass('btn-primary').addClass('remove-row').addClass('btn-danger');
        $(this).find('span').removeClass('fa-plus').addClass('fa-minus');
    });

    $(document).on('click','.remove-row',function(){
        $(this).parents('tr').remove();
    });


</script>