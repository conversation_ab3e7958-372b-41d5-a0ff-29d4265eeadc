<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<?php
$postUrl = \Sofast\Core\router::getController().'/'.\Sofast\Core\router::getMethod();
?>
<form action="<?=site_url($postUrl)?>" method="post" name="search" id="search">
    <table width="100%" align="center" class="table">
        <tbody id="show_search">
        <tr>
            <td>关键词：
                <input class="form-control w-auto custom-control-inline" id="search" name="search" value="<?=input::getInput("post.search")?>" />
                <label><input name="field" type="radio" value="user_name" checked="checked" />姓名</label>
                <label><input type="radio" name="field" value="user_mobile" />手机</label>

                <select name="user_level_rank" id="user_level_rank" class="form-control w-auto custom-control-inline">
                    <option value="">=职称=</option>
                    <?=getSelectFromArray(['正高','副高','中级','初级','其他'],input::getMix('user_level_rank'))?>
                </select>
                <select name="education" id="education" class="form-control w-auto custom-control-inline">
                    <option value="">=文化程度=</option>
                    <?=getSelectFromArray(['博士研究生','硕士研究生','大学本科','专科','中职/高中','初中','小学','其他'],input::getMix('education'))?>
                </select>
                <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                    <option value="">=申请年度=</option>
                    <?=getYearList(input::getMix('declare_year'),2023,date('Y'))?>
                </select>
                <select name="is_join_academic" id="is_join_academic" class="form-control w-auto custom-control-inline">
                    <option value="">=是否参加学术组织=</option>
                    <?=getSelectFromArray(['是','否'],input::getMix('is_join_academic'))?>
                </select>
                <select name="statement" id="statement" class="form-control w-auto custom-control-inline">
                    <option value="">=状态=</option>
                    <?=societySelect(input::getMix('statement'))?>
                </select>
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        <tr>
            <td>
                <?php
                $companyId = input::getInput("mix.corporation_id")?:'9B581D83-B2BA-7337-0678-000000000000';
                ?>
                <label><span style="float: left;line-height: 35px">单位/部门：</span><div class="cxselect" data-selects="level1,level2,level3,level4" data-url="<?=site_url('ajax/companys') ?>" data-json-value="v" style="float: left">
                        <select class="form-control w-auto custom-control-inline parent_company level1" data-level=1 data-value="<?=getParentCompanyId($companyId,1,true)?>" ></select>
                        <select class="form-control w-auto custom-control-inline parent_company level2"  data-level=2 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,2,true)?>"></select>
                        <select class="form-control w-auto custom-control-inline parent_company level3"  data-level=3 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,3,true)?>"></select>
                        <select class="form-control w-auto custom-control-inline parent_company level4"  data-level=4 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,4,true)?>"></select>
                        <input type="hidden" name="corporation_id" id="corporation_id" value="<?=$companyId?>">
                    </div>
                </label>
            </td>
        </tr>
        </tbody>
    </table>
</form>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(".parent_company").change(function (){
        var level = $(this).data('level');
        if($(this).val()){
            $('#corporation_id').val($(this).val());
        }else{
            if(level>1){
                var pLevel = level-1;
                var pVal = $('.level'+pLevel).val();
                $('#corporation_id').val(pVal);
            }else{
                $('#corporation_id').val('');
            }
        }

    });
</script>

