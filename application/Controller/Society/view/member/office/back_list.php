<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    已退回的提名申请
                </h3>
                <div class="block-options">
                </div>
            </div>
            <div class="block-content">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序 号
                        </th>
                        <th>
                            <?=getColumnStr('姓名','user_name')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('工作单位','corporation_id')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('职称','user_level_rank')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('文化程度','education')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('申请年度','declare_year')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('是否参加学术组织','is_join_academic')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('填表时间','created_at')?>
                        </th>
                        <th class="d-none d-sm-table-cell" style="width: 120px">
                            <?=getColumnStr('状态','statement')?>
                        </th>
                        <th class="text-center" style="width: 140px;">
                            操作
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($member = $pagers->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <?=$member->getMark()?><a href="<?=site_url('society/member/apply/show/id/'.$member->getMemberId())?>"><?=$member->getUserName()?></a>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCorporationName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getUserLevelRank()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getEducation()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getDeclareYear()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getIsJoinAcademic()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCreatedAt('Y-m-d')?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getState()?>
                        </td>
                        <td class="text-center">
                            <?=Button::setName('审核同意')->setUrl(site_url("society/member/office/doAcceptOnlyOne/id/".$member->getMemberId()))->setIcon('check')->link()?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
