<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                选举结果上传
            </h3>
            <div class="block-options">
                <?=Button::setUrl(site_url('society/member/vote/upload'))->setIcon('upload')->window('上传选举文件')?>
            </div>
        </div>
        <div class="block-content pt-0">
            <div class="main">
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="20"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th><?=getColumnStr('文件名','file_note')?></th>
                                <th width="150"><?=getColumnStr('上传者','user_id')?></th>
                                <th width="200"><?=getColumnStr('上传时间','created_at')?></th>
                                <th width="150">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($file = $pager->getObject()):?>
                                <tr> <td><input name="select_id[]" type="checkbox" value="<?=$file->getItemId()?>" /></td>
                                    <td><a target="_blank" href="<?=site_path('up_files/'.$file->getFilePath())?>"><?=$file->getFileNote()?></a></td>
                                    <td><?=$file->getUserName()?></td>
                                    <td><?=$file->getCreatedAt()?></td>
                                    <td><?=Button::setUrl(site_url('society/member/vote/delete/fid/'.$file->getId().'/id/'.$file->getItemId()))->setClass('btn-alt-danger')->delete('删除')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="8" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
