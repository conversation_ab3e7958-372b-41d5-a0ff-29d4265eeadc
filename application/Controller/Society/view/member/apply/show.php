<script>
    function save(cid,subject){
        $.post('<?=site_url("society/member/office/change")?>',{cid:cid,member_id:'<?=$member->getMemberId()?>'},function(json){
            if(json == 'OK') {
                showSuccess('单位变更成功！');
                $("#unit").html(subject);
            }else{
                showError(json);
            }
        });
        return true;
    }

    function unit(json)
    {
        save(json.id,json.subject);
        closeWindow();
    }
</script>
<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        查看会员登记表
                    </h3>
                    <div class="block-options">
                        <?=Button::back();?>
                        <?=Button::setUrl(site_url('society/member/apply/export/id/'.$member->getMemberId()))->setIcon('export')->setEvent('loading()')->link('导出')?>
                    </div>
                </div>
                <div class="block-content tab-content">
                    <printer>
                        <?php
                            if($print=='yes'):
                        ?>
                                <style>
                                    table td,div_table_content{
                                        font-size: 16px;
                                    }
                                </style>
                        <?php endif;?>
                    <p align="center" style="font-size: 22px">学术委员会委员提名申请表</p>
                    <p align="right">填表时间：<?=$member->getCreatedAt('Y年m月d日')?></p>
                    <div>
                        <table width="680" align="center" cellpadding="5" cellspacing="0" class="table table-bordered" border="1">
                            <tbody>
                            <tr>
                                <td width="100" align="center">
                                    姓名
                                </td>
                                <td align="center">
                                    <?=$member->getUserName()?>
                                </td>
                                <td width="100" align="center">
                                    性别
                                </td>
                                <td align="center">
                                    <?=$member->getUserSex()?>
                                </td>
                                <td width="100" align="center">
                                    出生年月
                                </td>
                                <td align="center">
                                    <?=$member->getUserBirthday()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    籍贯
                                </td>
                                <td align="center">
                                    <?=$member->getBirthplace()?>
                                </td>
                                <td width="100" align="center">
                                    民族
                                </td>
                                <td align="center">
                                    <?=$member->getNation()?>
                                </td>
                                <td width="100" align="center">
                                    政治面貌
                                </td>
                                <td align="center">
                                    <?=$member->getParty()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    毕业院校<br>和专业
                                </td>
                                <td colspan="3" align="center">
                                    <?=$member->getSchool()?>/<?=$member->getMajor()?>
                                </td>
                                <td width="100" align="center">
                                    文化程度
                                </td>
                                <td align="center">
                                    <?=$member->getEducation()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    单位/部门
                                </td>
                                <td colspan="3" align="center">
                                    <?=$member->getCorporationName()?>
                                    <?php if(input::getInput("session.userlevel") == 1 || input::getInput("session.userlevel")==6):?>
                                        <a href="javascript::void();" onclick="return showWindow('修改单位','<?=site_url("common/unit_search")?>',600,460)" class="btn btn-sm btn-danger">调整单位</a>
                                    <?php endif;?>
                                </td>
                                <td width="100" align="center">
                                    职务
                                </td>
                                <td align="center">
                                    <?=$member->getUserDuty()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    职称或<br>
                                    技能等级
                                </td>
                                <td colspan="3" align="center">
                                    <?=$member->getUserLevel()?><?=($member->getUserLevel()&&$member->getSkill())?'/':''?><?=$member->getSkill()?>
                                </td>
                                <td width="100" align="center">
                                    手机
                                </td>
                                <td align="center">
                                    <?=$member->getUserMobile()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    通讯地址及邮编
                                </td>
                                <td colspan="3" align="center">
                                    <?=$member->getAddress()?><?=($member->getAddress()&&$member->getPostcode())?'/':''?><?=$member->getPostcode()?>
                                </td>
                                <td width="100" align="center">
                                    E-mail
                                </td>
                                <td align="center">
                                    <?=$member->getUserEmail()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    是否参加各级学术组织
                                </td>
                                <td colspan="5" align="center">
                                    <?=$member->getIsJoinAcademic()?>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    参加学术组织情况
                                </td>
                                <td colspan="5" align="center">
                                    <?php
                                    if($member->getIsJoinAcademic()=='是'):
                                    ?>
                                    <table width="100%" align="center" cellpadding="5" cellspacing="0" class="table table-bordered" border="1">
                                        <tr>
                                            <td align="center" width="140">学术组织级别</td>
                                            <td align="center">学术组织名称</td>
                                            <td align="center">职务</td>
                                            <td align="center">任期</td>
                                        </tr>
                                        <?php
                                        $data = $member->getAcademic();
                                        if(empty($data)) $data['subject'][0] = '';
                                        $i=0;
                                        foreach ($data['subject'] as $k=>$item):
                                            $i++;
                                            ?>
                                            <tr data-row="<?=$k?>">
                                                <td align="center">
                                                   <?=$data['level'][$k]?>
                                                </td>
                                                <td align="center"><?=$data['subject'][$k]?></td>
                                                <td align="center"><?=$data['duty'][$k]?></td>
                                                <td align="center"><?=$data['worktime'][$k]?></td>
                                            </tr>
                                        <?php endforeach;?>
                                    </table>
                                    <?php else:?>
                                    无
                                    <?php endif;?>
                                </td>
                            </tr>
                            <?php
                            if($print=='yes'):
                            ?>
                            </tbody>
                        </table>
                        <div class="div_table" style="width:100%;margin: 0 auto">
                            <p class="div_table_title"><strong>工作简历</strong></p>
                            <p class="div_table_content"><?= showText($member->getContent1()) ?></p>
                        </div>
                        <table width="680" align="center" cellpadding="5" cellspacing="0" class="table table-bordered" border="1">
                            <tbody>
                            <?php else:?>
                            <tr>
                                <td width="100" align="center">
                                    工作简历
                                </td>
                                <td colspan="5">
                                    <?= showText($member->getContent1()) ?>
                                </td>
                            </tr>
                            <?php endif;?>
                            <tr>
                                <td colspan="6" align="right">
                                    <p align="right">&nbsp;</p>
                                    <p align="right">&nbsp;</p>
                                    <p align="right">&nbsp;</p>
                                    <p align="right">&nbsp;</p>
                                    <p align="right">中国民用航空总局第二研究所</p>
                                    <p align="right">学术委员会审批意见（盖章）</p>
                                    <p align="right">&nbsp;</p>
                                    <p align="right">&nbsp;</p>
                                    <p align="right">年 &nbsp;&nbsp;&nbsp;月 &nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;</p>
                                    <p align="right">&nbsp;</p>
                                </td>
                            </tr>
                            <tr>
                                <td width="100" align="center">
                                    备注
                                </td>
                                <td colspan="5">
                                    &nbsp;
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <p>&nbsp;</p>
                    </printer>
                    <div style="display: none">
                    <div class="header no-margin-top">附件列表</div>
                    <table class="table">
                            <thead>
                            <tr>
                                <th class="text-center">序号</th>
                                <th class="text-center">文件名</th>
                                <th class="text-center">文件格式</th>
                                <th class="text-center">文件大小</th>
                                <th class="text-center">上传时间</th>
                                <th class="text-center">文件说明</th>
                                <th class="text-center" style="width:70px;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="file-list-<?=$itemType?>">
                            <?php if($member->getAttachment()->getTotal()>0):
                                $attachments = $member->getAttachment();
                                while($file = $attachments->getObject()):
                                    ?>
                                    <tr id="file<?=$file->getId()?>">
                                        <td class="text-center" style="width: 70px;">
                                            <?=$attachments->getIndex()?>
                                        </td>
                                        <td><i class="fa fa-caret-right orange"></i> <a href="<?=site_path('up_files/'.$file->getFilePath())?>" target="_blank"><?=$file->getFileName()?></a></td>
                                        <td class="text-center"><?=$file->getFileExt()?></td>
                                        <td class="text-center"><?=$file->getFileSize()?></td>
                                        <td class="text-center"><?=$file->getCreatedAt()?></td>
                                        <td class="text-center" style="width: 300px">
                                            <?=$file->getFileNote()?>
                                        </td>
                                        <td class="text-center">
                                            <?=Button::setUrl(site_path('up_files/'.$file->getFilePath()))->link('查看')?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                            <?php endif;?>
                            </tbody>
                            <tr>
                        </table>
                    </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
