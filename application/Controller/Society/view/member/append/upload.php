<style>
    .custom-file-label::after {
        content: "浏览";
    }
</style>
<div class="content">
    <div class="block">
        <div class="block-content">
            <div class="container" style="margin-bottom: 100px">
                <form name="form1" action="<?=site_url('society/member/append/doUpload')?>" method="post" enctype="multipart/form-data">
                    <h2 class="content-heading pt-0">
                        <i class="fa fa-fw fa-asterisk text-muted mr-1"></i> 第一步
                    </h2>
                    <div class="row push">
                        <div class="col-lg-4">
                            <p class="text-muted">
                                点击右侧按钮下载Excel导入模板，并在模板中填写需导入的信息。
                            </p>
                        </div>
                        <div class="col-lg-8 col-xl-7">
                            <div class="form-group row">
                                <div class="col-sm-10 col-md-8 col-xl-6">
                                    <a class="btn btn-block btn-alt-info text-left" download="<?=$filename?>" href="<?=$filelink?>"><i class="fa fa-fw fa-file-excel mr-1"></i>Excel导入模板</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2 class="content-heading pt-0">
                        <i class="fa fa-fw fa-asterisk text-muted mr-1"></i> 第二步
                    </h2>
                    <div class="row push">
                        <div class="col-lg-4">
                            <p class="text-muted">
                                点击右侧按钮上传第一步下载的文件并点击提交按钮。
                            </p>
                        </div>
                        <div class="col-lg-8 col-xl-7">
                            <div class="form-group">
                                <label>选择上传文件：</label>
                                <div class="custom-file">
                                    <input type="file" name="file" class="custom-file-input" data-toggle="custom-file-input" id="example-file-input-custom"> <label class="custom-file-label" for="example-file-input-custom">选择文件</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h2 class="content-heading pt-0 d-none">
                        可选项
                    </h2>
                    <div class="row push d-none">
                        <div class="col-lg-4">
                            <p class="text-muted">
                                是否自动免除不在名单中的委员
                            </p>
                        </div>
                        <div class="col-lg-8 col-xl-7">
                            <div class="form-group">
                                <?=get_radio([1=>'是',0=>'否'],'is_clear',0,'',false)?>
                            </div>
                        </div>
                    </div>
                    <div class="row push">
                        <div class="col-lg-8 col-xl-5 offset-lg-4">
                            <div class="form-group">
                                <button type="submit" class="btn btn-alt-primary" onclick="btnLoading(this)"><i class="fa fa-check-circle mr-1"></i> 开始导入</button>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1.3rem;height: 1.3rem"></span> 正在导入...');
        form1.submit();
    }
</script>


