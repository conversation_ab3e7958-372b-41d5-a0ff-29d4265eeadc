<div class="content">
    <div class="row">
        <div class="col-lg-12">
        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    学术委员会委员列表
                </h3>
                <div class="block-options">
                    <?=Button::setUrl(site_url('society/member/vote/member_upload'))->setIcon('upload')->setEndEvent('location.reload()')->window('上传委员名单')?>
                </div>
            </div>
            <div class="block-content">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <table class="table table-hover table-vcenter">
                    <thead>
                    <tr>
                        <th class="text-center" style="width: 100px;">
                            序 号
                        </th>
                        <th>
                            <?=getColumnStr('姓名','user_name')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('工作单位','corporation_id')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('职称','user_level_rank')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('文化程度','education')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('申请年度','declare_year')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('是否参加学术组织','is_join_academic')?>
                        </th>
                        <th class="d-none d-sm-table-cell">
                            <?=getColumnStr('填表时间','created_at')?>
                        </th>
                        <th class="d-none d-sm-table-cell" style="width: 120px">
                            <?=getColumnStr('状态','statement')?>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php while($member = $pagers->getObject()):?>
                    <tr>
                        <td class="text-center">
                            <?=$pagers->getIndex()?>
                        </td>
                        <td class="font-w600">
                            <?=$member->getMark()?><a href="<?=site_url('society/member/apply/show/id/'.$member->getMemberId())?>"><?=$member->getUserName()?></a>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCorporationName()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getUserLevelRank()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getEducation()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getDeclareYear()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getIsJoinAcademic()?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getCreatedAt('Y-m-d')?>
                        </td>
                        <td class="d-none d-sm-table-cell">
                            <?=$member->getState()?>
                        </td>
                    </tr>
                    <?php endwhile;?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        </div>
    </div>
</div>
