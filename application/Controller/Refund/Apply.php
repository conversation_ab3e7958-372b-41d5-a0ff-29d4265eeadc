<?php
namespace App\Controller\Refund;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class apply extends BaseController
{

    private $tabs = array();
    private $refund;
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->refund = sf::getModel('ProjectRefunds')->selectByItemId(input::getMix('id'));
        if($this->refund->isNew()){
            $user = sf::getModel('Declarers')->selectByUserId(input::getInput('session.roleuserid'));
            $company = $user->getCorporation();
            if($company->isNew()){
                $this->page_debug('请先完善你的单位信息',site_url('user/profile/base'));
            }
            $this->refund->setCorporationId($company->getUserId());
            $this->refund->setUserId(input::getInput('session.roleuserid'));
            $this->refund->setUserName(input::getInput('session.nickname'));
            $this->refund->setCorporationName($company->getSubject());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('refund/apply/edit/id/' . $this->refund->getItemId())
            ],
            [
                'method' => 'detail',
                'text' => '报销明细',
                'url' => site_url('refund/apply/detail/id/' . $this->refund->getItemId())
            ],
            [
                'method' => 'payment',
                'text' => '支付信息',
                'url' => site_url('refund/apply/payment/id/' . $this->refund->getItemId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('refund/apply/attachment/id/' . $this->refund->getItemId())
            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }

    public function index()
    {
        $this->gird();
    }


    public function wait_list()
    {
        $this->gird('apply/wait_list','statement IN (1,3,6,12)');
    }

    public function submit_list()
    {
        $this->gird('apply/index','statement IN (2,5,9,10)');
    }

    public function back_list()
    {
        $this->gird('apply/back_list','statement IN (3,6,12)');
    }

    public function accept_list()
    {
        $this->gird('apply/index','statement = 10');
    }

    function edit()
    {
        if(input::post()){
            if(!input::post('subject')) $this->error('请填写项目名称');
            if(!input::post('apply_at')) $this->error('请选择报销日期');
            if(!input::post('bill_count')) $this->error('请填写票据数');
            if(input::post('project_id')){
                $project = sf::getModel('Projects')->selectByProjectId(input::post('project_id'));
                if($project->isNew()){
                    $this->error('该项目不存在');
                }
                $this->refund->setProjectId(input::post('project_id'));
                $this->refund->setCorporationId($this->refund->getProject()->getCorporationId());
                $this->refund->setCorporationName($this->refund->getProject()->getCorporationName());
                $this->refund->setDepartmentId($this->refund->getProject()->getDepartmentId());
                $this->refund->setDepartmentName($this->refund->getProject()->getDepartmentName());
            }else{
                $company = sf::getModel('Corporations')->selectByUserId(input::post('corporation_id'));
                if($company->isNew()){
                    $this->error('请选择承担单位');
                }
                $department = sf::getModel('Departments')->selectByUserId(input::post('department_id'));
                if($department->isNew()){
                    $this->error('请选择承担部门');
                }
                $this->refund->setCorporationId($company->getUserId());
                $this->refund->setCorporationName($company->getSubject());
                $this->refund->setDepartmentId($department->getUserId());
                $this->refund->setDepartmentName($department->getSubject());
            }
            $this->refund->setSubject(input::post('subject'));
            $this->refund->setAmount(input::post('amount'));
            $this->refund->setApplyAt(input::post('apply_at'));
            $this->refund->setBillCount(input::post('bill_count'));
            $this->refund->setNote(input::post('note'));
            $this->refund->setStatement(1);
            $this->refund->save();
            $this->success('保存成功！',site_url('refund/apply/edit/id/'.$this->refund->getItemId()));
        }
        $this->view->set("refund",$this->refund);
        $this->view->apply('inc_body','apply/edit');
        $this->view->display('page');
    }

    public function detail()
    {
        if($this->refund->isNew()){
            $this->error('请先填写基本信息',site_url('refund/apply/edit/id/'.$this->refund->getItemId()));
        }
        $details = $this->refund->getDetails();
        $this->view->set("refund",$this->refund);
        $this->view->set("details",$details);
        $this->view->apply("inc_body",'apply/detail');
        $this->view->display("page");
    }

    public function payment()
    {
        if($this->refund->isNew()){
            $this->error('请先填写基本信息',site_url('refund/apply/edit/id/'.$this->refund->getItemId()));
        }
        if($postdata = input::post()){
            $types = $postdata['type'];
            $datas = [];
            foreach ($types as $k=>$type){
                $datas[$k]['no'] = $postdata['no'][$k];
                $datas[$k]['type'] = $postdata['type'][$k];
                $datas[$k]['username'] = $postdata['username'][$k];
                $datas[$k]['bankno'] = $postdata['bankno'][$k];
                $datas[$k]['bankname'] = $postdata['bankname'][$k];
                $datas[$k]['amount'] = $postdata['amount'][$k];
            }
            $this->refund->setPayment($datas);
            $this->refund->save();
            $this->success('保存成功！',site_url('refund/apply/payment/id/'.$this->refund->getItemId()));
        }
        $this->view->set("refund",$this->refund);
        $this->view->apply("inc_body",'apply/payment');
        $this->view->display("page");
    }

    public function detail_edit()
    {
        $detail = sf::getModel('ProjectRefundDetails',input::getMix('did'));
        if(input::post()){
            if(!input::post('code')) $this->error('请选择科目');
            if(!input::post('amount')) $this->error('请填写报销金额');
            $budgetSubject = sf::getModel('BudgetSubjects')->selectByCode(input::post('code'));
            if($budgetSubject->isNew()) $this->error('该科目不存在');
            $detail->setItemId($this->refund->getItemId());
            $detail->setProjectId($this->refund->getProjectId());
            $detail->setCode($budgetSubject->getCode());
            $detail->setSubject($budgetSubject->getSubject());
            $detail->setBillType(input::post('bill_type'));
            $detail->setBillCount(input::post('bill_count'));
            $detail->setAmount(input::post('amount'));
            $detail->setNote(input::post('note'));
            $detail->setUserId(input::getInput('session.roleuserid'));
            $detail->setUserName(input::getInput('session.nickname'));
            if($detail->isNew()) $detail->setCreatedAt(date('Y-m-d H:i:s'));
            $detail->setUpdatedAt(date('Y-m-d H:i:s'));
            $detail->save();
            exit("<script>top.location.href=top.location.href+'/_save/yes';</script>");
        }
        $this->view->set("detail",$detail);
        $this->view->set("refund",$this->refund);
        $this->view->apply("inc_body",'apply/detail_edit');
        $this->view->display("page_blank");
    }

    public function detail_remove()
    {
        $detail = sf::getModel('ProjectRefundDetails',input::getMix('did'));
        if($detail->isNew()){
            $this->error('没有找到该明细');
        }
        $detail->delete();
        $this->success('删除成功！',site_url('refund/apply/detail/id/'.$this->refund->getItemId()));
    }

    function show()
    {
        if($this->refund->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $details = $this->refund->getDetails();
        $this->view->set("refund",$this->refund);
        $this->view->set("details",$details);
        $this->view->apply('inc_body','apply/show');
        $this->view->display('page');
    }

    /**
     * 上报
     */
    public function submit()
    {
        if($this->refund->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $this->refund->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->refund->setSubmitAt(date("Y-m-d H:i:s"));
            $this->refund->setStatement(2);
            $this->refund->save();
            sf::getModel('Historys')->addHistory($this->refund->getItemId(),'项目计划上报','refund');
            $this->success(lang::get("Has been submit!"),site_url("refund/apply/wait_list"));
        }
        $this->view->set('refund',$this->refund);
        $this->view->set('msg',$this->refund->validate());
        $this->view->apply("inc_body","apply/submit");
        $this->view->display("page");
    }

    function dodelete()
    {
        if($this->refund->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->refund->delete();
        $this->success(lang::get('Has been deleted!'),getFromUrl());
    }

    public function attachment()
    {
        if($this->refund->isNew()) $this->error('请先填写基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('refund',$this->refund);
        $this->view->set('itemType','refund');
        $this->view->apply('inc_body', 'apply/attachment');
        $this->view->display('page');
    }

    public function detail_attachment()
    {
        if($this->refund->isNew()) $this->error('请先填写基本信息',getFromUrl());
        $detail = sf::getModel('ProjectRefundDetails',input::getMix('did'));
        if($detail->isNew()) $this->error('请先填写报销明细基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('refund',$this->refund);
        $this->view->set('detail',$detail);
        $this->view->set('itemType','refund_detail');
        $this->view->apply('inc_body', 'apply/detail_attachment');
        $this->view->display('page_blank');
    }

    /**
     * 导出
     */
    public function export()
    {
        if ($this->refund->isNew()) $this->error('没有找到该项内容');
        $pdf = $this->refund->getConfigs('file.plan');
        if($pdf && !in_array($this->refund->getStatement(),[1,3,6,12]) && file_exists(WEBROOT.'/up_files/'.$pdf)){
            $this->jump(site_path('up_files/'.$pdf));
        }else{
            $this->makepdf();
        }
    }

    public function makepdf()
    {
        if ($this->refund->isNew()) $this->error('没有找到该项内容');
        $details = $this->refund->getDetails();
        $this->view->set("print",'yes');
        $this->view->set("refund",$this->refund);
        $this->view->set("details",$details);
        $htmlStr = $this->view->getContent("apply/show");
        //替换字体
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //提取打印内容
        //提取打印内容
        preg_match_all('/<printer>(.*?)<\/printer>/isx',$htmlStr,$matches);
        $htmlStr = $matches[1][0];
        $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="20%" style="border: 0px solid #fff;font-size: 12px">报销申请单</td><td width="80%" style="text-align: right;border: 0px solid #fff;font-size: 12px">'.$this->refund->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;"></td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->refund->getSubject())
            ->setSubject($this->refund->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->refund->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('报销申请单',0.1)
            ->show();
    }

    public function deleteAttachment()
    {
        $filemanager = sf::getModel("Filemanager",input::getInput('mix.file_id'));
        if($filemanager->isNew()) $this->error('未找到该附件',getFromUrl());
        if($filemanager->getItemType()!='plan') $this->error('没有权限删除',getFromUrl());
        if($this->refund->getItemId()!=$filemanager->getItemId()) $this->error('没有权限删除',getFromUrl());
        $filemanager->delete();
        $this->success('删除成功',site_url('refund/apply/attachment/id/'.input::getInput('mix.id')));
    }

    public function gird($tpl = 'apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `user_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array('field','search','corporation_id','corporation_name','department_id','department_name','subject','statement','user_name');
        $this->view->set("pagers",sf::getModel('ProjectRefunds')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

}