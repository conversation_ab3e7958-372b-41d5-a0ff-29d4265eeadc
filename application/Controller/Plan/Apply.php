<?php
namespace App\Controller\plan;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class apply extends BaseController
{

    private $tabs = array();
    private $plan;
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/View/');
        $this->plan = sf::getModel('ProjectPlans')->selectByPlanId(input::getMix('id'));
        if($this->plan->isNew()){
            if(input::session('userlevel')==2){
                $user = sf::getModel('Declarers')->selectByUserId(input::getInput('session.roleuserid'));
                $company = $user->getCorporation();
            }else{
                $company = sf::getModel('Corporations')->selectByUserId(input::session('roleuserid'));
            }
            if($company->isNew()){
                $this->page_debug('请先完善你的单位信息',site_url('user/profile/base'));
            }
            $this->plan->setCorporationId($company->getUserId());
            $this->plan->setUserId(input::getInput('session.roleuserid'));
            $this->plan->setUserName(input::getInput('session.nickname'));
            $this->plan->setCorporationName($company->getSubject());
            $this->plan->setParentId($company->getParentId());
            $this->plan->setParentName($company->getParentName());
            $this->plan->setFirstId($company->getFirstId());
            $this->plan->setSecondId($company->getSecondId());
            $this->plan->setThirdId($company->getThirdId());
            $this->plan->setFourthId($company->getFourthId());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('plan/apply/edit/id/' . $this->plan->getPlanId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('plan/apply/attachment/id/' . $this->plan->getPlanId())
            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }

    public function index()
    {
        $this->gird();
    }


    public function wait_list()
    {
        $this->gird('apply/wait_list','statement IN (1,3,6,12)');
    }

    public function submit_list()
    {
        $this->gird('apply/index','statement IN (2,5,9,10)');
    }

    public function back_list()
    {
        $this->gird('apply/back_list','statement IN (3,6,12)');
    }

    function edit()
    {
        if(input::post()){
            if(!input::post('subject')) $this->error('请填写项目名称');
            if(!input::post('declare_year')) $this->error('请选择年度');
            $this->plan->setDeclareYear(input::post('declare_year'));
            $this->plan->setSubject(input::post('subject'));
            $this->plan->setMoney(input::post('money'));
            $this->plan->setType(input::post('type'));
            $this->plan->setStartAt(input::post('start_at'));
            $this->plan->setEndAt(input::post('end_at'));
            $this->plan->setProjectState(input::post('project_state'));
            $this->plan->setLinkman(input::post('linkman'));
            $this->plan->setLinkmanMobile(input::post('linkman_mobile'));
            $this->plan->setContent1(input::post('content1'));
            $this->plan->setContent2(input::post('content2'));
            $this->plan->setWaitForCompany($this->plan->getCorporation()->getLevel());
            $this->plan->setCompanyLevel($this->plan->getCorporation()->getLevel());
            $this->plan->save();
            $this->success('保存成功！',site_url('plan/apply/edit/id/'.$this->plan->getPlanId()));
        }
        $this->view->set("plan",$this->plan);
        $this->view->apply('inc_body','apply/edit');
        $this->view->display('page');
    }

    function show()
    {
        if($this->plan->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->view->set("plan",$this->plan);
        $this->view->apply('inc_body','apply/show');
        $this->view->display('page');
    }

    /**
     * 上报
     */
    public function submit()
    {
        if($this->plan->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $this->plan->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->plan->setSubmitAt(date("Y-m-d H:i:s"));
            $this->plan->setStatement(2);
            if($this->plan->getWaitForCompany()<=2){
                $this->plan->setStatement(9);  //待科技创新部审核
            }else{
                if(input::session('userlevel')==3){
                    $this->plan->setWaitForCompany($this->plan->getCompanyLevel()-1);   //待上级单位审核
                }
            }
            $this->plan->save();
            sf::getModel('Historys')->addHistory($this->plan->getPlanId(),'项目计划上报','plan');
            $this->success(lang::get("Has been submit!"),site_url("plan/apply/wait_list"));
        }
        $this->view->set('plan',$this->plan);
        $this->view->set('msg',$this->plan->validate());
        $this->view->apply("inc_body","apply/submit");
        $this->view->display("page");
    }

    function dodelete()
    {
        if($this->plan->isNew()) $this->error('没有找到该项内容',getFromUrl());
        $this->plan->delete();
        $this->success(lang::get('Has been deleted!'),getFromUrl());
    }

    public function attachment()
    {
        if($this->plan->isNew()) $this->error('请先填写基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('plan',$this->plan);
        $this->view->set('itemType','plan');
        $this->view->apply('inc_body', 'apply/attachment');
        $this->view->display('page');
    }

    /**
     * 导出
     */
    public function export()
    {
        if ($this->plan->isNew()) $this->error('没有找到该项内容');
        $pdf = $this->plan->getConfigs('file.plan');
        if($pdf && !in_array($this->plan->getStatement(),[1,3,6,12]) && file_exists(WEBROOT.'/up_files/'.$pdf)){
            $this->jump(site_path('up_files/'.$pdf));
        }else{
            $this->makepdf();
        }
    }

    public function makepdf()
    {
        if ($this->plan->isNew()) $this->error('没有找到该项内容');
        $this->view->set("print",'yes');
        $this->view->set("plan",$this->plan);
        $htmlStr = $this->view->getContent("apply/show");
        //替换字体
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //提取打印内容
        //提取打印内容
        preg_match_all('/<printer>(.*?)<\/printer>/isx',$htmlStr,$matches);
        $htmlStr = $matches[1][0];
        $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="20%" style="border: 0px solid #fff;font-size: 12px">项目计划表</td><td width="80%" style="text-align: right;border: 0px solid #fff;font-size: 12px">'.$this->plan->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;"></td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->plan->getSubject())
            ->setSubject($this->plan->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->plan->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('中国民航局第二研究所科研项目计划表',0.1)
            ->show();
    }

    public function deleteAttachment()
    {
        $filemanager = sf::getModel("Filemanager",input::getInput('mix.file_id'));
        if($filemanager->isNew()) $this->error('未找到该附件',getFromUrl());
        if($filemanager->getItemType()!='plan') $this->error('没有权限删除',getFromUrl());
        if($this->plan->getPlanId()!=$filemanager->getItemId()) $this->error('没有权限删除',getFromUrl());
        $filemanager->delete();
        $this->success('删除成功',site_url('plan/apply/attachment/id/'.input::getInput('mix.id')));
    }

    public function file_delete()
    {
        $file = sf::getModel("Filemanager",input::getInput("mix.fid"));
        $data['code'] = 0;
        if($file->isNew()){
            $data['msg'] = '找不到该文档';
            echo json_encode($data);exit();
        }
        if($file->getItemId()!=$this->plan->getPlanId()){
            $data['msg'] = '不能删除';
            echo json_encode($data);exit();
        }
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $data['code'] = 1;
        }else{
            $data['msg'] = '删除失败';
        }
        echo json_encode($data);exit();
    }


    public function gird($tpl = 'apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `user_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array('field','search','corporation_id','parent_id','subject','declare_year','statement','user_name','corporation_name','parent_name');

        $this->view->set("pagers",sf::getModel('ProjectPlans')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}