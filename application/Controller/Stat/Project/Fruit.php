<?php
namespace App\Controller\Stat\Project;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Fruit extends BaseController
{
    private $view = NULL;
    private $startAt = '';
    private $endAt = '';
    private $mark = 'project_fruit';
    private $cache = NULL;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
        $this->cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
        if(input::getMix('start_at')) $this->startAt = input::getMix('start_at');
        if(input::getMix('end_at')) $this->endAt = input::getMix('end_at');
    }

    public function index()
    {
        if(input::getMix('update')=='yes'){
            $datas = $this->doStat();
        }else{
            $datas = $this->getStat();
        }
        $this->view->set('datas',$datas);
        $this->view->set('startAt',$this->startAt);
        $this->view->set('endAt',$this->endAt);
        $this->view->apply("inc_body","project/fruit/index");
        $this->view->display("page");
    }

    private function getStat()
    {
        return $this->doStat();
//        $years = 'ALL';
//        if($this->startAt && $this->endAt){
//            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
//        }
//        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
//
//        if($statistic->isNew()){
//            return $this->doStat();
//        }
//        return $statistic->getData();
    }

    private function doStat()
    {
        $db = sf::getLib('db');
        $startAt = $this->startAt;
        $endAt = $this->endAt;

        $addWhere = "statement IN (29,30)";
        if($startAt && $endAt){
            $startYear = substr($startAt,0,4);
            $endYear = substr($endAt,0,4);
            $addWhere .= " and (`radicate_year` >= '{$startYear}' and `radicate_year` <= '{$endYear}')";
        }
        //二级单位
        $secondCompanys = getSecondCompanysShort();
        $companyIds = [];
        //按项目数量排序
        $query = $db->query("select corporation_id,count(*) c from projects where {$addWhere} GROUP BY corporation_id order by c desc");
        while($row = $db->fetch_array($query)) {
            if (in_array($row['corporation_id'],$companyIds)) continue;
            $companyIds[] = $row['corporation_id'];
        }
        if(count($companyIds)<count($secondCompanys)){
            foreach ($secondCompanys as $companyId=>$secondCompany){
                if (in_array($companyId,$companyIds)) continue;
                $companyIds[] = $companyId;
            }
        }

        $companyIds = array_unique($companyIds);
        $companyIds = array_filter($companyIds);
        $types = [
            'patent' => '专利',
            'paper' => '论文',
            'product' => '产品',
            'standard' => '标准',
            'soft' => '软著',
        ];
        foreach ($companyIds as $companyId){
            if(!$secondCompanys[$companyId]) continue;
            $datas[$companyId]['subject'] = $secondCompanys[$companyId];
            foreach ($types as $k=>$type){
                $departmentWhere = $addWhere;
                $departmentWhere .= " and corporation_id = '{$companyId}'";
                $count= $db->result_first("select count(*) c from `{$k}s` where `{$k}_id` in (select item_id from fruit_projects where item_type = '{$k}' and project_id in (select project_id from projects where {$departmentWhere}))");
                $datas[$companyId][$k] = $count;
                $datas[$companyId]['total']+=$count;
            }
        }
        $years = 'ALL';
        if($this->startAt && $this->endAt){
            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
        }
        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
        $statistic->setData($datas);
        $statistic->save();
        return $datas;
    }

    public function chart(){
        $stat = $this->getStat();
        $xAxisData = []; //X轴（二级单位）
        $seriesData = [];                  //Y轴

        $types = [
            'patent' => '专利',
            'paper' => '论文',
            'product' => '产品',
            'standard' => '标准',
            'soft' => '软著',
        ];

        foreach ($types as $k=>$type){
            $seriesData[$k] = [];
        }

        foreach ($stat as $s){
            $xAxisData[] = $s['subject'];
            foreach ($types as $k=>$type){
                $seriesData[$k][] = $s[$k];
            }
            $seriesData['total'][] = $s['total'];
        }

        //图表标题
        $option['title']['text'] = '项目产出统计图';
        $option['title']['x'] = 'center';
        //工具栏
        $option['toolbox']['show'] = true;
        $option['toolbox']['feature']['saveAsImage']['show'] = true;
        //提示框
        $option['tooltip']['trigger'] = 'axis';
        $option['tooltip']['axisPointer']['type'] = 'shadow';
        //数据集
//        $option['dataset'] = $dataset;
        //图例
        $option['legend']['show'] = true;
//        $option['legend']['orient'] = 'vertical';
//        $option['legend']['right'] = 0;
        $option['legend']['top'] = 30;
//        $option['legend']['data'] =  $xAxisData;
        //X轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列
        $option['xAxis']['type'] = 'category';
        $option['xAxis']['data'] = $xAxisData;
        //X轴，字体倾斜度数
        $option['xAxis']['axisLabel']['rotate'] = 40;
        $option['xAxis']['axisLabel']['show'] = true;
        //X轴，字体样式
        $option['xAxis']['axisLabel']['textStyle']['fontSize'] = 11;
        //Y轴，数值轴
        $option['yAxis']['type'] = 'value';
        //Y轴名称
        $option['yAxis']['name'] = '数量（个）';
        $option['yAxis']['min'] = 0;
//        $option['yAxis']['max'] = 1035;
        $option['yAxis']['axisLabel']['formatter'] = '{value}';
        $option['yAxis']['nameLocation'] = 'middle';
        //Y轴名称名称与轴线之间的距离
        $option['yAxis']['nameGap'] = 40;
        //在系列（series） 中设置数据
        $i=0;
        foreach ($types as $k=>$type){
            $option['series'][$i] = [
                'type' => 'bar',
                'stack' => 'total',
                'name' => $type,
                'data' => $seriesData[$k],
                'label' => [
                    'show' => true,
                    'emphasis' => [
                        'focus' => 'series'
                    ]
                ]
            ];
            $i++;
        }
        //折线
        $option['series'][$i]['name'] = '合计';
        $option['series'][$i]['data'] = $seriesData['total'];
        $option['series'][$i]['label']['show'] = true;
        $option['series'][$i]['type'] = 'line';

        //图表离容器底部的距离
        $option['grid']['bottom'] = '30%';

        $data['option'] = $option;

        echo json_encode($data,JSON_UNESCAPED_UNICODE);exit();

    }
}