<?php
namespace App\Controller\Stat\Fruit;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Patent extends BaseController
{
    private $view = NULL;
    private $startAt = '';
    private $endAt = '';
    private $mark = 'fruit_patent';
    private $cache = NULL;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
        $this->cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
        if(input::getMix('start_at')) $this->startAt = input::getMix('start_at');
        if(input::getMix('end_at')) $this->endAt = input::getMix('end_at');
    }

    public function index()
    {
        if(input::getMix('update')=='yes'){
            $datas = $this->doStat();
        }else{
            $datas = $this->getStat();
        }
        $this->view->set('datas',$datas);
        $this->view->set('startAt',$this->startAt);
        $this->view->set('endAt',$this->endAt);
        $this->view->apply("inc_body","fruit/patent/index");
        $this->view->display("page");
    }

    private function getStat()
    {
        return $this->doStat();
//        $years = 'ALL';
//        if($this->startAt && $this->endAt){
//            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
//        }
//        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
//        if($statistic->isNew()){
//            return $this->doStat();
//        }
//        return $statistic->getData();
    }

    private function doStat()
    {
        $db = sf::getLib('db');
        $startAt = $this->startAt;
        $endAt = $this->endAt;
        $addWhere = "statement = 20";
        if($startAt && $endAt){
            $addWhere .= " and (`date` >= '{$startAt}' and `date` <= '{$endAt}')";
        }
        //二级单位
        $secondCompanys = getSecondCompanysShort();
        $companyIds = [];
        //按专利数量排序
        $query = $db->query("select company_id,count(*) c from patents where {$addWhere} GROUP BY company_id order by c desc");
        while($row = $db->fetch_array($query)) {
            if (in_array($row['company_id'],$companyIds)) continue;
            $companyIds[] = $row['company_id'];
        }
        if(count($companyIds)<count($secondCompanys)){
            foreach ($secondCompanys as $companyId=>$secondCompany){
                if (in_array($companyId,$companyIds)) continue;
                $companyIds[] = $companyId;
            }
        }

        $companyIds = array_unique($companyIds);
        $companyIds = array_filter($companyIds);
        foreach ($companyIds as $companyId){
            if(!$secondCompanys[$companyId]) continue;
            $departmentWhere = $addWhere;
            //发明
            $departmentWhere .= " and `type` = '发明' and company_id = '{$companyId}'";
            $count1= $db->result_first("select count(*) c from patents where ".$departmentWhere);

            $datas[$companyId]['subject'] = $secondCompanys[$companyId];
            $datas[$companyId]['fm'] = $count1;
            //实用新型
            $departmentWhere = $addWhere;
            $departmentWhere .= " and `type` = '实用新型' and company_id = '{$companyId}'";
            $count2= $db->result_first("select count(*) c from patents where ".$departmentWhere);
            $datas[$companyId]['syxx'] = $count2;
            //外观设计
            $departmentWhere = $addWhere;
            $departmentWhere .= " and `type` = '外观设计' and company_id = '{$companyId}'";
            $count3= $db->result_first("select count(*) c from patents where ".$departmentWhere);
            $datas[$companyId]['wgsj'] = $count3;
            //合计
            $datas[$companyId]['total'] = $count1+$count2+$count3;
        }
        $years = 'ALL';
        if($this->startAt && $this->endAt){
            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
        }
        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
        $statistic->setData($datas);
        $statistic->save();
        return $datas;
    }

    public function chart(){
        $stat = $this->getStat();
        $xAxisData = []; //X轴（二级单位）
        $seriesData = [];                  //Y轴
        $seriesData['fm'] = [];  //发明数量
        $seriesData['syxx'] = [];  //实用新型数量
        $seriesData['wgsj'] = [];  //外观设计数量

        foreach ($stat as $s){
            $xAxisData[] = $s['subject'];
            $seriesData['fm'][] = $s['fm'];
            $seriesData['syxx'][] = $s['syxx'];
            $seriesData['wgsj'][] = $s['wgsj'];
            $seriesData['total'][] = $s['total'];
        }

        //图表标题
        $option['title']['text'] = '专利成果统计图';
        $option['title']['x'] = 'center';
        //工具栏
        $option['toolbox']['show'] = true;
        $option['toolbox']['feature']['saveAsImage']['show'] = true;
        //提示框
        $option['tooltip']['trigger'] = 'axis';
        $option['tooltip']['axisPointer']['type'] = 'shadow';
        //数据集
//        $option['dataset'] = $dataset;
        //图例
        $option['legend']['show'] = true;
//        $option['legend']['orient'] = 'vertical';
//        $option['legend']['right'] = 0;
        $option['legend']['top'] = 30;
//        $option['legend']['data'] =  $xAxisData;
        //X轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列
        $option['xAxis']['type'] = 'category';
        $option['xAxis']['data'] = $xAxisData;
        //X轴，字体倾斜度数
        $option['xAxis']['axisLabel']['rotate'] = 40;
        $option['xAxis']['axisLabel']['show'] = true;
        //X轴，字体样式
        $option['xAxis']['axisLabel']['textStyle']['fontSize'] = 11;
        //Y轴，数值轴
        $option['yAxis']['type'] = 'value';
        //Y轴名称
        $option['yAxis']['name'] = '数量（个）';
        $option['yAxis']['min'] = 0;
        $option['yAxis']['minInterval'] = 1;
        $option['yAxis']['axisLabel']['formatter'] = '{value}';
        $option['yAxis']['nameLocation'] = 'middle';
        //Y轴名称名称与轴线之间的距离
        $option['yAxis']['nameGap'] = 40;
        //在系列（series） 中设置数据
        $option['series'][0]['type'] = 'bar';
        $option['series'][0]['stack'] = 'total';
        //系列名称
        $option['series'][0]['name'] = '发明';
        $option['series'][0]['data'] = $seriesData['fm'];
        //显示系列标签
        $option['series'][0]['label']['show'] = true;
        $option['series'][0]['emphasis']['focus'] = 'series';
        //在系列（series） 中设置数据
        $option['series'][1]['type'] = 'bar';
        $option['series'][1]['stack'] = 'total';
        //系列名称
        $option['series'][1]['name'] = '实用新型';
        $option['series'][1]['data'] = $seriesData['syxx'];
        //显示系列标签
        $option['series'][1]['label']['show'] = true;
        $option['series'][1]['emphasis']['focus'] = 'series';
        //在系列（series） 中设置数据
        $option['series'][2]['type'] = 'bar';
        $option['series'][2]['stack'] = 'total';
        //系列名称
        $option['series'][2]['name'] = '外观设计';
        $option['series'][2]['data'] = $seriesData['wgsj'];
        //显示系列标签
        $option['series'][2]['label']['show'] = true;
        $option['series'][2]['emphasis']['focus'] = 'series';

        //折线
        $option['series'][3]['name'] = '合计';
        $option['series'][3]['data'] = $seriesData['total'];
        $option['series'][3]['label']['show'] = true;
        $option['series'][3]['type'] = 'line';

        //图表离容器底部的距离
        $option['grid']['bottom'] = '30%';

        $data['option'] = $option;

        echo json_encode($data,JSON_UNESCAPED_UNICODE);exit();

    }
}