<?php
namespace App\Controller\Stat\Talent;
use App\Controller\BaseController;
use Sofast\Core\config;
use Sofast\Support\Template;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;
class Age extends BaseController
{
    private $view = NULL;
    private $startAt = '';
    private $endAt = '';
    private $mark = 'talent_age';
    private $cache = NULL;

    public function load()
    {
        parent::load();
        $this->view = new Template(realpath(dirname(__FILE__)) . '/../view/');
        $this->cache = sf::getLib("cache",config::get('cache_dir','cache'),config::get('cache_limit',3600));
        if(input::getMix('start_at')) $this->startAt = input::getMix('start_at');
        if(input::getMix('end_at')) $this->endAt = input::getMix('end_at');
    }

    public function index()
    {
        if(input::getMix('update')=='yes'){
            $datas = $this->doStat();
        }else{
            $datas = $this->getStat();
        }
        $this->view->set('datas',$datas);
        $this->view->set('startAt',$this->startAt);
        $this->view->set('endAt',$this->endAt);
        $this->view->apply("inc_body","talent/age/index");
        $this->view->display("page");
    }

    private function getStat()
    {
        return $this->doStat();
//        $years = 'ALL';
//        if($this->startAt && $this->endAt){
//            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
//        }
//        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
//
//        if($statistic->isNew()){
//            return $this->doStat();
//        }
//        return $statistic->getData();
    }

    private function doStat()
    {
        $db = sf::getLib('db');
        $startAt = $this->startAt;
        $endAt = $this->endAt;

        $addWhere = "is_lock>=0 and user_birthday is not null and user_birthday!=''";
        if($startAt && $endAt){
            $addWhere .= " and (`created_at` >= '{$startAt}' and `created_at` <= '{$endAt}')";
        }

        $ranges = [
            '0-35' => '35岁及以下',
            '36-45' => '36~45岁',
            '46-55' => '46~55岁',
            '56-100' => '56岁及以上',
        ];

        $datas = [];
        foreach ($ranges as $k=>$range){
            switch ($k){
                case '0-35':
                    $sql  = "SELECT count(*) c,FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) AS age FROM declarers WHERE {$addWhere} and FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) <=35";
                    if($db->getDbType()=='dm'){
                        $sql = "SELECT count(*) c FROM declarers where DATEDIFF(YEAR, user_birthday, CURRENT_DATE) <=35";
                    }
                    break;
                case '36-45':
                    $sql  = "SELECT count(*) c,FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) AS age FROM declarers WHERE {$addWhere} and FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) BETWEEN 36 AND 45";
                    if($db->getDbType()=='dm'){
                        $sql = "SELECT count(*) c FROM declarers where DATEDIFF(YEAR, user_birthday, CURRENT_DATE) BETWEEN 36 AND 45";
                    }
                    break;
                case '46-55':
                    $sql  = "SELECT count(*) c,FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) AS age FROM declarers WHERE {$addWhere} and FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) BETWEEN 46 AND 55";
                    if($db->getDbType()=='dm'){
                        $sql = "SELECT count(*) c FROM declarers where DATEDIFF(YEAR, user_birthday, CURRENT_DATE) BETWEEN 46 AND 55";
                    }
                    break;
                case '56-100':
                    $sql  = "SELECT count(*) c,FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) AS age FROM declarers WHERE {$addWhere} and FLOOR(DATEDIFF(CURDATE(), user_birthday) / 365) >=56";
                    if($db->getDbType()=='dm'){
                        $sql = "SELECT count(*) c FROM declarers where DATEDIFF(YEAR, user_birthday, CURRENT_DATE) >=56";
                    }
                    break;
            }
            $c = $db->result_first($sql);
            $datas[$k]['subject'] = $range;
            $datas[$k]['count'] = $c;
        }

        $years = 'ALL';
        if($this->startAt && $this->endAt){
            $years = substr($this->startAt,0,4).'-'.substr($this->endAt,0,4);
        }
        $statistic = sf::getModel('Statistics')->selectByMark($this->mark,$years);
        $statistic->setData($datas);
        $statistic->save();
        return $datas;
    }

    public function chart(){
        $stat = $this->getStat();
        $xAxisData = []; //X轴（二级单位）
        $seriesData = [];                  //Y轴
        $seriesData['js'] = [];  //技术专家数量
        $seriesData['gl'] = [];  //管理专家数量
        $seriesData['cw'] = [];  //财务专家数量

        foreach ($stat as $s){
            $xAxisData[] = $s['work_unit'];
            $seriesData['js'][] = $s['js'];
            $seriesData['gl'][] = $s['gl'];
            $seriesData['cw'][] = $s['cw'];
            $seriesData['total'][] = $s['total'];
        }

        //图表标题
        $option['title']['text'] = '专家类别统计图';
        $option['title']['x'] = 'center';
        //工具栏
        $option['toolbox']['show'] = true;
        $option['toolbox']['feature']['saveAsImage']['show'] = true;
        //提示框
        $option['tooltip']['trigger'] = 'axis';
        $option['tooltip']['axisPointer']['type'] = 'shadow';
        //数据集
//        $option['dataset'] = $dataset;
        //图例
        $option['legend']['show'] = true;
//        $option['legend']['orient'] = 'vertical';
//        $option['legend']['right'] = 0;
        $option['legend']['top'] = 30;
//        $option['legend']['data'] =  $xAxisData;
        //X轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列
        $option['xAxis']['type'] = 'category';
        $option['xAxis']['data'] = $xAxisData;
        //X轴，字体倾斜度数
        $option['xAxis']['axisLabel']['rotate'] = 40;
        $option['xAxis']['axisLabel']['show'] = true;
        //X轴，字体样式
        $option['xAxis']['axisLabel']['textStyle']['fontSize'] = 11;
        //Y轴，数值轴
        $option['yAxis']['type'] = 'value';
        //Y轴名称
        $option['yAxis']['name'] = '数量';
        $option['yAxis']['min'] = 0;
        $option['yAxis']['minInterval'] = 1;
//        $option['yAxis']['max'] = 1035;
        $option['yAxis']['axisLabel']['formatter'] = '{value}';
        $option['yAxis']['nameLocation'] = 'middle';
        //Y轴名称名称与轴线之间的距离
        $option['yAxis']['nameGap'] = 40;
        //在系列（series） 中设置数据
        $option['series'][0]['type'] = 'bar';
        $option['series'][0]['stack'] = 'total';
        //系列名称
        $option['series'][0]['name'] = '技术专家';
        $option['series'][0]['data'] = $seriesData['js'];
        //显示系列标签
        $option['series'][0]['label']['show'] = true;
        $option['series'][0]['emphasis']['focus'] = 'series';
        //在系列（series） 中设置数据
        $option['series'][1]['type'] = 'bar';
        $option['series'][1]['stack'] = 'total';
        //系列名称
        $option['series'][1]['name'] = '管理专家';
        $option['series'][1]['data'] = $seriesData['gl'];
        //显示系列标签
        $option['series'][1]['label']['show'] = true;
        $option['series'][1]['emphasis']['focus'] = 'series';
        //在系列（series） 中设置数据
        $option['series'][2]['type'] = 'bar';
        $option['series'][2]['stack'] = 'total';
        //系列名称
        $option['series'][2]['name'] = '财务专家';
        $option['series'][2]['data'] = $seriesData['cw'];
        //显示系列标签
        $option['series'][2]['label']['show'] = true;
        $option['series'][2]['emphasis']['focus'] = 'series';

        //折线
        $option['series'][3]['name'] = '合计';
        $option['series'][3]['data'] = $seriesData['total'];
        $option['series'][3]['label']['show'] = true;
        $option['series'][3]['type'] = 'line';

        //图表离容器底部的距离
        $option['grid']['bottom'] = '30%';

        $data['option'] = $option;

        echo json_encode($data,JSON_UNESCAPED_UNICODE);exit();

    }
}