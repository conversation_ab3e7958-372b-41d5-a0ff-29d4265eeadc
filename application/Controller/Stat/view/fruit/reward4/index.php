<script src="<?=site_path('assets/echarts/echarts.min.js')?>"></script>
<script src="<?=site_path('assets/echarts/extension/dataTool.js')?>"></script>
<?php
$baseurl = site_url(strtolower(\Sofast\Core\router::getController()));
?>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                奖励荣誉统计
            </h3>
            <div class="block-options">
                <?php
                $url = $baseurl.'/index/update/yes';
                if($startAt && $endAt) $url.='/start_at/'.$startAt.'/end_at/'.$endAt;
                ?>
                <!--
                <?= Button::setUrl(site_url($url))->setEvent('btnLoading(this)')->link('更新统计数据') ?>
                -->
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="btn-group btn-group-sm" role="group">
                    <p style="clear:both;"></p>
                </div>
                <div class="search">
                    <?php include_once('search_part.php') ?>
                </div>
                <div class="box">
                    <h4>省部级科技奖励获奖情况</h4>
                    <?php
                    $rewardNames = [
                        'kjjb'=>'省科学技术进步奖',
                        'zrkx'=>'省自然科学奖',
                        'jsfm'=>'省技术发明奖',
                        'gjhz'=>'省国际合作奖',
                        'jcgx'=>'省科技杰出贡献奖',
                        'jcqn'=>'省杰出青年科技创新奖',
                    ];
                    ?>
                    <table class="table table-bordered">
                        <tr>
                            <th class="text-center">单位/部门</th>
                            <?php
                                foreach ($rewardNames as $k=>$rewardName):
                            ?>
                            <th class="text-center"><?=$rewardName?></th>
                            <?php
                                    $total[$k] = 0;
                                endforeach;?>
                        </tr>
                        <?php
                            foreach ($datas as $companyId=>$data):
                                $link = site_url('office/reward/index?corporation_id='.$companyId.'&statement=20&fuzzy=no&category=科技奖励&level=省部级');
                                if($startAt && $endAt) $link .= '&start_at='.$startAt.'&end_at='.$endAt;
                        ?>
                        <tr>
                            <td align="center"><?=$data['subject']?></td>
                            <?php
                            foreach ($rewardNames as $k=>$rewardName):
                                $total[$k] += $data[$k];
                                ?>
                                <th class="text-center"><a href="<?=$link.'&search='.$rewardName.'&field=reward_name'?>" target="_blank"><?=$data[$k]?></a></th>
                            <?php endforeach;?>
                        </tr>
                        <?php endforeach;
                        ?>
                        <tr>
                            <td align="center"><b>总计</b></td>
                            <?php
                            foreach ($rewardNames as $k=>$rewardName):
                            ?>
                            <td align="center"><?=$total[$k]?></td>
                            <?php endforeach;?>
                        </tr>
                    </table>
                    <div class="chart" id="chart1" style="width: 100%;height:600px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me) {
        $(me).attr("disabled", "disabled");
        $(me).addClass("disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 统计中...');
    }
    var charts = [];
    var actions = [];
    var startTime,endTime;      //统计加载时间
    var t;
    $(".chart").each(function (){
        var id = $(this).attr('id');
        var chart = echarts.init(document.getElementById(id));
        charts.push(chart);
        actions.push(id);
    });

    for(var i =0;i<charts.length;i++){
        charts[i].showLoading({
            text : 'loading',
            effect : 'whirling'
        });
    }

    $(function () {
        // $.ajaxSettings.async = false;
        getDatas();

        $("#fullscreen").click(function (){
            window.setTimeout(function (){
                for(var i =0;i<charts.length;i++) {
                    charts[i].resize();
                }
            },500);
        });
    });

    function getDatas() {
        var year = 'ALL';
        startTime = new Date().getTime();
        t = window.setTimeout("getChartOption(0,'"+year+"')",100);
    }

    function getChartOption(chartNo,year)
    {
        window.clearTimeout(t);
        var action = actions[chartNo];
        var chartObj = charts[chartNo];
        var start_at = $('#start_at').val();
        var end_at = $('#end_at').val();
        $.getJSON('<?=$baseurl.'/chart'?>',{start_at:start_at,end_at:end_at}).done(function (data) {
            chartObj.hideLoading();
            var content = data.content;
            if(content){
                for (var i in content){
                    $("#"+i).html(content[i]);
                }
            }
            // 填入数据
            var option = data.option;
            chartObj.setOption(option);
            //加载下一个图表
            var nextChatNo = chartNo+1;
            $("#charts-loaded-count").text(nextChatNo);
            //所统计加载时间
            endTime=new Date().getTime();
            var spendSeconds = (endTime-startTime)/1000;
            $("#charts-loaded-second").text(spendSeconds);

            if(actions.length>nextChatNo){
                t = window.setTimeout("getChartOption("+nextChatNo+",'"+year+"')",100);
            }
        });
    }

</script>
