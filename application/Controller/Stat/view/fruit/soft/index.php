<script src="<?=site_path('assets/echarts/echarts.min.js')?>"></script>
<script src="<?=site_path('assets/echarts/extension/dataTool.js')?>"></script>
<?php
$baseurl = site_url(strtolower(\Sofast\Core\router::getController()));
?>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                软件著作权统计
            </h3>
            <div class="block-options">
                <?php
                $url = $baseurl.'/index/update/yes';
                if($startAt && $endAt) $url.='/start_at/'.$startAt.'/end_at/'.$endAt;
                ?>
                <!--
                <?= Button::setUrl(site_url($url))->setEvent('btnLoading(this)')->link('更新统计数据') ?>
                -->
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="btn-group btn-group-sm" role="group">
                    <p style="clear:both;"></p>
                </div>
                <div class="search">
                    <?php include_once('search_part.php') ?>
                </div>
                <div class="box">
                    <h4>软件著作权统计</h4>
                    <table class="table table-bordered">
                        <tr>
                            <th class="text-center" style="width: 50%">单位/部门</th>
                            <th class="text-center" style="width: 50%">软著数量</th>
                        </tr>
                        <?php
                            $total = 0;
                            foreach ($datas as $companyId=>$data):
                                $total += $data['count'];
                                $link = site_url('office/soft/index?corporation_id='.$companyId.'&statement=20&fuzzy=no');
                                if($startAt && $endAt) $link .= '&start_at='.$startAt.'&end_at='.$endAt;
                        ?>
                        <tr>
                            <td align="center"><?=$data['subject']?></td>
                            <td align="center"><a href="<?=$link?>" target="_blank"><?= $data['count'] ?></a></td>
                        </tr>
                        <?php endforeach;
                        ?>
                        <tr>
                            <td align="center"><b>总计</b></td>
                            <td align="center"><?=$total?></td>
                        </tr>
                    </table>
                    <div class="chart" id="chart1" style="width: 100%;height:600px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function btnLoading(me) {
        $(me).attr("disabled", "disabled");
        $(me).addClass("disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 统计中...');
    }
    var charts = [];
    var actions = [];
    var startTime,endTime;      //统计加载时间
    var t;
    $(".chart").each(function (){
        var id = $(this).attr('id');
        var chart = echarts.init(document.getElementById(id));
        charts.push(chart);
        actions.push(id);
    });

    for(var i =0;i<charts.length;i++){
        charts[i].showLoading({
            text : 'loading',
            effect : 'whirling'
        });
    }

    $(function () {
        // $.ajaxSettings.async = false;
        getDatas();

        $("#fullscreen").click(function (){
            window.setTimeout(function (){
                for(var i =0;i<charts.length;i++) {
                    charts[i].resize();
                }
            },500);
        });
    });

    function getDatas() {
        var year = 'ALL';
        startTime = new Date().getTime();
        t = window.setTimeout("getChartOption(0,'"+year+"')",100);
    }

    function getChartOption(chartNo,year)
    {
        window.clearTimeout(t);
        var action = actions[chartNo];
        var chartObj = charts[chartNo];
        var start_at = $('#start_at').val();
        var end_at = $('#end_at').val();
        $.getJSON('<?=$baseurl.'/chart'?>',{start_at:start_at,end_at:end_at}).done(function (data) {
            chartObj.hideLoading();
            var content = data.content;
            if(content){
                for (var i in content){
                    $("#"+i).html(content[i]);
                }
            }
            // 填入数据
            var option = data.option;
            chartObj.setOption(option);
            //加载下一个图表
            var nextChatNo = chartNo+1;
            $("#charts-loaded-count").text(nextChatNo);
            //所统计加载时间
            endTime=new Date().getTime();
            var spendSeconds = (endTime-startTime)/1000;
            $("#charts-loaded-second").text(spendSeconds);

            if(actions.length>nextChatNo){
                t = window.setTimeout("getChartOption("+nextChatNo+",'"+year+"')",100);
            }
        });
    }

</script>
