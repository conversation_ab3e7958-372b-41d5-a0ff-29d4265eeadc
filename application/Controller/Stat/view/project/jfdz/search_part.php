<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>

<form action="<?=$baseurl.'/index'?>" method="post" name="search" id="search">
    <table width="100%" align="center">
        <tbody id="show_search">
        <tr>
            <td>
                <button type="button" class="btn" id="daterange-btn" style="border: 1px solid #d4dcec">
                    <i class="fa fa-calendar"></i>
                    <span>时间</span>
                    <i class="fa fa-caret-down"></i>
                </button>
                <input type="hidden" name="start_at" value="" id="start_at">
                <input type="hidden" name="end_at" value="" id="end_at">
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        </tbody>
    </table>
</form>
<script>

    function init() {
        //定义locale汉化插件
        var locale = {
            "format": 'YYYY-MM-DD',
            "separator": " -222 ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间'",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        var ranges = {
            // '本月': [moment().startOf('month'), moment().endOf('month')],
            // '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
            '本年': [moment().startOf('year'), moment().endOf('year')],
            '上年': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
            // '最近一月': [moment().subtract(29, 'days'), moment()],
            '最近一年': [moment().subtract(1, 'year'), moment()],
            // '最近三年': [moment().subtract(3, 'year').startOf('year'),moment().subtract(1, 'year').endOf('year')],
            '最近三年': [moment().subtract(2, 'year').startOf('year'), moment()],
            '最近五年': [moment().subtract(4, 'year').startOf('year'), moment()],
            '十三五': ['2016-01-01', '2020-12-31'],
            '十四五': ['2021-01-01', '2025-12-31'],
        };
        //初始化显示当前时间
        var start_at = "<?=input::getMix('start_at')?>";
        var end_at = "<?=input::getMix('end_at')?>";
        $("#start_at").val(start_at);
        $("#end_at").val(end_at);
        if(start_at && end_at){
            $('#daterange-btn span').html(start_at + ' - ' + end_at);
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    'ranges': ranges,
                    startDate: start_at,
                    endDate: end_at
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }else{
            $('#daterange-btn span').html('请选择起始时间');
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    'ranges': ranges,
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }
    };
    $(document).ready(function() {
        init();
    });
</script>