<div class="form-group row">
    <?php
    $details = $item->getDetails();
    $count = count($details);
    $selectedCount = 0;
    foreach($details as $k=>$v){
        if($v['is_selected']) $selectedCount++;
    }
    ?>
    <table class="table">
        <thead>
        <tr>
            <th style="width: 50px" class="text-center"><input name="selectAll" type="checkbox" id="selectAll" <?=($selectedCount>0 && $selectedCount==$count)?'checked':''?> /></th>
            <th>奖励荣誉名称</th>
            <th style="width: 15%">奖项级别</th>
            <th style="width: 15%">获奖日期</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $i=0;
        foreach($details as $k=>$v):
            $i++;
            ?>
            <tr data-row="<?=$k?>">
                <td class="text-center"><input name="select_id[]" type="checkbox" value="<?=$v['id']?>" <?=$v['is_selected']?'checked':''?> /></td>
                <td><a href="<?=site_url('user/reward/show/id/'.$v['project_id'])?>" target="_blank"><?=$v['subject']?></a></td>
                <td><?=$v['level']?></td>
                <td><?=$v['radicate_at']?></td>
            </tr>
        <?php endforeach;?>
        </tbody>
    </table>
</div>