<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            科研考核自评表
        </h2>
        <div class="content-options">
            <?=Button::back()?>
            <?php
            if(!$evaluate->isNew()):
                ?>
                <?=Button::setUrl(site_url("evaluate/apply/show/id/".$evaluate->getEvaluateId()))->setIcon('show')->link('查看预览')?>
                <?=Button::setUrl(site_url("evaluate/apply/submit/id/".$evaluate->getEvaluateId()))->setIcon('submit')->setClass('btn-alt-danger')->link('资料上报')?>
            <?php endif;?>
        </div>
    </div>

    <form name="validateForm" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
        <input type="hidden" name="plan_id" value="<?=$evaluate->getPlanId()?>">
        <input type="hidden" name="id" value="<?=$evaluate->getEvaluateId()?>">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">科研考核计划<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <?=$evaluate->getPlan()->getSubject()?>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">填报单位<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input type="text" id="subject" value="<?=$evaluate->getCompanyName()?>" disabled class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">联系人<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="linkman" type="text" id="linkman" value="<?=$evaluate->getLinkman()?>" required="required" class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">联系人电话<em>*</em></label>
                            <div class="col-md-5 col-sm-5 col-xs-5">
                                <input name="mobile" type="text" id="mobile" value="<?=$evaluate->getMobile()?>" required="required" class="form-control"/>
                                <small class="help-block"></small>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <?=Button::setType('submit')->setEvent('btnLoading(this)')->setIcon('save')->button('保存并填写下一页')?>
                        </div>
                    </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    function btnLoading(me)
    {
        $(me).attr("disabled","disabled");
        $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
        validateForm.submit();
    }
</script>