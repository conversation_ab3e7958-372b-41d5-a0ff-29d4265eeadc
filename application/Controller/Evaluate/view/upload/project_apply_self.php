<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<div class="block block-rounded">
    <div class="page-body">
        <div class="block-content tab-content">
            <div class="tab-pane active" id="tab1" role="tabpanel">
                <div class="clearfix"></div>
                <?php
                    if(!$is_show):
                ?>
                <div class="alert alert-warning alert-dismissable" role="alert">
                    <h3 class="alert-heading font-size-h6 my-2">
                        附件上传说明
                    </h3>
                    <p>1.请上传<b class="text-danger">申请书盖章扫描件</b>，一个项目(课题)只需要上传一份文件<br>
                        2.上传文件格式：pdf<br>
                        3.单个文件体积不超过20MB<br>
                        4.上传后请完善项目编号、项目名称、项目负责人等信息</p>
                </div>
                <div class="form-group">
                    <div id="uploader" class="wu-example dropzone">
                        <div class="queueList">
                            <div id="dndArea" class="placeholder">
                                <div id="upload-<?=$item_type?>"></div>
                                <?=Button::setIcon('fa fa-upload')->button('点击上传文件')?>
                                <p>或者将文件拖到此处，上传文件格式为：pdf，体积不超过20MB</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clearfix"></div>
                <?php endif;?>
                <div class="header no-margin-top">已上传文件列表</div>

                <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="item_id" value="<?=$evaluate->getEvaluateId()?>">
                    <input type="hidden" name="table" value="filemanager">
                    <input type="hidden" name="upload_size" value="20480000">
                    <table class="table table-sm">
                        <thead>
                        <tr>
                            <?php
                            if(!$is_show):
                            ?>
                            <th class="text-center" style="width: 60px">排序</th>
                            <?php endif;?>
                            <th class="text-center" style="width: 80px">序号</th>
                            <th>项目来源</th>
                            <th>项目编号</th>
                            <th>项目名称</th>
                            <th>项目负责人</th>
                            <th class="text-center" style="width: 120px">操作</th>
                        </tr>
                        </thead>
                        <tbody id="file-list-<?=$item_type?>" class="file-list-group">
                        <?php if($evaluate->getAttachment($item_type)->getTotal()>0):
                            $attachments = $evaluate->getAttachment($item_type);
                            while($file = $attachments->getObject()):
                                ?>
                                <tr id="file<?=$file->getId()?>">
                                    <?php
                                    if(!$is_show):
                                    ?>
                                    <td class="text-center">
                                        <i class="fa fa-arrows-alt handle"></i>
                                    </td>
                                    <?php endif;?>
                                    <td class="text-center">
                                        <input type="hidden" name="attachment_id[]" value="<?=$file->getId()?>">
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" style="text-align: center" type="number" name="no[<?=$file->getId()?>]" min="1" value="<?=$attachments->getIndex()?>">
                                        <?php else:?>
                                        <?=$attachments->getIndex()?>
                                        <?php endif;?>
                                    </td>
                                    <td>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <select class="form-control" name="filenote[<?=$file->getId()?>][source]">
                                            <?=getSelectFromArray(['所立项目'],$file->getFileNoteByJson('source'))?>
                                        </select>
                                        <?php else:?>
                                        <?=$file->getFileNoteByJson('source')?>
                                        <?php endif;?>
                                    </td>
                                    <td>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" type="text" name="filenote[<?=$file->getId()?>][accept_id]" value="<?=$file->getFileNoteByJson('accept_id')?>">
                                        <?php else:?>
                                        <?=$file->getFileNoteByJson('accept_id')?>
                                        <?php endif;?>
                                    </td>
                                    <td>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" type="text" name="filenote[<?=$file->getId()?>][subject]" value="<?=$file->getFileNoteByJson('subject')?>">
                                        <?php else:?>
                                        <?=$file->getFileNoteByJson('subject')?>
                                        <?php endif;?>
                                    </td>
                                    <td>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <input class="form-control" type="text" name="filenote[<?=$file->getId()?>][user_name]" value="<?=$file->getFileNoteByJson('user_name')?>">
                                        <?php else:?>
                                        <?=$file->getFileNoteByJson('user_name')?>
                                        <?php endif;?>
                                    </td>
                                    <td class="text-center">
                                        <a href="<?=$file->getPreviewUrl()?>" class="btn btn-alt-primary btn-sm" target="_blank">查看</a>
                                        <?php
                                        if(!$is_show):
                                        ?>
                                        <a href="javascript:delete_file('<?=$file->getId()?>','')" onClick="return confirm('确定要删除该文件吗？')" class="btn btn-alt-danger btn-sm">删除</a>
                                        <?php endif;?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                        <?php endif;?>
                        </tbody>
                        <tr>
                    </table>

                    <div class="clearfix"></div>

                    <?php
                    if(!$is_show):
                    ?>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <div style="width:100px;margin:0 auto;text-align:center">
                            <a href="javascript:void(0);" onclick="form1.submit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a>
                        </div>
                    </div>
                    <?php endif;?>
                </form>
                <div class="clearfix"></div>
            </div>
        </div>
    </div>
    <div class="page-footer">
        <!--右下角浮动-->

    </div>
</div>
<script>
    var baseurl = $('#baseUrl').val();
    var fileSizeLimit = '2000';   //单位：MB
    var fileSingleSizeLimit = '20';   //单位：MB
    var fileNumLimit = 100;   //允许上传的文件数量
    $(function(){
        var uploadFileNum = '<?=$evaluate->getAttachment()->getTotal()?>';   //已上传的文件数量
        $('#upload-<?=$item_type?>').webupload({
            formData:{item_id:'<?=$evaluate->getEvaluateId()?>',item_type:'<?=$item_type?>'},
            accept:{
                title: 'PDF',
                extensions: 'pdf',
                mimeTypes: 'pdf'
            },
            dnd: '#uploader .queueList',
            paste: document.body,
            uploadFileNum:uploadFileNum,
            fileNumLimit:fileNumLimit,
            fileSizeLimit:fileSizeLimit*1024*1024,
            fileSingleSizeLimit:fileSingleSizeLimit*1024*1024,
        },{
            success:function(file, response){
                this.options.uploadFileNum++;
                var data = eval(response._raw);
                var id = data[0].id;
                var file_name = data[0].file_name;
                var index1=file_name.lastIndexOf(".");
                var index2=file_name.length;
                var file_ext=file_name.substring(index1,index2);//后缀名
                var file_savepath = data[0].path;
                var file_size = _sizeFormat(data[0].size);
                var upload_time = data[0].time;
                var fid = file.id;
                var filenote = file_name.replace(file_ext, '');
                var fileno = $("#file-list-<?=$item_type?> tr").length;

                var trhtml = '<tr id="file'+id+'">\n' +
                    '    <td class="text-center">\n' +
                    '    <i class="fa fa-arrows-alt handle"></i>\n' +
                    '    </td>\n' +
                    '    <td class="text-center"><input type="hidden" name="attachment_id[]" value="'+id+'"><input class="form-control" style="text-align: center" type="number" min=1 name="no['+id+']" value="'+(fileno+1)+'" /></td>\n' +
                    '    <td class="text-center"><select class="form-control" name="filenote['+id+'][source]"><?=getSelectFromArray(['所立项目'],0,true,'')?></select></td>\n' +
                    '    <td class="text-center"><input class="form-control" type="text" name="filenote['+id+'][accept_id]" value="" /></td>\n' +
                    '    <td class="text-center"><input class="form-control" type="text" name="filenote['+id+'][subject]" value="'+filenote+'" /></td>\n' +
                    '    <td class="text-center"><input class="form-control" type="text" name="filenote['+id+'][user_name]" value="" /></td>\n' +
                    '    <td class="text-center">\n' +
                    '\t<a href="'+baseurl+'up_files/'+file_savepath+'" class="btn btn-alt-primary btn-sm" target="_blank">查看</a>\n' +
                    '\t<a href="javascript:delete_file('+id+',\''+fid+'\')" class="btn btn-alt-danger btn-sm" onClick="return confirm(\'确定要删除该文件吗？\')">删除</a>\n' +
                    '    </td>\n' +
                    '</tr>';
                $(".ant-table-placeholder").remove();
                $("#file-list-<?=$item_type?>").append(trhtml);
            }
        });
    });

    function delete_file(id,fid) {
        var url = "<?=site_url('common/file_delete')?>";
        $.getJSON(url,{fid:id,id:'<?=$evaluate->getEvaluateId()?>'},function (data) {
            if(data.code==1){
                showSuccess('删除成功！');
                $("#file"+id).remove();
                $("#attachment_"+id).remove();
                if(fid) $('#PDF_'+fid).remove();
            }else{
                showError(data.msg);
            }
        });
    }

    var filetable = document.getElementById('file-list-<?=$item_type?>');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $(".file-list-group tr").each(function (){
                $(this).find("input[name^=no]").val(++index);
            })
        },

    });
</script>