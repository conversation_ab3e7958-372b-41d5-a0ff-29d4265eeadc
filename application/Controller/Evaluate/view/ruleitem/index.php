<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h1 class="flex-sm-fill font-size-h2 font-w400 mt-2 mb-0 mb-sm-2">

        </h1>
        <nav class="flex-sm-00-auto ml-sm-3" aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?=site_url('evaluate/ruleitem/index')?>">考核指标管理</a></li>
                <?php
                if(!$ruleitem->isNew() && $ruleitem->getLevel()==1):
                ?>
                <li class="breadcrumb-item"><a href="<?=site_url('evaluate/ruleitem/child_index/id/'.$ruleitem->getId())?>"><?=$ruleitem->getSubject()?></a></li>
                <?php endif;?>
                <?php
                if(!$ruleitem->isNew() && $ruleitem->getLevel()==2):
                    $pIndex = $ruleitem->getParent();
                ?>
                <li class="breadcrumb-item"><a href="<?=site_url('evaluate/ruleitem/child_index/id/'.$pIndex->getId())?>"><?=$pIndex->getSubject()?></a></li>
                <li class="breadcrumb-item"><a href="<?=site_url('evaluate/ruleitem/child_index/id/'.$ruleitem->getId())?>"><?=$ruleitem->getSubject()?></a></li>
                <?php endif;?>
            </ol>
        </nav>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        考核指标管理
                    </h3>
                    <div class="block-options">
                        <?=Button::back('返回','javascript:history.go(-1);')?>
                        <?=Button::setUrl(site_url("evaluate/ruleitem/edit/pid/".$ruleitem->getId()))->setIcon('add')->link('新增考核指标')?>
                        <!--
                        <?=Button::setUrl(site_url("evaluate/ruleitem/updateJson"))->setIcon('reload')->link('更新JSON')?>
                        -->
                        <?=Button::setUrl(site_url("evaluate/ruleitem/updateWeight"))->setIcon('reload')->link('更新分值')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <table class="table table-hover table-vcenter">
                        <thead>
                        <tr>
                            <th class="text-center" style="width: 100px;">
                                序号
                            </th>
                            <th>
                                <?=getColumnStr('考核标准','subject')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标类型','type')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标标记','code')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('考核环节','step')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                <?=getColumnStr('指标级别','level')?>
                            </th>
                            <th class="d-none d-sm-table-cell">
                                备注
                            </th>
                            <th class="d-none d-sm-table-cell">
                                分值
                            </th>
                            <th class="text-center" style="width: 200px;">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($ruleitem = $pagers->getObject()):?>
                            <tr>
                                <td class="text-center">
                                    <?=$ruleitem->getSort()?>
                                </td>
                                <td class="font-w600">
                                    <?=$ruleitem->getSubject()?>
                                </td>
                                <td>
                                    <?=$ruleitem->getTypes()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$ruleitem->getCode()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$ruleitem->getStep()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$ruleitem->getLevel()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$ruleitem->getNote()?>
                                </td>
                                <td class="d-none d-sm-table-cell">
                                    <?=$ruleitem->getWeight()?>
                                </td>
                                <td class="text-center">
                                    <?=Button::setName('编辑')->setUrl(site_url("evaluate/ruleitem/edit/id/".$ruleitem->getId()))->link()?>
                                    <?php
                                        if($ruleitem->getLevel()<3):
                                    ?>
                                    <?=Button::setName('下级指标('.$ruleitem->getChildCount().')')->setUrl(site_url("evaluate/ruleitem/child_index/id/".$ruleitem->getId()))->link()?>
                                    <?php endif;?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<?=$pagers->fromto().$pagers->navbar(10)?></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
