<?php
namespace App\Controller\Stage;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class Apply extends BaseController
{

    private $tabs = array();
    private $stage;
    private $project;
    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/view/');
        $this->stage = sf::getModel('ProjectStages')->selectByStageId(input::getMix('id'));
        if($this->stage->isNew()){
            $this->project = sf::getModel('Projects')->selectByProjectId(input::getMix('project_id'));
            $this->stage->setProjectId($this->project->getProjectId());
            $this->stage->setSubject($this->project->getSubject());
            $this->stage->setCorporationId($this->project->getCorporationId());
            $this->stage->setCorporationName($this->project->getCorporationName());
            $this->stage->setUserId($this->project->getUserId());
            $this->stage->setUserName($this->project->getUserName());
            $this->stage->setAssistantId($this->project->getAssistantIds());
            $this->stage->setAssistantName($this->project->getAssistantName());
        }else{
            $this->project = $this->stage->getProject();
        }
        if($this->project->isNew()){
            $this->error('没有找到该项目',getFromUrl());
        }
        $this->tabs = [
            [
                'method' => 'edit',
                'text' => '基本信息',
                'url' => site_url('stage/apply/edit/id/' . $this->stage->getStageId())
            ],
            [
                'method' => 'attachment',
                'text' => '相关附件',
                'url' => site_url('stage/apply/attachment/id/' . $this->stage->getStageId())
            ]
        ];
        $this->view->set('method', input::getInput("mix.method"));
        $this->view->set('tabs', $this->tabs);
    }

    function edit()
    {
        if (!$this->stage->hasEditAuth()) {
            $this->page_debug("你没有权限执行该操作！", getFromUrl());
        }
        if(input::post()){
            if(!input::post('subject')) $this->error('请填写项目名称');
            $this->stage->setStageYear(input::post('stage_year'));
            if($this->stage->isRepeat()) $this->error('已存在'.$this->stage->getStageYear().'年的年度报告，不能重复填写');
            $this->stage->setCompanyLevel($this->project->getCompanyLevel());
            $this->stage->setStartAt(input::post('start_at'));
            $this->stage->setNote(input::post('note'));
            $this->stage->setConclusion(input::post('conclusion'));
            $this->stage->setUpdatedAt(date('Y-m-d H:i:s'));
            $this->stage->save();
            $this->success('保存成功！',site_url('stage/apply/edit/id/'.$this->stage->getStageId()));
        }
        $this->view->set("stage",$this->stage);
        $this->view->apply('inc_body','apply/edit');
        $this->view->display('page');
    }

    function show()
    {
        if($this->stage->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if (!$this->stage->hasShowAuth()) {
            $this->page_debug("你没有权限执行该操作！", getFromUrl());
        }
        $this->view->set("stage",$this->stage);
        $this->view->set("itemType",'stage');
        $this->view->apply('inc_body','apply/show');
        $this->view->display('page');
    }

    /**
     * 上报
     */
    public function submit()
    {
        if($this->stage->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if(input::getInput('post')){
            //判断验证是否通过
            if($message = $this->stage->validate())
                $this->error(sprintf(lang::get("Your content can not be verified, because:s%!"),implode(';',$message)),getFromUrl());
            $this->stage->setSubmitAt(date("Y-m-d H:i:s"));
            if($this->stage->isAssistant()){
                //待项目负责人审核
                $this->stage->setStatement(2);
            }else{
                //待单位/部门助理审核
                $this->stage->setStatement(4);
            }
            $this->stage->save();
            $this->stage->sendMessage();
//            $this->project->setStateForStage(6);       //待单位/部门管理员审核
//            $this->project->save();
            sf::getModel('Historys')->addHistory($this->stage->getStageId(),'项目年度报告上报','stage');
            $this->success(lang::get("Has been submit!"),site_url("stage/user/submit_list"));
        }
        $this->view->set('stage',$this->stage);
        $this->view->set('msg',$this->stage->validate());
        $this->view->apply("inc_body","apply/submit");
        $this->view->display("page");
    }

    function dodelete()
    {
        if($this->stage->isNew()) $this->error('没有找到该项内容',getFromUrl());
        if (!$this->stage->hasEditAuth()) {
            $this->page_debug("你没有权限执行该操作！", getFromUrl());
        }
        $this->stage->delete();
        $this->success(lang::get('Has been deleted!'),getFromUrl());
    }

    public function attachment()
    {
        if($this->stage->isNew()) $this->error('请先填写基本信息',getFromUrl());
        if(input::getInput('post')){
            $attachmentIds = input::getInput('post.attachment_id');
            $filenote = input::getInput('post.filenote');
            $no = input::getInput('post.no');
            foreach ($attachmentIds as $fileId){
                $filemanager = sf::getModel('Filemanager',$fileId);
                if($filemanager->isNew()) continue;
                $filemanager->setNo($no[$fileId]);
                $filemanager->setFileNote($filenote[$fileId]);
                $filemanager->save();
            }
            $this->success("保存成功！", getFromUrl());
        }
        $this->view->set('stage',$this->stage);
        $this->view->set('itemType','stage');
        $this->view->apply('inc_body', 'apply/attachment');
        $this->view->display('page');
    }

    /**
     * 导出
     */
    public function export()
    {
        if ($this->stage->isNew()) $this->error('没有找到该项内容');
        $pdf = $this->stage->getConfigs('file.plan');
        if($pdf && !in_array($this->stage->getStatement(),[1,3,6,12]) && file_exists(WEBROOT.'/up_files/'.$pdf)){
            $this->jump(site_path('up_files/'.$pdf));
        }else{
            $this->makepdf();
        }
    }

    public function makepdf()
    {
        if ($this->stage->isNew()) $this->error('没有找到该项内容');
        $this->view->set("print",'yes');
        $this->view->set("stage",$this->stage);
        $htmlStr = $this->view->getContent("apply/show");
        //替换字体
        $htmlStr = str_replace('font-family','font-myname',$htmlStr);
        //提取打印内容
        //提取打印内容
        preg_match_all('/<printer>(.*?)<\/printer>/isx',$htmlStr,$matches);
        $htmlStr = $matches[1][0];
        $htmlStr = '<link rel="stylesheet" href="'.site_path("css/template.css").'">'.$htmlStr;
        $pdf =  PDF::setHeader('<table width="100%" style="border:0px;border-bottom:1px solid #000; vertical-align: bottom; font-size: 9pt; color: #666666;"><tr><td width="20%" style="border: 0px solid #fff;font-size: 12px">项目年度报告</td><td width="80%" style="text-align: right;border: 0px solid #fff;font-size: 12px">'.$this->stage->getSubject().'</td></tr></table>')
            ->setFooter('<table width="100%" style="font-size: 9pt; color: #000;border: 0px solid #fff;"><tr><td width="33%" style="border: 0px solid #fff;"></td><td width="33%" align="center" style="border: 0px solid #fff;"></td><td width="33%" style="text-align: right;border: 0px solid #fff;"></td></tr></table>')
            ->setTitle($this->stage->getSubject())
            ->setSubject($this->stage->getSubject())
            ->setCreator(input::session("nickname"))
            ->setAuthor($this->stage->getUserName())
            ->setContent($htmlStr)
            ->hasCover(false)
            ->setWaterMark('项目年度报告',0.1)
            ->show();
    }


    public function gird($tpl = 'apply/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        $addWhere .= " AND `user_id` = '".input::getInput('session.roleuserid')."'";
        input::getInput("mix.field") && $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".input::getInput("mix.search")."%' ";

        $form_vars = array('field','search','corporation_id','subject','statement','user_name','corporation_name','department_name');
        $this->view->set("pagers",sf::getModel('ProjectStages')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }
}