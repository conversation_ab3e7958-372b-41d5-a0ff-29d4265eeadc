<style>
    .table td{
        word-break: break-all;
    }
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<script type="text/javascript">
    function todoSuite(json)
    {
        $.post('<?=site_url("relevance/product/addSuite")?>',{id:"<?=$product->getProductId()?>",suite_id:json.suite_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
    function todoPatent(json)
    {
        $.post('<?=site_url("relevance/product/addPatent")?>',{id:"<?=$product->getProductId()?>",patent_id:json.patent_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
    function todoStandard(json)
    {
        $.post('<?=site_url("relevance/product/addStandard")?>',{id:"<?=$product->getProductId()?>",standard_id:json.standard_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
    function todoTransform(json)
    {
        $.post('<?=site_url("relevance/product/addTransform")?>',{id:"<?=$product->getProductId()?>",transform_id:json.transform_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }

    function todoCertificate(json)
    {
        $.post('<?=site_url("relevance/product/addCertificate")?>',{id:"<?=$product->getProductId()?>",cert_id:json.cert_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }

    function todoLicense(json)
    {
        $.post('<?=site_url("relevance/product/addLicense")?>',{id:"<?=$product->getProductId()?>",license_id:json.license_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }

    function todoQualificate(json)
    {
        $.post('<?=site_url("relevance/product/addQualificate")?>',{id:"<?=$product->getProductId()?>",qual_id:json.qual_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            产品关联成果
        </h2>
        <div class="content-options">
            <?=Button::setName('返回')->back()?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <!-- Block Tabs Default Style -->
            <div class="block block-rounded">
                <div class="block-content tab-content block-content-full">
                    <div class="form-group row">
                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">产品名称</label>
                        <div class="col-xs-12 col-sm-5 col-md-8">
                            <p class="help-block"><?=$product->getSubject()?></p>
                        </div>
                    </div>
                    <form name="form_patent" class="form-horizontal" id="form_patent" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="patent">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="7">专利</td>
                        </tr>
                        <tr>
                            <td class="text-center" style="width: 80px">排序</td>
                            <td class="text-center" style="width: 80px">序号</td>
                            <td style="width: 30%">专利名称</td>
                            <td style="width: 10%">专利类型</td>
                            <td>专利号</td>
                            <td style="width: 10%">状态</td>
                            <td style="width:100px;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="patent-list">
                        <?php
                        $patents = $product->selectPatents();
                        while($patent = $patents->getObject()):
                            $_patent = $patent->getPatent(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$patent->getId()?>]" class="sort" value="<?=$patent->getSort()?>">
                                    <span class="sort_text"><?=$patent->getSort()?></span>
                                </td>
                                <td><a href="<?=site_url('user/patent/show/id/'.$_patent->getPatentId())?>" target="_blank"><?=$_patent->getSubject()?></a></td>
                                <td><?=$_patent->getType()?></td>
                                <td><?=$_patent->getSn()?></td>
                                <td><?=$_patent->getState()?></td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delPatent/id/'.$product->getProductId().'/pid/'.$patent->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/patent_search/callback/todoPatent'))->setIcon('add')->window('关联专利')?>
                            </div>
                        </div>
                    </div>
                    </form>
                    <form name="form_standard" class="form-horizontal" id="form_standard" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="standard">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="7">标准</td>
                        </tr>
                        <tr>
                            <td class="text-center" style="width: 80px">排序</td>
                            <td class="text-center" style="width: 80px">序号</td>
                            <td style="width: 30%">标准名称</td>
                            <td style="width: 10%">标准类别</td>
                            <td>发布日期</td>
                            <td style="width: 10%">状态</td>
                            <td style="width:100px;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="standard-list">
                        <?php
                        $standards = $product->selectStandards();
                        while($standard = $standards->getObject()):
                            $_standard = $standard->getStandard(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$standard->getId()?>]" class="sort" value="<?=$standard->getSort()?>">
                                    <span class="sort_text"><?=$standard->getSort()?></span>
                                </td>
                                <td><a href="<?=site_url('user/standard/show/id/'.$_standard->getStandardId())?>" target="_blank"><?=$_standard->getSubject()?></a></td>
                                <td><?=$_standard->getType()?></td>
                                <td><?=$_standard->getDate()?></td>
                                <td><?=$_standard->getState()?></td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delStandard/id/'.$product->getProductId().'/pid/'.$standard->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/standard_search/callback/todoStandard'))->setIcon('add')->window('关联标准')?>
                            </div>
                        </div>
                    </div>
                    </form>
                    <form name="form_standard" class="form-horizontal" id="form_transform" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="transform">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="7">成果转化</td>
                            </tr>
                            <tr>
                                <td class="text-center" style="width: 80px">排序</td>
                                <td class="text-center" style="width: 80px">序号</td>
                                <td style="width: 30%">成果名称</td>
                                <td style="width: 10%">首单转化日期</td>
                                <td>转化方式</td>
                                <td style="width: 10%">状态</td>
                                <td style="width:100px;">操作</td>
                            </tr>
                            </thead>
                            <tbody id="transform-list">
                            <?php
                            $transforms = $product->selectTransforms();
                            while($transform = $transforms->getObject()):
                                $_transform = $transform->getTransform(true);
                                ?>
                                <tr>
                                    <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                    <td class="text-center">
                                        <input type="hidden" name="sort[<?=$transform->getId()?>]" class="sort" value="<?=$transform->getSort()?>">
                                        <span class="sort_text"><?=$transform->getSort()?></span>
                                    </td>
                                    <td><a href="<?=site_url('user/transform/show/id/'.$_transform->getTransformId())?>" target="_blank"><?=$_transform->getSubject()?></a></td>
                                    <td><?=$_transform->getFirstOrderAt()?></td>
                                    <td><?=$_transform->getType()?></td>
                                    <td><?=$_transform->getState()?></td>
                                    <td>
                                        <?=Button::setUrl(site_url('relevance/product/delTransform/id/'.$product->getProductId().'/pid/'.$transform->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <div class="form-group row">
                            <div class="col-lg-12">
                                <div style="width: 120px;margin: 0 auto;">
                                    <?=Button::setUrl(site_url('common/transform_search/callback/todoTransform'))->setIcon('add')->window('关联成果转化')?>
                                </div>
                            </div>
                        </div>
                    </form>
                    <form name="form_suite" class="form-horizontal" id="form_suite" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="suite">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="11">首台套</td>
                        </tr>
                        <tr>
                            <th class="text-center" style="width: 80px">排序</th>
                            <th class="text-center" style="width: 80px">序号</th>
                            <th width="15%">产品名称</th>
                            <th>类型</th>
                            <th>级别</th>
                            <th>产品型号</th>
                            <th>产品规格</th>
                            <th>发证部门</th>
                            <th>发证日期</th>
                            <th>有效期</th>
                            <th style="width:100px;">操作</th>
                        </tr>
                        </thead>
                        <tbody id="suite-list">
                        <?php $suites = $product->selectSuites(); ?>
                        <?php while ($suite = $suites->getObject()) :
                            $_suite = $suite->getSuite(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$suite->getId()?>]" class="sort" value="<?=$suite->getSort()?>">
                                    <span class="sort_text"><?=$suite->getSort()?></span>
                                </td>
                                <td>
                                    <a href="<?=site_url('scientific/suite/user/show/id/'.$_suite->getSuiteId())?>" target="_blank"><?=$_suite->getSubject()?></a>
                                </td>
                                <td>
                                    <?=$_suite->getType()?>
                                </td>
                                <td>
                                    <?=$_suite->getLevel()?>
                                </td>
                                <td>
                                    <?=$_suite->getModelBumber()?>
                                </td>
                                <td>
                                    <?=$_suite->getSpecification()?>
                                </td>
                                <td>
                                    <?=$_suite->getCerDepartment()?>
                                </td>
                                <td>
                                    <?=$_suite->getCerDate()?>
                                </td>
                                <td>
                                    <?=$_suite->getExpireAt()?>
                                </td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delSuite/id/'.$product->getProductId().'/pid/'.$suite->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/suite_search/callback/todoSuite'))->setIcon('add')->window('关联首台套')?>
                            </div>
                        </div>
                    </div>
                    </form>
                    <form name="form_certificate" class="form-horizontal" id="form_certificate" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="certificate">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="11">产品认证</td>
                        </tr>
                        <tr>
                            <th class="text-center" style="width: 80px">排序</th>
                            <th class="text-center" style="width: 80px">序号</th>
                            <th width="15%">证书名称</th>
                            <th>证书号</th>
                            <th>批准机关</th>
                            <th>发证日期</th>
                            <th>有效期</th>
                            <th style="width:100px;">操作</th>
                        </tr>
                        </thead>
                        <tbody id="certificate-list">
                        <?php $certificates = $product->selectCertificates(); ?>
                        <?php while ($certificate = $certificates->getObject()) :
                            $_certificate = $certificate->getCertificate(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$certificate->getId()?>]" class="sort" value="<?=$certificate->getSort()?>">
                                    <span class="sort_text"><?=$certificate->getSort()?></span>
                                </td>
                                <td>
                                    <?=$_certificate->getSubject()?>
                                </td>
                                <td>
                                    <?=$_certificate->getSn()?>
                                </td>
                                <td>
                                    <?=$_certificate->getDepartment()?>
                                </td>
                                <td>
                                    <?=$_certificate->getDate()?>
                                </td>
                                <td>
                                    <?=$_certificate->getEndAt()?>
                                </td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delCertificate/id/'.$product->getProductId().'/pid/'.$certificate->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/certificate_search/callback/todoCertificate'))->setIcon('add')->window('关联产品认证')?>
                            </div>
                        </div>
                    </div>
                    </form>
                    <form name="form_license" class="form-horizontal" id="form_license" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="license">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="11">行业许可</td>
                        </tr>
                        <tr>
                            <th class="text-center" style="width: 80px">排序</th>
                            <th class="text-center" style="width: 80px">序号</th>
                            <th width="15%">产品名称</th>
                            <th>设备型号</th>
                            <th>制造商</th>
                            <th>质量一致性审核有效期</th>
                            <th>通告编号</th>
                            <th>初始通告日期</th>
                            <th>公示网址</th>
                            <th style="width:100px;">操作</th>
                        </tr>
                        </thead>
                        <tbody id="license-list">
                        <?php $licenses = $product->selectLicenses(); ?>
                        <?php while ($license = $licenses->getObject()) :
                            $_license = $license->getLicense(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$license->getId()?>]" class="sort" value="<?=$license->getSort()?>">
                                    <span class="sort_text"><?=$license->getSort()?></span>
                                </td>
                                <td>
                                    <?=$_license->getSubject()?>
                                </td>
                                <td>
                                    <?=$_license->getVersion()?>
                                </td>
                                <td>
                                    <?=$_license->getDepartment()?>
                                </td>
                                <td>
                                    <?=$_license->getEndAt()?>
                                </td>
                                <td>
                                    <?=$_license->getSn()?>
                                </td>
                                <td>
                                    <?=$_license->getNoticeAt()?>
                                </td>
                                <td>
                                    <?=$_license->getWebsite()?>
                                </td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delLicense/id/'.$product->getProductId().'/pid/'.$license->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/license_search/callback/todoLicense'))->setIcon('add')->window('关联行业许可')?>
                            </div>
                        </div>
                    </div>
                    </form>
                    <form name="form_qualificate" class="form-horizontal" id="form_qualificate" action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="type" value="qualificate">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <td class="text-center" colspan="11">资质</td>
                        </tr>
                        <tr>
                            <th class="text-center" style="width: 80px">排序</th>
                            <th class="text-center" style="width: 80px">序号</th>
                            <th width="15%">资质名称</th>
                            <th>有无证书</th>
                            <th>技术领域</th>
                            <th>批准机关</th>
                            <th>批准日期</th>
                            <th>有效期</th>
                            <th style="width:100px;">操作</th>
                        </tr>
                        </thead>
                        <tbody id="qualificate-list">
                        <?php $qualificates = $product->selectQualificates(); ?>
                        <?php while ($qualificate = $qualificates->getObject()) :
                            $_qualificate = $qualificate->getQualificate(true);
                            ?>
                            <tr>
                                <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                <td class="text-center">
                                    <input type="hidden" name="sort[<?=$qualificate->getId()?>]" class="sort" value="<?=$qualificate->getSort()?>">
                                    <span class="sort_text"><?=$qualificate->getSort()?></span>
                                </td>
                                <td>
                                    <?=$_qualificate->getSubject()?>
                                </td>
                                <td>
                                    <?=$_qualificate->getCertificate()?>
                                </td>
                                <td>
                                    <?=$_qualificate->getIndustry()?>
                                </td>
                                <td><?=$_qualificate->getDepartment()?></td>
                                <td><?=$_qualificate->getDate()?></td>
                                <td><?=$_qualificate->getEndAt()?></td>
                                <td>
                                    <?=Button::setUrl(site_url('relevance/product/delQualificate/id/'.$product->getProductId().'/pid/'.$qualificate->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                        </tbody>
                    </table>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/qualificate_search/callback/todoQualificate'))->setIcon('add')->window('关联资质')?>
                            </div>
                        </div>
                    </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var patenttable = document.getElementById('patent-list');
    var sortable = new Sortable(patenttable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#patent-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_patent');
        },
    });
    var suitetable = document.getElementById('suite-list');
    var sortable = new Sortable(suitetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#suite-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_suite');
        },
    });
    var standardtable = document.getElementById('standard-list');
    var sortable = new Sortable(standardtable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#standard-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_standard');
        },
    });
    var standardtable = document.getElementById('transform-list');
    var sortable = new Sortable(standardtable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#transform-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_transform');
        },
    });
    var certificatetable = document.getElementById('certificate-list');
    var sortable = new Sortable(certificatetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#certificate-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_certificate');
        },
    });
    var licensetable = document.getElementById('license-list');
    var sortable = new Sortable(licensetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#license-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_license');
        },
    });
    var qualificatetable = document.getElementById('qualificate-list');
    var sortable = new Sortable(qualificatetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#qualificate-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
                var fid = $(this).closest('form').attr('id');
            });
            updateSort('form_qualificate');
        },
    });

    function updateSort(formid) {
        var url = $("#"+formid).attr('action');
        $.post(url,$("#"+formid).serialize(),function(data){
            if (data.code==0) {
                // showSuccess('排序更新成功');
            } else {
                // showError('排序更新失败');
            }
        },'json');
    }
</script>