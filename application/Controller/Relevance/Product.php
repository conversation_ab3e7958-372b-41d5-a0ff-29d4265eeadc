<?php
namespace App\Controller\Relevance;
use App\Controller\BaseController;
use App\Facades\PDF;
use Sofast\Support\Template;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class Product extends BaseController
{

    private $view = NULL;

    public function load()
    {
        $this->view = new Template(realpath(dirname(__FILE__)).'/view/');
    }

    public function index()
    {
        $addWhere = " statement >=1";
        switch(input::session('userlevel')){
            case 2:
                $addWhere.=" AND (user_id = '".input::getInput('session.roleuserid')."' or product_id in (SELECT item_id FROM `fruit_members` where item_type = 'product' and user_type = 'inner' and user_id = '".input::getInput('session.roleuserid')."'))";
                break;
            case 3:
            case 4:
                $addWhere.=" AND (`company_id` = '".input::session('roleuserid')."' or product_id in (SELECT item_id FROM `fruit_members` where item_type = 'product' and user_type = 'inner' and company_id = '".input::getInput('session.roleuserid')."'))";
                break;
        }
        $this->gird('product/index',$addWhere);
    }
    public function product_list()
    {
        $addWhere = " statement >=1";
        switch(input::session('userlevel')){
            case 2:
                $addWhere.=" AND (user_id = '".input::getInput('session.roleuserid')."' or product_id in (SELECT item_id FROM `fruit_members` where item_type = 'product' and user_type = 'inner' and user_id = '".input::getInput('session.roleuserid')."'))";
                break;
            case 3:
            case 4:
                $addWhere.=" AND (`company_id` = '".input::session('roleuserid')."' or product_id in (SELECT item_id FROM `fruit_members` where item_type = 'product' and user_type = 'inner' and company_id = '".input::getInput('session.roleuserid')."'))";
                break;
        }
        $this->gird('product/product_list',$addWhere);
    }

    public function project()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()) $this->error('找不到该产品',site_url('relevance/product/index'));
        if(isAjax()){
            $sorts = input::post('sort');
            foreach ($sorts as $pid=>$sort){
                $project = $product->getProjectById($pid);
                if($project->isNew()) continue;
                $project->setSort($sort);
                $project->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
        $this->view->set("product",$product);
        $this->view->apply('inc_body','product/project');
        $this->view->display('page_main');
    }

    public function fruit()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()) $this->error('找不到该产品',site_url('relevance/product/product_list'));
        if(isAjax()){
            $sorts = input::post('sort');
            $type = input::post('type');
            foreach ($sorts as $fid=>$sort){
                switch ($type){
                    case 'suite':
                        $project = $product->getFruitSuite($fid);
                        break;
                    case 'certificate':
                        $project = $product->getFruitCertificate($fid);
                        break;
                    case 'license':
                        $project = $product->getFruitLicense($fid);
                        break;
                    case 'qualificate':
                        $project = $product->getFruitQualificate($fid);
                        break;
                    default:
                        $project = $product->getFruitProduct($fid);
                        break;
                }

                if($project->isNew()) continue;
                $project->setSort($sort);
                $project->save();
            }
            exit(json_encode(['code'=>0],JSON_UNESCAPED_UNICODE));
        }
        $this->view->set("product",$product);
        $this->view->apply('inc_body','product/fruit');
        $this->view->display('page_main');
    }

    /**
     * 添加项目
     */
    function addProject()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $project = sf::getModel('Projects')->selectByProjectId(input::getMix('project_id'));
        if($project->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该项目']);exit;
        }
        $fruitProject = sf::getModel("FruitProjects")->selectByProjectId($project->getProjectId(),$product->getProductId(),'product');
        if($fruitProject->isNew()){
            $sort = $fruitProject->getNewSort();
            $fruitProject->setSort($sort);
        }
        if(!$fruitProject->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该项目已添加，不能重复添加']);exit;
        }
        $fruitProject->save();
        addHistory($project->getProjectId(),'增加关联产品《'.$product->getSubject().'》','relevance');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除项目
     */
    function delProject()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitProject = sf::getModel('FruitProjects',input::get('pid'));
        if($fruitProject->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitProject->getItemId()!=$product->getProductId() && $fruitProject->getItemType()!='product'){
            $this->error('没有权限删除');
        }
        $fruitProject->delete();
        addHistory($fruitProject->getItemId(),'删除关联产品《'.$fruitProject->getProduct()->getSubject().'》','relevance');
        $this->success('删除成功',getFromUrl());
    }


    /**
     * 添加首台套
     */
    function addSuite()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $suite = sf::getModel('ScientificSuites')->selectBySuiteId(input::getMix('suite_id'));
        if($suite->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该首台套']);exit;
        }
        $fruitSuite = sf::getModel("FruitSuites")->selectBySuiteId($suite->getSuiteId(),$product->getProductId(),'product');
        if($fruitSuite->isNew()){
            $sort = $fruitSuite->getNewSort();
            $fruitSuite->setSort($sort);
        }
        if(!$fruitSuite->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该首台套已添加，不能重复添加']);exit;
        }
        $fruitSuite->save();
        addHistory($product->getProductId(),'增加关联首台套《'.$suite->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除首台套
     */
    function delSuite()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitSuite = sf::getModel('FruitSuites',input::get('pid'));
        if($fruitSuite->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitSuite->getItemId()!=$product->getProductId() && $fruitSuite->getItemType()!='product'){
            $this->error('没有权限删除');
        }
        $fruitSuite->delete();
        addHistory($product->getProductId(),'删除关联首台套《'.$fruitSuite->getSuite()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }


    /**
     * 添加专利
     */
    function addPatent()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $patent = sf::getModel('Patents')->selectByPatentId(input::getMix('patent_id'));
        if($patent->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该专利']);exit;
        }
        $fruitProduct = sf::getModel("FruitProducts")->selectByProductId($product->getProductId(),$patent->getPatentId(),'patent');
        if($fruitProduct->isNew()){
            $sort = $fruitProduct->getNewSort();
            $fruitProduct->setSort($sort);
        }
        if(!$fruitProduct->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该专利已添加，不能重复添加']);exit;
        }
        $fruitProduct->save();
        addHistory($product->getProductId(),'增加关联专利《'.$patent->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除专利
     */
    function delPatent()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitProduct = sf::getModel('FruitProducts',input::get('pid'));
        if($fruitProduct->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitProduct->getItemId()!=$product->getProductId() && $fruitProduct->getItemType()!='patent'){
            $this->error('没有权限删除');
        }
        $fruitProduct->delete();
        addHistory($product->getProductId(),'删除关联专利《'.$fruitProduct->getPatent()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }


    /**
     * 添加标准
     */
    function addStandard()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $standard = sf::getModel('Standards')->selectByStandardId(input::getMix('standard_id'));
        if($standard->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该标准']);exit;
        }
        $fruitProduct = sf::getModel("FruitProducts")->selectByProductId($product->getProductId(),$standard->getStandardId(),'standard');
        if($fruitProduct->isNew()){
            $sort = $fruitProduct->getNewSort();
            $fruitProduct->setSort($sort);
        }
        if(!$fruitProduct->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该标准已添加，不能重复添加']);exit;
        }
        $fruitProduct->save();
        addHistory($product->getProductId(),'增加关联标准《'.$standard->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除标准
     */
    function delStandard()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitProduct = sf::getModel('FruitProducts',input::get('pid'));
        if($fruitProduct->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitProduct->getItemId()!=$product->getProductId() && $fruitProduct->getItemType()!='standard'){
            $this->error('没有权限删除');
        }
        $fruitProduct->delete();
        addHistory($product->getProductId(),'删除关联标准《'.$fruitProduct->getPatent()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }


    /**
     * 添加成果转化
     */
    function addTransform()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $transform = sf::getModel('Transforms')->selectByTransformId(input::getMix('transform_id'));
        if($transform->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该成果转化']);exit;
        }
        $fruitProduct = sf::getModel("FruitProducts")->selectByProductId($product->getProductId(),$transform->getTransformId(),'transform');
        if($fruitProduct->isNew()){
            $sort = $fruitProduct->getNewSort();
            $fruitProduct->setSort($sort);
        }
        if(!$fruitProduct->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该成果转化已添加，不能重复添加']);exit;
        }
        $fruitProduct->save();
        addHistory($product->getProductId(),'增加关联成果转化《'.$transform->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除成果转化
     */
    function delTransform()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitProduct = sf::getModel('FruitProducts',input::get('pid'));
        if($fruitProduct->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitProduct->getItemId()!=$product->getProductId() && $fruitProduct->getItemType()!='transform'){
            $this->error('没有权限删除');
        }
        $fruitProduct->delete();
        addHistory($product->getProductId(),'删除关联成果转化《'.$fruitProduct->getPatent()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }

    /**
     * 添加产品认证
     */
    function addCertificate()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $certificate = sf::getModel('Certificates')->selectByCertId(input::getMix('cert_id'));
        if($certificate->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品认证']);exit;
        }
        $fruitCertificate = sf::getModel("FruitCertificates")->selectByCertId($certificate->getCertId(),$product->getProductId(),'product');
        if($fruitCertificate->isNew()){
            $sort = $fruitCertificate->getNewSort();
            $fruitCertificate->setSort($sort);
        }
        if(!$fruitCertificate->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该产品认证已添加，不能重复添加']);exit;
        }
        $fruitCertificate->save();
        addHistory($product->getProductId(),'增加关联产品认证《'.$certificate->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除产品认证
     */
    function delCertificate()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitCertificate = sf::getModel('FruitCertificates',input::get('pid'));
        if($fruitCertificate->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitCertificate->getItemId()!=$product->getProductId() && $fruitCertificate->getItemType()!='product'){
            $this->error('没有权限删除');
        }
        $fruitCertificate->delete();
        addHistory($product->getProductId(),'删除关联产品认证《'.$fruitCertificate->getCertificate()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }

    /**
     * 添加行业许可
     */
    function addLicense()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $license = sf::getModel('Licenses')->selectByLicenseId(input::getMix('license_id'));
        if($license->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该行业许可']);exit;
        }
        $fruitLicense = sf::getModel("FruitLicenses")->selectByLicenseId($license->getLicenseId(),$product->getProductId(),'product');
        if($fruitLicense->isNew()){
            $sort = $fruitLicense->getNewSort();
            $fruitLicense->setSort($sort);
        }
        if(!$fruitLicense->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该行业许可已添加，不能重复添加']);exit;
        }
        $fruitLicense->save();
        addHistory($product->getProductId(),'增加关联行业许可《'.$license->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除行业许可
     */
    function delLicense()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitLicense = sf::getModel('FruitLicenses',input::get('pid'));
        if($fruitLicense->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitLicense->getItemId()!=$product->getProductId() && $fruitLicense->getItemType()!='product'){
            $this->error('没有权限删除');
        }
        $fruitLicense->delete();
        addHistory($product->getProductId(),'删除关联行业许可《'.$fruitLicense->getLicense()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }


    /**
     * 添加资质
     */
    function addQualificate()
    {
        $product = sf::getModel('Products')->selectByProductId(input::post('id'));
        if($product->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该产品']);exit;
        }
        $qualificate = sf::getModel('Qualificates')->selectByQualId(input::getMix('qual_id'));
        if($qualificate->isNew()){
            echo json_encode(['code'=>1,'msg'=>'找不到该资质']);exit;
        }
        $fruitQualificate = sf::getModel("FruitQualificates")->selectByQualId($qualificate->getQualId(),$product->getProductId(),'product');
        if($fruitQualificate->isNew()){
            $sort = $fruitQualificate->getNewSort();
            $fruitQualificate->setSort($sort);
        }
        if(!$fruitQualificate->isNew()){
            echo json_encode(['code'=>1,'msg'=>'该资质已添加，不能重复添加']);exit;
        }
        $fruitQualificate->save();
        addHistory($product->getProductId(),'增加关联资质《'.$qualificate->getSubject().'》','product');
        echo json_encode(['code'=>0,'msg'=>'添加成功！']);exit;
    }

    /**
     * 删除资质
     */
    function delQualificate()
    {
        $product = sf::getModel('Products')->selectByProductId(input::getMix('id'));
        if($product->isNew()){
            $this->error('找不到该产品');
        }
        $fruitQualificate = sf::getModel('FruitQualificates',input::get('pid'));
        if($fruitQualificate->isNew()){
            $this->error('找不到该项目');
        }
        if($fruitQualificate->getItemId()!=$product->getProductId() && $fruitQualificate->getItemType()!='product'){
            $this->error('没有权限删除');
        }
        $fruitQualificate->delete();
        addHistory($product->getProductId(),'删除关联资质《'.$fruitQualificate->getQualificate()->getSubject().'》','product');
        $this->success('删除成功',getFromUrl());
    }


    public function gird($tpl = 'product/index',$addWhere = '1',$showMax=16,$page='page')
    {
        //处理排序
        $orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
        $ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
        $addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
        if(input::getInput("mix.search") && input::getInput("mix.field")==="tag"){
            $itemIds=sf::getModel("Tags")->getItemIdsByTypeAndTag("product",input::getInput("mix.search"));
            foreach($itemIds as $key=>$itemId){
                $itemIds[$key]="'$itemId'";
            }
            $inSql=count($itemIds)?implode(",",$itemIds):"''";
            $addWhere.=" AND `product_id` IN ($inSql)";
        }elseif(input::getInput("mix.search") && input::getInput("mix.field")==="user_name"){
            $addWhere .= " AND `product_id` IN (SELECT item_id FROM `fruit_members` where user_name like '%".input::getInput("mix.search")."%' and item_type = 'product')";
        }elseif(input::getInput("mix.search") && input::getInput("mix.field")){
            $addWhere .= " AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
        }
        input::getInput("mix.type") && $addWhere .= " AND type = ".input::getInput("mix.type");
        input::getInput("mix.transform_at") && $addWhere .= " AND transform_at = '".input::getInput("mix.transform_at")."'";
        input::getInput("mix.fruit_domain") && $addWhere .= " AND `fruit_domain` like '%".input::getInput("mix.fruit_domain")."%'";
        input::getInput("mix.statement") && $addWhere .= " AND statement = ".input::getInput("mix.statement");
        if(input::getMix('company_id')) $addWhere .= " AND company_id = '".input::getMix('company_id')."' ";
        $form_vars = array('field','search','subject','user_id','product_id','transform_at','fruit_domain','company_id');
        $this->view->set("pagers",sf::getModel('Products')->getPager($addWhere,$addSql,$showMax,'','',$form_vars));
        $this->view->apply("inc_body",$tpl);
        $this->view->display($page);
    }

}