<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Core\Log;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Lang;
use Sofast\Core\Sf;

class account extends BaseController
{	
	/**
	 * 数据列表
	 */
	function index()
	{
		$orderfield = input::getInput("get.orderfield") ? input::getInput("get.orderfield") : 'updated_at';
		$ordermode = input::getInput("get.ordermode") ? input::getInput("get.ordermode") : 'DESC';
		$addSql = 'ORDER BY '.$orderfield.' '.$ordermode.' ';
		
		input::getInput("mix.search") && $addWhere .= "`".input::getInput("mix.field")."` like '%".input::getInput("mix.search")."%' ";	
		
		$from_vars = array('search','field');
		view::set("pager",sf::getModel("Users")->getPager($addWhere ,$addSql ,20,'','',$from_vars));
		view::apply("inc_body","admin/account/index");
		view::display("page_main");
	}

	/**
	 * 删除数据
	 */
	function delete()
	{
		if(input::getInput("post.select_id")){
			$ids = implode("','",input::getInput("post.select_id"));
		}else $ids[] = input::getInput("get.userid");
		sf::getModel("historys")->addHistory('system',lang::get("Delete expert for the system!"));
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 通过
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("mix.userid"); 
		
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("Users")->selectByUserId($ids[$i]);
			if($user->isNew()) continue;
			$user->setIslock(0);//审核通过
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'账号开通！');
		}
		$this->page_debug("账号开通成功！",getFromUrl());
	}
	
	/**
	 * 锁定
	 */
	function doBack()
	{
		if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("mix.userid"); 
		
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("Users")->selectByUserId($ids[$i]);
			if($user->isNew()) continue;
			$user->setIslock(4);//账号锁定，兼容老系统
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'账号锁定！');
		}
		$this->page_debug("账号锁定成功！",getFromUrl());
	}

	/**
	 * 编辑账号信息
	 */
	function edit()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));		
		if(input::getInput("post"))
		{
			$msg = array();
			if($user->hasByUserName(trim(input::getInput("post.user_name")))) $msg[] = "登录账号已被占用";
			if(!isEmail(input::getInput("post.user_email"))) $msg[] = "填写内容不是有效的电子信箱";
			if($user->hasByEmail(trim(input::getInput("post.user_email")))) $msg[] = "电子邮箱已经注册";
			if(!isIdcard(input::getInput("post.user_idcard"))) $msg[] = "填写内容不是有效的身份证号码";
			if($user->hasByCardId(trim(input::getInput("post.user_idcard")))) $msg[] = "身份证号码已经注册";	
			if(!isMobile(input::getInput("post.user_mobile"))) $msg[] = "填写内容不是有效的手机号码";
			if($user->hasByMobile(input::getInput("post.user_mobile"))) $msg[] = "填写的手机号码已经注册";
			
			input::getInput("post.user_name") && $user->setUserName(input::getInput("post.user_name"));
			input::getInput("post.user_username") && $user->setUserUsername(input::getInput("post.user_username"));
			input::getInput("post.user_idcard_type") && $user->setUserIdcardType(input::getInput("post.user_idcard_type"));
			input::getInput("post.user_idcard") && $user->setUserIdcard(input::getInput("post.user_idcard"));
			input::getInput("post.user_mobile") && $user->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.user_email") && $user->setUserEmail(input::getInput("post.user_email"));
			$user->setIsLock(input::getInput("post.is_lock")?1:0);
			$user->setUpdatedAt(date("Y-m-d H:i:s"));
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),$msg,"Users");
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/account/edit");
		view::display("page_blank");
	}
	
	/**
	 * 重置密码
	 */
	function setpasswd()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('账号信息不存在！',getFromUrl());
		$password = 'abc'.mt_rand(10000,99999);
		$user->setUserPassword($password);
		$user->setUpdatedAt(date("Y-m-d H:i:s"));
		$user->save();
//		$user->sendMessage('尊敬的'.$user->getUserUsername().'，您好！您的账号为："'.$user->getUserName().'"，密码为："'.$password.'"。请及时登录系统修改密码！');
		sf::getModel("historys")->addHistory($user->getUserId(),"重置密码");
        $this->success('你的密码是:'.$password);
	}
	
	/**
	 * 显示关联账号
	 */
	function more()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));	
		view::set("user",$user);
		view::apply("inc_body","admin/account/more");
		view::display("page_blank");
	}
	
	/**
	 * 现实账号关联角色信息
	 */
	function ajaxMore()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));
		view::set('user',$user);
		exit(view::getContent("admin/account/ajax_more"));
	}
	
	/**
	 * 合并账号
	 */
	function join()
	{
		if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());
		
		//被合并的项目负责人
		$user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
		if($user->isNew()) $this->page_debug("被合并账号不存在！",getFromUrl());
		
		$_userid  = $user->getUserId();
		$_username = $user->getUserUsername();
		
		$ids = $_ids = array();
		$db = sf::getLib("db");
		if(is_array(input::getInput("post.select_id"))) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("post.userid");
		
		for($i=0,$n=count($ids);$i<$n;$i++)
		{
			$_user = sf::getModel("Users")->selectByUserId($ids[$i]);
			if($_user->isNew()) continue;
			//更新关联
			$db->query("UPDATE user_roles SET user_id = '".$_userid."' WHERE user_id = '".$_user->getUserId()."' ");
			$_user->delete();
			sf::getModel("historys")->addHistory($_userid,sprintf("将账号“%s(%s)”的相关信息附加到该帐号！",$_user->getUserName(),$_user->getUserIdcard()),'users');
		}
		exit("<script>parent.location.reload();</script>");
	}
	
	/**
	 * 修改时验证
	 */
	function valid()
	{
		@header('Content-type: application/json');
		$message = '';
		$user_id = input::getInput("mix.userid") ? input::getInput("mix.userid") : '';
		if(input::getInput("post.user_idcard")){//验证身份证
			if(!isIdcard(input::getInput("post.user_idcard"))){
				$valid = false;
				$message = '输入内容不是有效的身份证号码';
			}elseif(sf::getModel("Users")->selectAll("user_idcard = '".input::getInput("post.user_idcard")."' and user_id != '".$user_id."'")->getTotal()){
				$message = '身份证号码已经注册不能重复注册，请使用找回密码功能找回账号和密码！';
				$valid = false;
			}else $valid = true;
		}else if(input::getInput("post.safe_code")){//验证验证码
			if(input::getInput("session.SafetyCode") != input::getInput("post.safe_code"))
			{
				$valid = false;
				$message = '安全校验码不正确请重新输入';	
			}else $valid = true;	
		}else if(input::getInput("post.user_name")){//登录名
			if(sf::getModel("Users")->selectAll("user_name = '".input::getInput("post.user_name")."' and user_id != '".$user_id."'")->getTotal()){
				$valid = false;
				$message = '登录账号已经被占用，请更换一个登录账号试试';	
			}else $valid = true;	
		}else if(input::getInput("post.user_email")){
			if(!isEmail(input::getInput("post.user_email"))){
				$valid = false;
				$message = '输入内容不是有效的电子邮箱地址';
			}elseif(sf::getModel("Users")->selectAll("user_email = '".input::getInput("post.user_email")."' and user_id != '".$user_id."'")->getTotal()){
				$valid = false;
				$message = '该电子邮箱已经注册过了，您可以换一个电子邮箱试一试，如果忘记密码请使用找回密码功能。';	
			}else $valid = true;	
		}else if(input::getInput("post.user_mobile")){
			if(!isMobile(input::getInput("post.user_mobile"))){
				$valid = false;
				$message = '手机号码无效，请重新输入';			
			}else if(sf::getModel("Users")->selectAll("user_mobile = '".input::getInput("post.user_mobile")."' and user_id != '".$user_id."'")->getTotal()){
				$message = '该手机号码已经注册，如果忘记密码请使用找回密码功能！';
				$valid = false;
			}else $valid = true;
		}
		
		exit(json_encode(array(
			'valid'   => $valid,
			'message' => $message
			)));	
	}
	function login()
	{
		$user = sf::getModel('Users')->selectByUserId(input::getInput('get.userid'));
		if($user->isNew()) $this->page_debug("未找到该用户");
        if($user->getIsLock() == 4) exit(new sfException(lang::get("You has been lock!")));//4为黑名单

        $_SESSION['id'] 		=  $user->getId();
        $_SESSION['userid'] 	=  $user->getUserId();
        $_SESSION['roleuserid'] =  $user->getRoleUserId();
        $_SESSION['username'] 	=  $user->getUserName();
        $_SESSION['nickname'] 	=  $user->getUserUsername();
        $_SESSION['userlevel'] 	=  $user->getUserGroupId();
        $_SESSION['groupname'] 	=  $user->getUserGroupName();
        $_SESSION['office_id'] 	=  $user->getOfficeId();
        $_SESSION['useremail'] 	=  $user->getUserEmail();
        $_SESSION['lastlogin'] 	=  $user->getLastloginAt();
        $_SESSION['userip']		=  $user->getUserIp();
        $_SESSION['sign']		=  md5($user->getUserName().$user->getUserGroupId().input::getIp());

        $_session_id = $user->getSessionId();
        $user->setLoginNum();
        $user->setSessionId(session_id());
        $user->setLastloginAt(date("Y-m-d H:i:s"));
        $user->setUserIp(input::getIp());
        $user->save();
        $this->page_debug(lang::get("Has been login!"),site_url("manager/choose"));
    }


    function role()
    {
    	$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));
    	if(input::getInput('post.user_group_ids')){
    		foreach (input::getInput('post.user_group_ids') as $group_id){
    			if(!$user->hasRole($group_id)){
    				$user->addRole($group_id,$user->getUserId());
    			}
    		}
    		$this->page_debug("保存成功",site_url("admin/account"));
    	}else{
    		$groups = sf::getModel("UserGroups")->selectAll();
    		$roles = $user->selectRoles();
    		$role_arr = [];
    		while($role = $roles->getObject()){
    			$role_arr[] = $role->getRoleId();
    		}
    		view::set("user",$user);
    		view::set("groups",$groups);
    		view::set("role_arr",$role_arr);
    		view::apply("inc_body","admin/account/role");
    		view::display("page");
    	}
    }
    function delLogGetpwd(){
    	$user_id = input::getInput('mix.id');
    	$log = sf::getModel("LogGetpwds");
    	$lognum = sf::getModel("NumberGetpwds")->selectByUserId($user_id);
    	if($lognum->getNumber()){
    		$lognum->setNumber($lognum->getNumber()+$log->getCount($user_id));
    	}else{
    		$lognum->setNumber($log->getCount($user_id));
    	}
    	$lognum->setUserId($user_id);
    	$lognum->save();
    	if($log->getCount($user_id) > 5){
    		$db = sf::getLib("db");
    		$sql = "DELETE FROM log_getpwds WHERE user_id = '".$user_id."'";
    		$db->query($sql);
    	}
    	$this->page_debug('操作成功！',getFromUrl());
    }
	// 科协会员申报截止时间
    function memberAt(){
    	$guide = sf::getModel("Guides",17);
    	if(input::getInput("post"))
    	{
    		$guide->setEndAt(input::getInput("post.end_at"));
    		$guide->setShowAt(input::getInput("post.show_at"));
    		$guide->save();
    		exit("<script>parent.location.reload();</script>");	
    	}
    	view::set('guide',$guide);
    	view::apply("inc_body","admin/account/member_at");
    	view::display("page_blank");
    }

    function createAccount()
    {
        if($data = input::getInput("post")){
            if(input::getInput("session.SafetyCode") != input::getInput("post.safe_code"))//安全性判断
                $this->error("安全校验码不正确",getFromUrl());

            $userid = sf::getLib("MyString")->getRandString();
            $user = sf::getModel("Users");
            if($user->hasByUserName($data['user_name'])) $this->error('登录账号已存在！');
            if($user->hasByMobile($data['user_mobile']) && $data['user_mobile']) $this->error('手机号已注册！');
            if($user->hasByCardId($data['user_idcard']) && $data['user_idcard']) $this->error('身份证号已注册！');

            $user->setUserId($userid);
            $user->setUserUsername($data['user_username']);
            $user->setUserName($data['user_name']);
            $user->setUserPassword($data['user_password']);
            $user->setUserGroupId((int)$data['user_role']);
            $user->setUserIdcard($data['user_idcard']);
            $user->setUserMobile($data['user_mobile']);
            $user->setUserEmail($data['user_email']);
            $user->setCreatedAt(date('Y-m-d H:i:s'));
            $user->save();

            exit("<script>parent.location.reload();</script>");
        }
        view::set('user',sf::getModel("Users"));
        view::apply("inc_body","admin/account/account");
        view::display("page_blank");
    }
}
?>