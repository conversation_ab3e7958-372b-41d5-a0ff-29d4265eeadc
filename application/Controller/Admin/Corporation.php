<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Lang;
use Sofast\Core\Input;
use Sofast\Core\Sf;
class corporation extends BaseController
{	
	/**
	 * 单位检索
	 */
	public function index()
	{
		$this->unit_gird("1","admin/corporation/index","order by level asc,orders asc");
	}

    public function figure_list()
    {
        $this->unit_gird("user_id like '9B581D83-B2BA-7337-0678%' and (level = 2 or (subject IN ('物流公司','信息公司','北斗公司') and level = 3)) ","admin/corporation/figure_list","order by level asc,orders asc");
    }

	/**
	 * 单位资料填写状态
	 */
	public function stat_list()
	{
		$this->unit_gird("1","admin/corporation/stat_list","order by level asc,id asc");
	}
	
	/**
	 * 等待审核的单位
	 */
	public function wait_list()
	{
		$this->unit_gird("`is_lock` in ('5','6')","admin/corporation/wait_list");
	}

	/**
	 * 已经审核的单位
	 */
	public function submit_list()
	{
		$this->unit_gird("`is_lock` = 0","admin/corporation/submit_list");
	}

	/**
	 * 被锁定的单位
	 */

	public function back_list()
	{
		$this->unit_gird("`is_lock` = 3","admin/corporation/back_list");
	}
	
	public function black_list()
	{
		$this->unit_gird("`is_lock` = 4 ","admin/corporation/black_list");
	}	
	
	/**
	 * 注册中的账号
	 */
	public function reg_list()
	{
		$this->unit_gird("`is_lock` = 9 ","admin/corporation/reg_list");
	}
	
	public function paperwork_list()
	{
		$this->unit_gird("is_lock = 6","admin/corporation/paperwork_list");
	}

	function show()
	{
		$dept = sf::getModel("Corporations")->selectByUserId(input::getInput("mix.id"));
		view::set("dept",$dept);
		view::apply("inc_body","admin/corporation/show");
		view::display("page");	
	}
	
	function delete()
	{	
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$corporation->isNew()){
			//有项目不能删除
			if($corporation->hasProject()) $this->page_debug(lang::get("Can not be deleted, because the project!"),getFromUrl());
			$corporation->delete();
			sf::getModel("historys")->addHistory($corporation->getUserId(),'单位被删除，其原因是：<br />'.input::getInput("post.content"),'corporations',1);
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$corporation);
		view::apply("inc_body","admin/corporation/note");
		view::display("page_blank");
	}
	
	/**
	 * 审核申报单位
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("mix.userid"); 
		
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("corporations")->selectByUserId($ids[$i]);
			if($user->isNew()) continue;
			$user->setIslock(0);//审核通过
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'通过认证！','corporations');
			// $user->sendMessage(sprintf(lang::get('MSG.C001'),$user->getLinkman()),$user->getUserId(),'corporations');
		}
		$this->page_debug('认证成功！',getFromUrl());
	}
	
	/**
	 * 退回
	 */
	function doBack()
	{
		$user = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->setIslock(3);//平台锁定
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'账号认证被退回！其原因是：<br />'.input::getInput("post.content"),'corporations');
			// $user->sendMessage(sprintf(lang::get('MSG.C002'),$user->getLinkman()),$user->getUserId(),'corporations');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/corporation/note");
		view::display("page_blank");
	}
	
	/**
	 * 黑名单
	 */
	function doBlack()
	{
		$user = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->toBlack();//黑名单
			sf::getModel("historys")->addHistory($user->getUserId(),'账号被列入黑名单！其原因是：<br />'.input::getInput("post.content"),'corporations');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/corporation/note");
		view::display("page_blank");
	}
	
	function coupling()
	{
		$msg = [];
		$company = sf::getModel("Corporations")->selectByUserId(input::getInput("post.cid"));
		$user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
		if($company->isNew()) $msg[] = '关联单位不存在';
		if($user->isNew())    $msg[] = '关联账号不存在';
		if($company->coupling($user->getUserId()))
		{
			sf::getModel("historys")->addHistory($company->getUserId(),"附加管理员【".$user->getUserUsername()."】到该单位！",'corporations',1);
			sf::getModel("historys")->addHistory($user->getUserId(),"被设置成【".$company->getSubject()."】的管理员！",'users',1);
			$msg[] = '绑定成功！';
		}
		exit('{msg:"'.implode("、",$msg).'"}');
	}
	
	function decoupling()
	{
		$company = sf::getModel("corporations")->selectByUserId(input::getInput("mix.companyid"));
		if(input::getInput("post.content")){
			$user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
			if($user->isNew()) exit("<script>parent.location.reload();</script>");
			if($company->decoupling($user->getUserId()))
			{
				sf::getModel("historys")->addHistory($company->getUserId(),"解除【".$user->getUserUsername()."】与该单位的关联，原因是：<br />".input::getInput("post.content"),'corporations',1);
				sf::getModel("historys")->addHistory($user->getUserId(),"解除与【".$company->getSubject()."】的关联，原因是：<br />".input::getInput("post.content"),'users',1);
			}
			exit("<script>parent.location.reload();</script>");
		}
		view::set("userid",input::getInput("mix.userid"));
		view::set("company",$company);
		view::apply("inc_body","admin/corporation/decoupling");
		view::display("page_blank");	
	}

    /**
     * 设置默认管理员
     */
    public function setManager()
    {
        $corporation = sf::getModel("Corporations")->selectByUserId(input::mix("companyid"));
        if($corporation->isNew()) $this->page_debug('操作的单位不存在！',getFromUrl());
        $user = sf::getModel("Users")->selectByUserId(input::mix("userid"));
        if($user->isNew()) $this->page_debug('关联账号不存在',getFromUrl());
        if($corporation->setManager($user->getUserId())) $this->page_debug('设置成功！',getFromUrl());
        else $this->page_debug('设置失败！',getFromUrl());
    }

    function addAssistant()
    {
        $msg = [];
        $company = sf::getModel("Corporations")->selectByUserId(input::getInput("post.cid"));
        $user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
        if($company->isNew()) $msg[] = '关联单位不存在';
        if($user->isNew())    $msg[] = '关联账号不存在';
        if($company->addAssistant($user->getUserId()))
        {
            sf::getModel("historys")->addHistory($company->getUserId(),"附加助理【".$user->getUserUsername()."】到该单位！",'corporations',1);
            sf::getModel("historys")->addHistory($user->getUserId(),"被设置成【".$company->getSubject()."】的助理！",'users',1);
            $msg[] = '绑定成功！';
        }
        exit('{msg:"'.implode("、",$msg).'"}');
    }

    function removeAssistant()
    {
        $company = sf::getModel("corporations")->selectByUserId(input::getInput("mix.companyid"));
        if(input::getInput("post.content")){
            $user = sf::getModel("Users")->selectByUserId(input::getInput("post.userid"));
            if($user->isNew()) exit("<script>parent.location.reload();</script>");
            if($company->removeAssistant($user->getUserId()))
            {
                sf::getModel("historys")->addHistory($company->getUserId(),"解除【".$user->getUserUsername()."】与该单位的关联，原因是：<br />".input::getInput("post.content"),'corporations',1);
                sf::getModel("historys")->addHistory($user->getUserId(),"解除与【".$company->getSubject()."】的关联，原因是：<br />".input::getInput("post.content"),'users',1);
            }
            exit("<script>parent.location.reload();</script>");
        }
        view::set("userid",input::getInput("mix.userid"));
        view::set("company",$company);
        view::apply("inc_body","admin/corporation/decoupling");
        view::display("page_blank");
    }
	
	function edit()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post"))
		{
			$_subject = $corporation->getSubject();
			$_department = $corporation->getDepartmentId();
			$_departmentname = $corporation->getDepartmentName();
			$_parent = $corporation->getParentId();
			$_parentname = $corporation->getParentName();
			$_code = $corporation->getCode();
			$_property = $corporation->getCompanyProperty();
			$_quota = $corporation->getQuota();
			$msg = array();
			if(input::getInput("post.subject") && input::getInput("post.subject") != $_subject){
				$corporation->setSubject(input::getInput("post.subject"));
				$corporation->getUser()->setUserUsername(input::getInput("post.subject"));
				$msg[] = sprintf('单位名称由【%s】变更为【%s】',$_subject,input::getInput("post.subject"));
			}

			if(input::getInput("post.parent_id") && input::getInput("post.parent_id") != $_parent){
				$corporation->setParentId(input::getInput("post.parent_id"));
				$msg[] = sprintf('单位的上级单位由【%s】变更为【%s】',$_parentname,sf::getModel("Corporations")->selectByUserId(input::getInput("post.parent_id"))->getSubject());
			}
			if(input::getInput("post.code") && input::getInput("post.code") != $_code){
				$corporation->setCode(input::getInput("post.code"));
				$msg[] = sprintf('组织机构代码由【%s】变更为【%s】',$_code,input::getInput("post.code"));
			}
			
			if(input::getInput("post.type")){
				$_type = $corporation->getType();
				$corporation->setType(input::getInput("post.type"));
				$msg[] = sprintf('单位类型由【%s】变更为【%s】',$_type,input::getInput("post.type"));
			}

			if(input::getInput("post.company_property") && input::getInput("post.company_property") != $_property){
				$corporation->setCompanyProperty(input::getInput("post.company_property"));
				$msg[] = sprintf('单位类型由【%s】变更为【%s】',$_property,input::getInput("post.company_property"));
			}
			if(input::getInput("post.quota") && input::getInput("post.quota") != $_quota){
				$corporation->setQuota(input::getInput("post.quota"));
				$msg[] = sprintf('科协会员总配额由【%s】变更为【%s】',$_quota,input::getInput("post.quota"));
			}
			
			$corporation->setUpdatedAt(date("Y-m-d H:i:s"));
			$corporation->save();
			sf::getModel("historys")->addHistory($corporation->getUserId(),count($msg)?implode(';',$msg)."。":'未作变更！','corporations',1);
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$corporation);
		view::apply("inc_body","admin/corporation/edit");
		view::display("page_blank");
	}
	
	/**
	 * 显示用户更多信息
	 */
	function more()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));	
		view::set("dept",$corporation);
		view::apply("inc_body","admin/corporation/more");
		view::display("page_blank");
	}
	
	/**
	 * 合并单位
	 */
	function join()
	{
		if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());
		
		//被合并的单位
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("post.userid"));
		if($corporation->isNew()) $this->page_debug("被合并单位不存在！",getFromUrl());
		$_userid  = $corporation->getUserId();
		$_subject = $corporation->getSubject();

		//合并并删除单位
		if(is_array(input::getInput("post.select_id"))) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("post.userid");
		
		for($i=0,$n=count($ids);$i<$n;$i++)
		{
			$_unit = sf::getModel("corporations")->selectByUserId($ids[$i]);
			if($_unit->isNew()) continue;
			
			//合并项目负责人
			$users = sf::getModel("declarers")->selectAll("corporation_id = '".$_unit->getUserId()."'");
			while($user = $users->getObject()){
				if($user->isNew()) continue;
				$user->setCorporationId($_userid);
				$user->setCorporationName($_subject);
				$user->save();
				sf::getModel("historys")->addHistory($user->getUserId(),"单位进行合并帐号被转移到新的申报单位！",'declarers');
			}
			
			//合并专家
			$experts = sf::getModel("Experts")->selectAll("corporation_id = '".$_unit->getUserId()."'");
			while($expert = $experts->getObject()){
				if($expert->isNew()) continue;
				$expert->setCorporationId($_userid);
				$expert->setWorkUnit($_subject);
				$expert->save();
				sf::getModel("historys")->addHistory($expert->getUserId(),"单位进行合并帐号被转移到新的申报单位！",'experts');
			}
			
			//合并申报的项目
			$pager = sf::getModel("projects")->selectAll("corporation_id = '".$_unit->getUserId()."'");
			while($p = $pager->getObject()){
				if($p->isNew()) continue;
				$p->setCorporationId($_userid);
				$p->setCorporationName($_subject);
				//暂时不转移归口部门
				$p->save();
				sf::getModel("historys")->addHistory($p->getProjectId(),"单位进行合并，该项目被转移到新的申报单位！");
			}
			//删除帐号
			$_unit->delete();
			sf::getModel("historys")->addHistory($_userid,sprintf("将申报单位“%s(%s)”的相关信息附加到该帐号！",$_unit->getSubject(),$_unit->getCode()),'corporations');
		}
		
		$this->page_debug("单位合并成功！",getFromUrl());
	}

	/**
	 * 单位管理员
	 */
	function managers()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		view::set("corporation",$corporation);
		view::apply("inc_body","admin/corporation/managers");
		view::display("page_blank");
	}

	/**
	 * 单位助理
	 */
	function assistants()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.userid"));
		view::set("corporation",$corporation);
		view::apply("inc_body","admin/corporation/assistants");
		view::display("page_blank");
	}

	/**
	 * 添加分公司
	 */
	function addSon()
	{
		$corporation = sf::getModel("corporations")->selectByUserId(input::getInput("mix.id"));
		if($data = input::getInput("post")){
			$userid = sf::getLib("MyString")->getRandString();

			$user = sf::getModel("Users");
			if($user->hasByUserName($data['user_name'])) $this->page_debug('登录账号已存在！');
			$user->setUserId($userid);
			$user->setUserName($data['user_name']);
			$user->setUserPassword('abcd1234');
			$user->setUserGroupId('3');
			$user->save();
			
			$user_role = sf::getModel("UserRoles");
			$user_role->setUserId($userid);
			$user_role->setRoleId(3);
			$user_role->setUserRoleId($userid);
			$user_role->setUserRoleType('AppModelsCompany');
			$user_role->save();

			$corporation = sf::getModel("corporations");
			if($corporation->isHave($data['subject'])) $this->page_debug('单位名称已存在！');
			$corporation->setUserId($userid);
			$corporation->setParentId($data['id']);
			$corporation->setSubject($data['subject']);
			$corporation->setLevel('2');
			$corporation->setIslock(0);
			$corporation->save();

			exit("<script>parent.location.reload();</script>");
		}
		view::set("corporation",$corporation);
		view::apply("inc_body","admin/corporation/add_son");
		view::display("page_blank");
	}

	/**
	 * 添加公司
	 */
	function add()
	{
		$corporation = sf::getModel("corporations")->selectByUserId();
		if(input::getInput("post")){
            $subject = str_replace('  ','',trim(input::post('subject')));
            $subject = str_replace(' ','',trim($subject));
            if(strlen($subject)<5){
                $this->error('单位名称格式不正确');
            }
            $corporation=sf::getModel("Corporations")->selectByCode(input::post('code'));
            if(!$corporation->isNew()){
                $this->error('社会统一信用代码已存在');
            }
            $corporation=sf::getModel("Corporations")->selectBySubject($subject);
            if(!$corporation->isNew()){
                $this->error('单位名称已存在');
            }
            $level = 1;
            if(empty(input::post('parent_id'))){
                //一级单位
                $userId = sf::getLib("MyString")->getRandString(20).str_repeat('0', 12);
            }else{
                $parentCompany=sf::getModel("Corporations")->selectByUserId(input::post('parent_id'));
                if($parentCompany->isNew()){
                    $this->error('没有找到上级单位');
                }
                $level = $parentCompany->getLevel()+1;
                $userId = $parentCompany->getChildUserId();
            }
            $corporation = sf::getModel('Corporations')->selectByUserId($userId);
            $corporation->setCode(input::post('code'));
            $corporation->setSubject($subject);
            $corporation->setLevel($level);
            $corporation->setParentId(input::post('parent_id'));
            $corporation->setLinkman(input::post('user_name'));
            $corporation->setMobile(input::post('user_mobile'));
            $corporation->setPhone(input::post('user_tel'));
            $corporation->setCreatedAt(date('Y-m-d H:i:s'));
            $corporation->setRegisterAt(date('Y-m-d H:i:s'));
            $corporation->setUpdatedAt(date('Y-m-d H:i:s'));
            $corporation->setIsLock(0);
            $corporation->save();
            exit("<script>parent.location.href = parent.location.href+'/_add/yes';</script>");
		}

		view::set("corporation",$corporation);
		view::set("parentId",input::getMix('parent_id'));
		view::apply("inc_body","admin/corporation/add");
		view::display("page_blank");
	}
	
    public function map()
    {
        view::apply("inc_body","admin/corporation/map");
        view::display("page_blank");
    }

    public function export()
    {
        $companys = sf::getModel('Corporations')->selectAll("user_id like '9B581D83-B2BA-7337-0678%' and level = 2","ORDER BY orders asc");
        $datas = [];
        $i=0;
        //二级单位
        while($company = $companys->getObject()){
            $datas[$i][] = $i+1;
            $datas[$i][] = $company->getOaCode();
            $datas[$i][] = $company->getSubject();
            $datas[$i][] = $company->getLevel();
            $datas[$i][] = $company->getAllParentSubject();
            $i++;
        }
        //三级单位
        $companys->reset();
        while($company = $companys->getObject()){
            $company2s = sf::getModel('Corporations')->selectAll("parent_id = '".$company->getUserId()."' and level = 3","ORDER BY orders asc");
            while($company2 = $company2s->getObject()){
                $datas[$i][] = $i+1;
                $datas[$i][] = $company2->getOaCode();
                $datas[$i][] = $company2->getSubject();
                $datas[$i][] = $company2->getLevel();
                $datas[$i][] = $company2->getAllParentSubject();
                $i++;
            }
        }
        //四级单位
        $companys->reset();
        while($company = $companys->getObject()){
            $company2s = sf::getModel('Corporations')->selectAll("parent_id = '".$company->getUserId()."' and level = 3","ORDER BY orders asc");
            while($company2 = $company2s->getObject()){
                $company3s = sf::getModel('Corporations')->selectAll("parent_id = '".$company2->getUserId()."' and level = 4","ORDER BY orders asc");
                while($company3 = $company3s->getObject()){
                    $datas[$i][] = $i+1;
                    $datas[$i][] = $company3->getOaCode();
                    $datas[$i][] = $company3->getSubject();
                    $datas[$i][] = $company3->getLevel();
                    $datas[$i][] = $company3->getAllParentSubject();
                    $i++;
                }
            }
        }
        $head = ['序号','OA编号','单位名称','单位层级','上级单位'];
        excel_out($head,$datas,'组织机构','组织机构');
    }

    public function allowLogin()
    {
        $corporation = sf::getModel("corporations")->selectByUserId(input::getMix('id'));
        if($corporation->isNew()) $this->error('没有找到该单位');
        $type = input::getMix('type');
        if(!$type) $this->error('没有找到操作类型');
        if($type=='forbidden'){
            $corporation->setLoginAuth('no');
        }else{
            $corporation->setLoginAuth('yes');
        }
        $corporation->save();
        $this->refresh();
    }
}
?>