<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Lang;
class member extends BaseController
{	
	/**
	 * 综合查询
	 */
	public function index()
	{
		$this->user_gird("talent = 3","admin/member/index");
	}

	public function wait_list()
	{
		$this->user_gird("is_lock in (5,6) and talent = 3","admin/member/wait_list");
	}
	
	public function submit_list()
	{
		$this->user_gird("is_lock in (0) and talent = 3","admin/member/submit_list");
	}
	
	public function back_list()
	{
		$this->user_gird("is_lock = 3 and talent = 3","admin/member/back_list");
	}

	/**
	 * 显示用户更多信息
	 */
	function more()
	{
		$declarers = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));	
		view::set("user",$declarers);
		view::apply("inc_body","admin/member/more");
		view::display("page_blank");
	}
	
	/**
	 * 关联的账号
	 */
	function users()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));	
		view::set("declarer",$declarer);
		view::apply("inc_body","admin/member/users");
		view::display("page_blank");
	}

	/**
	 * 删除数据
	 */
	function doDelete()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			//有项目不能删除
			if($user->hasProject()) $this->page_debug(lang::get("Can not be deleted, because the project!"),getFromUrl());
			//删除
			$user->delete();
			//记录
			sf::getModel("historys")->addHistory($user->getUserId(),"删除申报人角色，其原因是：<br />".input::getInput("post.content"),'declarers',1);
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/member/note");
		view::display("page_blank");
	}
	
	/**
	 * 认证科协会员
	 */
	function doSubmit()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("mix.userid");
		if(!$ids[0])
			$this->page_debug('请至少选择一位会员！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("Declarers")->selectByUserId($ids[$i]);
			if(!$user->isNew()){
				$user->setIslock(0);
				$user->setOrde(10);
				$user->setTalentAt(date('Y-m-d H:i:s'));
				$user->save();
				sf::getModel("historys")->addHistory($user->getUserId(),'通过实名认证！认定为：【'.$user->getTalen().'】','declarers');
			// $user->sendMessage(sprintf(lang::get('MSG.D001'),$user->getPersonname()),$user->getUserId(),'declarers');
				// exit("<script>parent.location.reload();</script>");
			}
		}
		$this->page_debug('认证成功！',getFromUrl());				
	}
	
	/**
	 * 退回单个
	 */
	function doBack()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->setIslock(3);
			$user->setOrde(3);
			$user->save();
			$corporation = sf::getModel("Corporations")->selectByUserId($user->getCorporationId());
			$corporation->setUseQuota($corporation->getUseQuota() - 1);
			$corporation->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'实名认证退回！其原因是：<br />'.input::getInput("post.content"),'declarers');
			// $user->sendMessage(sprintf(lang::get('MSG.D002'),$user->getPersonname()),$user->getUserId(),'declarers');
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/member/note");
		view::display("page_blank");
	}

	/**
	 * 退回多个
	 */
	function doRejected()
	{
		if(input::getInput("post.select_id")) 
			$ids = input::getInput("post.select_id");
		else 
			$ids[] = input::getInput("mix.userid");
		if(!$ids[0])
			$this->page_debug('请至少选择一位会员！',getFromUrl());	
		for($i=0,$n=count($ids);$i<$n;$i++){
			$user = sf::getModel("Declarers")->selectByUserId($ids[$i]);
			if(!$user->isNew()){
				$user->setIslock(3);
				$user->setOrde(3);
				$user->save();
				$corporation = sf::getModel("Corporations")->selectByUserId($user->getCorporationId());
				$corporation->setUseQuota($corporation->getUseQuota() - 1);
				$corporation->save();
				sf::getModel("historys")->addHistory($user->getUserId(),'实名认证退回！','declarers');
			// $user->sendMessage(sprintf(lang::get('MSG.D001'),$user->getPersonname()),$user->getUserId(),'declarers');
				// exit("<script>parent.location.reload();</script>");
			}
		}
		$this->page_debug('退回成功！',getFromUrl());				
	}
	/**
	 * 黑名单
	 */
	function doBlack()
	{
		$user = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post.content") && !$user->isNew()){
			$user->toBlack();//黑名单
			$user->save();
			sf::getModel("historys")->addHistory($user->getUserId(),'账号被列入黑名单！其原因是：<br />'.input::getInput("post.content"),'declarers',1);
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$user);
		view::apply("inc_body","admin/member/note");
		view::display("page_blank");
	}
	
	function change(){
		$userid = input::getInput("post.userid");
		$cid = input::getInput("post.cid");
		
		if(!$userid) exit('NO1');
		if(!$cid) exit('NO2');
		//修改单位
		$declarer = sf::getModel("Declarers")->selectByUserId($userid);
		$old_name = $declarer->getCorporationName();
		$declarer->setCorporationId($cid);
		$declarer->setCorporationName(sf::getModel("Corporations")->selectByUserId($cid)->getSubject());
		sf::getModel("historys")->addHistory($declarer->getUserId(),"申报单位由【".$old_name."】调整为【".$declarer->getCorporationName()."】。",'declarers');
		if($declarer->save()) exit('OK');
		else exit("NO3");
	}
	
	/**
	 * 重置密码
	 */
	function setpasswd()
	{
		$user = sf::getModel("Users")->selectByUserId(input::getInput("mix.userid"));
		if($user->isNew()) $this->page_debug('账号信息不存在！',getFromUrl());
		$password = 'abc'.rand(100000,999999);
		$user->setUserPassword($password);
		$user->setUpdatedAt(date("Y-m-d H:i:s"));
		$user->save();
		// $user->sendMessage('尊敬的'.$user->getUserUsername().'，您好！您在中国民航局第二研究所科管平台上进行了取回密码操作！您的账号为："'.$user->getUserName().'"，密码为："'.$password.'"。请及时登录系统修改密码！');
		sf::getModel("historys")->addHistory($user->getUserId(),lang::get("The password has been change!"),'declarers');
		$this->page_debug(sprintf(lang::get("The new password is:%s!"),$password),getFromUrl());	
	}
	
	function edit()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		$_declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.userid"));
		if(input::getInput("post"))
		{
			$msg = array();
			input::getInput("post.personname") && $declarer->setPersonname(input::getInput("post.personname"));
			input::getInput("post.user_idcard") && $declarer->setUserIdcard(input::getInput("post.user_idcard"));
			input::getInput("post.card_type") && $declarer->setCardType(input::getInput("post.card_type"));
			input::getInput("post.user_birthday") && $declarer->setUserBirthday(input::getInput("post.user_birthday"));
			input::getInput("post.user_grade") && $declarer->setUserGrade(input::getInput("post.user_grade"));
			input::getInput("post.user_mobile") && $declarer->setUserMobile(input::getInput("post.user_mobile"));
			input::getInput("post.user_email") && $declarer->setUserEmail(input::getInput("post.user_email"));
			if(input::getInput("post.talent")){
				$declarer->setTalent(input::getInput("post.talent"));
			}else{
				$declarer->setTalent('');
			}
			
			if(input::getInput("post.high")){
				$declarer->setHigh(1);
				if ($_declarer->getHigh() != input::getInput("post.high")) {
					$msg[] = "开启【高层次人才】相关权限；";
				}
			}else{
				$declarer->setHigh(0);
				if ($_declarer->getHigh() != input::getInput("post.high")) {
					$msg[] = "关闭【高层次人才】相关权限；";
				}
			} 
			if(input::getInput("post.is_lock")){
				$declarer->setIsLock(0);//通过认证
				$declarer->setOrde(10);
			}
			input::getInput("post.personname") && $declarer->getUser(true)->setUserUsername(input::getInput("post.personname"));
			if(input::getInput("post.personname") != $_declarer->getPersonname()){
				$msg[] = "姓名更改为【{$declarer->getPersonname()}】";
			}
			if(input::getInput("post.card_type") != $_declarer->getCardType()){
				$msg[] = "证件类型更改为【{$declarer->getCardType()}】";
			}
			if(input::getInput("post.user_idcard") != $_declarer->getUserIdcard()){
				$msg[] = "证件号码更改为【{$declarer->getUserIdcard()}】";
			}
			if(input::getInput("post.talent") != $_declarer->getTalent()){
				$msg[] = "人才类别更改为【{$declarer->getTalen()}】";
			} 
			if(input::getInput("post.corporation_id")){
				$company = sf::getModel("Corporations")->selectByUserId(input::getInput("post.corporation_id"));
				if(!$company->isNew()){
					$_old_name = $declarer->getCorporationName();
					$declarer->setCorporationId($company->getUserId());
					$declarer->setCorporationName($company->getSubject());
					$msg[] = "申报单位由【{$_old_name}】调整为【{$company->getSubject()}】";
				}
			}
			$declarer->setIgnoreComplete(input::getInput("post.ignore_complete")?1:0);
			$declarer->setIgnoreDeclare(input::getInput("post.ignore_declare")?1:0);
			$declarer->setIgnoreEndat(input::getInput("post.ignore_endat")?1:0);
			$declarer->setUpdatedAt(date("Y-m-d H:i:s"));
			if(!$msg){
				$msg[] = '修改资料信息。';
			}
			if($declarer->save()){
				sf::getModel("historys")->addHistory($declarer->getUserId(),implode("，",$msg),'declarers',1);
			}
			exit("<script>parent.location.reload();</script>");
		}
		view::set("user",$declarer);
		view::apply("inc_body","admin/member/edit");
		view::display("page_blank");
	}
	
	/**
	 * 合并项目申报人
	 */
	function join()
	{
		if(input::getInput("session.SafetyCode") != input::getInput("post.SafetyCode")) $this->page_debug(lang::get("The safety code is error!"),getFromUrl());
		
		//被合并的项目申报人
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("post.userid"));
		if($declarer->isNew()) $this->page_debug("被合并项目申报人不存在！",getFromUrl());
		$_userid  = $declarer->getUserId();
		$_username = $declarer->getPersonname();

		//合并并删除单位
		if(is_array(input::getInput("post.select_id"))) $ids = input::getInput("post.select_id");
		else $ids[] = input::getInput("post.userid");
		
		for($i=0,$n=count($ids);$i<$n;$i++)
		{
			$_user = sf::getModel("Declarers")->selectByUserId($ids[$i]);
			if($_user->isNew()) continue;
			//合并申报的项目
			$pager = sf::getModel("projects")->selectAll("user_id = '".$_user->getUserId()."'");
			while($p = $pager->getObject()){
				if($p->isNew()) continue;
				$p->setUserId($_userid);
				$p->setUserName($_username);
				//暂时不转移归口部门
				$p->save();
				sf::getModel("historys")->addHistory($p->getProjectId(),"项目申报人进行合并，该项目被转移到新的申报人帐号！");
			}
			//暂时不合并国家备案
			//删除帐号
			$_user->delete();
			sf::getModel("historys")->addHistory($_userid,sprintf("将申报人“%s(%s)”的相关信息附加到该帐号！",$_user->getPersonname(),$_user->getUserIdcard()),'declarers');
		}
		$this->page_debug("项目申报人合并成功！",getFromUrl());
	}
	/*
	科技项目情况
	 */
	function showProject()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		view::set("declarer",$declarer);
		view::apply("inc_body","admin/member/show_project");
		view::display("page_blank");
	}
	/*
	科技奖励情况
	 */
	function showAward()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		view::set("declarer",$declarer);
		view::apply("inc_body","admin/member/show_award");
		view::display("page_blank");
	}
	/*
	专利信息情况
	 */
	function showPatent()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		view::set("declarer",$declarer);
		view::apply("inc_body","admin/member/show_patent");
		view::display("page_blank");
	}
	/*
	论文专著情况
	 */
	function showPaper()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		view::set("declarer",$declarer);
		view::apply("inc_body","admin/member/show_paper");
		view::display("page_blank");
	}
	/*
	打开个人信息填报
	 */
	function open()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		$declarer->setIslock(9);
		$declarer->setOrde(1);
		$declarer->save();
		$this->page_debug("操作成功！",getFromUrl());
	}
	/*
	关闭个人信息填报
	 */
	function close()
	{
		$declarer = sf::getModel("Declarers")->selectByUserId(input::getInput("mix.id"));
		$declarer->setIslock(1);
		$declarer->setOrde(5);
		$declarer->save();
		$this->page_debug("操作成功！",getFromUrl());
	}
	//更新年龄
	public function updateAge()
	{
		$declarers = sf::getModel("Declarers")->selectAll("user_birthday is not NULL");
		while ($declarer = $declarers->getObject()) {
			$year = date('Y',strtotime($declarer->getUserBirthday()));
			$month = date('m',strtotime($declarer->getUserBirthday()));
			$day = date('d',strtotime($declarer->getUserBirthday()));
			$age = date('Y')-$year;
			if($month>date('m') || $month == date('m') && $day>date('d')){
				$declarer->setAge($age-1);
			}else{
				$declarer->setAge($age);
			}
			$declarer->save();
		}
		$this->page_debug("更新完成！共计：".$declarers->getTotal()." 条数据。",getFromUrl());
	}
}
?>