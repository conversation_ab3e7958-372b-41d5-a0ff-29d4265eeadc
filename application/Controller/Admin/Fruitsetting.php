<?php
namespace App\Controller\Admin;

use App\Controller\BaseController;
use Sofast\Core\Input;
use Sofast\Core\lang;
use Sofast\Core\router;
use Sofast\Support\View;
use App\Models\FruitRegister as Fruit;
use App\Facades\Form;
use App\Facades\Coms;
use Sofast\Core\Sf;

class Fruitsetting extends BaseController
{


	public function index()
	{
        $addWhere = "1 ";


        $pagers = sf::getModel("FruitSettings")->getPager($addWhere,"ORDER BY created_at DESC",20);
        view::set("pagers",$pagers);
        view::apply("inc_body","admin/fruitsetting/index");
        view::display("page");
	}

    /**
     * 数据编辑
     */
    function edit()
    {
        $setting = sf::getModel('FruitSettings',input::getInput("mix.id"));
        if(input::post())
        {
            $setting->setIsShow((int)input::post('is_show'));
            $setting->save();
            $this->refresh();
        }
        view::set("setting",$setting);
        view::apply("inc_body","admin/fruitsetting/edit");
        view::display("page_blank");
    }

}	
?>