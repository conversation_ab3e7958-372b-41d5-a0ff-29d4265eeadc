<?php
namespace App\Controller\Admin;
use App\Controller\BaseController;
use Sofast\Support\View;
use App\Models\Guide as guideModel;
use App\Facades\Form;
use Sofast\Core\Input;
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Lang;
use App\Facades\Cache;

class Guide extends BaseController
{	
	private $dir = 'admin';
	private $year = '2020';
	
	function load()
	{
		$this->year = input::getInput("mix.year") ? input::getInput("mix.year") : config::get("current_declare_year",date("Y"));
		view::set("year",$this->year);
	}
	
	/**
	 * 数据列表
	 */
	function index()
	{
		$parentId = input::get("pid") ? input::get("pid") : 0;
		//$category = sf::getModel("Guides",$parentId,$this->year);
		$category = sf::getModel("Guides",$parentId);
		$addWhere = $addSql = '';
        $addWhere .= "`parent_id` = '".$parentId."' ";
		if($category->getCatId()){
            $addWhere.=" AND `cat_id` = '".$category->getCatId()."'" ;
        }else{
            $addWhere.=" AND `cat_id` != '896'" ;
        }
		input::getInput("mix.search") && $addWhere .= "AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";
		
		view::set("category",$category);
		//取得带翻页的数据集
		view::set("pager",$category->getPager($addWhere ,$addSql,100));
		view::apply("inc_body","admin/guide/index");
		view::display("page_main");
	}

	/**
	 * 数据列表
	 */
	function award()
	{
		$parentId = input::get("pid") ? input::get("pid") : 0;
		//$category = sf::getModel("Guides",$parentId,$this->year);
		$category = sf::getModel("Guides",$parentId);
		$addWhere = $addSql = '';

		$addWhere .= "`parent_id` = '".$parentId."' AND cat_id = '896'";
		input::getInput("mix.search") && $addWhere .= "AND `".input::getInput("mix.field")."` LIKE '%".trim(input::getInput("mix.search"))."%' ";

		view::set("category",$category);
		//取得带翻页的数据集
		view::set("pager",$category->getPager($addWhere ,$addSql,100));
		view::apply("inc_body","admin/guide/index");
		view::display("page_main");
	}
	
	/**
	 * 构建树
	 */
	function rebuildTree()
	{
		sf::getModel("Guides",0,$this->year)->rebuildTree();
		exit("<script>parent.location.reload();</script>");
	}
	
	/**
	 * 数据编辑
	 */
	function edit()
	{
		$category = sf::getModel("Guides",input::getInput("mix.id"),$this->year);
        if(input::getInput("get.pid") && $category->isNew()){
            $category->setParentId(input::getInput("get.pid"));
            $parent = $category->getParent();
            $category->setCatId($parent->getCatId());
            $category->setYear($parent->getYear());
            $category->setStartAt($parent->getStartAt());
            $category->setEndAt($parent->getEndAt());
            $category->setMarker($parent->getMarker());
            $category->setCurrentGroup($parent->getCurrentGroup());
            $category->setOrders($parent->getNewOrders());
            $category->setDeclareMapId($parent->getDeclareMapId());
        }
		if(input::getInput("post.subject"))
		{
			$_data = array();
			if($category->isNew()) $category->setGuideid(sf::getLib("MyString")->getRandString());
			input::getInput("post.subject") && $category->setSubject(input::getInput("post.subject"));
			$category->setProjectName(input::post("project_name"));//固定项目名称
			input::post("cat_id") && $category->setCatId(input::post("cat_id"));
			$category->setMarker(input::post("marker"));
			$category->setParentId(input::getInput("post.parent_id") ? input::getInput("post.parent_id") : 0);
			input::getInput("post.orders") && $category->setOrders(input::getInput("post.orders"));
			input::getInput("post.content") && $category->setContent(input::getInput("post.content"));
			$category->setIsDirectional(input::post("is_directional")?1:0);//是否定向
			//技术领域
			$category->setSkillBranch(input::post("skill_branch"));
			if(input::post("skill_son"))
				$_data['skill_branch'] = implode('|',(array)input::post("skill_branch"));
			
			if(input::post("skill_id")){
				$category->setSkillId(input::post("skill_id"));
				if(input::post("skill_son"))
					$_data['skill_id'] = input::post("skill_id");
			}
			if(input::getInput("post.year")){
				$category->setYear(input::getInput("post.year"));
				$_data['year'] = input::getInput("post.year");
			}
			if(input::post("user_level")){
				$category->setUserLevel(input::post("user_level"));
				$_data['user_level'] = input::post("user_level");
			}
			if(input::getInput("post.current_group")){
				$category->setCurrentGroup(input::getInput("post.current_group"));
				$_data['current_group'] = input::getInput("post.current_group");
			}
			if(input::getInput("post.start_at")){
				$category->setStartAt(input::getInput("post.start_at"));
				$_data['start_at'] = input::getInput("post.start_at");
			}
			if(input::getInput("post.end_at")){
				$category->setEndAt(input::getInput("post.end_at"));
				$_data['end_at'] = input::getInput("post.end_at");
			}
			if(input::getInput("post.gather_end_at")){
				$category->setGatherEndAt(input::getInput("post.gather_end_at"));
				$_data['gather_end_at'] = input::getInput("post.gather_end_at");
			}
			if(input::getInput("post.assess_id")){//评审指标
				$rule = sf::getModel("Rules",input::getInput("post.assess_id"));
				$category->setAssessId($rule->getId());
				$category->setAssessPath("assess/rule".$rule->getCode());
				$_data['assess_id'] = $rule->getId();
				$_data['assess_path'] = "assess/rule".$rule->getCode();
			}
			if(input::getInput("post.declare_map_id")){//申报书
				$type = sf::getModel("Types",input::getInput("post.declare_map_id"));
				$category->setDeclareMapId($type->getId());
				$category->setApplyPath($type->getProjectPath());
				$_data['declare_map_id'] = $type->getId();
				$_data['apply_path'] = $type->getProjectPath();
			}
			if(input::getInput("post.task_map_id")){//任务书
				$book = sf::getModel("EngineWorkers",input::getInput("post.task_map_id"));
				$category->setTaskMapId($book->getId());
				$category->setTaskPath($book->getPath());
				$_data['task_map_id'] = $book->getId();
				$_data['task_path'] = $book->getPath();
			}
			if(input::getInput("post.budget_map_id")){//预算书
				$book = sf::getModel("BookMaps",input::getInput("post.budget_map_id"));
				$category->setBudgetMapId($book->getId());
				$category->setBudgetPath($book->getPath());
				$_data['budget_map_id'] = $book->getId();
				$_data['budget_path'] = $book->getPath();
			}
			if(input::getInput("post.complete_map_id")){//验收书
                $book = sf::getModel("EngineWorkers",input::getInput("post.complete_map_id"));
                $category->setCompleteMapId($book->getId());
                $category->setCompletePath($book->getPath());
				$_data['complete_map_id'] = $book->getId();
				$_data['complete_path'] = $book->getPath();
			}
			if(input::post("project_start_at") != ''){
				$category->setProjectStartAt(input::post("project_start_at"));
				$_data['project_start_at'] = input::post("project_start_at");
			}
			if(input::post("project_end_at") != ''){
				$category->setProjectEndAt(input::post("project_end_at"));
				$_data['project_end_at'] = input::post("project_end_at");
			}
			if(input::post("month_min") != ''){
				$category->setMonthMin(input::post("month_min"));
				$_data['month_min'] = input::post("month_min");
			}
			if(input::post("month_max") != ''){
				$category->setMonthMax(input::post("month_max"));
				$_data['month_max'] = input::post("month_max");
			}
			if(input::getInput("post.office_id")){//归属处室
				$category->setOfficeId(input::getInput("post.office_id"));
				$_data['office_id'] = input::getInput("post.office_id");
			}
			
			if(input::getInput("post.allow_grade")){//负责人设置
				$category->setAllowGrade(input::getInput("post.allow_grade"));
				$_data['allow_grade'] = implode('|',input::post("allow_grade"));
			}
			if(input::post()){//单位设置
				$category->setAllowCompany(input::post("allow_company"));
				if(input::post('allow_company_son')) $_data['allow_company'] = implode('|',input::post("allow_company"));
			}

            if(input::post()){//特殊性质
				$category->setProperty(input::post("property"));
				if(input::post('property_son')) $_data['property'] = implode('|',input::post("property"));
			}
            //单位限制
            $category->setCorporationIds(input::post('corporation_id')?:[]);
            //人员限制
            $chose = input::post('chose');
            if($chose){
                $chose_arr=explode(",",$chose);
                $declarerIds=[];
                foreach($chose_arr as $choose){
                    $declarerIds[]=explode("?",$choose)[0];
                }
                $category->setUserIds($declarerIds);
            }else{
                $category->setUserIds([]);
            }
            //审核人员
            $category->setOfficerIds(input::post('officer_id')?:[]);
            $category->setFinanceIds(input::post('finance_id')?:[]);
            //附件
            $category->setFile(input::post('file'));
            $category->setUpdatedAt(date("Y-m-d H:i:s"));
            $category->save();
            $parent = $category->getParent();
            if(!$parent->isNew()){
                $parent->setIsLastnode($parent->getIsLastnode()?1:0);
                $parent->save();
            }

            //是否修改父类
			if(input::getInput("post.set_parent")){
				$category->setPathAttribute($_data);
			}

            //是否修改子类
			if(input::getInput("post.set_son")){
				$category->setTreeAttribute($_data);	
			}
			addHistory($category->getId(),lang::get('The guide has been edit!'),'guides');



            if(input::post('is_release')==2){
                $this->jump(site_url('admin/guide/preview/guideid/'.$category->getGuideId()));
            }
            if(input::post('is_release')==1){
                $url = site_url('admin/guide/index/year/'.$category->getYear());
                if($category->getCatId()==896){
                    $url = site_url('admin/guide/award/year/'.$category->getYear());
                }
                //更新指南树
                sf::getModel("Guides",0,$this->year)->rebuildTree();
                $this->success("指南已发布",$url);
            }
            $this->success("草稿已更新",site_url('admin/guide/edit/year/'.$category->getYear())."/id/".$category->getId());

		}
		view::set("category",$category);
		view::set("pid",input::getInput("get.pid") ? input::getInput("get.pid") : 0);
		view::set("parent_data",$category->selectAll('','',0));
		view::apply("inc_body","admin/guide/edit");
		view::display("page_main");
	}

    function preview()
    {
        $guide = sf::getModel("Guides")->selectByGuideId(input::mix('guideid'));
        view::set('guide',$guide);
        view::apply('inc_body','admin/guide/preview');
        view::display('page_main');
    }
	
	/**
	 * 删除数据
	 */
	function delete()
	{
        if(input::session('userlevel')>1) $this->error('删除功能已关闭！');
		sf::getModel("Guides",input::getInput("mix.id"),$this->year)->remove();
		sf::getModel("Historys")->addHistory(input::getInput("mix.id"),lang::get('The guide has been delete!'),'Guides');
		$this->page_debug(lang::get("Has been deleted!"),getFromUrl());
	}
	
	/**
	 * 申报书配置
	 */
	function config()
	{
		$category = sf::getModel("Guides",input::getInput("mix.id"),$this->year);
		//取得附件模块
		if(!$worker_id = $category->types()->getWorkerId()) $this->page_debug("该指南不支持配置(或没有指定申报书)",getFromUrl());
		
		if(input::post())
		{ 
			$widget_name = input::post("widget_name");
			$category->setConfigs($widget_name,input::post("configs"));
			$category->save();
			$_data = array();
			//是否修改子类
			if(input::post("set_son")){
				//$_data['configs'] = json_encode($category->getConfigs(),JSON_UNESCAPED_UNICODE);
				//批量设置子栏目
				//$category->setTreeAttribute($_data);
				//取得所有子类
				$pager = $category->selectSonTree();
				while($obj = $pager->getObject()){
					$obj->setConfigs($widget_name,input::post("configs"));
					$obj->save();	
				}	
			}
			exit("<script>parent.location.reload();</script>");
		}
		//模块列表
		$widgets = sf::getModel("EngineWorkerWidgets")->selectAll("engine_worker_id = '".$worker_id."' and engine_widget_id in ('2','4','8','9') ","ORDER BY engine_widget_id DESC");
		if($widgets->getTotal() < 0) $this->page_debug("模块未找到！",getFromUrl());
		//获取模块
		$widgetid = input::mix("widgetid") ? input::mix("widgetid") : 0;
		if($widgetid){
			$widget = sf::getModel("EngineWorkerWidgets",$widgetid);
		}else{
			$widget = $widgets->getObject();
			$widgets->reset();
		}
		if($widget->isNew()) $this->page_debug("模块未找到！",getFromUrl());
		
		view::set("widgets",$widgets);
		view::set("widgetname",$widget->getWidgetName());
		view::set("widgetid",$widget->getId());
		view::set("widgetHtml",$widget->manager($category->getConfigs($widget->getWidgetName())));
		view::set("category",$category);
		view::apply("inc_body","admin/guide/Config");
		view::display("page_blank");
	}
	
	/**
	 * 将指南下面的指南复制到指定的指南下面
	 */
	function docopy()
	{
		$from 	= sf::getModel("Guides",input::mix("from"),$this->year);
		if($from->isNew()) $this->page_debug("指南未找到！",getFromUrl());
		if(input::post()){
			$from->copyto(input::post("to"));
			exit("<script>parent.location.reload();</script>");
		}
		view::set("category",$from);
		view::set("parent_data",$from->selectAll('','',0));
		view::apply("inc_body","admin/guide/docopy");
		view::display("page_blank");
	}

    function deleteFile()
    {
        $guide = sf::getModel("Guides",input::getInput("mix.aid"));
        $file = $guide->getFile();
        $fileArr = explode(',',$file);
        if(is_array($fileArr)){
            foreach ($fileArr as $k=>&$item){
                if($item==input::getInput('mix.fid'))
                    unset($fileArr[$k]);
            }
        }
        $fileStr = implode(',',$fileArr);
        $guide->setFile($fileStr);
        $guide->save();
        $file = sf::getModel('Filemanager',input::getInput("mix.fid"));
        $result = $file->remove(input::getInput("mix.fid"),true);
        if($result===true){
            $this->success('删除成功',getFromUrl());
        }else{
            $this->error('删除失败',getFromUrl());
        }

    }
    
    function grab()
    {
        view::apply("inc_body","admin/guide/grab");
        view::display("page");
    }

    function doGrab()
    {
        $content = curlGet("https://kjt.sc.gov.cn/kjt/gstz/newschild.shtml");
        $rule = '<div class="column-list" style="min-height:500px;">[内容]</ul>';
        $result = GetHtmlArea("[内容]",$rule,$content);
        $result = preg_match_all('/<a href="(.*?)".*<\/a>/',$result,$matches);
        if(count($matches[1])==0){
            $this->error('抓取内容失败，未获取到数据');
        }
        $topUrl = 'https://kjt.sc.gov.cn';
        $pageUrls = [];
        foreach ($matches[1] as $url){
            $pageUrls[] = $topUrl.$url;
        }
        $_SESSION['grab_pages'] = $pageUrls;
        $this->success('共获取到'.count($matches[1]).'条数据，现在开始获取第1条...',site_url('admin/guide/grabPage/no/1'));
    }

    function grabPage()
    {
        $no = input::getMix('no');
        if(empty($no)){
            $this->success('未获取到数据编号！',site_url('admin/guide/grab'));
        }
        if(empty($_SESSION['grab_pages'])){
            $this->success('获取完毕！',site_url('admin/article/index'));
        }
        $index = $no-1;
        $pageUrl = $_SESSION['grab_pages'][$index];
        $pageContent = curlGet($pageUrl);
        $rule = '<h1>[内容]</h1>';
        $title = GetHtmlArea("[内容]",$rule,$pageContent);
        $rule = '<div class="newsCon">[内容]</div>';
        $content = GetHtmlArea("[内容]",$rule,$pageContent);
        $article = sf::getModel('Articles');
        $article->setSubject($title);
        $article->setChannelIds([1]);
        $article->setContent($content);
        $article->setCreatedAt(date('Y-m-d H:i:s'));
        $article->setUpdatedAt(date('Y-m-d H:i:s'));
        $article->save();
        if($no>=count($_SESSION['grab_pages'])){
            unset($_SESSION['grab_pages']);
            $this->success('获取完毕！',site_url('admin/article/index'));
        }
        $nextNo = $no+1;
        $this->success('获取第'.$no.'条数据成功，现在获取第'.$nextNo.'条...',site_url('admin/guide/grabPage/no/'.$nextNo),'_self','',1);
    }

    public function autoGrab()
    {
        if($grab = input::post('grab')){
            createNewProcess('cmd/grab');
            exit("<script>top.location.href=top.location.href+'/_save/yes/_msg/定时获取已开启！';</script>");
        }
        view::apply("inc_body","admin/guide/auto_grab");
        view::display("page_blank");
    }
}
?>