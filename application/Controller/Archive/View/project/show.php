<style>
    .table td{
        word-break: break-all;
    }
</style>
<script language="javascript" type="text/javascript">
    var more = {
        toggle:function(project_id,fund_code){
            $('#show_'+fund_code).toggle();
            if(!$('#show_'+fund_code).is(":hidden")){
                $('#show_'+fund_code).children().html('数据加载中...');
                $('#show_'+fund_code).children().load('<?=site_url("office/payment/morePayment/fund_code/")?>'+'/'+fund_code+'/project_id/'+project_id);
            }
        }
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            项目资料
        </h2>
        <div class="content-options">
            <?=Button::setName('返回')->back()?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <!-- Block Tabs Default Style -->
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block" data-toggle="tabs" role="tablist">
                    <?php $i=0;foreach($tabs as $tab):?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method || $i==0)echo 'nav-link active';else echo 'nav-link'; ?>" href="#<?=$tab['method']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-pane active pt-5" id="base">
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getSubtitle()?></p>
                            </div>
                        </div>
                        <?php
                        if($project->getPattern()=='课题'):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">课题名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getSubject()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <?php
                        if($project->getSpecialSubject()):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">专项名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getSpecialSubject()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目大类</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getCatSubject()?></p>
                            </div>
                        </div>
                        <?php
                            if($project->getCatId()==17):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目类型</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?= $project->getTypeSubject() ?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目编号</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getAcceptId()?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">申报年度</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getDeclareYear()?></p>
                            </div>
                        </div>
                        <?php
                        if(in_array($project->getStatement(),[29,30])):?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">立项年度</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getRadicateYear()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目状态</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <?=$project->getState()?>
                            </div>
                        </div>
                        <?php
                        if($project->getCatId()==16):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目来源</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getProjectLevel()?><?=$project->getProjectSource()?'（'.$project->getProjectSource().'）':''?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">承担类型</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getCompanyRoleStr()?></p>
                            </div>
                        </div>
                        <?php
                        if($project->getCompanyRole()=='B'):
                            ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">牵头单位名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getLeadCorporationName()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">承担单位</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <?=$project->getTopCorporationName()?>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">承担部门</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><a href="<?=site_url('unit/profile/show/userid/'.$project->getCorporationId())?>" target="_blank"><?=$project->getCorporationName()?></a></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">合作单位</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getCooperationStr()?></p>
                            </div>
                        </div>
                        <?php
                        if($project->getCatId()==16):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目类型</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getProjectType()?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">经费补助类型</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getGrantType()?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">是否为标准修订/制定项目</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getIsStandardStr()?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">是否为下属公司独立承担项目（二所未参与）</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getIsOwnStr()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <?php
                        if($project->getCatId()==17):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">合同金额（万元）</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getTotalMoney()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <?php
                        if($project->getCatId()==17 && $project->getTypeSubject()=='普通横向项目'):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">甲方单位名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getBaseinfo('apply')->getData('parta')?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">甲方统一社会信用代码</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getBaseinfo('apply')->getData('parta_code')?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">甲方单位性质</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getBaseinfo('apply')->getData('parta_propery')?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <?php
                        if($project->getCatId()==17 && $project->getTypeSubject()=='外部揭榜挂帅项目'):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">发榜单位名称</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getBaseinfo('apply')->getData('parta')?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">发榜单位性质</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getBaseinfo('apply')->getData('parta_propery')?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目负责人</label>
                            <div class="col-xs-12 col-sm-8 col-md-8">
                                <p class="help-block"><a href="<?=site_url('user/profile/show/userid/'.$project->getUserId())?>" target="_blank"><?=$project->getUserName()?></a></p>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">科研助理</label>
                            <div class="col-xs-12 col-sm-8 col-md-8">
                                <p class="help-block"><?=$project->getAssistantNameStr()?></p>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目周期</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getStartAt()?>-<?=$project->getEndAt()?></p>
                            </div>
                        </div>
                        <?php
                        if($project->getStatement()==30):
                        ?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">验收日期</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <p class="help-block"><?=$project->getCompleteApplyAt()?></p>
                            </div>
                        </div>
                        <?php endif;?>
                        <div class="form-group row">
                            <label class="control-label text-right col-xs-12 col-sm-3 col-md-3">项目概述</label>
                            <div class="col-xs-12 col-sm-5 col-md-8">
                                <?= showText($project->getSummary()) ?>
                            </div>
                        </div>
                        <p style="clear:both"></p>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="member">
                        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="table table-bordered">
                            <thead><tr>
                                <td width="5%" align="center">序号</td>
                                <td height="36" align="center" width="80">姓名</td>
                                <td height="36" width="60" align="center">性别</td>
                                <td align="center">证件类型</td>
                                <td align="center">证件号</td>
                                <td align="center">出生日期</td>
                                <td align="center">学历</td>
                                <td align="center">职称</td>
                                <td align="center">专业</td>
                                <td align="center">单位/部门</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $members = $project->members($project->getMemberType());
                            while($member = $members->getObject()):
                            ?>
                            <tr>
                                <td height="35" align="center"><?=$members->getIndex()?></td>
                                <td height="36" align="center">
                                    <?php
                                        if($member->getUserId()):
                                    ?>
                                            <a href="<?=site_url('user/profile/show/userid/'.$member->getUserId())?>" target="_blank"><?=$member->getName()?></a>
                                    <?php else:?>
                                            <?=$member->getName()?>
                                    <?php endif;?>
                                </td>
                                <td align="center"><?=$member->getSex()?></td>
                                <td align="center"><?=$member->getCardType()?></td>
                                <td align="center"><?=getMask($member->getCertificate(),12,3)?></td>
                                <td align="center"><?=$member->getBirthday()?></td>
                                <td align="center"><?=$member->getEducation()?></td>
                                <td align="center"><?=$member->getTitleType()?></td>
                                <td align="left"><?=$member->getMarjor()?></td>
                                <td align="left"><?=$member->getCompanyName()?></td>
                            </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="target">
                        <?php
                        $html = $project->widgets('target');
                        echo str_replace('class="abc"','class="table table-bordered"',$html);
                        ?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="plan">
                        <?php
                        $html = $project->widgets('schedule');
                        echo str_replace('class="abc"','class="table table-bordered"',$html);
                        ?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="money">
                        <?php
                        $moneyType = $project->getMoneyType();
                        ?>
                        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="table table-bordered" style="overflow:wrap">
                            <thead>
                            <tr>
                                <th colspan="3">经费来源<small style=" float:right;">（单位：万元）</small></th>
                            </tr>
                            </thead>
                            <tr>
                                <td height="36" width="87%" align="center" class="text-center"><strong>科目名称</strong></td>
                                <td width="15%" align="center" class="text-center"><strong>经费</strong></td>
                            </tr>
                            <tr>
                                <td height="36"><strong>1、专项经费</strong></td>
                                <td align="center"><?=$project->fund('declare_money',$moneyType,'in')->getAmount()?></td>
                            </tr>
                            <tr>
                                <td height="36"><strong>2、自筹经费</strong></td>
                                <td align="center"><?=$project->fund('own_money',$moneyType,'in')->getAmount()?></td>
                            </tr>
                            <tr>
                                <td height="36"><strong>3、其他经费</strong></td>
                                <td align="center"><?=$project->fund('other_money',$moneyType,'in')->getAmount()?></td>
                            </tr>
                            <tr>
                                <td height="36"><strong>项目总经费</strong></td>
                                <td align="center"><?=$project->fund('total_money',$moneyType,'in')->getAmount()?></td>
                            </tr>
                        </table>
                        <br>
                        <table width="680" border="0" align="center" cellpadding="3" cellspacing="0" class="table table-bordered" style="overflow:wrap">
                            <thead>
                            <tr>
                                <th colspan="5">经费支出<small style=" float:right;">（单位：万元）</small></th>
                            </tr>
                            </thead>
                            <tr>
                                <td height="36" align="center" class="text-center"><strong>科目名称</strong></td>
                                <td width="15%" align="center" class="text-center"><strong>专项经费</strong></td>
                                <td width="15%" align="center" class="text-center"><strong>自筹经费</strong></td>
                                <td width="15%" align="center" class="text-center"><strong>其他经费</strong></td>
                                <td width="15%" align="center" class="text-center"><strong>合计</strong></td>
                            </tr>
                            <?php
                            $fundTpl = $project->getFundTpl()?:'kjt';
                            $indexs = getFunds('code',false,$fundTpl,4);
                            foreach ($indexs as $indexCode=>$indexName):
                                ?>
                                <tr>
                                    <td height="36"><strong><?= $indexName?></strong></td>
                                    <td align="center"><?= $project->fund($indexCode,$moneyType)->getAmountCz()?></td>
                                    <td align="center"><?= $project->fund($indexCode,$moneyType)->getAmountZc()?></td>
                                    <td align="center"><?= $project->fund($indexCode,$moneyType)->getAmountQt()?></td>
                                    <td align="center"><?= $project->fund($indexCode,$moneyType)->getAmount()?></td>
                                </tr>
                            <?php endforeach;?>
                            <tr>
                                <td height="36"><strong>合计</strong></td>
                                <td align="center"><?=$project->fund('total',$moneyType)->getAmountCz()?></td>
                                <td align="center"><?=$project->fund('total',$moneyType)->getAmountZc()?></td>
                                <td align="center"><?=$project->fund('total',$moneyType)->getAmountQt()?></td>
                                <td align="center"><?=$project->fund('total',$moneyType)->getAmount()?></td>
                            </tr>
                        </table>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="income">
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-bordered">
                            <thead>
                            <tr>
                                <th colspan="8">经费到账总体情况</th>
                            </tr>
                            </thead>
                            <tr>
                                <th class="text-center">总经费</th>
                                <td class="text-center"><?=$project->getTotalMoney()?>万元</td>
                                <th class="text-center">专项经费</th>
                                <td class="text-center"><?=$project->getDeclareMoney()?>万元</td>
                                <th class="text-center">到账经费</th>
                                <td class="text-center"><?=$project->getIncomeMoney()?>万元</td>
                                <th class="text-center">到账比例</th>
                                <td class="text-center"><?=$project->getIncomePercent()?></td>
                            </tr>
                        </table>
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-bordered">
                            <thead>
                            <tr>
                                <th colspan="4">经费到账记录
                                    <?php
                                    if(in_array(input::session('userlevel'),[5,6])):
                                    ?>
                                    <span class="float-right"><?=Button::setUrl(site_url("income/project/apply/edit/pid/".$project->getProjectId()))->setIcon('add')->setClass('btn-alt-success')->link('到账登记')?></span>
                                    <?php endif;?>
                                </th>
                            </tr>
                            <tr>
                                <th>到账金额</th>
                                <th>到账日期</th>
                                <th>经办人</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $incomes = $project->selectIncomes();
                            while($income = $incomes->getObject()):
                                $project = $income->getProject(true);
                                ?>
                                <tr>
                                    <td><?=$income->getMoney('万元')?> 万元</td>
                                    <td><?=$income->getIncomeAt()?></td>
                                    <td><?=$income->getUserName()?></td>
                                    <td><?=Button::setUrl(site_url('income/project/apply/show/id/'.$income->getIncomeId()))->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="payment">
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-bordered">
                            <thead>
                            <tr>
                                <th colspan="8">经费支出总体情况</th>
                            </tr>
                            </thead>
                            <tr>
                                <th class="text-center">总经费</th>
                                <td class="text-center"><?=$project->getTotalMoney()?>万元</td>
                                <th class="text-center">专项经费</th>
                                <td class="text-center"><?=$project->getDeclareMoney()?>万元</td>
                                <th class="text-center">自筹经费</th>
                                <td class="text-center"><?=$project->getOwnMoney()?>万元</td>
                            </tr>
                            <tr>
                                <th class="text-center">已支出</th>
                                <td class="text-center"><?=$project->getUseMoney()?>万元</td>
                                <th class="text-center">已支出</th>
                                <td class="text-center"><?=$project->getUseCzMoney()?>万元</td>
                                <th class="text-center">已支出</th>
                                <td class="text-center"><?=$project->getUseZcMoney()?>万元</td>
                            </tr>
                            <tr>
                                <th class="text-center">支出比例</th>
                                <td class="text-center"><?=$project->getUsePercent()?></td>
                                <th class="text-center">支出比例</th>
                                <td class="text-center"><?=$project->getUseCzPercent()?></td>
                                <th class="text-center">支出比例</th>
                                <td class="text-center"><?=$project->getUseZcPercent()?></td>
                            </tr>
                        </table>
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-bordered">
                            <thead>
                            <tr>
                                <th colspan="5">经费支出明细</th>
                            </tr>
                            <tr>
                                <th width="50">详</th>
                                <th width="40%">科目名称</th>
                                <th>总经费（万元）</th>
                                <th>已支出（万元）</th>
                                <th>剩余（万元）</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $category = $project->getFundCategory();
                            $funds = sf::getModel('ProjectFunds')->selectAll("category = '{$category}' and project_id = '".$project->getProjectId()."' and type = 'out' and fund_readonly=0 and amount > 0","order by fund_orders asc");
                            while($fund = $funds->getObject()):
                                ?>
                                <tr>
                                    <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="more.toggle('<?=$project->getProjectId()?>','<?=$fund->getFundCode()?>')">+</label></td>
                                    <td><?=$fund->getFundName()?></td>
                                    <td><?=(float)$fund->getAmount()?></td>
                                    <td><?=(float)$fund->getUsed()?></td>
                                    <td><?=(float)$fund->getBalance()?></td>
                                </tr>
                                <tr id="show_<?=$fund->getFundCode()?>" style="display:none;">
                                    <td colspan="5"></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-bordered">
                            <thead>
                            <tr>
                                <th colspan="5">经费支出记录</th>
                            </tr>
                            <tr>
                                <th>支出科目</th>
                                <th>支出金额</th>
                                <th>登记日期</th>
                                <th>经办人</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $payments = $project->payments(20);
                            while($payment = $payments->getObject()):
                                ?>
                                <tr>
                                    <td><?=$payment->getMarkSubject()?></td>
                                    <td><?=$payment->getMoney('万元')?> 万元</td>
                                    <td><?=$payment->getCreatedAt('Y-m-d')?></td>
                                    <td><?=$payment->getOperatorName()?></td>
                                    <td><?=Button::setUrl(site_url('apply/payment/show/id/'.$payment->getPaymentId()))->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="fruit">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">产品</td>
                            </tr>
                            <tr>
                                <td style="width: 30%">产品名称</td>
                                <td style="width: 10%">产品类型</td>
                                <td>产品号</td>
                                <td style="width: 10%">状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $products = $project->selectProducts();
                            while($product = $products->getObject()):
                                $_product = $product->getProduct(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/product/show/id/'.$_product->getProductId())?>" target="_blank"><?=$_product->getSubject()?></a></td>
                                <td><?=$_product->getType()?></td>
                                <td><?=$_product->getSn()?></td>
                                <td><?=$_product->getState()?></td>
                            </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">专利</td>
                            </tr>
                            <tr>
                                <td style="width: 30%">专利名称</td>
                                <td style="width: 10%">专利类型</td>
                                <td>专利号</td>
                                <td style="width: 10%">状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $patents = $project->selectPatents();
                            while($patent = $patents->getObject()):
                            $_patent = $patent->getPatent(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/patent/show/id/'.$_patent->getPatentId())?>" target="_blank"><?=$_patent->getSubject()?></a></td>
                                <td><?=$_patent->getType()?></td>
                                <td><?=$_patent->getSn()?></td>
                                <td><?=$_patent->getState()?></td>
                            </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">软件著作权</td>
                            </tr>
                            <tr>
                                <td style="width: 30%">软件全称</td>
                                <td style="width: 10%">登记号</td>
                                <td>登记日期</td>
                                <td style="width: 10%">状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $softs = $project->selectSofts();
                            while($soft = $softs->getObject()):
                                $_soft = $soft->getSoft(true);
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('user/soft/show/id/'.$_soft->getSoftId())?>" target="_blank"><?=$_soft->getSubject()?></a></td>
                                    <td><?=$_soft->getSn()?></td>
                                    <td><?=$_soft->getRegisterAt()?></td>
                                    <td><?=$_soft->getState()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="5">科技论文</td>
                            </tr>
                            <tr>
                                <td width="30%">论文题目</td>
                                <td>刊物名称</td>
                                <td>刊物级别</td>
                                <td>作者</td>
                                <td style="width: 10%">状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $papers = $project->selectPapers();
                            while($paper = $papers->getObject()):
                                $_paper = $paper->getPaper(true);
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('user/paper/show/id/'.$_paper->getPaperId())?>" target="_blank"><?=$_paper->getSubject()?></a></td>
                                    <td><?=$_paper->getPublication()?></td>
                                    <td><?=$_paper->getLevel()?></td>
                                    <td><?=$_paper->getUserNames()?></td>
                                    <td><?=$_paper->getState()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="5">标准</td>
                            </tr>
                            <tr>
                                <td>标准名称</td>
                                <td>标准号</td>
                                <td>标准类别</td>
                                <td>发布形式</td>
                                <td style="width: 10%">状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $standards = $project->selectStandards();
                            while($standard = $standards->getObject()):
                                $_standard = $standard->getStandard(true);
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('user/standard/show/id/'.$_standard->getStandardId())?>" target="_blank"><?=$_standard->getSubject()?></a></td>
                                    <td><?=$_standard->getSn()?></td>
                                    <td><?=$_standard->getType()?></td>
                                    <td><?=$_standard->getWay()?></td>
                                    <td><?=$_standard->getState()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="5">成果转化</td>
                            </tr>
                            <tr>
                                <td>成果名称</td>
                                <td>首单转化日期</td>
                                <td>成果水平</td>
                                <td>转化方式</td>
                                <td>状态</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $transforms = $project->selectTransforms();
                            while($transform = $transforms->getObject()):
                                $_transform = $transform->getTransform(true);
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('user/transform/show/id/'.$_transform->getTransformId())?>" target="_blank"><?=$_transform->getSubject()?></a></td>
                                    <td><?=$_transform->getFirstOrderAt()?></td>
                                    <td><?=$_transform->getCgsp()?></td>
                                    <td><?=$_transform->getType()?></td>
                                    <td><?=$_transform->getState()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="attachment">
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">申报
                                    <?php
                                    if(in_array(input::session('userlevel'),[2,3,4,5,6])):
                                    ?>
                                    <span class="float-right"><?=Button::setUrl(site_url('user/applysupplement/index/id/'.$project->getProjectId()))->setIcon('upload')->setClass('btn-alt-success')->window('补传')?></span>
                                    <?php endif;?>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 30%">附件名称</td>
                                <td style="width: 30%">附件类型</td>
                                <td>附件说明</td>
                                <td style="width: 10%">操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td><a href="<?=site_url('apply/project/show/id/'.$project->getProjectId())?>" target="_blank">申报书</a></td>
                                <td>申报书</td>
                                <td>申报书</td>
                                <td><?=Button::setUrl(site_url('apply/project/show/id/'.$project->getProjectId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $itemType = 'project_sbs';
                            if($project->getCatId()==15) $itemType = 'apply';
                            $files = $project->getAttachments($itemType,1,'order by id desc');
                            while($file = $files->getObject()):
                            ?>
                            <tr>
                                <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                <td>申报书附件</td>
                                <td><?=$file->getFileNote()?></td>
                                <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php endwhile;?>
                            <?php if($files->getTotal()==0 && $project->getCatId()==15):?>
                            <tr>
                                <td><a href="<?=site_url('engine/worker/output/id/'.$project->getProjectId())?>" target="_blank">申报书</a></td>
                                <td>申报书PDF版</td>
                                <td>申报书PDF版</td>
                                <td><?=Button::setUrl(site_url('engine/worker/output/id/'.$project->getProjectId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php endif;?>
                            <?php
                            $files = $project->getAttachments('project_word',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>申报书Word版</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php if($project->getCatId()==15):
                            $files = $project->getAttachments('project_spqk',0,'order by id desc');
                            while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>申请书中审批情况页盖章件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endif;?>
                            <?php if($project->getCatId()==15):
                            $files = $project->getAttachments('project_hzxy',0,'order by id desc');
                            while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>体现项目负责人科技创新能力的证明材料</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endif;?>
                            <?php
                            $joints = $project->getJoints();
                            while($joint = $joints->getObject()):
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/joint/show/id/'.$joint->getJointId())?>" target="_blank"><?=$joint->getSubject()?></a></td>
                                <td>联合申报协议</td>
                                <td>联合申报协议</td>
                                <td><?=Button::setUrl(site_url('user/joint/show/id/'.$joint->getJointId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $joint->getAttachment();
                            while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>联合申报协议附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                                $files = $project->getAttachments('project_joint',0,'order by id desc');
                                while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>联合申报协议附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                                $files = $project->getAttachments('project_qt',0,'order by id desc');
                                while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>其他附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">执行
                                    <?php
                                    if(in_array(input::session('userlevel'),[2,3,4,5,6])):
                                    ?>
                                    <span class="float-right"><?=Button::setUrl(site_url('user/tasksupplement/index/id/'.$project->getProjectId()))->setIcon('upload')->setClass('btn-alt-success')->window('补传')?></span>
                                    <?php endif;?>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 30%">附件名称</td>
                                <td style="width: 30%">附件类型</td>
                                <td>附件说明</td>
                                <td style="width: 10%">操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            if($project->getConfigs("path.task")):
                            ?>
                            <tr>
                                <td><a href="<?=site_url('apply/task/show/id/'.$project->getProjectId())?>" target="_blank">任务书</a></td>
                                <td>任务书</td>
                                <td>任务书</td>
                                <td><?=Button::setUrl(site_url('apply/task/show/id/'.$project->getProjectId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php endif;?>
                            <?php
                            $files = $project->getAttachments('task_rws',0,'order by id desc');
                            while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>任务书附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('task_qt',0,'order by id desc');
                            while($file = $files->getObject()):
                            ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>任务书其他附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $changes = $project->changes();
                            while($change = $changes->getObject()):
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('apply/change/show/id/'.$change->getId())?>" target="_blank"><?=$change->getProjectName()?></a></td>
                                    <td>项目变更申请</td>
                                    <td><?=$change->getChangeTypeName()?></td>
                                    <td><?=Button::setUrl(site_url('apply/change/show/id/'.$change->getId()))->setIcon('show')->link('查看')?></td>
                                </tr>
                                <?php
                                $files = $change->attachments();
                                while($file = $files->getObject()):
                                    ?>
                                    <tr>
                                        <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                        <td>项目变更申请附件</td>
                                        <td><?=$file->getFileNote()?></td>
                                        <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                    </tr>
                                <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $startup = $project->startup();
                            if(!$startup->isNew()):
                            ?>
                            <tr>
                                <td><a href="<?=site_url('startup/apply/show/id/'.$startup->getProjectId())?>" target="_blank">开题报告</a></td>
                                <td>开题报告</td>
                                <td>开题报告</td>
                                <td><?=Button::setUrl(site_url('startup/apply/show/id/'.$startup->getProjectId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $startup->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>开题报告附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endif;?>
                            <?php
                            $files = $project->getAttachments('task_startup',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>开题报告附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $inspect = $project->inspect();
                            if(!$inspect->isNew()):
                                ?>
                                <tr>
                                    <td><a href="<?=site_url('inspect/apply/show/id/'.$inspect->getProjectId())?>" target="_blank">中期检查报告</a></td>
                                    <td>中期检查报告</td>
                                    <td>中期检查报告</td>
                                    <td><?=Button::setUrl(site_url('inspect/apply/show/id/'.$inspect->getProjectId()))->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php
                            $files = $inspect->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>中期检查报告附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endif;?>
                            <?php
                            $files = $project->getAttachments('task_inspect',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>中期检查附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $stages = $project->selectStages();
                            while($stage = $stages->getObject()):
                            ?>
                            <tr>
                                <td><a href="<?=site_url('stage/apply/show/id/'.$stage->getStageId())?>" target="_blank"><?=$stage->getSubject()?></a></td>
                                <td>年度报告</td>
                                <td><?=$stage->getStageYear()?>年度</td>
                                <td><?=Button::setUrl(site_url('stage/apply/show/id/'.$stage->getStageId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $stage->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>年度报告附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('task_stage',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>年度报告附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('task_execute',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>执行中的过程文档</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $products= $project->selectProducts();
                            while($product = $products->getObject()):
                                $_product = $product->getProduct(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/product/show/id/'.$_product->getProductId())?>" target="_blank"><?=$_product->getSubject()?></a></td>
                                <td>成果-产品</td>
                                <td>产品</td>
                                <td><?=Button::setUrl(site_url('user/product/show/id/'.$_product->getProductId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_product->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-产品</td>
                                    <td><?=$file->getFileNote()?:'产品附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $patents= $project->selectPatents();
                            while($patent = $patents->getObject()):
                                $_patent = $patent->getPatent(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/patent/show/id/'.$_patent->getPatentId())?>" target="_blank"><?=$_patent->getSubject()?></a></td>
                                <td>成果-专利</td>
                                <td>专利</td>
                                <td><?=Button::setUrl(site_url('user/patent/show/id/'.$_patent->getPatentId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_patent->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-专利</td>
                                    <td><?=$file->getFileNote()?:'专利附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $softs= $project->selectSofts();
                            while($soft = $softs->getObject()):
                                $_soft = $soft->getSoft(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/soft/show/id/'.$_soft->getSoftId())?>" target="_blank"><?=$_soft->getSubject()?></a></td>
                                <td>成果-软著</td>
                                <td>软著</td>
                                <td><?=Button::setUrl(site_url('user/soft/show/id/'.$_soft->getSoftId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_soft->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-软著</td>
                                    <td><?=$file->getFileNote()?:'软著附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $rewards= $project->selectRewards();
                            while($reward = $rewards->getObject()):
                                $_reward = $reward->getReward(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/reward/show/id/'.$_reward->getRewardId())?>" target="_blank"><?=$_reward->getSubject()?></a></td>
                                <td>成果-奖励荣誉</td>
                                <td>奖励荣誉</td>
                                <td><?=Button::setUrl(site_url('user/reward/show/id/'.$_reward->getRewardId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_reward->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-奖励荣誉</td>
                                    <td><?=$file->getFileNote()?:'奖励荣誉附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $papers= $project->selectPapers();
                            while($paper = $papers->getObject()):
                                $_paper = $paper->getPaper(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/paper/show/id/'.$_paper->getPaperId())?>" target="_blank"><?=$_paper->getSubject()?></a></td>
                                <td>成果-科技论文</td>
                                <td>科技论文</td>
                                <td><?=Button::setUrl(site_url('user/paper/show/id/'.$_paper->getPaperId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_paper->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-科技论文</td>
                                    <td><?=$file->getFileNote()?:'论文附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $standards= $project->selectStandards();
                            while($standard = $standards->getObject()):
                                $_standard = $standard->getStandard(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/standard/show/id/'.$_standard->getStandardId())?>" target="_blank"><?=$_standard->getSubject()?></a></td>
                                <td>成果-标准</td>
                                <td>标准</td>
                                <td><?=Button::setUrl(site_url('user/standard/show/id/'.$_standard->getStandardId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_standard->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-标准</td>
                                    <td><?=$file->getFileNote()?:'标准附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            <?php
                            $transforms= $project->selectTransforms();
                            while($transform = $transforms->getObject()):
                                $_transform = $transform->getTransform(true);
                            ?>
                            <tr>
                                <td><a href="<?=site_url('user/transform/show/id/'.$_transform->getTransformId())?>" target="_blank"><?=$_transform->getSubject()?></a></td>
                                <td>成果-成果转化</td>
                                <td>成果转化</td>
                                <td><?=Button::setUrl(site_url('user/transform/show/id/'.$_transform->getTransformId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php
                            $files = $_transform->getAttachment();
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>成果-成果转化</td>
                                    <td><?=$file->getFileNote()?:'成果转化附件'?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                        <table class="table table-bordered">
                            <thead>
                            <tr>
                                <td class="text-center" colspan="4">验收
                                    <?php
                                    if(in_array(input::session('userlevel'),[2,3,4,5,6])):
                                        ?>
                                        <span class="float-right"><?=Button::setUrl(site_url('user/completesupplement/index/id/'.$project->getProjectId()))->setIcon('upload')->setClass('btn-alt-success')->window('补传')?></span>
                                    <?php endif;?>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 30%">附件名称</td>
                                <td style="width: 30%">附件类型</td>
                                <td>附件说明</td>
                                <td style="width: 10%">操作</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            if($project->getConfigs("path.complete")):
                            ?>
                            <tr>
                                <td><a href="<?=site_url('apply/complete/show/id/'.$project->getProjectId())?>" target="_blank">验收申请表</a></td>
                                <td>验收书</td>
                                <td>验收申请表</td>
                                <td><?=Button::setUrl(site_url('apply/complete/show/id/'.$project->getProjectId()))->setIcon('show')->link('查看')?></td>
                            </tr>
                            <?php endif;?>
                            <?php
                            $files = $project->getAttachments('complete_yss',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>验收报告PDF版</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('complete_zjbg',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>项目完成情况总结报告</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('complete_idea',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>验收专家组意见</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            <?php
                            $files = $project->getAttachments('complete_qt',0,'order by id desc');
                            while($file = $files->getObject()):
                                ?>
                                <tr>
                                    <td><i class="fa fa-paperclip mr-1 text-gray"></i><a href="<?=$file->getPreviewUrl()?>" target="_blank"><?=$file->getFileName()?></a></td>
                                    <td>验收其他附件</td>
                                    <td><?=$file->getFileNote()?></td>
                                    <td><?=Button::setUrl($file->getPreviewUrl())->setIcon('show')->link('查看')?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
