<style>
    table.snote td{
        padding: 0.25rem;
        border:none;
        border-bottom: 1px solid #e2e8f2;
    }
</style>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已审核的借阅申请
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include_once('search_part.php') ?>
                </div>
                <div class="box">
                    <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                        <thead>
                        <tr>
                            <th ><?=getColumnStr('案卷题名','subject')?></th>
                            <th ><?=getColumnStr('借阅人','user_name')?></th>
                            <th lass="text-center" style="width: 150px"><?=getColumnStr('借阅日期','borrow_at')?></th>
                            <th lass="text-center" style="width: 150px"><?=getColumnStr('归还日期','return_at')?></th>
                            <th class="text-center" style="width: 150px"><?=getColumnStr('状态','statement')?></th>
                            <th class="text-center" style="width: 170px;">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($borrow = $pager->getObject()):?>
                            <tr>
                                <td><?=link_to("archive/borrow/apply/show/id/".$borrow->getBorrowId(),$borrow->getSubject())?><p class="snote">档案类别：<?=$borrow->getArchive(true)->getTypeStr()?><br>档号：<?=$borrow->getArchive()->getNo()?></p></td>
                                <td><?=$borrow->getUserName()?></td>
                                <td><?=$borrow->getBorrowAt()?></td>
                                <td><?=$borrow->getReturnAt()?></td>
                                <td align="center"><?=$borrow->getState()?></td>
                                <td class="text-center">
                                    <?=Button::setName('退回')->setTitle('退回')->setUrl(site_url("archive/borrow/company/doBack/id/".$borrow->getBorrowId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="6" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?></span></td>
                        </tr>
                        </tfoot>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>
