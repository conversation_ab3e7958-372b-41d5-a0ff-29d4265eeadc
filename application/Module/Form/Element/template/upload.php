<link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
<script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.min.js')?>"></script>
<div class="clearfix"></div>
<div class="center search">
<div id="uploader" class="wu-example">
    <!--用来存放文件信息-->
    <div class="btns">
        <div id="picker" class="col-md-2">选择文件</div>
        <?php if($config['hasNote']):?>
        <div class="col-md-1">备注：</div>
        <div class="col-md-5"><input id="file_note" value="" class="form-control" placeholder="备注信息"/></div>
        <?endif;?>
        <button id="ctlBtn" class="btn btn-primary btn-sm col-md-1">开始上传</button>
    </div>
    <div id="thelist" class="uploader-list col-md-12 text-left">
        <div id="" class="item">
            <h4 class="info"></h4>
        </div>
    </div>
</div>
<div class="clearfix"></div>
</div>
    <div class="clearfix"></div>
<script language="javascript" type="text/javascript">
$(function(){
var data;
var $btn =$("#ctlBtn");
var formData = {
    item_id:'<?=$config['item_id']?>',
    item_type:'<?=$config['item_type']?>',
    file_note:'',
};

var uploader = WebUploader.create({
    swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
    server: "<?=$config['url']?>",
    pick: '#picker',
   // auto:false,
    fileVal:'file[]',
    fileNumLimit:'<?=$config['limit_num']?>',
    fileSingleSizeLimit:'<?=$config['limit_size']?>',
    // 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
    resize: false,
    formData:formData
}).on('ready', function () {
    $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
});

function displayProp(obj){
        var names="";
        for(var name in obj){
           names+=name+": "+obj[name]+", ";
        }
        alert(names);
    }
uploader.on('fileQueued', function( file ) {
    $('#thelist').append( '<div id="' + file.id + '" class="item well well-sm">' +
        '<h4 class="info">文件：' + file.name + '</h4>' +
        '<p class="state">等待上传...</p>' +
    '</div>' );
});

uploader.on('uploadProgress', function( file, percentage ) {
    var $li = $( '#'+file.id ),
        $percent = $li.find('.progress .progress-bar');

    // 避免重复创建
    if ( !$percent.length ) {
        $percent = $('<div class="progress progress-striped active">' +
          '<div class="progress-bar" role="progressbar" style="width: 0%">' +
          '</div>' +
        '</div>').appendTo( $li ).find('.progress-bar');
    }

    $li.find('p.state').text('上传中');

    $percent.css( 'width', percentage * 100 + '%' );
});
uploader.on( 'uploadSuccess', function( file ) {
    $( '#'+file.id ).find('p.state').text('已上传');
});

uploader.on( 'uploadError', function( file ) {
    $( '#'+file.id ).find('p.state').text('上传出错');
});

uploader.on( 'uploadComplete', function( file ) {
    $( '#'+file.id ).find('.progress').fadeOut();
});
uploader.on('uploadAccept', function( file, response ) {
    if ( response.hasError ) {
        // 通过return false来告诉组件，此文件上传有错。
        return false;
    }
    data = response._raw;
    //data = eval("("+data+")");

    if('<?=$config['callback']?>')
        eval('<?=$config['callback']?>(data)');

    $.cookie('uploaddata', data,{path:'/'});
    // for(var i=0; i<data.length;i++)
    //     for(var key in data[i])
    //         alert(data[i][key]);
    return true;
});

//当某个文件的分块在发送前触发，主要用来询问是否要添加附带参数，大文件在开起分片上传的前提下此事件可能会触发多次。
uploader.on('uploadBeforeSend', function (obj, data, headers) {
    data.file_note = $('#file_note').val();
});

    $btn.on('click', function() {  
        console.log("上传...");  
        uploader.upload();  
        console.log("上传成功");  
    }); 
});
</script>