<?php if($label):?>
    <div class="clearfix"></div>
    <div class="header no-margin-top"><?=$label?></div>
<?php endif;?>

<?php if($tips):?>
<div class="help-block alert alert-info"><?=$tips?'<i class="fa fa-exclamation-circle fa-lg orange"></i> '.$tips:''?></div>
<hr/>
<?php endif;?>
<div class="clearfix"></div>
<table class="table table-striped table-hover datatable col-md-12" bPaginate="false" bInfo="false" bFilter="false" bSort="false">
<thead>
    <tr>
        <th class="text-center">机构代码</th>
        <?php if(in_array('company_name',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_company_name">单位名称</th>
        <?php endif;?>
        <?php if(in_array('works',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_works">在本项目中分工</th>
        <?php endif;?>
        <?php if(in_array('address',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_address">单位地址</th>
        <?php endif;?>
        <?php if(in_array('chairman',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_chairman">法人代表</th>
        <?php endif;?>
        <?php if(in_array('linkman',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_linkman">联系人</th>
        <?php endif;?>
        <?php if(in_array('linkman_mobile',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_linkman_mobile">手机</th>
        <?php endif;?>
        <?php if(in_array('linkman_phone',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_linkman_phone">电话</th>
        <?php endif;?>
        <?php if(in_array('company_property',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_company_property">单位属性</th>
        <?php endif;?>
        <?php if(in_array('project_type',$config['presetfields'])):?>
        <th class="text-center" mark="<?=$name?>_project_type">项目类型</th>
        <?php endif;?>
        <th class="text-center" style="width:70px;">加/减</th>
    </tr>
</thead>
<tbody>
<?php if(!$value) $value[0]=''; foreach($value as $key=>$val):?>
    <tr ><td class="text-center">
        <input type="text" name="<?=$name?>[code][]" onblur="return fillcompany(this);" style="width:100%;" value="<?=$value[$key]['code']?>"/>
        <input type="hidden" name="<?=$name?>[ids][]" value="<?=$value[$key]['id']?>"/>
    </td>
<?php if(in_array('company_name',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[company_name][]" value="<?=$value[$key]['company_name']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('works',$config['presetfields'])):?>
    <td class="text-center">
    <textarea class="form-control" name="<?=$name?>[works][]" style="width:100%;height:33px" class=""><?=$value[$key]['works']?></textarea></td>
<?php endif;?>
<?php if(in_array('address',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[address][]" value="<?=$value[$key]['address']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('chairman',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[chairman][]" value="<?=$value[$key]['chairman']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('linkman',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[linkman][]" value="<?=$value[$key]['linkman']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('linkman_mobile',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[linkman_mobile][]" value="<?=$value[$key]['linkman_mobile']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('linkman_phone',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[linkman_phone][]" value="<?=$value[$key]['linkman_phone']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
<?php if(in_array('company_property',$config['presetfields'])):?>
    <td class="text-center">
    <select name="<?=$name?>[company_property][]" class="form-control" style="width:100%;">
            <?=getPropertyList($value[$key]['company_property'])?>
    </select></td>
<?php endif;?>
<?php if(in_array('project_type',$config['presetfields'])):?>
    <td class="text-center">
    <input type="text" name="<?=$name?>[project_type][]" value="<?=$value[$key]['project_type']?>" class=" form-control" style="width:100%;" /></td>
<?php endif;?>
    <td class="text-center">
        <input name="<?=$name?>[company_id][]" type="hidden" value="<?=$value[$key]['company_id']?>"/>
        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
    </td>
    </tr>
<?php endforeach;?>
</tbody>
</table>

<div class="clearfix"></div>

<input type="hidden" id="presetfields" value="<?=implode(',',$config['presetfields'])?>" />
<script language="javascript" type="text/javascript">
$(function(){
    // var fields = $('#presetfields').val();
    // fields = fields.split(',');
    // $('th[mark^="<?=$name?>_"]').each(function(){
    //     var mark = $(this).attr('mark');
    //     mark = mark.replace('<?=$name?>_','');
    //     if($.inArray(mark,fields)==-1)
    //     {
    //         $(this).remove();
    //         $('[name^="<?=$name?>['+mark+']"]').closest('td').remove();
    //     }
    // });
});
</script>