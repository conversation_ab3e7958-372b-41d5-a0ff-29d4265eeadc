<?php namespace App\Module\AutoGroup\Contracts;

interface Factory
{
	/*
		载入配置
	 */
	public function loadConfig();

	/*
		分配
	 */
    public function distribution(array $config);

    // public function getProjectIds($group_id);
    // public function getExpertIds($group_id);
    // public function setSubjectIds($subjectIds);

    public function addExpertToGroup($group_id,array $ids);

    public function listExperts($level);

    public function getGroupInfo(array $config);
}