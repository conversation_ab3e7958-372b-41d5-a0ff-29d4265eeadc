<?php namespace App\models;
use Illuminate\Database\Eloquent\Model;
use App\Models\Laboratory;

class DeviceAccept extends Model
{
    protected $table = "device_accept";

    protected $guarded = array('id');
    //追加字段
    protected $appends = ['statementObj'];

    public function getLaboratoryListAttribute($value) {
        if($value){
            return json_decode($value);
        }else{
            return [];
        }
    }

    // public function getDeviceListNameAttribute()
    // {
    //     return $this->devices()->toArray();
    // }

    public function devices()
    {
        // return $this->belongsToMany(
        //     Device::class,
        //     'accept_device_list',
        //     'accept_key',
        //     'device_key'
        // )->withTimestamps()->withPivot('statement');
        return $this->belongsToMany(
            'App\Models\Device',
            'accept_device_list'
        );
    }
    

    // 赋值
    public function getStatementObjAttribute()
    {
        switch ($this->statement) {
            case 0:
                $statementObj = ['name'=>'未填写','class'=>'warning','allow'=>true];
                break;
            case 1:
                $statementObj = ['name'=>'正在填写','class'=>'warning','allow'=>true];
                break;
            case 2:
                $statementObj = ['name'=>'等待通过','class'=>'info','allow'=>false];
                break;
            case 3:
                $statementObj = ['name'=>'被驳回','class'=>'error','allow'=>true];
                break;
            case 4:
                $statementObj = ['name'=>'申请通过，可以使用','class'=>'info','allow'=>false];
                break;
            case 5:
                $statementObj = ['name'=>'申请归还','class'=>'warning','allow'=>false];
                break;
            case 8:
                $statementObj = ['name'=>'部分归还','class'=>'info','allow'=>false];
                break;
            case 10:
                    $statementObj = ['name'=>'全部归还','class'=>'success','allow'=>false];
                    break;
            // case 7:
            //         $statementName = '申请被驳回';
            //         break;
            // case 10:
            //         $statementName = '完成归还';
            //         break;
            default:
                $statementObj = ['name'=>'未知','class'=>''];
                break;
        }
        return $statementObj;
    }

    public function user(){
        return $this->belongsTo('App\Models\Users','user_id','user_id');
    }

    public function showLaboratoryArray(){
        $result = [];
        if($this->laboratory_list){
            foreach($this->laboratory_list as $id){
                $result[] = Laboratory::find($id)->toArray();
            }
        }
        return $result;
    }
}