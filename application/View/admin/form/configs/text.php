<div class="form-group">
	<label class="control-label col-md-3">内容</label>
	<div class="col-md-9">
		<textarea type="text" name="content" class="form-control" rows="8"><?=$options['content']?></textarea>
	<small class="help-block">文字块内容</small>
	</div>
</div>

<div class="form-group">
	<label class="control-label col-md-3">主题</label>
	<div class="col-md-9">
		<div class="input-group">
			<input type="text" name="class" id="class" class="form-control" value="<?=$options['class']?>"/>
			<label class="input-group-addon">
				<select onchange="$('#class').val(this.value)" class="no-border">
					<option>无</option>
					<?=getSelectFromArray(['alert alert-default'=>'alert-default','alert alert-primary'=>'alert-primary','alert alert-success'=>'alert-success','alert alert-warning'=>'alert-warning','alert alert-danger'=>'alert-danger','well well-default'=>'well-default','well well-primary'=>'well-primary','well well-success'=>'well-success','well well-warning'=>'well-warning','well well-danger'=>'well-danger','label label-default'=>'label-default','label label-primary'=>'label-primary','label label-success'=>'label-success','label label-warning'=>'label-warning','label label-danger'=>'label-danger'],'',false)?>
				</select>
			</label>
		</div>
		
		<small class="help-block">文字块主题</small>
	</div>
</div>