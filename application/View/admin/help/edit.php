<script type="text/javascript" >
$(function(){
	$('#sign').blur(function(){
		var sign = $(this);
		var value = $(this).val();
		$.ajax({
		type:"GET",
		url:'<?=site_url("admin/help/checkSign/sign/'+value+'")?>',
		dataType:"TEXT",
		success:function(msg){
			if(msg)
			{
				alert(msg);
			}
			else sign.css('border','');	
		}
		});
	});
	
});

</script>

<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="btn-group btn-group-sm" role="group"> <a href="<?=site_url("admin/help/index")?>" class="glyphicon glyphicon-chevron-left">返回列表</a> <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存修改</a> </div>
  <div class="box">
    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
      编辑文章
      </caption>
      <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/help/edit")?>">
        <tr>
          <th width="15%">标题<em>*</em></th>
          <td width="35%"><input name="subject" type="text" class="required" id="subject" value="<?=$helps->getSubject(0,true)?>" style="width:98%;"/></td>
          <th width="15%">系统名称</th>
          <td width="35%">使用帮助</td>
        </tr>
        <tr>
          <th>封面上传</th>
          <td><input name="cover" type="text" id="cover" value="<?=$helps->getCover()?>"  readonly="readonly" />
            <input type="button" name="button3" id="button3" value="上传封面" onclick="loadbox.load('<?=site_url("common/upload")?>',function(json){$('#cover').val(json[0].path)},350,230);" /> </td>
          <th>附件上传</th>
          <td><input name="file" type="text" id="file" value="<?=$helps->getFile()?>"  readonly="readonly" />
            <input type="button" name="button2" id="button2" value="上传附件" onclick="loadbox.load('<?=site_url("common/upload")?>',function(json){$('#file').val(json[0].path)},350,230);" /></td>
        </tr>
        <tr>
          <th>描述</th>
          <td colspan="3"><textarea name="brief" cols="60" rows="5" style="width:99%;"><?=$helps->getBrief()?>
</textarea></td>
        </tr>
        <tr>
          <th>所属分类</th>
          <td colspan="3"><select name="category_id" id="category_id" class="required" >
            <option value="">=所属分类=</option>
			  <?=getHelpList($helps->getCategoryId())?>
            </select>
          </select></td>
        </tr>
        <tr>
          <th>所属用户组</th>
          <td colspan="3">
		  	<?php while($usergroup = $usergroups->getObject()):?>
            	<input type="checkbox" name="user_group_id[]" value="<?=$usergroup->getId()?>" <?php if(in_array($usergroup->getId(),$helps->getUserGroupId())) echo 'checked="checked"';?>  /><?=$usergroup->getUserGroupName()?>
            <?php endwhile;?>
            <br /><em>*</em><font size="1" color="#F00">默认选中全部用户组 </font>
         </td>
        </tr>
        <tr>
          <th>上传文件类型</th>
          <td colspan="3">
          	<input type="radio" name="file_type" class="request" value="doc" <?php if($helps->getFileType()!='video') echo 'checked="checked"';?>/>文章
            <input type="radio" name="file_type" class="request" value="video" <?php if($helps->getFileType()=='video') echo 'checked="checked"';?>/>视频
          </td>
        </tr>
        <tr>
          <th>标识</th>
          <td colspan="3"><textarea name="sign" rows="5" style="width:98%"><?=$helps->getSign()?></textarea></td>
        </tr>
        <tr>
          <th>详细内容</th>
          <td colspan="4"><?=sf::getPlugin("Fckeditor","content",$helps->getContent(),'100%',450)->create()?></td>
        </tr>
        <tr>
          <td colspan="4" align="center"><input type="submit" name="button" id="button" value="保存资料" />
            <input name="id" type="hidden" id="id" value="<?=$helps->getId()?>" />
            <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
        </tr>
      </form>
    </table>
  </div>
</div>