<div class="panel panel-primary">
  <div class="panel-heading center"><a class="btn btn-sm  pull-left btn-primary" onclick="javascript:history.go(-1)"><i class="glyphicon glyphicon-arrow-left"></i>返回</a>共 享 文 档 柜</div>
  <div class="panel-body">
    <div class="search">
      <form class="form-inline" method="post" action="<?=site_url("admin/office/shareSearch")?>">
        <div class="form-group ">
           关&nbsp;键&nbsp;字：
           <input name="search" class="form-control" value="<?=$_SESSION['condition']['search']?>">
           &nbsp;&nbsp;类型：
           <select name="type" >
             <?=getSelectFromArray(['title'=>'标题','describe'=>'描述'],$_SESSION['condition']['type'],false)?>
           </select>
           &nbsp;&nbsp;文档分类：
           <select name="type_id" >
            <option value="">全部</option>
            <?=getSelectFromArray($type,$_SESSION['condition']['type_id'],false)?>
           </select>
           &nbsp;&nbsp;共享状态：
           <select name="share" >
            <option value="">全部</option>
            <?=getSelectFromArray(['1'=>'已共享','2'=>'未共享'],$_SESSION['condition']['share'],false)?>
           </select>
             &nbsp;&nbsp;&nbsp;<button type="submit" class="btn btn-primary btn-sm">搜索</button>
        </div>
      </form>
    </div>
    <form action="">
      <div class="panel panel-default">
        <div class="panel-heading">
            <i class="ace-icon fa fa-list"></i> 共享文档列表 
             &nbsp;
             (&nbsp;共 <strong><?= $d->lastPage()?></strong>&nbsp;页，<strong><?=$d->total()?></strong>&nbsp;条记录 )
        </div>
        <table class="table table-hover center tb_list">
            <tr>
              <th width="4%">序号</th>
              <th width="15%"><?=getColumnStr('标题','title')?></th>
              <th width="15%"><?=getColumnStr('描述','describe')?></th>
              <th width="8%"><?=getColumnStr('作者','author')?></th>
              <th width="8%"><?=getColumnStr('文档分类','type')?></th>
              <th width="8%"><?=getColumnStr('创建日期','created_at')?></th>
              <th width="8%"><?=getColumnStr('更新时间','updated_at')?></th>
              <th width="8%"><?=getColumnStr('共享状态','share')?></th>
              <th width="10%">操作</th>
            </tr>
          <?php foreach($d as $k => $v) :?>
            <tr>
              <td><?=$k+1?></td>
              <td><a href="<?=site_url('admin/office/lookDoc/id/'.$v->id)?>"><?= $v->title;?></a></td>
              <td><?= $v->describe;?></td>
              <td><?= $v->author;?></td>
              <td><?= $v->type;?></td>
              <td><?= date('Y-m-d',strtotime($v->created_at));?></td>
              <td><?= date('Y-m-d',strtotime($v->updated_at));?></td>
              <td>
                <?php if ($v->share == 1): ?>
                  已共享
                <?php else: ?>
                  未共享
                <?php endif ?>
              </td>
              <td>
               <a href="<?=site_url('admin/office/lookDoc/id/'.$v->id)?>">查看</a>
              </td>
            </tr>
          <?php endforeach;?>
        </table>
      </div>
      <div align="center">
         <?= $d->render() ?>
      </div>
    </form>
  </div>
</div>