<div class="tabbable no-padding no-margin">
<ul class="nav nav-tabs" id="settingTab">
	<li class="active">
		<a data-toggle="tab" href="#" url="<?=site_url('admin/settings/usermenu/index')?>">
			<i class="grey ace-icon fa fa-cog bigger-120"></i>
			菜单
		</a>
	</li>
    <!--
	<li class="">
		<a data-toggle="tab" href="#" url="<?=site_url('admin/settings/award/index')?>">
			<i class="grey ace-icon fa fa-cog bigger-120"></i>
			进步奖
		</a>
	</li>

	 <li class="dropdown">
		<a data-toggle="dropdown" class="dropdown-toggle" href="#">
			Dropdown &nbsp;
			<i class="ace-icon fa fa-caret-down bigger-110 width-auto"></i>
		</a>

		<ul class="dropdown-menu dropdown-info">
			<li>
				<a data-toggle="tab" href="#dropdown1">@fat</a>
			</li>

			<li>
				<a data-toggle="tab" href="#dropdown2">@mdo</a>
			</li>
		</ul>
	</li>
 --></ul>

<div class="tab-content">
	<div id="setting_content" class="tab-pane fade active in">
		<i class="fa fa-spin fa-refresh"></i> 载入中...
	</div>
	<div class="clearfix"></div>
</div>
</div>
<script type="text/javascript">
$(function(){

	loadContent($('#settingTab li[class="active"] a'),$('#setting_content'));

	$('#settingTab li a').click(function(){
		loadContent($(this),$('#setting_content'));
	});
});

function loadContent(obj,dom){
	var url = $(obj).attr('url');console.log(url);
	if(!url || typeof url=='undefined') return true;

	ajaxHtml(url,{},dom);
}
</script>