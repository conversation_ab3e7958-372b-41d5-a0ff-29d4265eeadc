<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        已退回的账号
                    </h3>
                </div>
                <div class="block-content">
                    <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a><p style="clear:both"></p></div>
                    <div class="search">
                        <?php include_once("search_part.php");?>
                    </div>
                    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/declarer/doSubmit")?>">
                            <tr>
                                <th width="30" class="fold_head" onclick="fold.showAll()">详</th>
                                <th><?=getColumnStr('姓 名','personname')?></th>
                                <th><?=getColumnStr('性 别','user_sex')?></th>
                                <th><?=getColumnStr('出生年月','user_birthday')?></th>
                                <th><?=getColumnStr('手机号码','user_mobile')?></th>
                                <th><?=getColumnStr('学 位','education')?></th>
                                <th><?=getColumnStr('职 称','user_honor')?></th>
                                <th width="20%"><?=getColumnStr('申报单位','corporation_name')?></th>
                                <th width="20%">上级单位</th>
                                <!-- <th><?=getColumnStr('注册时间','created_at')?></th> -->
                                <th>操 作</th>
                            </tr>
                            <?php while($user = $pager->getObject()):?>
                                <tr>
                                    <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                    <td align="center">
                                        <?=$user->getMark()?><?=link_to("user/member/show/userid/".$user->getUserId(),$user->getPersonname())?><?=$user->getUserGrade()?><p><small><?=$user->getTalentStr()?></small></p>
                                    </td>
                                    <td align="center"><?=$user->getUserSex()?></td>
                                    <td align="center"><?=$user->getUserBirthday()?></td>
                                    <td align="center"><?=$user->getUserMobile()?></td>
                                    <td align="center"><?=$user->getEducation()?></td>
                                    <td align="center"><?=$user->getUserHonor()?></td>
                                    <td align="center"><?=$user->getCorporationName()?></td>
                                    <td align="center"><?=$user->getDepartment()->getSubject()?></td>
                                    <!-- <td align="center"><?=$user->getUser(true)->getCreatedAt()?$user->getUser(true)->getCreatedAt("Y-m-d"):$user->getUpdatedAt("Y-m-d")?></td> -->
                                    <td align="center">
                                        <a href="<?=site_url("admin/member/dosubmit/userid/".$user->getUserId())?>" class="btn btn-info btn-xs" role="button"><i class="ace-icon fa fa-star"></i>认 证</a>
                                    </td>
                                </tr>
                                <tr class="fold_body">
                                    <td colspan="12"><?php include('more_part.php') ?></td>
                                </tr>
                            <?php endwhile; ?>
                            <tr>
                                <td colspan="12">
              <span class="pager_bar">
                <?=$pager->fromto().$pager->navbar(10)?>
              </span></td>
                            </tr>
                        </form>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

