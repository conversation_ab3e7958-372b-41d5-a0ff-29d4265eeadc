<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            获取互联网侧信息
        </h2>
        <div class="content-options">

        </div>
    </div>

    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
        <div class="row">
            <div class="col-lg-12">
                <div class="block block-rounded">
                    <div class="block-content tab-content">
                        <div class="tab-pane active" id="tab1" role="tabpanel">

                            <div class="form-group row">
                                <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">目标网站<em>*</em></label>
                                <div class="col-md-5 col-sm-5 col-xs-5">
                                    <select name="" id="" class="form-control">
                                        <?=getSelectFromArray(['四川省科学技术厅'])?>
                                    </select>
                                </div>
                            </div>


                            <div class="clearfix"></div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-8 ml-auto">
                                <?=Button::setUrl(site_url('admin/guide/doGrab'))->setSize('btn')->setIcon('check')->link('现在获取')?>
                                <?=Button::setUrl(site_url('admin/guide/autoGrab'))->setSize('btn')->setIcon('fa fa-clock')->window('定时获取')?>
                            </div>
                        </div>
                    </div><!-- END Block Tabs Default Style -->
                </div>
            </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
