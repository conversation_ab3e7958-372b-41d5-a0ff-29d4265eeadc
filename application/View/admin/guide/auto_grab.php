<link href="<?=site_path('assets/js/clockpicker/bootstrap-clockpicker.min.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/clockpicker/bootstrap-clockpicker.min.js')?>"></script>
<div class="content">
    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
        <div class="row">
            <div class="col-lg-12">
                <div class="block block-rounded">
                    <div class="block-content tab-content">
                        <div class="tab-pane active" id="tab1" role="tabpanel">

                            <div class="form-group row"">
                                <label class="col-md-3 col-sm-3 col-xs-3 control-label text-right">获取时间<em>*</em></label>
                                <div class="col-md-5 col-sm-5 col-xs-5">
                                    <select name="grab[type]" id="type" class="form-control w-auto custom-control-inline">
                                        <?=getSelectFromArray(['everyday'=>'每天','mon-fri'=>'周一至周五','workday'=>'法定工作日','close'=>'关闭'],'everyday',false)?>
                                    </select>
                                    <div class="clockrange custom-control-inline" style="line-height: 35px;">
                                        <input type="text" name="grab[start_time]" id="start_time" class="form-control custom-control-inline" style="width: 80px;margin-right: 10px;" value="09:00">
                                    </div>
                                </div>
                            </div>


                            <div class="clearfix"></div>
                        </div>
                    <div class="form-group row">
                        <div class="col-sm-8 ml-auto">
                            <?=Button::setType('submit')->setIcon('save')->button('保存')?>
                        </div>
                    </div>
                    </div><!-- END Block Tabs Default Style -->
                </div>
            </div><!-- END Block Tabs -->
    </form>
</div>
<script src="<?=site_url('js/jquery.webupload.js')?>"></script>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    $(function(){
        $('#start_time').clockpicker({
            autoclose: true
        });
    });
</script>