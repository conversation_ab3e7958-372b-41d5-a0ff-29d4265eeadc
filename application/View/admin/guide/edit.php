<link rel="stylesheet" type="text/css" href="<?=site_path("js/webuploader/webuploader.css")?>">
<script type="text/javascript" src="<?=site_path("js/webuploader/webuploader.nolog.min.js")?>"></script>
<script type="text/javascript">
    function todo(json)
    {
        $("#chose").val(json.join(","));
        let temp=[];
        for(const ele of json){
            temp.push(ele.split("?")[1]);
        }
        $("#user_list").html(temp.join("、"));
        layer.closeAll();
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑指南
        </h2>
        <div class="content-options">
            <?=Button::setUrl(site_url("admin/guide/index/year/".$year))->setIcon('back')->link('返回')?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="box">
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/guide/edit/year/".$year)?>" class="form-inline">
                            <table width="100%" cellpadding="3" cellspacing="1" class="tb_data form-group table table-hover table-striped">
                                <tr>
                                    <th width="18%">上级指南<em>*</em></th>
                                    <td width="82%"><select name="parent_id" id="parent_id" class="form-control">
                                            <option value="0">root</option>
                                            <?php
                                            while($parent = $parent_data->getObject()){
                                                echo '<option value="'.$parent->getId().'" ';
                                                if($parent->getId() == $category->getParentId() || $pid == $parent->getId()) echo 'selected="selected" ';
                                                echo '>'.$parent->getHeadStr().$parent->getSubject().'</option>';
                                            }
                                            ?>
                                        </select></td>
                                </tr>
                                <tr>
                                    <th>项目类型<em>*</em></th>
                                    <td><select name="cat_id" id="cat_id" class="form-control" required>
                                            <option value="">请选择</option>
                                            <?=getSelectFromArray(get_select_data('cat'),$category->getCatId(),false)?>
                                        </select></td>
                                </tr>
                                <tr>
                                    <th>指南名称<em>*</em></th>
                                    <td><input name="subject" type="text" class="form-control" id="subject" value="<?=$category->getSubject()?>"  required /></td>
                                </tr>
                                <tr>
                                    <th>项目名称</th>
                                    <td><input name="project_name" type="text" class="form-control" id="project_name" value="<?=$category->getProjectName()?>" size="40" /><small>如果需要默认项目名称请填写。</small></td>
                                </tr>
                                <tr>
                                    <th>项目编号规则</th>
                                    <td><input name="marker" type="text" class="form-control" id="marker" value="<?=$category->getMarker()?>" size="40" /><small>用于项目自动编号。</small></td>
                                </tr>
                                <tr class="d-none">
                                    <th>项目周期</th>
                                    <td>
                                        <div style="width: 200px;float: left">
                                            <input type="text" name="project_start_at" class="form-control w-auto custom-control-inline" value="<?=$category->getProjectStartAt()?>" data-com="date" data-icon="none"/>
                                        </div>
                                        <div class="text-center" style="display:inline-block;width: 60px;float: left">
                                            到
                                        </div>
                                        <div style="width: 200px;float: left">
                                            <input type="text" name="project_end_at" class="form-control w-auto custom-control-inline" value="<?=$category->getProjectEndAt()?>" data-com="date" data-icon="none"/>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th>排序</th>
                                    <td><input name="orders" type="number" class="form-control" id="orders" value="<?=$category->getOrders()?>" />
                                    <p>数字越小，显示则越靠前。</p>
                                    </td>
                                </tr>
                                <tr class="d-none">
                                    <th>申报对象</th>
                                    <td><select name="user_level" id="user_level" class="form-control">
                                            <?=getSelectFromArray(array('0'=>'==选择申报主体==','2'=>'申报人申报','3'=>'申报单位申报'),$category->getUserLevel(),false)?>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <th>年度</th>
                                    <td><select name="year" id="year" class="form-control w-auto custom-control-inline">
                                            <option value="">=申报年度=</option>
                                            <?=getYearList($category->getYear()?:config::get("current_declare_year"))?>
                                        </select>
                                        <select name="current_group" id="current_group" class="form-control w-auto custom-control-inline">
                                            <option value="">=项目批次=</option>
                                            <?=getSelectFromArray(array('1'=>'第一批','2'=>'第二批','3'=>'第三批','4'=>'第四批','5'=>'第五批','6'=>'第六批','7'=>'第七批','8'=>'第八批'),$category->getCurrentGroup()?:config::get("type_current_group"),false)?>
                                        </select></td>
                                </tr>
                                <tr>
                                    <th>申报书</th>
                                    <td><select name="declare_map_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择申报书--</option>
                                            <?=getSelectFromArray(getBookMapList(5,true),$category->getDeclareMapId(),false)?>
                                        </select>
                                   </td>
                                </tr>
                                <tr>
                                    <th>任务书</th>
                                    <td>
                                        <select name="task_map_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择任务书--</option>
                                            <?=getSelectFromArray(getBookMapList(1,true),$category->getTaskMapId(),false)?>
                                        </select>
                                    </td>
                                </tr>

                                <tr style="display: none">
                                    <th>项目预算</th>
                                    <td><select name="budget_map_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择预算书--</option>
                                            <?=getSelectFromArray(getBookMapList(3,true),0,false)?>
                                        </select>
                                        [
                                        <?=$category->getBudgetMapId()?>
                                        ](如果不更新请留空)</td>
                                </tr>
                                <tr>
                                    <th>验收书</th>
                                    <td><select name="complete_map_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择验收书--</option>
                                            <?=getSelectFromArray(getBookMapList(2,true),$category->getCompleteMapId(),false)?>
                                        </select>
                                   </td>
                                </tr>
                                <tr>
                                    <th>评审指标</th>
                                    <td><select name="assess_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择评审指标--</option>
                                            <?=getSelectFromArray(getBookMapList(4,true),$category->getAssessId(),false)?>
                                        </select>
                                    </td>
                                </tr>
                                <tr style="display: none">
                                    <th>归属部门</th>
                                    <td><select name="office_id" class="form-control w-auto custom-control-inline">
                                            <option value="">--选择归属部门--</option>
                                            <?=getOfficeList(0)?>
                                        </select>
                                        [
                                        <?=$category->getOfficeId()?>
                                        ](如果不更新请留空)</td>
                                </tr>
                                <tr style="display: none">
                                    <th>负责人限制</th>
                                    <td class="text-left"><?=get_checkbox(['A+'=>'A+级用户','A'=>'A级用户','B+'=>'B+级用户','B'=>'B级用户','C'=>'C级用户','D'=>'D级用户','F'=>'F级用户','G'=>'G级用户','M'=>'M苗子','S'=>'S级用户'],'allow_grade',array(),'','',false)?>
                                        [
                                        <?=implode('、',$category->getAllowGrade())?>
                                        ](如果不更新请留空)</td>
                                </tr>
                                <tr style="display: none">
                                    <th>单位限制</th>
                                    <td><?=get_checkbox(get_select_data('unitlibrary'),'allow_company',$category->getAllowCompany(),'','',false)?>
                                        (如果不更新请留空，如果有选择则只有选择范围类的企业才可以申报)
                                        <div class="form-check form-check-inline">
                                            <input type="checkbox"  name="allow_company_son" id="allow_company_son" value="1">
                                            <label class="checkbox-inline">同时修改子类</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr style="display: none">
                                    <th>特殊性质</th>
                                    <td><?=get_checkbox(array('is_subsidy'=>'不需要验收(后补助)','is_shift'=>'转移支付','no_complete'=>'忽略结题验证','no_limit'=>'不限制上报个数','is_majorproject'=>'是重大专项','title_with_guide_name'=>'项目名和指南名一致','title_with_company_name'=>'项目名申报单位名称一致','open_budget'=>'上报后填写预算书','open_task'=>'上报后填写任务合同','is_software'=>'软科学或软件类项目','company_limit'=>'单位数量限制','is_directional'=>'定向申报','none'=>'无特殊属性'),'property',$category->getProperty(),'','',false)?>
                                        <div class="form-check form-check-inline">
                                            <input type="checkbox"  name="property_son" id="property_son" value="1">
                                            <label class="checkbox-inline">同时修改子类</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="d-none">
                                    <th>技术方向</th>
                                    <td><?=get_checkbox(array('1'=>'高新技术类','2'=>'社会发展类','3'=>'软科学类','4'=>'科技服务业','5'=>'农业类','6'=>'平台类'),'skill_branch',$category->getSkillBranch(),'','',false)?></td>
                                </tr>
                                <tr>
                                    <th>单位限制</th>
                                    <td>
                                        <?php
                                        $corporationArr=getSecondCompanys();
                                        $corporationIds = $category->getCorporationIds();
                                        if(empty($corporationIds)) $corporationIds = [];
                                        ?>
                                        <?=get_checkbox($corporationArr,'corporation_id',$corporationIds,'',false)?>
                                    </td>
                                </tr>
                                <?php
                                $declarerIds = $category->getUserIds();
                                if(empty($declarerIds)) $declarerIds = [];
                                $user_list=[];
                                $chose=[];
                                if(!empty($declarerIds)){
                                    $temp=[];
                                    foreach($declarerIds as $declarerId){
                                        $temp[]="'$declarerId'";
                                    }
                                    $temp_str=implode(",",$temp);
                                    $declarers = sf::getModel('declarers')->selectAll("user_id in ($temp_str)",'',0);

                                    while($declarer = $declarers->getObject()){
                                        $user_list[]=$declarer->getPersonname();
                                        $chose[]=$declarer->getUserId()."?".$declarer->getPersonname();
                                    }
                                }
                                ?>
                                <tr>
                                    <th>人员限制</th>
                                    <td>
                                        <span id="user_list"><?=implode("、",$user_list)?></span>
                                        <input type="hidden" name="chose" id="chose" value="<?=implode(',',$chose)?>"/>
                                        <div class="m-t-20">
                                            <?=Button::setName('选择')->setUrl(site_url("admin/article/people"))->setWidth('1200px')->setHeight('800px')->window()?>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                                $officerIds = getManagers();
                                $financeIds = getFinances();
                                ?>
                                <tr>
                                    <th>科技处审核人员</th>
                                    <td>
                                        <?=get_checkbox($officerIds,'officer_id',$category->getOfficerIds(true),'',false)?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>财务处审核人员</th>
                                    <td>
                                        <?=get_checkbox($financeIds,'finance_id',$category->getFinanceIds(true),'',false)?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>开放填报时间</th>
                                    <td>
                                        <div style="width: 200px;float: left">
                                        <input name="start_at" type="text" class="form-control w-auto custom-control-inline" placeholder="<?=date("Y-m-d H:i:s")?>" value="<?=$category->getStartAt("Y-m-d H:i:s")?>" data-com="date" data-format="yyyy-mm-dd HH:mm:ss" data-icon="none" >
                                        </div>
                                        <div class="text-center" style="display:inline-block;width: 60px;float: left">
                                        到
                                        </div>
                                        <div style="width: 200px;float: left">
                                        <input name="end_at" type="text" class="form-control w-auto custom-control-inline" placeholder="<?=date("Y-m-d H:i:s")?>" value="<?=$category->getEndAt("Y-m-d H:i:s")?>" data-com="date" data-format="yyyy-mm-dd HH:mm:ss" data-icon="none" >
                                        </div>
                                    </td>
                                </tr>
                                <tr style="display: none">
                                    <td>
                                        <div class="form-check form-check-inline">
                                            <input type="checkbox" name="set_parent" id="set_parent" value="1">
                                            <label class="form-check-label">同时修改父类</label>&nbsp;
                                            <input type="checkbox" name="set_son" id="set_son" value="1">
                                            <label class="form-check-label">同时修改子类</label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <th colspan="2" align="left">指南说明</th>
                                </tr>
                                <tr>
                                    <td colspan="2"><?=sf::getPlugin("Rte","content",$category->getContent(),'100%',300)->create()?></td>
                                </tr>
                                <tr>
                                    <th colspan="2" align="left">附件</th>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <div id="uploader" class="wu-example">
                                            <!--用来存放文件信息-->
                                            <div class="btns">
                                                <div id="picker">选择文件</div>
                                            </div>
                                            <div id="PDF_list" class="uploader-list">
                                                <?php
                                                $filearr = [];
                                                $fids = $category->getFile();
                                                $fids = array_filter($fids);
                                                if($fids){
                                                    foreach ($fids as $id){
                                                        $file = sf::getModel('Filemanager',$id);
                                                        if($file->isNew()) continue;
                                                        $filearr[] = $file;
                                                    }
                                                }
                                                if($filearr):
                                                    foreach ($filearr as $file):
                                                        ?>
                                                        <div class="item">
                                                            <h6 class="info"><?=$file->getFileName()?></h6>
                                                            <p class="state">已上传   <a href="<?=site_url('admin/guide/deleteFile/aid/'.$category->getId().'/fid/'.$file->getId())?>">删除</a></p>
                                                            <div class="progress progress-striped active" style="display: none;">
                                                                <div class="progress-bar" role="progressbar" style="width: 100%;"></div>
                                                            </div>
                                                        </div>
                                                        <input type="hidden" name="file[]" value="<?=$file->getId()?>">
                                                    <?php
                                                    endforeach;
                                                endif;
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" align="center">
                                        <?=Button::setType('button')->setClass('btn-alt-light')->setEvent('saveDraft(this)')->button('保存草稿')?>
                                        <?=Button::setType('button')->setClass('btn-alt-light')->setEvent('savePreview(this)')->button('预览')?>
                                        <?=Button::setType('button')->setClass('btn-alt-primary')->setEvent('send(this)')->button('发布')?>
                                        <input type="hidden" name="is_release" id="is_release" value="0">
                                        <input name="id" type="hidden" id="id" value="<?=$category->getId()?>" />
                                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
                                </tr>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
<script>
    function send(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            $('#is_release').val(1);
            validateForm.submit();
        }
    }
    function savePreview(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            $('#is_release').val(2);
            validateForm.submit();
        }
    }
    function saveDraft(me)
    {
        var form = document.getElementById('validateForm');
        if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
            form.reportValidity();
        }else{
            $(me).attr("disabled","disabled");
            $(me).html('<span class="spinner-border spinner-border-sm" style="width: 1rem;height: 1rem"></span> 正在加载...');
            validateForm.submit();
        }
    }
    $('.deletefile').click(function(){
        if(confirm('确认要删除附件？'))
        {
            $(this).closest('li').slideUp();
            var url = $(this).attr('href');
            $.ajax({
                url:url,
                type:'get',
                dataType:'html',
                success:function(html){
                }
            });
        }
        return false;
    });


    var uploader = WebUploader.create({
        swf: "<?=site_path("js/webuploader/Uploader.swf")?>",
        server: "<?=site_url("Common/webupload")?>",
        pick: '#picker',
        auto:true,
        fileVal:'file[]',
        fileNumLimit:5,
        fileSingleSizeLimit:'20MB',
        // 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
        resize: false,
        formData:{item_type:'guide',upload_size:'20971520',upload_type:'doc,docx,xls,xlsx,zip,rar,pdf'},
        accept:{
            title: 'PDF',
            extensions: 'doc,docx,xls,xlsx,zip,rar,pdf',
            mimeTypes: '.doc,.docx,.xls,.xlsx,.zip,.rar,.pdf'
        }
    }).on('ready', function () {
        $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
    });

    function displayProp(obj){
        var names="";
        for(var name in obj){
            names+=name+": "+obj[name]+", ";
        }
        alert(names);
    }
    uploader.on('fileQueued', function( file ) {
        $('#PDF_list').append( '<div id="PDF_' + file.id + '" class="item">' +
            '<h6 class="info">' + file.name + '</h6>' +
            '<p class="state">等待上传...</p>' +
            '</div>' );
    });

    uploader.on('uploadProgress', function( file, percentage ) {
        var $li = $( '#PDF_'+file.id ),
            $percent = $li.find('.progress .progress-bar');

        // 避免重复创建
        if ( !$percent.length ) {
            $percent = $('<div class="progress progress-striped active">' +
                '<div class="progress-bar" role="progressbar" style="width: 0%">' +
                '</div>' +
                '</div>').appendTo( $li ).find('.progress-bar');
        }

        $li.find('p.state').text('上传中');

        $percent.css( 'width', percentage * 100 + '%' );
    });
    uploader.on( 'uploadSuccess', function( file, response ) {
        var data = eval(response._raw);
        console.log('data');
        console.log(data);
        console.log(response);
        var id = data[0].id;
        var inputHtml = "<input type='hidden' name='file[]' value='"+id+"' />";
        $( '#PDF_'+file.id ).append(inputHtml);
        $( '#PDF_'+file.id ).find('p.state').text('已上传');
        $("#picker .webuploader-pick").text("继续添加");
    });

    uploader.on( 'uploadError', function( file,reason) {
        console.log(reason);
        $( '#PDF_'+file.id ).find('p.state').text('上传出错');
    });

    uploader.on( 'uploadComplete', function( file ) {
        $( '#PDF_'+file.id ).find('.progress').fadeOut();
    });
    uploader.on( 'uploadAccept', function( file, response ) {
        //alert(response._raw);
        console.log(response._raw);
        if ( response.hasError ) {
            // 通过return false来告诉组件，此文件上传有错。
            return false;
        }
    });


</script>
