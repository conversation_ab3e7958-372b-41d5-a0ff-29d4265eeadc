<script>
  function delNetnews(){
    var aids = getCheckId();
    location.href=baseurl+"admin/netnews/del/id/"+aids;
  }
  function getCheckId(){
    var spCodesTemp = "";
    $("input[name='select_id[]']:checked").each(function(i){
      if(0==i){
        spCodesTemp = $(this).val();
      }else{
        spCodesTemp += (","+$(this).val());
      }
    });
    return spCodesTemp;
  }
</script>
<div class="col-xs-12 col-sm-12 col-md-12 no-padding">

  <div class="btn-group btn-group-xs header blue" role="group">
      <a href="<?=site_url("admin/netnews/index")?>" class="btn btn-primary" role="button">
        <i class="fa fa-list"></i> 参考消息列表
      </a>
    <a href="<?=site_url("admin/netnews/add")?>" class="btn btn-info" role="button">
      <i class="fa fa-plus"></i> 新增参考消息
    </a>
    <p style="clear:both;"></p>
  </div>
<div class="search">
  <form id="form1" name="form1" method="post" action="">
    <label>搜索关键字
      <input type="text" name="search" id="search" value="<?=$search?>" />
    </label>
    <label>
<input type="submit" name="button" id="button" value="提交" />
    </label>
  </form>
</div>
<div class="panel panel-default">
<div class="panel-heading">
    <i class="ace-icon fa fa-list"></i> 参考消息列表
    <div class="pull-right hidden-480 tools">
      <div class="dropdown dropdown-hover inline">
        <a href="#" style="">排序<i class="ace-icon fa fa-caret-down"></i></a>
        <ul class="dropdown-menu dropdown-menu-right no-margin">
          <li><?=getColumnStr('序号','id')?></li>
          <li><?=getColumnStr('添加时间','created_at')?></li>
        </ul>
      </div>
      |
    </div>
  </div>
<table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
<thead>
  <tr>
    <th class="hidden-480 text-center" width="5%"><input name="selectAll" type="checkbox" id="selectAll"></th>
    <th width="5%">序号</th>
    <th width="40%">标题</th>
    <th width="10%">日期</th>
    <th width="15%">来源</th>
    <th width="10%">是否收录</th>
    <th width="20%">操作</th>
  </tr>
  </thead>
  <tbody>
  <?php while($item = $pager->getObject()):?>
  <tr>
    <td class="hidden-480 text-center">
      <?php
        if(empty($item->getInfoid())):
      ?>
      <input name="select_id[]" type="checkbox" value="<?=$item->getId()?>" class="np">
      <?php
        endif;
      ?>
    </td>
    <td align="center"><?=$item->getId()?>
    <td align="center"><a href="<?=site_url('admin/netnews/edit/id/'.$item->getId())?>"><?=$item->getSubject()?></a>
    </td>
    <td align="center"><?=$item->getTime()?></td>
    <td align="center"><?=$item->getSource()?></td>
    <td align="center"><?=$item->getIsRecord()?></td>
    <td align="center">
      <div class="pull-center action-buttons">
        <a class="dark" target="_blank" href="<?=site_url('admin/netnews/show/id/'.$item->getId())?>" title="预览">
          <i class="ace-icon fa fa-eye bigger-130"></i>
        </a>
        <a class="blue" href="<?=site_url('admin/netnews/edit/id/'.$item->getId())?>" title="编辑">
          <i class="ace-icon fa fa-pencil bigger-130"></i>
        </a>
      </div>
      </td>
  </tr>
  <?php endwhile;?>
  </tbody>
  <tfoot>
    <tr>
      <td colspan="5" align="left">
        <div class="btn-group btn-group-xs header blue">
          <a href="javascript:selectAll();" class="btn btn-primary"><i class="fa "></i> 全选</a>
          <a href="javascript:cancelSelect();" class="btn btn-primary"><i class="fa "></i> 取消</a>
          <a href="javascript:delNetnews()" class="btn btn-danger"><i class="fa "></i> 删除</a>
        </div>
      </td>
    </tr>
    <tr>
      <td colspan="5" align="center"><?=$pager->navbar()?></td>
    </tr>
  </tfoot>
</table>
</div>
</div>
