<?php 
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
?>
<div class="main">
  <div class="box">
    <table width="100%" cellpadding="3" cellspacing="1" class="table no-margin">
      <form id="validateForm" name="validateForm" method="post" action="">
        <tr>
          <th class="center width-20">需发送短信通知的专家<em class="red">*</em></th>
          <td>
            <?php
              while($committee = $committees->getObject()):
            ?>
                <div>
                  <input type="checkbox" name="mobiles[]" id="mobiles" value="<?=$committee->getExpertTel()?>" checked>
                  <?=$committee->getExpertName()?>（手机：<?=$committee->getExpertTel()?>）
                </div>
            <?php
              endwhile;
            ?>
          </td>
        </tr>
        <tr>
          <th class="center width-20">发送时间<em class="red">*</em></th>
          <td><input name="send_at" type="text" class="required" id="send_at" value="<?=date("Y-m-d H:i:s")?>" size="12" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:50%;"/></td>
        </tr>
        <tr>
          <th class="center width-20">发送内容<em class="red">*</em></th>
          <td><textarea name="message" cols="60" rows="2" style="width:99%;">尊敬的专家，请抽空尽快登录中国民航局第二研究所网站(http://www.ncnet.ac.cn/)对成果鉴定项目进行鉴定。感谢您的参与！</textarea></td>
        </tr>
        <tr>
          <td colspan="4" align="center">
            <button type="submit" name="button" id="button" class="btn btn-sm btn-primary" >
              发送消息 <i class='ace-icon fa fa-paper-plane-o'></i>
            </button>

          </td>
        </tr>
      </form>
    </table>
  </div>
</div>
