<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="no-padding no-margin panel panel-default" >
  <div class="panel-heading">
    <i class="ace-icon fa fa-list"></i> 项目列表<div>
<table class="table table-condensed datalist no-margin no-padding" bPaginate="false" bInfo="false" bSort="false" bFilter="false">
    <td width="25%"><form id="validateForm" name="validateForm" method="post" action="">
    <caption>对“<?=$project->getSubject()?>”进行负责人替换操作。</caption>
    <tr>
    <th>姓名</th>
    <th>申报单位</th>
    <th>可用操作</th>
    </tr>
    <?php while($user = $pager->getObject()):?>
    <tr>
    <td><a href="<?=site_url("user/profile/show/userid/".$user->getUserId())?>"><?=$user->getPersonname()?></a></td>
    <td><?=$user->getCorporationName()?></td>
    <td><a href="javascript:void(0);" onclick="parent.">选择</a></td>
    </tr>
    <?php endwhile;?>
    <tr>
    <td colspan="3" align="center"><input type="submit" name="button" id="button" value=" 替换负责人 " />
        <input name="id" type="hidden" id="id" value="<?=$project->getProjectId()?>" />
        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
    </tr>
    </form>
    </table>
  </div>
</div>
