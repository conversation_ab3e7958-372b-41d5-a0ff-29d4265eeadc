<style type="text/css">
.active {
	border:solid #F90 1px;
	padding:3px;
	background-color:#F96;
	color:#FFF;
}
#show table th {
	font-size:12px;
}
</style>
<script type="text/javascript">
$(function(){
	$("label").click(function(){
		if($(this).find("input").attr("checked")) $(this).addClass('active');
		else $(this).removeClass('active');
	});
	$('#clear').click(function(){
		$("label").each(function(){
			$(this).removeClass('active');
		});
	});
});

function checkscore()
{
	if(($('#highscore').val()<$('#lowscore').val()) && $('#highscore').val()!='')
	{
		$('#highscore').val('');
		$('#highscore').focus();
		alert("范围不正确！");
	}
}


</script>
    <table width="700" border="0" align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
    <form action="" method="post" name="detailsearch" id="detailsearch">
        <tr>
          <th colspan="2" width="60">关键词:</th>
          <td><div class="c_list">
              <input id="search" name="search" class="form-control w-auto custom-control-inline" />
              <input name="field" type="radio" value="subject"<?php if(input::getInput("mix.field") == 'subject'):?> checked="checked"<?php endif;?> />
        项目名称
        <input type="radio" name="field" value="corporation_name"<?php if(input::getInput("mix.field") == 'corporation_name'):?> checked="checked"<?php endif;?> />
        单位名称
        <input type="radio" name="field" value="user_name"<?php if(input::getInput("mix.field") == 'user_name'):?> checked="checked"<?php endif;?> />
        项目负责人
        <input type="radio" name="field" value="accept_id"<?php if(input::getInput("mix.field") == 'accept_id'):?> checked="checked"<?php endif;?> />
        申报编号
        <input type="radio" name="field" value="radicate_id"<?php if(input::getInput("mix.field") == 'radicate_id'):?> checked="checked"<?php endif;?> />
        立项编号
        <input type="radio" name="field" value="subject_name"<?php if(input::getInput("mix.field") == 'subject_name'):?> checked="checked"<?php endif;?> />
        研究领域</div></td>
        </tr>
        <tr>
          <th height="25" colspan="2">项目年度</th>
          <td width="91%"><div class="c_list">
              <?=getYearSearch('year',input::getInput("mix.year"))?>
            </div></td>
        </tr>
        <tr>
          <th height="29" colspan="2">分管科室</th>
          <td height="29"><div class="c_list">
              <?=getOfficeSearch('office',input::getInput("mix.office"))?>
            </div></td>
        </tr>
        <tr>
          <th colspan="2">项目类型 </th>
          <td ><div class="c_list">
              <?=getTypeSearch('types',input::getInput("mix.types"))?>
            </div></td>
        </tr>
        <tr>
          <th colspan="2">归口部门 </th>
          <td><div class="easyui-tabs" style="width:650px; background-color:#E6EbEc;">
              <div title="市州科协办" class="c_list">
                <?=getGatherSearch('gather',input::getInput("mix.gather"),'9')?>
              </div>
              <div title="省级厅局" class="c_list">
                <?=getGatherSearch('gather',input::getInput("mix.gather"),'11,12')?>
              </div>
              <div title="扩权强县" class="c_list">
                <?=getGatherSearch('gather',input::getInput("mix.gather"),'10')?>
              </div>
              <div title="其他类别" class="c_list">
                <?=getGatherSearch('gather',input::getInput("mix.gather"),'13')?>
              </div>
            </div></td>
        </tr>
        <tr>
          <th width="2%" rowspan="3">项目状态</th>
          <th width="7%">申报书</th>
          <td><div class="c_list">
              <?=getStateSearch('project_state',input::getInput("mix.project_state"))?>
            </div></td>
        </tr>
        <tr>
          <th>任务书</th>
          <td><div class="c_list">
              <?=getStateSearch('task_state',input::getInput("mix.task_state"))?>
            </div></td>
        </tr>
        <tr>
          <th>结题书</th>
          <td><div class="c_list">
              <?=getStateSearch('complate_state',input::getInput("mix.complate_state"))?>
            </div></td>
        </tr>
        <tr>
          <th colspan="2">评审得分</th>
          <td><div class="c_list">
              <?=get_radio(array('<60','60-70','70-80','80-90','90-100'),'score',input::getInput("mix.score"),'')?>
              分数范围：
              <input id="lowscore" name="lowscore" maxlength="2" style="width:20px;" />
              -
              <input id="highscore" onblur="checkscore();" name="highscore" maxlength="3" style="width:25px;"/>
            </div></td>
        </tr>
        <tr>
          <td colspan="3" style="text-align:center" height="25"><input style="cursor:pointer;" type="submit" name="button" id="button" value=" >>搜索<< " /></td>
        </tr>
      </form>
    </table>
</div>
