<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="btn-group btn-group-sm" role="group">
    <!-- <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a> -->
    <a href="<?=site_url("export/export/index/table/declarers")?>" class="btn btn-info btn-sm"><i class="ace-icon fa fa-download"></i>导出数据</a>
    <p style="clear:both"></p></div>
    <div class="search">
      <?php include_once("search_part.php");?>
    </div>
    <div class="box">
      <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/declarer/doSubmit")?>">
          <tr>
            <!-- <th class="tselect"><input name="selectAll" id="selectAll" type="checkbox" /></th> -->
            <th class="fold_head" onclick="fold.showAll()">详</th>
            <th><?=getColumnStr('姓名','personname')?></th>
            <th><?=getColumnStr('性别','user_sex')?></th>
            <th><?=getColumnStr('出生年月','user_birthday')?></th>
            <th><?=getColumnStr('手机号码','user_mobile')?></th>
            <th><?=getColumnStr('学位','education')?></th>
            <th width="20%"><?=getColumnStr('申报单位','corporation_name')?></th>
            <!-- <th width="20%">上级单位</th> -->
            <th><?=getColumnStr('注册时间','created_at')?></th>
            <th><?=getColumnStr('账号状态','is_lock')?></th>
          </tr>
          <?php while($user = $pager->getObject()):?>
            <tr>
              <!-- <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td> -->
              <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
              <td align="center">
                <?=$user->getMark()?><?=link_to("user/profile/show/userid/".$user->getUserId(),$user->getPersonname())?><?=$user->getUserGrade()?>
              </td>
              <td align="center"><?=$user->getUserSex()?></td>
              <td align="center"><?=$user->getUserBirthday()?></td>
              <td align="center"><?=$user->getUserMobile()?></td>
              <td align="center"><?=$user->getEducation()?></td>
              <td align="center"><?=link_to("unit/profile/show/userid/".$user->getCorporationId(),$user->getCorporationName())?></td>
              <!-- <td align="center"><?=$user->getParent()?></td> -->
              <td align="center"><?=$user->getUser(true)->getCreatedAt("Y-m-d")?></td>
              <td align="center"><?=$user->getState()?></td>
            </tr>
            <tr>
              <td></td>
              <td colspan="12"> 
                <a href="javascript:void(0);" onclick="return showWindow('科技项目情况','<?=site_url("admin/declarer/showProject/id/".$user->getUserId())?>');" class="btn btn-info btn-xs" role="button">科技项目 <?=$user->getProjectNum()?></a>
                <a href="javascript:void(0);" onclick="return showWindow('科技奖励情况','<?=site_url("admin/declarer/showAward/id/".$user->getUserId())?>');" class="btn btn-danger btn-xs" role="button">科技奖励 <?=$user->getAwardNum()?></a>
                <a href="javascript:void(0);" onclick="return showWindow('专利信息情况','<?=site_url("admin/declarer/showPatent/id/".$user->getUserId())?>');" class="btn btn-warning btn-xs" role="button">专利信息 <?=$user->getUserPatentNum()?></a>
                <a href="javascript:void(0);" onclick="return showWindow('论文专著情况','<?=site_url("admin/declarer/showPaper/id/".$user->getUserId())?>');" class="btn btn-success btn-xs" role="button">论文专著 <?=$user->getUserPaperNum()?></a>
              </td>
            </tr>
            <tr class="fold_body">
              <td colspan="12"><?php include('more_part.php') ?></td>
            </tr>
          <?php endwhile; ?>
          <tr>
            <td colspan="12">
              <span class="pager_bar">
                <?=$pager->fromto().$pager->navbar(10)?>
              </span></td>
            </tr>
          </form>
        </table>
      </div>
    </div>
