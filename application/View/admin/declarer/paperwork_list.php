<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a><p style="clear:both"></p></div>
  <div class="search">
    <?php include_once("search_part.php");?>
  </div>
  <div class="box">
    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/declarer/doSubmit")?>">
        <tr>
          <th class="tselect"><input name="selectAll" id="selectAll" type="checkbox" /></th>
          <th class="fold_head" onclick="fold.showAll()">详</th>
          <th><?=getColumnStr('姓名','personname')?></th>
          <th><?=getColumnStr('性别','user_sex')?></th>
          <th><?=getColumnStr('出生年月','user_birthday')?></th>
          <th><?=getColumnStr('证件类型','card_type')?></th>
          <th><?=getColumnStr('证件号','user_idcard')?></th>
          <th><?=getColumnStr('学位','user_degree')?></th>
          <th><?=getColumnStr('职称','user_honor')?></th>
          <th><?=getColumnStr('申报单位','corporation_name')?></th>
          <th><?=getColumnStr('注册时间','created_at')?></th>
          <th width="120">操作</th>
        </tr>
        <?php while($user = $pager->getObject()):?>
        <tr>
          <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td>
          <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
          <td align="center"><?=link_to("user/profile/show/userid/".$user->getUserId(),$user->getPersonname())?><?=$user->getUserGrade()?></td>
          <td align="center"><?=$user->getUserSex()?></td>
          <td align="center"><?=$user->getUserBirthday()?></td>
          <td align="center"><?=$user->getCardType()?></td>
          <td align="center"><?=$user->getUserIdcard(0,true)?></td>
          <td align="center"><?=$user->getUserDegree()?></td>
          <td align="center"><?=$user->getUserHonor()?></td>
          <td><?=$user->getCorporationName()?></td>
          <td align="center"><?=$user->getUser(true)->getCreatedAt("Y/m/d")?></td>
          <td align="center"><a href="javascript:void(0);" onclick="return showWindow('实名认证','<?=site_url("admin/declarer/dosubmit/userid/".$user->getUserId())?>',400,260);" class="btn btn-info btn-sm" role="button"><i class="ace-icon fa fa-star"></i>认证</a> <a href="javascript:void(0);" onclick="return showWindow('退回项目负责人','<?=site_url("admin/declarer/doBack/userid/".$user->getUserId())?>',400,260);" class="btn btn-info btn-sm" role="button"><i class="glyphicon glyphicon-chevron-left"></i>退回</a></td>
        </tr>
        <tr class="fold_body">
          <td colspan="12"><?php include('more_part.php')?></td>
        </tr>
        <?php endwhile; ?>
        <tr>
          <td colspan="12">
            <span class="pager_bar">
            <?=$pager->fromto().$pager->navbar(10)?>
            </span></td>
        </tr>
      </form>
    </table>
  </div>
</div>
