<style type="text/css">
.li_checkbox li {
	float:left;
	padding-right:10px;
}
</style>
<div class="content">
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                项目类型管理
            </h3>
        </div>
        <div class="block-content">
            <div class="col-xs-12 col-sm-12 col-md-12">
              <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a> <a href="javascript:$('#validateForm').submit();" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存资料</a> </div>
              <div class="box">
                <form id="validateForm" name="validateForm" method="post" action="<?=site_url("admin/deploy/edit")?>">
                  <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                    <caption>
                    编辑项目类型
                    </caption>
                    <tr>
                      <th width="15" colspan="2" align="right">类型名称</th>
                      <td width="35%"><input name="subject" type="text" id="subject" value="<?=$type->getSubject()?>" style="99%" class="required" /></td>
                      <th width="15%">当前年度</th>
                      <td width="35%"><select name="current_year" id="current_year" class="required">
                          <option value="">=年度=</option>
                          <?=getYearList($type->getCurrentYear())?>
                        </select></td>
                    </tr>
                    <tr>
                      <th colspan="2" align="right">所属大类</th>
                      <td><select name="cat_id" id="cat_id" class="required">
                          <?=get_select_option('cat',$type->getCatId())?>
                        </select></td>
                      <th>起止时间</th>
                      <td><input name="start_at" type="text" id="start_at" value="<?=$type->getStartAt()?>" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" />
                        到
                        <input name="end_at" type="text" id="end_at" value="<?=$type->getEndAt()?>" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" /></td>
                    </tr>
                    <tr>
                      <th colspan="2" align="right">简化标记</th>
                      <td><input name="marker" type="text" id="marker" value="<?=$type->getMarker()?>" class="required"/>
                        (用于项目受理自动编号)</td>
                      <th>是否可用</th>
                      <td><label>
                          <input type="radio" name="is_show"  value="1" <?php if($type->getIsShow() == 1):?> checked="checked"<?php endif;?> />
                          可用 </label>
                        <label>
                          <input type="radio" name="is_show"  value="0" <?php if($type->getIsShow() == 0):?> checked="checked"<?php endif;?> />
                          不可用 </label></td>
                    </tr>
                    <tr>
                      <th rowspan="2" align="right">申报书</th>
                      <th align="right">离线提纲</th>
                      <td><input name="project_file" type="text" id="project_file" value="<?=$type->getProjectFile()?>" readonly="readonly" />
                        <input type="button" name="button" id="button" value="上传..." onclick="return showWindow('上传','<?=site_url("common/webupload")?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = getCache('uploaddata');$('#project_file').val(uploaddata[0].path);}})" /></td>
                      <th rowspan="2">执行器</th>
                      <td rowspan="2"><select name="worker_id">
                          <?=getSelectFromArray(get_worker_option(),$type->getWorkerId(),false)?>
                        </select></td>
                    </tr>
                    <tr>
                      <th align="right">正文模板</th>
                      <td><input name="template" type="text" id="template" value="<?=$type->getTemplate()?>" readonly="readonly" />
                        <input type="button" name="button6" id="button6" value="上传..." onclick="return showWindow('上传','<?=site_url("common/webupload")?>',{area:['350px','250px'],maxmin:false,end:function(){var uploaddata = getCache('uploaddata');$('#template').val(uploaddata[0].path);}})" /></td>
                    </tr>
                    <tr>
                      <th colspan="2" align="right">经费预算</th>
                      <td><select name="budget_map_id">
                          <option value="">--选择预算书--</option>
                          <?=getSelectFromArray(getBookMapList(3),$type->getBudgetMapId(),false)?>
                        </select></td>
                      <th>任务合同</th>
                      <td><select name="task_map_id">
                          <option value="">--选择任务书--</option>
                          <?=getSelectFromArray(getBookMapList(1),$type->getTaskMapId(),false)?>
                        </select></td>
                    </tr>
                    <tr>
                      <th colspan="2" align="right">验收报告</th>
                      <td><select name="complete_map_id">
                          <option value="">--选择验收书--</option>
                          <?=getSelectFromArray(getBookMapList(2),$type->getCompleteMapId(),false)?>
                        </select></td>
                      <th>评审指标</th>
                      <td><select name="assess_id">
                          <option value="">--选择评审指标--</option>
                          <?=getSelectFromArray(getBookMapList(4),$type->getAssessId(),false)?>
                        </select></td>
                    </tr>
                  </table>
                  <table width="100%" cellpadding="3" cellspacing="1" class="tb_data">
                    <caption>
                    填写说明
                    </caption>
                    <tr>
                      <td><?=sf::getPlugin("Rte","remark",$type->getRemark(),'100%',600)->create()?></td>
                    </tr>
                    <tr>
                      <td align="center"><input type="submit" name="button" id="button" value="保存资料" />
                        <input name="id" type="hidden" id="id" value="<?=$type->getId()?>" />
                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" /></td>
                    </tr>
                  </table>
                </form>
              </div>
            </div>
        </div>
    </div>
</div>

