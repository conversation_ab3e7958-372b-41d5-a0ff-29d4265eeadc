<div class="col-xs-12 col-sm-12 col-md-12">
<div class="header blue">
 <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a>
 <a href="#" class="btn btn-primary btn-xs" onclick="return showWindow('新增类型','<?=site_url('model/project/getAdd')?>',{area:['60%','100%'],shadeClose:false})"><i class="ace-icon fa fa-plus"></i>新增类型</a>
<p style="clear:both"></p>
</div>
<div class="search">
  <form id="form1" name="form1" method="post" action="">
    <label>搜索关键词语
      <input type="text" name="search" id="search" />
    </label>
    <label>
      <input name="field" type="radio" id="radio" value="subject" checked="checked" />
      申报书名称
    </label>
<input type="submit" name="button" id="button" value="提交" />
  </form>
</div>
<div class="box">
<table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
<thead>
    <tr>
      <th class="hidden-480"><input name="selectAll" id="selectAll" type="checkbox" /></th>
      <th class="hidden-480"><?=getColumnStr('ID','id')?></th>
      <th><?=getColumnStr('类型名称','subject')?></th>
      <th class="hidden-480"><?=getColumnStr('所属分类','cat_id')?></th>
      <th class="hidden-480"><?=getColumnStr('开始时间','start_at')?></th>
      <th class="hidden-480"><?=getColumnStr('结束时间','end_at')?></th>
      <th class="hidden-480"><?=getColumnStr('当前年度','current_year')?></th>
      <th class="hidden-480"><?=getColumnStr('类型标记','marker')?></th>
      <th class="hidden-480"><?=getColumnStr('目录标记','dir')?></th>
      <th class="hidden-480"><?=getColumnStr('是否可用','is_show')?></th>
      <th class="hidden-480">管理</th>
    </tr>
  </thead>
  <tbody>
    <?php foreach($types as $type):?>
    <tr>
      <td align="center" class="hidden-480"><input name="select_id[]"  type="checkbox" value="<?=$type->id?>" /></td>
      <td align="center" class="hidden-480"><?=$type->id?></td>
      <td><?=$type->subject?></td>
      <td align="center" class="hidden-480"><?=$type->cat_name?></td>
      <td align="center" class="hidden-480"><?=$type->getStartAt("Y/m/d H:i:s")?></td>
      <td align="center" class="hidden-480"><?=$type->getEndAt("Y/m/d H:i:s")?></td>
      <td align="center" class="hidden-480"><?=$type->current_year?> (<?=$type->current_group?>)</td>
      <td align="center" class="hidden-480"><?=$type->marker?></td>
      <td align="center" class="hidden-480"><?=$type->dir?></td>
      <td align="center" class="hidden-480"><?=$type->getIsShow(true)?></td>
      <td align="center" class="hidden-480" width="80" style="position:relative;">
        <a href="javascript:void(0);" class="btn-detail-toggle"><i class="ace-icon fa fa-edit"></i> 编辑<i class="ace-icon fa fa-caret-down blue"></i></a>
        <div class="btn-detail-content hide">
          <a href="#" onclick="return showWindow('配置-<?=$model->subject?>','<?=site_url('model/nationalProject/getConfigure/id/'.$type->id)?>',{area:['60%','100%'],shadeClose:false})"><i class="ace-icon fa fa-edit"></i> 编辑</a>
          <a href="javascript:void(0);" onclick="return showWindow('设置受理处室','<?=site_url("admin/deploy/setOffice/id/".$type->id)?>',400,300);"><i class="ace-icon fa fa-key"></i> 权限</a>
          <a href="javascript:void(0);" onclick="return showWindow('设置项目属性','<?=site_url("admin/deploy/setSwitch/id/".$type->id)?>',400,300);"><i class="ace-icon fa fa-code-fork"></i> 属性</a>
          <a href="#" onclick="return showWindow('配置-<?=$model->subject?>','<?=site_url('model/nationalProject/getFieldsConfigure/id/'.$type->id)?>',{area:['60%','100%'],shadeClose:false})"><i class="ace-icon fa fa-cog"></i> 配置</a>
        </div>
      </td>
    </tr>
  <tr>
      <td colspan="11" align="left" style=" color:#0099FF;">受理处室：<?=$type->getOfficeName()?></td>
    </tr>
    <tr>
      <td colspan="11" align="left" style=" color:#0099FF;">类型属性：<?=$type->getSwitchSubject()?></td>
    </tr>
    <?php endforeach; ?>
  </tbody>
  <tfoot>
    <?php if($hasmore):?>
    <tr>
      <td colspan="12" align="center"><button class="btn btn-sm btn-block btn-primary" onclick="return loadMore(this,'<?=site_url('model/nationalProject/getLoadmore')?>',3)">点击加载更多...</button></td>
    </tr>
    <?php endif;?>
  </tfoot>
</table>
</div>
</div>