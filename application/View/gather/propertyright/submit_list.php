<div class="main">
  <div class="tools">
  <a href="#" onclick="selectAll();return false;" class="select btn btn-primary">全部选中</a>
  <!-- <a href="#" onclick="validateForm.action='<?=site_url("gather/propertyright/doRejected")?>';$('#validateForm').submit()" class="undoit">驳回选中项</a> -->
  <!-- <a href="<?=site_url("export/export/index")?>" class="export">导出数据</a> -->
  <a href="#" onclick="$('#show_search').toggle();return false;" class="change  btn btn-primary">显示（隐藏）搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php');?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th><?=getColumnStr('受理编号','accept_id')?></th>
      <th width="300"><?=getColumnStr('专利名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('专利号','zlh')?></th>
      <th><?=getColumnStr('申报单位','corporation_id')?></th>
      <th><?=getColumnStr('年度','declare_year')?></th>
      <th><?=getColumnStr('申报日期','created_at')?></th>
      <th><?=getColumnStr('项目状态','statement')?></th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
        <td align="center"><?=$project->getAcceptId()?></td>
        <td><?=link_to("declare/propertyright/show/id/".$project->getProjectId(),$project->getSubject(26))?></td>
        <td align="center"><?=$project->getTypeName()?></td>
        <td align="center"><?=$project->getZlh()?></td>
        <td align="center"><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
        <td align="center"><?=$project->getDeclareYear()?></td>
        <td align="center"><?=$project->getCreatedAt("Y/m/d")?></td>
        <td align="center"><?=$project->getState()?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="10">
         <?php include('more_part.php');?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="10" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
