<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
   <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a> -->
  <p style="clear:both;"></p>
  </div>
  <div class="search">
    <table width="100%" align="center">
      <tbody id="show_search">
        <form action="" method="post" name="search" id="search">
          <tr>
            <td>关键词：
              <input id="search" name="search" class="form-control w-auto custom-control-inline" />
              <label><input name="field" type="radio" value="subject" checked="checked" />项目名称</label>
              <label><input type="radio" name="field" value="radicate_id" />立项编号</label>
              <label><input type="radio" name="field" value="user_name" />项目负责人</label>
              <label><input type="radio" name="field" value="corporation_name" />申报单位</label>
              <label><input type="radio" name="field" value="radicate_money" />立项经费</label>
              <?=btn('button','搜索','submit','find')?>
            </td>
          <tr>
            <td>
              <select name="radicate_year" id="radicate_year">
                <option value="">=立项年度=</option>
                <?=getYearList(input::getInput("mix.radicate_year"))?>
              </select>
              <select name="type_subject" id="type_subject">
                <option value="">=研究对象=</option>
                <?=getProjectType(input::getInput("mix.type_subject"))?>
              </select>
              <select name="guide_id" id="guide_id">
                <option value="">=指南领域=</option>
                <?=getGuideField(input::getInput("mix.guide_id"))?>
              </select>
              <select name="state_for_complete_book" id="state_for_complete_book">
                <option value="">=验收书状态=</option>
                <option value="2">待承担单位推荐</option>
                <option value="4">待归口部门推荐</option>
                <option value="6">待科协办受理</option>
                <option value="10">分管科室已推荐</option>
                <option value="12">科协办已经受理</option>
                <option value="13">需要进行复议</option>
                <option value="14">科协办同意结题</option>
              </select>              
            </td>
          </tr>
        </form>
     </tbody> 
    </table>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
        <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th width="30">详</th>
        <th><?=getColumnStr('立项编号','radicate_id')?></th>
        <th><?=getColumnStr('项目名称','subject')?></th>
        <th><?=getColumnStr('项目负责人','user_id')?></th>
        <th><?=getColumnStr('申报单位','corporation_id')?></th>
        <th><?=getColumnStr('研究对象','type_id')?></th>
        <th><?=getColumnStr('指南领域','guide_id')?></th>
        <th><?=getColumnStr('立项经费','radicate_money')?></th>
        <th><?=getColumnStr('立项年度','radicate_year')?></th>
        <th><?=getColumnStr('起始年月','start_at')?></th>
        <th><?=getColumnStr('结束年月','end_at')?></th>  
        <th width="110"><?=getColumnStr('验收书状态','state_for_complete_book')?></th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> 
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
        <td align="center"><?=$project->getRadicateId()?></td>
        <td><?=$project->getMark()?><?=link_to("declare/complete/show/id/".$project->getProjectId(),$project->getSubject())?></td>
        <td align="center"><?=$project->getUserName()?></td>
        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
        <td align="center"><?=$project->getTypeSubject()?></td>
        <td align="center"><?=$project->getGuideField()?></td>
        <td align="center"><?=$project->getRadicateMoney()?> 万元</td>
        <td align="center"><?=$project->getRadicateYear()?></td>
        <td align="center"><?=$project->getStartAt()?></td>
        <td align="center"><?=$project->getEndAt()?></td>         
        <td align="center"><?=$project->getStateForComplete()?></td>         
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="13">
         <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="13" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
