<div class="main">
  <div class="tools">
  <a href="javascript:selectAll();" class="select">全部选中</a>
  <a href="<?=site_url("export/export/index/table/devotes")?>" class="export">导出数据</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search"><?php include('search_part.php')?></div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>详</th>
      <th><?=getColumnStr('姓名','user_name')?></th>
      <th><?=getColumnStr('性别','user_sex')?></th>
	  <th><?=getColumnStr('出生年月','user_date_of_birth')?></th>
      <th><?=getColumnStr('职称','user_job_title')?></th>
      <th><?=getColumnStr('职务','user_duties')?></th>
	  <th><?=getColumnStr('推荐年度','declare_year')?></th>
	  <th><?=getColumnStr('工作单位','user_work_unit')?></th>
	   <th><?=getColumnStr('推荐部门','department_id')?></th>
      <th><?=getColumnStr('受理状态','statement')?></th>
      </tr>
      <?php while($devote = $pager->getObject()):?>
      <tr>
      <td><input name="select_id[]" type="checkbox" value="<?=$devote->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$devote->getId()?>').toggle()">+</label></td>
      <td><?=$devote->getMark()?><?=link_to("declare/jcgx/show/id/".$devote->getProjectId(),$devote->getUserName())?></td>
      <td align="center"><?=$devote->getUserSex()?></td>
	   <td align="center"><?=$devote->getUserDateOfBirth("Y/m/d")?></td>
      <td align="center"><?=$devote->getUserJobTitle()?></td>
      <td align="center"><?=$devote->getUserDuties()?></td>
	  <td align="center"><?=$devote->getDeclareYear()?></td>
	  <td><?=$devote->getUserWorkUnit()?></td>
	  <td><?=$devote->getDepartmentName()?></td>
      <td align="center"><?=$devote->getState()?></td>
      </tr>
      <tr id="show_<?=$devote->getId()?>" style="display:none;">
        <td colspan="11">
         <?php include('more_part.php')?>
        </td>
        </tr>
      <?php endwhile;?>
	  <tr>
         <td colspan="11" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>