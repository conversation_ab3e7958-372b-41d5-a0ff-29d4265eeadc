<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title><?=$title?></title>
<link href="<?=site_path("images/admin.css")?>" type="text/css" media="screen" rel="stylesheet"/>
<script language="javascript" src="<?=site_path("js/jquery.js")?>" type="text/javascript"></script>
<script language="javascript" src="<?=site_path("js/jquery.form.js")?>" type="text/javascript"></script>
<script language="javascript" src="<?=site_path("js/validate/jquery.validate.js")?>" type="text/javascript"></script>
<script language="javascript" src="<?=site_path("js/jquery.datePicker.js")?>" type="text/javascript"></script>
<script language="javascript" type="text/javascript" src="<?=site_path("js/loadbox.js")?>"></script>
<script language="javascript" type="text/javascript" src="<?=site_path('js/func.js') ?>?v=<?=config::get('js.func_version')?>"></script>
<script language="javascript" type="text/jscript">
$(document).ready(function() {
    $("#selectAll").click( function() {
        $("input[name='select_id[]']").each( function() { $(this).attr("checked",!this.checked); })
    });
	$("#validateForm").validate({errorElement: "em"});
	$('.date-pick').datePicker({startDate:'1900-01-01'});
	$('.tb_data tr').addClass("mouseout");
	$('.tb_data tr').mouseover(function(){$(this).removeClass("mouseout").addClass("mouseover");});
	$('.tb_data tr').mouseout(function(){$(this).removeClass("mouseover").addClass("mouseout");});
});
</script>
</head>
<body>
<script language=javascript> 
function moveLeftOrRight(fromObj,toObj) 
{
    var fromObjOptions=fromObj.options;
    for(var i=0;i<fromObjOptions.length;i++){
        if(fromObjOptions[i].selected){
            toObj.appendChild(fromObjOptions[i]);
            i--;
        }
    }
    resetAutoWidth(fromObj);
    resetAutoWidth(toObj);
} 

function moveLeftOrRightAll(fromObj,toObj) 
{
    var fromObjOptions=fromObj.options;
    if(fromObjOptions.length>1000) {
        //if(!confirm("Are you sure to move options?")) return false;
    }
    for(var i=0;i<fromObjOptions.length;i++){
        fromObjOptions[0].selected=true;
        toObj.appendChild(fromObjOptions[i]);
        i--;
    }
    resetAutoWidth(fromObj);
    resetAutoWidth(toObj);
} 

function moveUp(selectObj) 
{ 
    var theObjOptions=selectObj.options;
    for(var i=1;i<theObjOptions.length;i++) {
        if( theObjOptions[i].selected && !theObjOptions[i-1].selected ) {
            swapOptionProperties(theObjOptions[i],theObjOptions[i-1]);
        }
    }
} 

function moveDown(selectObj) 
{ 
    var theObjOptions=selectObj.options;
    for(var i=theObjOptions.length-2;i>-1;i--) {
        if( theObjOptions[i].selected && !theObjOptions[i+1].selected ) {
            swapOptionProperties(theObjOptions[i],theObjOptions[i+1]);
        }
    }
} 

function moveToTop(selectObj){
    var theObjOptions=selectObj.options;
    var oOption=null;
    for(var i=0;i<theObjOptions.length;i++) {
        if( theObjOptions[i].selected && oOption) {
            selectObj.insertBefore(theObjOptions[i],oOption);
        }
        else if(!oOption && !theObjOptions[i].selected) {
            oOption=theObjOptions[i];
        }
    }
}

function moveToBottom(selectObj){
    var theObjOptions=selectObj.options;
    var oOption=null;
    for(var i=theObjOptions.length-1;i>-1;i--) {
        if( theObjOptions[i].selected ) {
            if(oOption) {
                oOption=selectObj.insertBefore(theObjOptions[i],oOption);
            }
            else oOption=selectObj.appendChild(theObjOptions[i]);
        }
    }

}

function selectAllOption(selectObj){
    var theObjOptions=selectObj.options;
    for(var i=0;i<theObjOptions.length;i++){
        theObjOptions[0].selected=true;
    }
}

/* private function */
function swapOptionProperties(option1,option2){
    //option1.swapNode(option2);
    var tempStr=option1.value;
    option1.value=option2.value;
    option2.value=tempStr;
    tempStr=option1.text;
    option1.text=option2.text;
    option2.text=tempStr;
    tempStr=option1.selected;
    option1.selected=option2.selected;
    option2.selected=tempStr;
}

function resetAutoWidth(obj){
    var tempWidth=obj.style.getExpression("width");
    if(tempWidth!=null) {
        obj.style.width="auto";
        obj.style.setExpression("width",tempWidth);
        obj.style.width=null;
    }
}

function getOption(selectObj){
    var theObjOptions=selectObj.options;
	var obj = document.getElementById("select");
	obj.value = '';
	if(theObjOptions.length < 1){
		alert('请选择需要打印的字段！');
		return false;
	}
    for(var i=0;i<theObjOptions.length;i++){
        if(obj.value) obj.value = obj.value + ',' + theObjOptions[i].value;
		else obj.value = theObjOptions[i].value;
    }
	return true;
}

</script> 
<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a>
  <a href="javascript:void(0);" onclick="if(getOption(document.validateForm.ObjSelect)){$('#validateForm').submit();}return false;" class="btn btn-info export" role="button">导出数据</a>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("export/expert/output")?>">
      <table width="493" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th colspan="3">专家导出</th>
      </tr>
     <tr>
      <td colspan="3">1、请给导出文件取个文件名：
        <label>
          <input name="file_name" type="text" id="file_name" size="26" value="<?=date("YmdHis")?>" />
          <select name="ext" id="ext">
            <option value="doc">doc</option>
            <option value="xls">xls</option>
          </select>
        </label></td>
      </tr>
     <tr>
       <td colspan="3">2、请选择你需要导出的字段内容</td>
       </tr>
     <tr>
       <td align="right" width="214"><label>
         <select name="SrcSelect" size="15" multiple="multiple" id="SrcSelect" style="width:260px;">
           <?php foreach($field as $key => $val){?>
           <option value="<?=$key?>"><?=$val?></option>
           <?php }?>
         </select>
       </label></td>
       <td align="center"><label>
         <input type="button" name="button6" id="button6" value=" ∧ " onclick="moveUp(document.validateForm.ObjSelect)" />
         <br />
<br />
         <input type="button" name="button3" id="button3" value="&gt;&gt;&gt;" onclick="moveLeftOrRight(document.validateForm.SrcSelect,document.validateForm.ObjSelect)"/>
         <br />
         <input type="button" name="button4" id="button4" value="&lt;&lt;&lt;" onclick="moveLeftOrRight(document.validateForm.ObjSelect,document.validateForm.SrcSelect)"/>
         <br />
         <br />
         <input type="button" name="button5" id="button5" value=" ∨ " onclick="moveDown(document.validateForm.ObjSelect)"/>
       </label></td>
       <td  width="40%" align="left"><select name="ObjSelect" size="15" multiple="multiple" id="ObjSelect" style="width:260px;">
       </select></td>
     </tr>
     <tr>
       <td colspan="3" align="center"><label>
         <input type="submit" name="button2" id="button2" value="导出数据" onclick="return getOption(document.validateForm.ObjSelect);" />
           <input name="select" type="hidden" id="select" value="" />
         </label></td>
       </tr>
      </table>
    </form>
  </div>
</div>
</body>
</html>