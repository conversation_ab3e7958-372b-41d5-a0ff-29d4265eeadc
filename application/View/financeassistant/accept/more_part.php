<table border="0" cellpadding="3" cellspacing="1" class="show_more tb_data table-sm">
  <tr>
    <th colspan="4" class="text-center">项目详细信息</th>
  </tr>
  <tr>
    <th width="15%">项目名称</th>
    <td width="35%"><?=$project->getSubject()?></td>
    <th width="15%">申报编号</th>
    <td width="35%"><?=$project->getAcceptId()?></td>
  </tr>
  <tr>
    <th>研究对象</th>
    <td><?=$project->getTypeSubject()?></td>
    <th>项目类别</th>
    <td><?=$project->getProjectTypeSubject()?></td>
  </tr>
  <tr>
    <th>项目总经费</th>
    <td><?=$project->getTotalMoney()?>万元</td>
    <th>申报单位</th>
    <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
  </tr>
  <tr>
    <th>项目负责人</th>
    <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?>&nbsp;</td>
    <th>计划年度</th>
    <td><?=$project->getDeclareYear()?>（<?=$project->getTypeCurrentGroup()?>）&nbsp;</td>
  </tr>
  <tr>
    <th>填报时间</th>
    <td><?=$project->getDeclareAt("Y-m-d")?>&nbsp;</td>
    <th>起止年限</th>
    <td><?=$project->getStartAt().' 至 '.$project->getEndAt()?>&nbsp;</td>
  </tr>
  <tr>
    <th>评审得分</th>
    <td><?=$project->getScore()?>&nbsp;</td>
    <th><?=$project->getCatId()==15 ? '申报书资料' : '项目资料';?></th>
    <td>
        <?=Button::setUrl(site_url("apply/project/show/id/".$project->getProjectId()))->link('查看')?>
    </td>
  </tr>
  <tr>
    <th>评审分组</th>
    <td colspan="3"><?=$project->getTypeGroup()?></td>
  </tr>
  <tr>
    <th>操作记录</th>
    <td>
      <?=Button::setName('查看记录')->setUrl(site_url("admin/history/index/id/".$project->getProjectId()))->setWidth('550px')->setHeight('80%')->window()?>
    </td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  <tr></tr>
  <?php if($project->getStatement() >= 29):?>
    <tr>
      <th colspan="4">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
    </tr>
    <tr>
      <th>立项编号</th>
      <td><?=$project->getRadicateId()?>&nbsp;</td>
      <th>立项经费</th>
      <td><?=$project->getRadicateMoney()?>万元&nbsp;
      </td>
    </tr>
    <tr>
      <th>计划任务书</th>
      <td><?=$project->getStateForTask()?>
        <?php if($project->getStateForPlanBook() > 0):?>
          【<a href="<?=site_url("declare/task/show/id/".$project->getProjectId())?>">查看</a>】
        <?php endif;?>
      </td>
      <th>验收书</th>
      <td><?=$project->getStateForComplete()?>
        <?php if($project->getStateForCompleteBook() > 0):?>
          【<a href="<?=site_url("declare/complete/show/id/".$project->getProjectId())?>">查看</a>】
        <?php endif;?>
      </td>
    </tr>
  <?php endif;?>
  <?php
  $historys = $project->getHistoryProjectForUser();
  if($historys->getTotal()):
   ?>
 <tr>
  <th colspan="4" class="text-center"><b>负责人历史项目一览表</b></th>
</tr>
<?php if ($project->getUserId()): ?>
  <?php while($history = $historys->getObject()):?>
    <tr>
      <th>项目名称</th>
      <td><?=link_to("apply/project/show/id/".$history->getProjectId(),$history->getSubject())?></td>
      <th>起止年限</th>
      <td><?=$history->getStartAt().' 至 '.$history->getEndAt()?></td>
    </tr>
  <?php endwhile;?>
<?php endif;?>
<?php endif;?>
</table>