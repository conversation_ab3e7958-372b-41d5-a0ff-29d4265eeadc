<style>
    td{border: 1px solid #000;}
</style>
<table width="100%" border="0" cellpadding="0" cellspacing="0">
    <!--DWLayoutTable-->
    <tr>
        <td align="center"  bgcolor="#F0F5FA"><strong style="font-size:18px;">德阳市企业基本情况登记表（单位：万元）</strong></td>
    </tr>
    <tr>
        <td height="525" valign="top">
            <table width="100%" border="0" cellpadding="0" cellspacing="1" bgcolor="#000000">
                <tr>
                    <td width="15%" bgcolor="#FFFFFF">企业名称</td>
                    <td width="35%" bgcolor="#FFFFFF"><?=$row->subject?></td>
                    <td width="15%" bgcolor="#FFFFFF">年度</td>
                    <td width="35%" bgcolor="#FFFFFF"><?=$row->assetsInfo->year?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;联系人</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->linkman?></td>
                    <td bgcolor="#FFFFFF">&nbsp;E-mail</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->linkman_email?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;联系电话</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->mobile?></td>
                    <td bgcolor="#FFFFFF">&nbsp;职工总数</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->staff_number?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;年度开发新产品数量</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->productsInfo->new_product_num?></td>
                    <td bgcolor="#FFFFFF">&nbsp;年度企业销售收入</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->assetsInfo->revenue_assets?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;年度企业利润</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->assetsInfo->tax_assets?></td>
                    <td bgcolor="#FFFFFF">&nbsp;年底企业交税</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->assetsInfo->tax_pay?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;年度技术开发经费</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->assetsInfo->tech_fund?></td>
                    <td bgcolor="#FFFFFF">&nbsp;年度新产品销售收入</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->assetsInfo->new_pro_sales?></td>
                </tr>

                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>年底企业技术中心情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;总人数</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_center_total?></td>
                    <td bgcolor="#FFFFFF">&nbsp;高级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_center_high?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;中级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_center_middle?></td>
                    <td bgcolor="#FFFFFF">&nbsp;初级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_center_low?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>年底企业科技人员情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;总人数</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_person_total?></td>
                    <td bgcolor="#FFFFFF">&nbsp;高级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_person_high?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;中级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_person_middle?></td>
                    <td bgcolor="#FFFFFF">&nbsp;初级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->tech_person_low?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>企业管理人员情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;总人数</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->manager_total?></td>
                    <td bgcolor="#FFFFFF">&nbsp;高级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->manager_high?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF">&nbsp;中级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->manager_middle?></td>
                    <td bgcolor="#FFFFFF">&nbsp;初级</td>
                    <td bgcolor="#FFFFFF">&nbsp;<?=$row->staffsInfo->manager_low?></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>企业主导产品情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4">
                        <table width="100%" border="1" cellpadding="0" cellspacing="0">
                            <tbody id="product">
                            <tr>
                                <td width="25%" class="borders">产品名称</td>
                                <td width="65%" class="borders">产品简介</td>
                            </tr>
                            <?php
                                foreach($row->productDetail as $item):
                            ?>
                            <tr>
                                <td class="borders">&nbsp;<?=$item['product_name']?></td>
                                <td class="borders">&nbsp;<?=$item['product_desc']?></td>
                            </tr>
                            <?php
                                endforeach;
                            ?>

                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>企业在研项目情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4">
                        <table width="100%" border="1" cellpadding="0" cellspacing="0">
                            <tbody id="project">
                            <tr>
                                <td width="15%" class="borders">名称</td>
                                <td width="10%" class="borders">编号</td>
                                <td width="8%" class="borders">级别</td>
                                <td width="10%" class="borders">计划来源</td>
                                <td width="8%" class="borders">合同金额</td>
                                <td width="8%" class="borders">开始时间</td>
                                <td width="8%" class="borders">结束时间</td>
                                <td width="25%" class="borders">简介</td>
                            </tr>
                            <?php
                            foreach($row->project as $item):
                            ?>
                                <tr>
                                    <td class="borders">&nbsp;<?=$item['subject']?></td>
                                    <td class="borders">&nbsp;<?=$item['sn']?></td>
                                    <td class="borders">&nbsp;<?=$item['level']?></td>
                                    <td class="borders">&nbsp;<?=$item['source']?></td>
                                    <td class="borders">&nbsp;<?=$item['amount']?></td>
                                    <td class="borders">&nbsp;<?=$item['start_at']?></td>
                                    <td class="borders">&nbsp;<?=$item['end_at']?></td>
                                    <td class="borders">&nbsp;<?=$item['brief']?></td>
                                </tr>
                                <?php
                            endforeach;
                            ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>企业专利情况</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4">
                        <table width="100%" border="1" cellpadding="0" cellspacing="0">
                            <tbody id="patent">
                            <tr>
                                <td width="10%" class="borders">专利类型</td>
                                <td width="30%" class="borders">申请号/专利号</td>
                                <td width="30%" class="borders">专利名称</td>
                                <td width="10%" class="borders">申请人</td>
                                <td width="10%" class="borders">申请日</td>
                                <td width="10%" class="borders">主分类号</td>
                            </tr>
                            <?php
                            foreach($row->unitpatents as $item):
                                ?>
                                <tr>
                                    <td class="borders">&nbsp;<?=$item['type']?></td>
                                    <td class="borders">&nbsp;<?=$item['sn']?></td>
                                    <td class="borders">&nbsp;<?=$item['subject']?></td>
                                    <td class="borders">&nbsp;<?=$item['owner']?></td>
                                    <td class="borders">&nbsp;<?=$item['time']?></td>
                                    <td class="borders">&nbsp;<?=$item['code']?></td>
                                </tr>
                                <?php
                            endforeach;
                            ?>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <!--
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><strong>驳回意见</strong></td>
                </tr>
                <tr>
                    <td bgcolor="#FFFFFF" colspan="4"><?=$row->assetsInfo->getStatus()?> <?=$row->assetsInfo->reject_reason?></td>
                </tr>
                -->

            </table>
        </td>
    </tr>
</table>
