<?php 
use Sofast\Core\Sf;
use Sofast\Support\View;
use Sofast\Core\Input;
?>
<div class="col-xs-12 col-sm-12 col-md-12">
<div class="btn-group btn-group-sm" role="group">
<!--<a href="javascript:void(0);" onclick="if(confirm('你确定修改选中单位的状态？')) $('#validateForm').submit();" class="lock">锁定选中单位</a>-->
<a href="javascript:history.back();" class="btn btn-info">
      <i class="ace-icon fa fa-arrow-left"></i>返回
    </a>
<p style="clear:both"></p>
</div>
<div class="search">
<div class="search">
<?php include_once("search_part.php");?>
</div>
</div>
<div class="box">
<table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <form id="validateForm" name="validateForm" method="post" action="<?=site_url("unit/manager/validate")?>">
    <tr>
      <th width="72"><input name="selectAll" id="selectAll" type="checkbox" /></th>
      <th width="54" class="fold_head" onclick="fold.showAll()">详</th>
      <th width="115"><?=getColumnStr('机构代码','code')?></th>
      <th width="230"><?=getColumnStr('单位名称','subject')?></th>
      <th width="115"><?=getColumnStr('单位性质','property')?></th>
      <th width="136"><?=getColumnStr('联系人','linkman')?></th>
      <th width="233"><?=getColumnStr('单位地址','address')?></th>
      <th width="118"><?=getColumnStr('注册时间','created_at')?></th>
      <th width="177">可用操作</th>
    </tr>
    <?php while($user = $pager->getObject()):?>
    <tr>
      <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td>
      <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
      <td align="center"><?=$user->getCode(0,true)?></td>
      <td><?=link_to("unit/manager/show/id/".$user->getUserId(),$user->getSubject())?></td>
      <td><?=$user->getProperty()?></td>
      <td align="center"><?=$user->getLinkman()?></td>
      <td><?=$user->getAddress()?></td>
      <td align="center"><?=$user->getCreatedAt("Y/m/d")?></td>
      <td align="center"><a href="<?=site_url("unit/register/two/userid/".$user->getUserId())?>" class="edit" target="_blank">代为完成</a> | <a href="<?=site_url("unit/manager/delete/userid/".$user->getUserId())?>" onclick="return confirm('删除后不可恢复，你确定删除？');" class="btn btn-sm btn-danger"><i class="ace-icon glyphicon glyphicon-remove-circle"></i> 删除</a></td>

    </tr>
    <tr class="fold_body">
          <td colspan="9"><?php include('more_part.php')?></td>
    </tr>
    <?php endwhile; ?>
    <tr>
      <td colspan="9"><span class="pager_bar">
        <?=$pager->fromto().$pager->navbar(10)?>
        </span>
        </td>
    </tr>
  </form>
</table>
</div>
</div>