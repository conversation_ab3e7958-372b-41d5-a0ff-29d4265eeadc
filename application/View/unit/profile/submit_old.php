<div class="page-header">
  <a href="javascript:history.back();" class="btn btn-success btn-sm"><i class="ace-icon fa fa-arrow-left"></i>返 回</a>&nbsp;<a href="<?=site_url("unit/profile/base")?>" class="btn btn-success btn-sm"><i class="ace-icon glyphicon glyphicon-pencil"></i>编 辑</a>
</div>
<div class="page-body">
  <div class="alert alert-success" role="alert">
    <h3>温馨提示：</h3>
    <ul>
      <li>1. 资料上报前请仔细校对所填写的单位信息，资料一经上报，在审核之前将不可更改；</li>
      <li>2. 资料上报后需要管理员进行审核、认证；</li>
    </ul>
    <?php if(count($msg)):?>
      <h3>系统检查到您填写的单位资料有以下问题，请检查并修改：</h3>
      <ul>
        <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
          <li>（<?=($i+1)?>）
            <?=$msg[$i]?>
          </li>
        <?php endfor;?>
      </ul>
    <?php endif;?>
    <p>&nbsp;</p>
    <p>
      <form name="up" id="up" method="post" action="">
        <input type="hidden" name="userid" value="<?=$corporation->getUserId()?>" />
        <input type="hidden" name="fromUrl" value="<?=getFromUrl()?>" />
        <?php if(count($msg)): ?>
          <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 单位资料有误，请返回修改后再上报！</button>
        <?php else:?>
          <button type="submit" class="btn btn-info" onclick="return confirm('单位资料一经上报将不可更改，您确定上报吗？')"><i class="ace-icon glyphicon glyphicon-send"></i> 上报单位资料</button>
        <?php endif;?>
      </form>
    </p>
  </div>
</div>
