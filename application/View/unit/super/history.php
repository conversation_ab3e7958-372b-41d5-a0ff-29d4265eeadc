<div class="col-xs-12 col-sm-12 col-md-12">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info btn-sm hidden-480">
      <i class="ace-icon fa fa-check-square-o"></i>全部选中</a>
   <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',{shift:1});" class="btn btn-primary btn-sm"><i class="ace-icon fa fa-search"></i>高级搜索</a> -->
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include('search_history_part.php');?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr> 
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th><?=getColumnStr('项目名称','project_id')?>&nbsp;</th>
      <th><?=getColumnStr('处理人姓名','user_name')?>&nbsp;</th>
      <th><?=getColumnStr('操作用户组','user_group_id')?>&nbsp;</th>
      <th><?=getColumnStr('处理时间','updated_at')?>&nbsp;</th>
      </tr>
      <?php while($history = $pager->getObject()):?>
      <tr>
      <td><input name="select_id[]" type="checkbox" value="<?=$history->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer;" onclick="$('#show_<?=$history->getId()?>').toggle()"><strong>+</strong></label></td>
      <td title="<?=$history->getContent()?>"><?=$history->getProjectSubject()?></td>
      <td align="center"><?=$history->getUserName()?></td>
      <td align="center"><?=$history->getUserGroupName()?></td>
      <td align="center"><?=$history->getUpdatedAt("Y/m/d")?></td>
      </tr>
     <tr id="show_<?=$history->getId()?>" style="display:none;">
        <td colspan="10"><?=$history->getContent()?></td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="10" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
