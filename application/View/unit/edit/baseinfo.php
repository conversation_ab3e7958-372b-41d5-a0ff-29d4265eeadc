<div class="row">

    <div style="clear: both"></div>
    <link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
    <script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script>
    <script language="javascript" type="text/javascript">
        var debug=false;
        var is_submit=false;


        function delHtmlTag(str)
        {
            return $.trim(str.replace(/<[^>]+>/g,""));//去掉所有的html标记
        }

        function formSubmit()
        {
            var id = $('.tab-content .active').find('form').attr('id');
            if($('.tab-content .active').attr('id')=='tab1')
            {
                if(!flagInput(id))
                {
                    showAlert('请完整填写内容');
                    return false;
                }
            }
            is_submit = true;
            if(debug) return $('#'+id).submit();
            else return ajaxSubmit(id);
        }

        function flagInput(id)
        {

            var flag = true;
            $('#'+id).find('input[type=text],textarea').each(function(){
                var val = String($(this).val());
                if(val!='0' && val=='' && $(this).attr('class')!='tagInputField ui-autocomplete-input')
                {
                    $(this).closest('.form-group').addClass('has-error');
                    flag=false;
                }
                else $(this).closest('.form-group').removeClass('has-error');
            });

            var radio_names = ['skill_domain','innovation_type','fruit_level','standard','yield_study','fruits','patent'];

            for(index in radio_names)
            {
                if($('#'+id).find('input[name*="['+radio_names[index]+']"]').length>0){
                    if($('#'+id).find('input[name*="['+radio_names[index]+']"]:checked').length>0)
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').removeClass('has-error');
                    else
                    {
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').addClass('has-error');
                        flag=false;

                    }
                }
            }

            return flag;
        }
    </script>
    <div class="header">
        <i class="ace-icon fa fa-arrow-circle-down fa-2x green"></i> 请认真填写以下内容
        <div class="pull-right">
            <div class="tool">
                <span class="red">（* 每页填写完都需要点击<span class="badge badge-info">保存</span>）</span>
                <a href="#" onclick="return formSubmit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存</a>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <?php
            foreach($tab as $k=>$item):
                ?>
                <li class="<?=$pathinfo['filename']==pathinfo($item['url'])['filename'] ? 'active' : ''?>" >
                    <a href="<?=$item['url']?>"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>
                        <?=$item['text']?></a>
                </li>
                <?php
            endforeach;
            ?>

        </ul>
        <div class="tab-content">
            <div id="tab1" class="tab-pane fade  in active ">
                <form class="form-horizontal" id="form_1" action="" method="post">

                    <?php
                    $readonly = isset($readonly) ? $readonly : false;
                    ?>

                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">组织机构代码</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[code]" id="code" class="form-control"  value="<?=$value['code']?>" readonly />
                                <?php
                            else:
                                echo '<label class="control-label">'.$value['code'].'</label>';
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">所属归口部门</label>
                        <div class="col-xs-12 col-sm-5 col-md-9">

                            <?php
                            if($readonly===false):
                                ?>
                                如果你修改了归口部门，帐号将自动锁定，你必须联系新的归口部门审核后才能登录系统!<br>
                                <?php
                            endif;
                            ?>
                            <select id="parent_id" onchange="getDepartment(this.value);" style="color:#666666;">
                                <option value="">--请选择归口部门类别--<?=$value['type']?></option>
                                <?php
                                foreach($value['department_tree'] as $v):
                                    ?>
                                    <option value="<?=$v['id']?>" <?=($v['id']==$value['type']?'selected':'')?>><?=$v['subject']?></option>
                                    <?php
                                endforeach;
                                ?>
                            </select>
                            <select name="baseinfo[department_id]" id="department_id" class="required" style="color:#666666;" title="请选择归口部门">
                                <option value="">--请选择归口部门--</option>
                                <?=$value['department_html']?>
                            </select><em>*</em>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">单位性质</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <select name="baseinfo[unit_property]" id="unit_property" class="required">
                                    <option value="0">请选择单位性质</option>
                                    <?=getSelectFromArray(get_select_data('unit_property'),$value['unit_property'])?>
                                </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">是否是高新企业</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <select name="baseinfo[is_hightech]" id="is_hightech" class="required">
                                    <option value="0">否</option>
                                    <option value="1">是</option>

                                </select>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">法人代表姓名</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[principal]" id="code" class="form-control"  value="<?=$value['principal']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['principal']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">法人代表电话</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[principal_phone]" id="code" class="form-control"  value="<?=$value['principal_phone']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['principal_phone']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">开户银行</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[bank_name]" id="code" class="form-control"  value="<?=$value['bank_name']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['bank_name']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">银行账号</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[bank_id]" id="code" class="form-control"  value="<?=$value['bank_id']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['bank_id']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">单位地址</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[address]" id="code" class="form-control"  value="<?=$value['address']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['address']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">所属地区</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[area]" id="code" class="form-control"  value="<?=$value['area']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['area']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">单位网页地址</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($readonly===false):
                                ?>
                                <input type="text" name="baseinfo[homepage]" id="homepage" class="form-control"  value="<?=$value['homepage']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['homepage']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">单位简介</label>
                        <div class="col-xs-12 col-sm-5 col-md-9">
                            <textarea name="baseinfo[remark]" id="remark"  cols="60" rows="10" ><?=$value['remark']?></textarea>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                    <script language="javascript" type="text/javascript">
                        function getDepartment(id)
                        {
                            $.post('<?=site_url('common/department')?>',{id:id},function(data){
                                $("#department_id").empty();
                                $('<option value="">--请选择归口部门--</option>').appendTo("#department_id");
                                $(data).appendTo("#department_id");
                            });
                        }
                    </script>
                    <script language="javascript" type="text/javascript">

                        function check(obj,type)
                        {
                            if($(obj).val() == ''){
                                $(obj).next("em").html('不能为空');
                                $(obj).css('border','1px red solid');
                                $(obj).focus();
                                return false;
                            }
                            $(obj).next("em").html('<span style="color:#000">验证中...</span>');
                            $.post('<?=site_url()?>ajax/check/userid/<?=$value['user_id']?>',{type:type,text:$(obj).val(),custom:1},function(json){
                                eval("json = "+json);
                                if(json.state){
                                    $(obj).val('').next("em").html(json.message);
                                    $(obj).css('border','1px red solid');
                                    $(obj).focus();
                                }else{
                                    $(obj).css('border','1px solid #d5d5d5');
                                    $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
                                }
                            });
                        }
                    </script>
                    <div class="clearfix"></div>
                </form>
            </div>
        </div>
    </div>

</div>