<div class="row">

    <div style="clear: both"></div>
    <link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
    <script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script>
    <script language="javascript" type="text/javascript">
        var debug=false;
        var is_submit=false;


        function delHtmlTag(str)
        {
            return $.trim(str.replace(/<[^>]+>/g,""));//去掉所有的html标记
        }

        function formSubmit()
        {
            var id = $('.tab-content .active').find('form').attr('id');
            if($('.tab-content .active').attr('id')=='tab1')
            {
                if(!flagInput(id))
                {
                    showAlert('请完整填写内容');
                    return false;
                }
            }
            is_submit = true;
            if(debug) return $('#'+id).submit();
            else return ajaxSubmit(id);
        }

        function flagInput(id)
        {

            var flag = true;
            $('#'+id).find('input[type=text],textarea').each(function(){
                var val = String($(this).val());
                if(val!='0' && val=='' && $(this).attr('class')!='tagInputField ui-autocomplete-input')
                {
                    $(this).closest('.form-group').addClass('has-error');
                    flag=false;
                }
                else $(this).closest('.form-group').removeClass('has-error');
            });

            var radio_names = ['skill_domain','innovation_type','fruit_level','standard','yield_study','fruits','patent'];

            for(index in radio_names)
            {
                if($('#'+id).find('input[name*="['+radio_names[index]+']"]').length>0){
                    if($('#'+id).find('input[name*="['+radio_names[index]+']"]:checked').length>0)
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').removeClass('has-error');
                    else
                    {
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').addClass('has-error');
                        flag=false;

                    }
                }
            }

            return flag;
        }
    </script>
    <div class="header">
        <i class="ace-icon fa fa-arrow-circle-down fa-2x green"></i> 请认真填写以下内容
        <div class="pull-right">
            <div class="tool">
                <span class="red">（* 每页填写完都需要点击<span class="badge badge-info">保存</span>）</span>
                <a href="#" onclick="return formSubmit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存</a>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <?php
            foreach($tab as $k=>$item):
                ?>
                <li class="<?=$pathinfo['filename']==pathinfo($item['url'])['filename'] ? 'active' : ''?>" >
                    <a href="<?=$item['url']?>"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>
                        <?=$item['text']?></a>
                </li>
                <?php
            endforeach;
            ?>

        </ul>
        <div class="tab-content">
            <div id="tab1" class="tab-pane fade  in active ">
                <form class="form-horizontal" id="form_1" action="" method="post">

                    <?php
                    $readonly = isset($readonly) ? $readonly : false;
                    ?>
                    <?php
                    if($value->status !=0):
                        ?>
                        <div class="form-group col-xs-12 col-sm-12 col-md-12">
                            <label class="control-label col-xs-12 col-sm-3 col-md-3"></label>
                            <div class="col-xs-12 col-sm-5 col-md-9">
                                <label class="control-label">状态：<?=$value->statusText?> </label>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <?php
                    endif;
                    ?>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3"></label>
                        <div class="col-xs-12 col-sm-5 col-md-9">
                            <label class="control-label">当年统计年度：<?=date('Y')?>  </label>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <input type="hidden" name="staff[year]" value="<?=date('Y')?>">
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">职工总人数</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <?php
                            if($value->status==0 || $value->status==-2):
                                ?>
                                <input type="text" name="staff[staff_number]" id="staff_number" class="form-control" value="<?=$value['staff_number']?>" />
                                <?php
                            else:
                                ?>
                                <label class="control-label"><?=$value['staff_number']?></label>
                                <?php
                            endif;
                            ?>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">企业技术中心情况</label>
                        <div class="col-xs-12 col-sm-5 col-md-6">
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">总人数</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_center_total]" id="tech_center_total" class="form-control" value="<?=$value['tech_center_total']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_center_total']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">初级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_center_low]" id="tech_center_low" class="form-control" value="<?=$value['tech_center_low']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_center_low']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                            <label class="control-label">中级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_center_middle]" id="tech_center_middle" class="form-control" value="<?=$value['tech_center_middle']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_center_middle']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">高级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_center_high]" id="tech_center_high" class="form-control" value="<?=$value['tech_center_high']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_center_high']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">企业科技人员情况</label>
                        <div class="col-xs-12 col-sm-5 col-md-6">
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">总人数</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_person_total]" id="tech_person_total" class="form-control" value="<?=$value['tech_person_total']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_person_total']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">初级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_person_low]" id="tech_person_low" class="form-control" value="<?=$value['tech_person_low']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_person_low']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                            <label class="control-label">中级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_person_middle]" id="tech_person_middle" class="form-control" value="<?=$value['tech_person_middle']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_person_middle']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">高级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[tech_person_high]" id="tech_person_high" class="form-control" value="<?=$value['tech_person_high']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['tech_person_high']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">企业管理人员情况</label>
                        <div class="col-xs-12 col-sm-5 col-md-6">
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">总人数</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[manager_total]" id="manager_total" class="form-control" value="<?=$value['manager_total']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['manager_total']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">初级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[manager_low]" id="manager_low" class="form-control" value="<?=$value['manager_low']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['manager_low']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">中级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[manager_middle]" id="manager_middle" class="form-control" value="<?=$value['manager_middle']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['manager_middle']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                            <div class="col-xs-12 col-sm-5 col-md-3">
                                <label class="control-label">高级</label>
                                <?php
                                if($value->status==0 || $value->status==-2):
                                    ?>
                                    <input type="text" name="staff[manager_high]" id="manager_high" class="form-control" value="<?=$value['manager_high']?>" />
                                    <?php
                                else:
                                    ?>
                                    <label class="control-label"><?=$value['manager_high']?></label>
                                    <?php
                                endif;
                                ?>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <script language="javascript" type="text/javascript">

                        function check(obj,type)
                        {
                            if($(obj).val() == ''){
                                $(obj).next("em").html('不能为空');
                                $(obj).css('border','1px red solid');
                                $(obj).focus();
                                return false;
                            }
                            $(obj).next("em").html('<span style="color:#000">验证中...</span>');
                            $.post('<?=site_url()?>ajax/check/userid/<?=$value['user_id']?>',{type:type,text:$(obj).val(),custom:1},function(json){
                                eval("json = "+json);
                                if(json.state){
                                    $(obj).val('').next("em").html(json.message);
                                    $(obj).css('border','1px red solid');
                                    $(obj).focus();
                                }else{
                                    $(obj).css('border','1px solid #d5d5d5');
                                    $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
                                }
                            });
                        }
                    </script>
                    <div class="clearfix"></div>
                </form>
            </div>
        </div>
    </div>

</div>