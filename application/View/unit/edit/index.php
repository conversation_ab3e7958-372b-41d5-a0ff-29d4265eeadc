<div class="row">
    <link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
    <script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script>
    <script language="javascript" type="text/javascript">
        var debug=false;
        var is_submit=false;


        function delHtmlTag(str)
        {
            return $.trim(str.replace(/<[^>]+>/g,""));//去掉所有的html标记
        }

        function formSubmit()
        {
            var id = $('.tab-content .active').find('form').attr('id');
            if($('.tab-content .active').attr('id')=='tab1')
            {
                if(!flagInput(id))
                {
                    showAlert('请完整填写内容');
                    return false;
                }
            }
            is_submit = true;
            if(debug) return $('#'+id).submit();
            else return ajaxSubmit(id);
        }

        function flagInput(id)
        {

            var flag = true;
            $('#'+id).find('input[type=text],textarea').each(function(){
                var val = String($(this).val());
                if(val!='0' && val=='' && $(this).attr('class')!='tagInputField ui-autocomplete-input')
                {
                    $(this).closest('.form-group').addClass('has-error');
                    flag=false;
                }
                else $(this).closest('.form-group').removeClass('has-error');
            });

            var radio_names = ['skill_domain','innovation_type','fruit_level','standard','yield_study','fruits','patent'];

            for(index in radio_names)
            {
                if($('#'+id).find('input[name*="['+radio_names[index]+']"]').length>0){
                    if($('#'+id).find('input[name*="['+radio_names[index]+']"]:checked').length>0)
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').removeClass('has-error');
                    else
                    {
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').addClass('has-error');
                        flag=false;

                    }
                }
            }

            return flag;
        }
    </script>
    <div class="header">
        <i class="ace-icon fa fa-arrow-circle-down fa-2x green"></i> 请认真填写以下内容
        <div class="pull-right">
            <div class="tool">
                <span class="red">（* 每页填写完都需要点击<span class="badge badge-info">保存</span>）</span>
                <a href="#" onclick="return formSubmit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存</a>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <?php
            foreach($tab as $k=>$item):
                ?>
                <li class="<?=$pathinfo['filename']==pathinfo($item['url'])['filename'] ? 'active' : ''?>" >
                    <a href="<?=$item['url']?>"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>
                        <?=$item['text']?></a>
                </li>
                <?php
            endforeach;
            ?>

        </ul>
        <div class="tab-content">
            <div id="tab1" class="tab-pane fade  in active ">
                <form class="form-horizontal" id="form_1" action="" method="post">

                    <script language="javascript" type="text/javascript" src="<?=site_path('js/DatePicker/WdatePicker.js')?>"></script>


                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">账号</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <input type="text"  id="name" class="form-control" value="<?=$value['user_name']?>" readonly />
                        </div>
                        <div class="clearfix"></div>
                    </div>

                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">姓名</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <input type="text"  id="user_username" class="form-control" value="<?=$value['user_username']?>" placeholder="请填写你的真实姓名" readonly/>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">身份证</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <input type="text" class="form-control" value="<?=$value['user_idcard']?>" readonly placeholder="认证的重要要素，填后不可改。" />
                        </div>
                        <div class="clearfix"></div>
                    </div>


                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">手机</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <input type="text" name="accountInfo[user_mobile]" id="user_mobile" class="form-control" onblur="check(this,'Mobile');" value="<?=$value['user_mobile']?>" /> <em></em>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">电子邮箱</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                                <input type="text" name="accountInfo[user_email]" id="user_email" class="form-control" onblur="check(this,'Email');" value="<?=$value['user_email']?>" /> <em></em>
                        </div>
                        <div class="clearfix"></div>
                    </div>

                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">最近登录时间</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <label class="control-label"><?=$value['lastlogin_at']?></label>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group col-xs-12 col-sm-12 col-md-12">
                        <label class="control-label col-xs-12 col-sm-3 col-md-3">最近登录IP</label>
                        <div class="col-xs-12 col-sm-5 col-md-3">
                            <label class="control-label"><?=$value['user_ip']?></label>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <script language="javascript" type="text/javascript">

                        function check(obj,type)
                        {
                            if($(obj).val() == ''){
                                $(obj).next("em").html('不能为空');
                                $(obj).css('border','1px red solid');
                                $(obj).focus();
                                return false;
                            }
                            $(obj).next("em").html('<span style="color:#000">验证中...</span>');
                            $.post('<?=site_url()?>ajax/check/userid/<?=$value['user_id']?>',{type:type,text:$(obj).val(),custom:1},function(json){
                                eval("json = "+json);
                                if(json.state){
                                    $(obj).val('').next("em").html(json.message);
                                    $(obj).css('border','1px red solid');
                                    $(obj).focus();
                                }else{
                                    $(obj).css('border','1px solid #d5d5d5');
                                    $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
                                }
                            });
                        }
                    </script>
                    <div class="clearfix"></div>
                </form>
            </div>
        </div>
    </div>

</div>