<div class="row">

    <div style="clear: both"></div>
    <link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
    <script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script>
    <script language="javascript" type="text/javascript">
        var debug=false;
        var is_submit=false;


        function delHtmlTag(str)
        {
            return $.trim(str.replace(/<[^>]+>/g,""));//去掉所有的html标记
        }

        function formSubmit()
        {
            var id = $('.tab-content .active').find('form').attr('id');
            if($('.tab-content .active').attr('id')=='tab1')
            {
                if(!flagInput(id))
                {
                    showAlert('请完整填写内容');
                    return false;
                }
            }
            is_submit = true;
            if(debug) return $('#'+id).submit();
            else return ajaxSubmit(id);
        }

        function flagInput(id)
        {

            var flag = true;
            $('#'+id).find('input[type=text],textarea').each(function(){
                var val = String($(this).val());
                if(val!='0' && val=='' && $(this).attr('class')!='tagInputField ui-autocomplete-input')
                {
                    $(this).closest('.form-group').addClass('has-error');
                    flag=false;
                }
                else $(this).closest('.form-group').removeClass('has-error');
            });

            var radio_names = ['skill_domain','innovation_type','fruit_level','standard','yield_study','fruits'];

            for(index in radio_names)
            {
                if($('#'+id).find('input[name*="['+radio_names[index]+']"]').length>0){
                    if($('#'+id).find('input[name*="['+radio_names[index]+']"]:checked').length>0)
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').removeClass('has-error');
                    else
                    {
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').addClass('has-error');
                        flag=false;

                    }
                }
            }

            return flag;
        }
    </script>
    <div class="header">
        <i class="ace-icon fa fa-arrow-circle-down fa-2x green"></i> 请认真填写以下内容
        <div class="pull-right">
            <div class="tool">
                <span class="red">（* 每页填写完都需要点击<span class="badge badge-info">保存</span>）</span>
                <a href="#" onclick="return formSubmit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存</a>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>

    <div class="tabbable">
        <ul class="nav nav-tabs" id="myTab">
            <?php
            foreach($tab as $k=>$item):
                ?>
                <li class="<?=$pathinfo['filename']==pathinfo($item['url'])['filename'] ? 'active' : ''?>" >
                    <a href="<?=$item['url']?>"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>
                        <?=$item['text']?></a>
                </li>
                <?php
            endforeach;
            ?>

        </ul>
        <div class="tab-content">
            <div id="tab1" class="tab-pane fade  in active ">
                <form class="form-horizontal" id="form_1" action="" method="post">
                    <script language="javascript" type="text/javascript" src="<?=site_path("js/DatePicker/WdatePicker.js")?>"></script>
                    <style>
                        .pic-thum{
                            position: absolute;  z-index: 9;  left: 67px;  top: 0;
                            border: 2px #000 solid;
                            display: none;
                        }
                        .pic-thum-td:hover .pic-thum{display: block;}
                    </style>
                    <?php
                    $readonly = isset($readonly) ? $readonly : false;
                    ?>
                    <div class="main">
                        <?php
                        if($readonly===false):
                            ?>
                            <div id="filemanager">
                                <span style="color:#0080FF">你已经上传的附件：<span style="color:#F00"><?=$value['num']->getSumSize()?></span>，你可以点击下方的蓝色按钮上传其他文件。</span>
                                <div id="uploader" class="wu-example">
                                    <!--用来存放文件信息-->
                                    <div class="btns" style="position: relative">
                                        <div id="fileList" class="uploader-list">
                                        </div>
                                    </div>
                                    <div id="filePicker">上传附件</div>
                                </div>
                            </div>
                            <br>
                            <?php
                        endif;
                        ?>
                        <div class="box">
                            <table cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
                                <form id="form1" name="form1" method="post" action="">

                                    <tr >
                                        <th><input name="selectAll" id="selectAll" type="checkbox" /></th>
                                        <th>ID</th>
                                        <th>文件名</th>
                                        <th>文件大小</th>
                                        <th>备注</th>
                                        <th>上传时间</th>
                                        <th>操作</th>
                                    </tr>

                                    <?php while($file = $value['pager']->getObject()):?>
                                        <tr>
                                            <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$file->getId()?>" /></td>
                                            <td align="center"><?=$file->getId()?></td>
                                            <td width="200" class="pic-thum-td" style="position: relative"><a href="<?=site_url("user/files/show/id/".$file->getId());?>">
                                                    <?=$file->getFileName()?>
                                                </a>
                                                <?php
                                                $picExt = ['jpg','bmp','png','jpeg','gif'];
                                                if(in_array(strtolower($file->getFileExt()),$picExt)):
                                                    ?>
                                                    <div class="pic-thum">
                                                        <img src="<?=site_url("user/files/show/id/".$file->getId());?>" style="height: 100px;" alt="">
                                                    </div>
                                                    <?php
                                                endif;
                                                ?>
                                            </td>
                                            <td align="center"><?=$file->getFileSize()?></td>
                                            <td><?=$file->getFileNote(20)?></td>
                                            <td align="center"><?=$file->getCreatedAt("Y-m-d H:i:s")?></td>
                                            <td align="center"><a href="javascript:void(0);" onclick="return showWindow('文件备注','<?=site_url("user/files/edit/id/".$file->getId())?>',400,300);" class="edit">编辑</a><a href="javascript:void(0);" onclick="del_file(<?=$file->getId()?>,this)" class="delete">删除</a></td>
                                        </tr>
                                    <?php endwhile; ?>
                                    <tr>
                                        <td colspan="9"><span class="pager_bar">
            <?=$value['pager']->fromto().$value['pager']->navbar(5)?>
            </span>
                                        </td>
                                    </tr>
                                </form>
                            </table>
                        </div>
                    </div>
                    <script language="javascript" type="text/javascript">
                        function upload(json){
                            $('<tr id="list_'+json[0].id+'"><td align="center"><input name="select_id[]"  type="checkbox" value="'+json[0].id+'"  /></td><td align="center">'+json[0].id+'</td><td><a href="<?=site_url("up_files")?>'+'/'+json[0].path+'">'+json[0].file_name+'</a></td><td align="center">'+json[0].size+'B</td><td align="center"></td><td align="center">'+json[0].time+'</td><td align="center"><a href="javascript:row_del('+json[0].id+');" onclick="return confirm(\'确定删除？\');">删除</a></td></tr>').insertAfter("#filemanager");
                            closeWindow();
                        }

                        function del_file(id,me){
                            if(confirm('确定要删除吗')){
                                var me = $(me);
                                $.post("<?=site_url("common/removeAttachments")?>",{file_id:id},function(json){
                                    eval("json = "+json);
                                    if(json.st == 'OK') {
                                        me.parent().parent().remove();
                                    }else{
                                        alert(json.msg);
                                    }
                                });
                            }
                        }
                    </script>
                    <script language="javascript" type="text/javascript">


                        $(function(){
                            // 初始化Web Uploader
                            var uploader = WebUploader.create({
                                // 选完文件后，是否自动上传。
                                auto: true,

                                // swf文件路径
                                swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
                                // 文件接收服务端。
                                server: "<?=site_url('common/webupload')?>",
                                formData: {
                                    item_type: 'corporation'
                                },
                                // 选择文件的按钮。可选。
                                // 内部根据当前运行是创建，可能是input元素，也可能是flash.
                                pick: '#filePicker',
                                fileVal:'file[]',
                                fileNumLimit:5,
                                resize: false,
                                // 只允许选择图片文件。
                                accept: {
                                    title: 'Images',
                                    extensions: 'gif,jpg,jpeg,bmp,png,txt,doc,docx,rar,zip',
                                    mimeTypes: '.gif,.jpg,.jpeg,.bmp,.png,.txt,.rar,.zip,.doc,.xls,.docx,.xlsx,.pdf'
                                }
                            }).on('ready', function () {
                                $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
                            });
                            // 当有文件添加进来的时候
                            uploader.on( 'fileQueued', function( file ) {
                                var $li = $(
                                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                                        '<img>' +
                                        '<div class="info">' + file.name + '</div>' +
                                        '</div>'
                                    ),
                                    $img = $li.find('img');


                                // $list为容器jQuery实例
//			$list.append( $li );
                                $("#fileList").html( $li );

                                // 创建缩略图
                                // 如果为非图片文件，可以不用调用此方法。
                                // thumbnailWidth x thumbnailHeight 为 100 x 100
                                uploader.makeThumb( file, function( error, src ) {
                                    if ( error ) {
                                        $img.replaceWith('<span>不能预览</span>');
                                        return;
                                    }

                                    $img.attr( 'src', src );
                                }, 300, 300 );
                            });
                            // 文件上传过程中创建进度条实时显示。
                            uploader.on( 'uploadProgress', function( file, percentage ) {
                                var $li = $( '#'+file.id ),
                                    $percent = $li.find('.progress span');

                                // 避免重复创建
                                if ( !$percent.length ) {
                                    $percent = $('<p class="progress"><span></span></p>')
                                        .appendTo( $li )
                                        .find('span');
                                }

                                $percent.css( 'width', percentage * 100 + '%' );
                            });

                            // 文件上传成功，给item添加成功class, 用样式标记上传成功。
                            uploader.on( 'uploadSuccess', function( file,response ) {
                                alert('上传成功');
                                location.href="<?=site_url('unit/edit/attachment')?>";
                            });

                            // 文件上传失败，显示上传出错。
                            uploader.on( 'uploadError', function( file ) {
                                var $li = $( '#'+file.id ),
                                    $error = $li.find('div.error');

                                // 避免重复创建
                                if ( !$error.length ) {
                                    $error = $('<div class="error"></div>').appendTo( $li );
                                }

                                $error.text('上传失败');
                            });

                            // 完成上传完了，成功或者失败，先删除进度条。
                            uploader.on( 'uploadComplete', function( file ) {
                                $( '#'+file.id ).find('.progress').remove();
                            });

                            // 初始化技术职称证书扫描件
                            var uploader_title = WebUploader.create({

                                // 选完文件后，是否自动上传。
                                auto: true,

                                // swf文件路径
                                swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
                                // 文件接收服务端。
                                server: "<?=site_url('common/webupload')?>",

                                // 选择文件的按钮。可选。
                                // 内部根据当前运行是创建，可能是input元素，也可能是flash.
                                pick: '#titlePicker',
                                fileVal:'file[]',
                                fileNumLimit:5,
                                resize: false,
                                // 只允许选择图片文件。
                                accept: {
                                    title: 'Images',
                                    extensions: 'gif,jpg,jpeg,bmp,png',
                                    mimeTypes: 'image/*'
                                }
                            }).on('ready', function () {
                                $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
                            });
                            // 当有文件添加进来的时候
                            uploader_title.on( 'fileQueued', function( file ) {
                                var $li = $(
                                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                                        '<img>' +
                                        '<div class="info">' + file.name + '</div>' +
                                        '</div>'
                                    ),
                                    $img = $li.find('img');


                                // $list为容器jQuery实例
//			$list.append( $li );
                                $("#titleList").html( $li );

                                // 创建缩略图
                                // 如果为非图片文件，可以不用调用此方法。
                                // thumbnailWidth x thumbnailHeight 为 100 x 100
                                uploader.makeThumb( file, function( error, src ) {
                                    if ( error ) {
                                        $img.replaceWith('<span>不能预览</span>');
                                        return;
                                    }

                                    $img.attr( 'src', src );
                                }, 300, 300 );
                            });
                            // 文件上传过程中创建进度条实时显示。
                            uploader_title.on( 'uploadProgress', function( file, percentage ) {
                                var $li = $( '#'+file.id ),
                                    $percent = $li.find('.progress span');

                                // 避免重复创建
                                if ( !$percent.length ) {
                                    $percent = $('<p class="progress"><span></span></p>')
                                        .appendTo( $li )
                                        .find('span');
                                }

                                $percent.css( 'width', percentage * 100 + '%' );
                            });

                            // 文件上传成功，给item添加成功class, 用样式标记上传成功。
                            uploader_title.on( 'uploadSuccess', function( file,response ) {
                                var json = eval(response);
                                var json = eval(json._raw);
                                $("#title_pic").val(json[0].path);
                                $( '#'+file.id ).addClass('upload-state-done');
                            });

                            // 文件上传失败，显示上传出错。
                            uploader_title.on( 'uploadError', function( file ) {
                                var $li = $( '#'+file.id ),
                                    $error = $li.find('div.error');

                                // 避免重复创建
                                if ( !$error.length ) {
                                    $error = $('<div class="error"></div>').appendTo( $li );
                                }

                                $error.text('上传失败');
                            });

                            // 完成上传完了，成功或者失败，先删除进度条。
                            uploader_title.on( 'uploadComplete', function( file ) {
                                $( '#'+file.id ).find('.progress').remove();
                            });

                        });
                    </script>
                    <script language="javascript" type="text/javascript">

                        function check(obj,type)
                        {
                            if($(obj).val() == ''){
                                $(obj).next("em").html('不能为空');
                                $(obj).css('border','1px red solid');
                                $(obj).focus();
                                return false;
                            }
                            $(obj).next("em").html('<span style="color:#000">验证中...</span>');
                            $.post('<?=site_url()?>ajax/check/userid/<?=$value['user_id']?>',{type:type,text:$(obj).val(),custom:1},function(json){
                                eval("json = "+json);
                                if(json.state){
                                    $(obj).val('').next("em").html(json.message);
                                    $(obj).css('border','1px red solid');
                                    $(obj).focus();
                                }else{
                                    $(obj).css('border','1px solid #d5d5d5');
                                    $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
                                }
                            });
                        }
                    </script>
                    <div class="clearfix"></div>
                </form>
            </div>
        </div>
    </div>

</div>