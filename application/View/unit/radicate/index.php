<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
   <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php') ?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th width="80"><?=getColumnStr('立项编号','radicate_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('承担单位','corporation_id')?></th>
      <th><?=getColumnStr('立项经费','radicate_money')?></th>
      <th><?=getColumnStr('项目负责人','user_name')?></th>
      <th width="110">资金拨付</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
      <td align="center"><?=$project->getRadicateId()?></td>
      <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
      <td><?=$project->getTypeSubject()?></td>
      <td><?=$project->getCorporationName()?></td>
      <td align="center"><?=$project->getRadicateMoney()?></td>
      <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
      <td align="center"><a href="javascript:void(0);" onclick="return showWindow('经费拨付','<?=site_url("office/payfor/edit/id/".$project->getProjectId())?>',450,400);" class="btn btn-danger btn-sm" role="button">拨付</a> <a href="javascript:void(0);" onclick="return showWindow('分年度拨款记录','<?=site_url("office/payfor/index/id/".$project->getProjectId())?>',450,400);"  class="btn btn-info btn-sm" role="button">记录</a></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
        <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="9" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>