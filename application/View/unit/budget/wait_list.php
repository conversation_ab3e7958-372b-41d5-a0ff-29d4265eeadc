<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
  <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include('search_part.php');?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("unit/budget/wait_list")?>">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>详</th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('所属年度','declare_year')?></th>
      <th><?=getColumnStr('项目负责人','user_id')?></th>
      <th><?=getColumnStr('所属归口部门','department_id')?></th>
      <th>可用操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
      <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
      <td><?=$project->getMark()?><?=link_to("declare/budget/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
      <td><?=$project->getTypeSubject()?></td>
      <td align="center"><?=$project->getDeclareYear()?>（<?=$project->getTypeCurrentGroup()?>）</td>
      <td align="center"><?=$project->getUserName()?></td>
      <td><?=$project->getDepartmentName()?></td>
      <td align="center"><a href="<?=site_url("unit/budget/doSubmit/id/".$project->getProjectId())?>" class="btn btn-info submit" role="button">推荐</a> | <a href="javascript:void(0);" onclick="return showWindow('退回项目预算','<?=site_url("unit/budget/doRejected/id/".$project->getProjectId())?>',450,350);" class="glyphicon glyphicon-chevron-left">退回</a> | <a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("Assess/history/index/type/budget/id/".$project->getProjectId())?>',450,350);return false;" class="message">查看记录</a></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="8">
         <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="8" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
