<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                待审核的验收书
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <?=Button::setName('审核选中项')->setEvent('validateForm.action=\''.site_url("unit/complete/doSubmit").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                <?=Button::setName('退回选中项')->setEvent('validateForm.action=\''.site_url("unit/complete/doRejected").'\';$(\'#validateForm\').submit()')->setIcon('undo')->setClass('btn-alt-danger')->button()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default">
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("unit/unit/complete_wait")?>">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th><?=getColumnStr('项目编号','accept_id')?></th>
                                    <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                    <th><?=getColumnStr('项目负责人','user_id')?></th>
                                    <th class="text-center"><?=getColumnStr('总经费','total_money')?></th>
                                    <th class="text-center"><?=getColumnStr('立项年度','radicate_year')?></th>
                                    <th width="180" class="text-center">可用操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($project = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                        <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
                                        <td><?=$project->getAcceptId()?></td>
                                        <td><?=$project->getMark()?><?=link_to("declare/complete/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
                                        <td><?=$project->getUserName()?></td>
                                        <td align="center"><?=$project->getTotalMoney()?> 万元</td>
                                        <td align="center"><?=$project->getRadicateYear()?></td>
                                        <td align="center">
                                            <?=Button::setName('审核')->setUrl(site_url("unit/complete/doSubmit/id/".$project->getProjectId()))->setIcon('check')->link()?>
                                            <?=Button::setName('退回')->setTitle('退回验收书')->setUrl(site_url("unit/complete/doRejectedOnlyOne/id/".$project->getProjectId()))->setClass('btn-alt-danger')->setIcon('undo')->setWidth('800px')->setHeight('600px')->window()?>
                                        </td>
                                    </tr>
                                    <tr id="show_<?=$project->getId()?>" style="display:none;">
                                        <td colspan="12">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


