<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
    <a href="javascript:void(0);" onclick="validateForm.action='<?=site_url("unit/complete/doRejectedApply")?>';$('#validateForm').submit();" class="btn btn-danger doit" role="button">驳回选中项</a>   
    <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a> -->
    <p style="clear:both;"></p>
  </div>
  <div class="search">
    <?php include('apply_search_part.php');?>
  </div>
  <div class="box">
    <div class="no-padding no-margin panel panel-default" >
      <div class="panel-heading">
        <i class="ace-icon fa fa-list"></i> 已审核的结题申请
      </div>
      <form id="validateForm" name="validateForm" method="post" action="">
        <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
          <tr>
            <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
            <th width="30">详</th>
            <th><?=getColumnStr('立项编号','radicate_id')?></th>
            <th width="30%"><?=getColumnStr('项目名称','subject')?></th>
            <th><?=getColumnStr('项目负责人','user_id')?></th>
            <th width="10%"><?=getColumnStr('研究对象','type_id')?></th>
            <th><?=getColumnStr('立项年度','radicate_year')?></th>
            <th><?=getColumnStr('批准经费','approval_money')?></th>
            <th><?=getColumnStr('使用经费','real_money')?></th>
            <th>申请资料</th>
            <th>操 作</th>
          </tr>
          <?php while($project = $pager->getObject()):?>
            <tr> 
              <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
              <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
              <td align="center"><?=$project->getRadicateId()?></td>
              <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
              <td align="center"><?=$project->getUserName()?></td>
              <td align="center"><?=$project->getTypeSubject()?></td>
              <td align="center"><?=$project->getRadicateYear()?></td>
              <td align="center"><?=$project->getApprovalMoney()?> 万元</td>
              <td align="center"><?=$project->getRealMoney()?> 万元</td>
              <td align="center">
                <a target="_black" href="<?=site_path('up_files/'.$project->getApplyContent()->getFilePath())?>"  class="btn btn-xs btn-info">申请表</a>
                <a target="_black" href="<?=site_path('up_files/'.$project->getApplyReport()->getFilePath())?>"  class="btn btn-xs btn-warning">结题报告</a>
              </td>
              <td align="center">
                <a href="javascript:void(0);" onclick="return showWindow('退回结题申请','<?=site_url("unit/complete/doRejectedApplyOne/id/".$project->getProjectId())?>',400,280);" class="btn btn-danger btn-xs" role="button"><i class="glyphicon glyphicon-chevron-left"></i>驳 回</a>
              </td>
            </tr>
            <tr id="show_<?=$project->getId()?>" style="display:none;">
              <td colspan="12">
               <?php include('more_part.php') ?>
             </td>
           </tr>
         <?php endwhile;?>
         <tr>
          <td colspan="12" align="right"><span class="pager_bar">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
</div>
