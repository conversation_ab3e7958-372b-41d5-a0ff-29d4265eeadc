<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        软著库
                    </h3>
                    <div class="block-options">
                        <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                        <?=btn('link','导出数据',site_url('export/export/index/table/softs'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('store_search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                        <div class="no-padding no-margin panel panel-default" >
                            <table cellpadding="3" cellspacing="1" class="table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th><?=getColumnStr('软件全称','subject')?></th>
                                    <th><?=getColumnStr('登记号','sn')?></th>
                                    <th><?=getColumnStr('著作权人','owner')?></th>
                                    <th><?=getColumnStr('登记日期','register_at')?></th>
                                    <th>状态</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($soft = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$soft->getSoftId()?>" /></td>
                                        <td width="25%"><a href="<?=site_url('user/soft/show/id/'.$soft->getSoftId())?>"><?=$soft->getSubject()?></a></td>
                                        <td><?=$soft->getSn()?></td>
                                        <td><?=$soft->getOwner()?></td>
                                        <td><?=$soft->getRegisterAt()?></td>
                                        <td><?=$soft->getState()?></td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>