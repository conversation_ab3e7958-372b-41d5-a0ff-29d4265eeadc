<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block" data-toggle="tabs" role="tablist">
                    <?php $i=0;foreach($tabs as $tab):?>
                        <li class="nav-item" class="tab-pane">
                            <a class="<?php if($tab['method'] == $method || $i==0)echo 'nav-link active';else echo 'nav-link'; ?>" href="#<?=$tab['method']?>"><?=$tab['text']?></a>
                        </li>
                        <?php $i++;endforeach;?>
                </ul>
                <div class="block-content tab-content block-content-full">
                    <div class="tab-pane active" id="edit">
                        <p align="center" style="font-size: 22px">历史科研项目信息表</p>
                        <div align="center">
                            <table width="680" align="center" cellpadding="5" cellspacing="0" class="table table-bordered" border="1">
                                <tbody>
                                <tr>
                                    <td width="113" align="center">
                                        项目名称
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getSubject()?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        立项年度
                                    </td>
                                    <td>
                                        <?=$project->getRadicateYear()?>
                                    </td>
                                    <td width="113">
                                        立项编号
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getRadicateId()?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        项目来源
                                    </td>
                                    <td>
                                        <?=$project->getCatSubject()?>
                                    </td>
                                    <td width="113" align="center">
                                        项目专项名称
                                    </td>
                                    <td>
                                        <?=$project->getSpecialSubject()?>
                                    </td>
                                </tr>
                                <?php
                                    if($project->getCatId()==15):
                                ?>
                                <tr>
                                    <td width="113" align="center">
                                        项目类别
                                    </td>
                                    <td>
                                        <?=$project->getProjectTypeSubject()?>
                                    </td>
                                    <td width="113" align="center">
                                        研究对象
                                    </td>
                                    <td>
                                        <?=$project->getTypeSubject()?>
                                    </td>
                                </tr>
                                <?php endif;?>
                                <tr>
                                    <td width="113" align="center">
                                        承担单位
                                    </td>
                                    <td>
                                        <?=$project->getCorporationName()?>
                                    </td>
                                    <td width="113" align="center">
                                        项目负责人
                                    </td>
                                    <td>
                                        <?=$project->getUserName()?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        项目级别
                                    </td>
                                    <td>
                                        <?=getCheckedStr(['国家级','省部级','市厅级','企业级'],$project->getProjectLevel())?>
                                    </td>
                                    <td width="113" align="center">
                                        项目状态
                                    </td>
                                    <td>
                                        <?=$project->getGovState()?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        起止时间
                                    </td>
                                    <td colspan="3">
                                        <?=$project->getStartAt()?> 至 <?=$project->getEndAt()?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        经费预算
                                    </td>
                                    <td colspan="3" width="529">
                                        <p align="left">专项经费 <u>&nbsp;<?=(float)$project->getRadicateMoney()?>&nbsp;</u>&nbsp;万元 &nbsp;自筹经费 <u>&nbsp;<?=(float)$project->getOwnMoney()?>&nbsp;</u>&nbsp;万元</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        投入情况
                                    </td>
                                    <td colspan="3" width="529">
                                        <p align="left">专项经费 <u>&nbsp;<?=(float)$project->getBaseinfo('apply')->getData('radicate_money_expend')?>&nbsp;</u>&nbsp;万元 &nbsp;自筹经费 <u>&nbsp;<?=(float)$project->getBaseinfo('apply')->getData('own_money_expend')?>&nbsp;</u>&nbsp;万元</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        项目实施情况
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getBaseinfo('apply')->getData('ssqk')?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        形成成果情况
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getBaseinfo('apply')->getData('cgqk')?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        项目联系人
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getBaseinfo('apply')->getData('linkman')?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        联系人电话
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=$project->getBaseinfo('apply')->getData('linkman_tel')?>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="113" align="center">
                                        项目概述
                                    </td>
                                    <td colspan="3" width="529">
                                        <?=showText($project->getSummary())?>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <p>&nbsp;</p>
                    </div>
                    <div class="tab-pane" id="cooperation">
                        <p align="center" style="font-size: 22px">合作单位</p>
                        <table width="100%" class="table table-hover">
                            <thead>
                            <tr>
                                <td width="10%" align="center">序号</td>
                                <td width="20%" align="center">信用代码</td>
                                <td width="20%" align="center">名称</td>
                                <td width="30%" align="center">在本项目中分工</td>
                                <td width="10%" align="center">总经费（万元）</td>
                            </tr>
                            </thead>
                            <tbody id="paper-content">
                            <?php
                            $cooperations = $project->getCooperations();
                            while($cooperation = $cooperations->getObject()):
                                ?>
                                <tr>
                                    <td align="center">
                                        <?=$cooperation->getNo()?>
                                    </td>
                                    <td>
                                        <?=$cooperation->getCode()?>
                                    </td>
                                    <td><?=$cooperation->getCompanyName()?></td>
                                    <td><?=$cooperation->getWorks()?></td>
                                    <td><?=$cooperation->getTotalMoney()?:'0.00'?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div class="tab-pane" id="member">
                        <table width="100%" class="table table-hover">
                            <thead>
                            <tr>
                                <td width="10%" align="center">序号</td>
                                <td width="15%" align="center">身份证</td>
                                <td width="10%" align="center">姓名</td>
                                <td width="10%" align="center">学位</td>
                                <td width="10%" align="center">职称</td>
                                <td width="15%" align="center">所在单位</td>
                                <td width="20%" align="center">在本项目中的任务</td>
                            </tr>
                            </thead>
                            <tbody id="paper-content">
                            <?php
                            $members = $project->getMembers();
                            while($member = $members->getObject()):
                                ?>
                                <tr>
                                    <td align="center">
                                        <?=$member->getNo()?>
                                    </td>
                                    <td>
                                        <?=$member->getCertificate()?>
                                    </td>
                                    <td><?=$member->getName()?></td>
                                    <td>
                                        <?=$member->getEducation()?>
                                    </td>
                                    <td>
                                        <?=$member->getTitleType()?>
                                    </td>
                                    <td><?=$member->getCompanyName()?></td>
                                    <td><?=$member->getWorks()?></td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div class="tab-pane" id="target">
                        <p align="center" style="font-size: 22px">项目计划进度和阶段目标</p>
                        <table width="100%" class="table table-hover">
                            <thead>
                            <tr>
                                <td width="15%">开始时间</td>
                                <td width="15%">结束时间</td>
                                <td width="70%">阶段目标</td>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                                $schedules = $project->schedules('apply')->toArray();
                                $count = count($schedules)?:1;
                                for($i=0,$n=$count;$i<$n;$i++):?>
                                    <tr>
                                        <td><?=$schedules[$i]['start_at']?></td>
                                        <td><?=$schedules[$i]['end_at']?></td>
                                        <td><?=$schedules[$i]['content']?></td>
                                    </tr>
                                <?php endfor;?>
                            </tbody>
                        </table>
                    </div>
                    <div class="tab-pane" id="cooperation">
                        <p align="center" style="font-size: 22px">合作单位</p>
                        <table width="100%" class="table table-hover">
                            <thead>
                            <tr>
                                <td width="10%" align="center">序号</td>
                                <td width="20%" align="center">信用代码</td>
                                <td width="20%" align="center">名称</td>
                                <td width="30%" align="center">在本项目中分工</td>
                                <td width="10%" align="center">总经费（万元）</td>
                                <td width="10%" align="center">操作</td>
                            </tr>
                            </thead>
                            <tbody id="paper-content">
                            <?php
                            $cooperations = $project->getCooperations();
                            while($cooperation = $cooperations->getObject()):
                                ?>
                                <tr>
                                    <td>
                                        <?=$cooperation->getId()?><?=$cooperation->getNo()?>
                                    </td>
                                    <td>
                                        <?=$cooperation->getCode()?>
                                    </td>
                                    <td><?=$cooperation->getCompanyName()?></td>
                                    <td><?=$cooperation->getWorks()?></td>
                                    <td><?=$cooperation->getTotalMoney()?:'0.00'?></td>
                                    <td align="center">
                                        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
                                        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                        </table>
                    </div>
                    <div class="tab-pane" id="money">
                        <p align="center" style="font-size: 22px">经费预算</p>
                        <table class="table table-bordered table-hover">
                            <tr>
                                <th colspan="2" class="text-left">经费来源（单位：万元）</span></th>
                            </tr>
                            <tr>
                                <th class="text-center">科目名称</th>
                                <th width="27%" class="text-center">经费</th>
                            </tr>
                            <tr>
                                <td>申请国家级、省部级等外部资助</td>
                                <td align="center"><?=$project->money($type)->getGovMoney()?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td>申请集团经费补助</td>
                                <td align="center"><?=$project->money($type)->getDeclareMoney()?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td>自筹经费</td>
                                <td align="center"><?=$project->money($type)->getOwnMoney()?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td>其他经费</td>
                                <td align="center"><?=$project->money($type)->getOtherMoney()?:'0.00'?></td>
                            </tr>
                            <tr >
                                <td>项目总经费</td>
                                <td align="center"><?=$project->money($type)->getTotalMoney()?:'0.00'?></td>
                            </tr>
                        </table>
                        <table class="table table-bordered table-hover table-valign-middle auto_count2 presetfields mb-5">
                            <tr>
                                <th colspan="5" class="text-left">经费支出<span class="pull-right">（单位：万元）</span></th>
                            </tr>
                            <tr>
                                <th width="32%" class="text-center" >科目名称</th>
                                <th width="17%" class="text-center">专项经费</th>
                                <th width="17%" class="text-center">自筹经费</th>
                                <th width="17%" class="text-center">其他资金</th>
                                <th width="17%" class="text-center">合计</th>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.indirect']?:'（一）间接费用'?></td>
                                <td align="center"><?=$project->money($type)->getIndirect('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getIndirect('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getIndirect('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getIndirect('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.performance']?:'  其中：绩效支出'?></td>
                                <td align="center"><?=$project->money($type)->getPerformance('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPerformance('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPerformance('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPerformance('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.direct']?:'（二）直接费用'?></td>
                                <td align="center"><?=$project->money($type)->getDirect('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDirect('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDirect('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDirect('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.device']?:'设备费'?></td>
                                <td align="center"><?=$project->money($type)->getDevice('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.device1']?:'（1）购置设备费'?></td>
                                <td align="center"><?=$project->money($type)->getDevice1('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice1('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice1('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice1('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.device2']?:'（2）设备试制、改造、租赁费'?></td>
                                <td align="center"><?=$project->money($type)->getDevice2('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice2('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice2('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getDevice2('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.material']?:'材料费'?></td>
                                <td align="center"><?=$project->money($type)->getMaterial('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getMaterial('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getMaterial('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getMaterial('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.test']?:'测试化验加工费'?></td>
                                <td align="center"><?=$project->money($type)->getTest('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTest('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTest('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTest('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.power']?:'燃料动力费'?></td>
                                <td align="center"><?=$project->money($type)->getPower('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPower('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPower('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getPower('hj')?:'0.00'?></td>
                            </tr>

                            <tr>
                                <td><?=$configs['langs']['jfzc.cooperation']?:'差旅费/会议费/国际合作与交流费'?></td>
                                <td align="center"><?=$project->money($type)->getCooperation('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getCooperation('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getCooperation('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getCooperation('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.reference']?:'出版/文献/信息传播/知识产权事务费'?></td>
                                <td align="center"><?=$project->money($type)->getReference('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getReference('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getReference('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getReference('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.manpower']?:'劳务费'?></td>
                                <td align="center"><?=$project->money($type)->getManpower('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getManpower('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getManpower('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getManpower('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.consultancy']?:'专家咨询费'?></td>
                                <td align="center"><?=$project->money($type)->getConsultancy('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getConsultancy('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getConsultancy('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getConsultancy('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td><?=$configs['langs']['jfzc.other']?:'其他费用'?></td>
                                <td align="center"><?=$project->money($type)->getOther('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getOther('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getOther('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getOther('hj')?:'0.00'?></td>
                            </tr>
                            <tr>
                                <td>支出合计</td>
                                <td align="center"><?=$project->money($type)->getTotal('cz')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTotal('zc')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTotal('qt')?:'0.00'?></td>
                                <td align="center"><?=$project->money($type)->getTotal('hj')?:'0.00'?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="tab-pane" id="attachment">
                        <p align="center" style="font-size: 22px">附件列表</p>
                        <table class="table">
                            <thead>
                            <tr>
                                <th class="text-center">序号</th>
                                <th class="text-center">文件名</th>
                                <th class="text-center">文件格式</th>
                                <th class="text-center">文件大小</th>
                                <th class="text-center">上传时间</th>
                                <th class="text-center">文件说明</th>
                                <th class="text-center" style="width:70px;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="file-list-project">
                            <?php if($project->attachments()->getTotal()>0):
                                $attachments = $project->attachments('','no asc,id asc');
                                while($file = $attachments->getObject()):
                                    ?>
                                    <tr id="file<?=$file->getId()?>">
                                        <td class="text-center" style="width: 70px;">
                                            <?=$attachments->getIndex()?>
                                        </td>
                                        <td><i class="fa fa-caret-right orange"></i> <a href="<?=site_path('up_files/'.$file->getFilePath())?>" target="_blank"><?=$file->getFileName()?></a></td>
                                        <td class="text-center"><?=$file->getFileExt()?></td>
                                        <td class="text-center"><?=$file->getFileSize()?></td>
                                        <td class="text-center"><?=$file->getCreatedAt()?></td>
                                        <td class="text-center" style="width: 300px">
                                            <?=$file->getFileNote()?>
                                        </td>
                                        <td class="text-center">
                                            <?=Button::setUrl(site_path('up_files/'.$file->getFilePath()))->link('查看')?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                            <?php endif;?>
                            </tbody>
                            <tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
