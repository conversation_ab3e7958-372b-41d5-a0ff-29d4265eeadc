<table width="100%" border="0" cellpadding="3" cellspacing="1" class=" table show_more tb_data table-sm">
    <tr>
        <th colspan="4" style="text-align:center;">项目详细信息</th>
    </tr>
    <tr>
        <th width="15%">项目名称</th>
        <td width="35%"><?=$project->getSubject()?></td>
        <th width="15%">立项编号</th>
        <td width="35%"><?=$project->getRadicateId()?></td>
    </tr>
    <tr>
        <th>申报年度</th>
        <td><?=$project->getDeclareYear()?></td>
        <th>申报编号</th>
        <td><?=$project->getAcceptId()?></td>
    </tr>
    <tr>
        <th>项目类别</th>
        <td><?=$project->getProjectTypeSubject()?>&nbsp;</td>
        <th>项目总经费</th>
        <td><?=$project->getTotalMoney()?> 万元</td>
    </tr>
    <tr>
        <th>项目负责人</th>
        <td><?=$project->getUserName()?> </td>
        <th>申报单位</th>
        <td><?=$project->getCorporationName()?></td>
    </tr>
    <tr>
        <th>操作记录</th>
        <td><?=Button::setName('查看记录')->setUrl(site_url("admin/history/index/id/".$project->getProjectId()))->setWidth('550px')->setHeight('80%')->window()?></td>
        <th><?=$project->getCatId()==15 ? '申报书资料' : '项目资料';?></th>
        <td>
            <?=Button::setUrl(site_url("apply/project/show/id/".$project->getProjectId()))->link('查看')?>
        </td>
    </tr>
    <tr>
        <th>填报时间</th>
        <td><?=$project->getDeclareAt("Y-m-d")?>&nbsp;</td>
        <th>起止年限</th>
        <td><?=$project->getStartAt().' 至 '.$project->getEndAt()?>&nbsp;</td>
    </tr>
    <tr>
    </tr>
    <?php if($project->getStatement() >= 29 && $project->getTypeId()==1):?>
        <tr>
            <th colspan="4" style="text-align:center;">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
        </tr>
        <tr>
            <th>立项编号</th>
            <td><?=$project->getRadicateId()?>&nbsp;</td>
            <th>立项经费</th>
            <td><?=$project->getRadicateMoney()?> 万元</td>
        </tr>
        <tr>
            <th>计划任务书</th>
            <td><?=$project->getStateForTask()?><?php if($project->getStateForPlanBook() > 0):?>
                    【<a href="<?=site_url("declare/task/show/id/".$project->getProjectId())?>">查 看</a>】
                    <!-- 【<a href="<?=site_url("declare/task/output/id/".$project->getProjectId())?>" target="_blank">导出</a>】 -->
                <?php endif;?>
            </td>
            <th>验收书</th>
            <td><?=$project->getStateForComplete()?>
                <?php if($project->getStateForCompleteBook() > 0):?>
                    【<a href="<?=site_url("declare/complete/show/id/".$project->getProjectId())?>">查 看</a>】
                    <!-- 【<a href="<?=site_url("declare/complete/output/id/".$project->getProjectId())?>" target="_blank">导出</a>】 -->
                <?php endif;?>
            </td>
        </tr>
    <?php endif;?>
</table>