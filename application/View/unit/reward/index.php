<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        奖励综合搜索
                    </h3>
                    <div class="block-options">
                        <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                        <?=btn('link','导出数据',site_url('export/export/index/table/rewards'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                            <div class="no-padding no-margin panel panel-default" >
                                <table cellpadding="3" cellspacing="1" class="table table-hover ">
                                    <thead>
                                    <tr>
                                        <th width="30">详</th>
                                        <th><?=getColumnStr('奖励荣誉名称','subject')?></th>
                                        <th><?=getColumnStr('牵头单位','company_id')?></th>
                                        <th><?=getColumnStr('奖项类别','category')?></th>
                                        <th><?=getColumnStr('奖项级别','level')?></th>
                                        <th><?=getColumnStr('获奖日期','date')?></th>
                                        <th><?=getColumnStr('填报人','user_id')?></th>
                                        <th><?=getColumnStr('状态','statement')?></th>
                                        <th style="width: 100px">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php while($reward = $pager->getObject()):?>
                                        <tr>
                                            <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                            <td width="30%"><?=$reward->getMark()?><a href="<?=site_url('user/reward/show/id/'.$reward->getRewardId())?>"><?=$reward->getSubject()?></a></td>
                                            <td><?=$reward->getCompanyName()?></td>
                                            <td><?=$reward->getCategory()?></td>
                                            <td><?=$reward->getLevel()?></td>
                                            <td><?=$reward->getDate()?></td>
                                            <td><?=$reward->getUserName()?></td>
                                            <td><?=$reward->getState()?></td>
                                            <td><?=Button::setUrl(site_url('unit/reward/supplementattachment/id/'.$reward->getRewardId()))->link('补充附件')?></td>
                                        </tr>
                                        <tr class="fold_body">
                                            <td colspan="10"><?php include('more_part.php') ?></td>
                                        </tr>
                                    <?php endwhile;?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </form>
                </div>
            </div>
        </div>
    </div>
</div>