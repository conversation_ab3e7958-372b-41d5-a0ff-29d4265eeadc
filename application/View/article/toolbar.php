<div class="toolbar">
	<ul class="col-md-1 col-md-offset-11 no-padding toolbar_pc hide">
		<li title="返回顶部" id="scroll_to_top" class="img-rounded"><i class="fa fa-angle-up fa-2x"></i></li>
	</ul>
</div>
<script>
var clientHeight,scrollHeight;
$(function(){
	clientHeight = $(window).height();
	scrollHeight = $(document).height();

	toggleToolbar($(document).scrollTop());
	$(document).scroll(function(){
		toggleToolbar($(this).scrollTop());
	});

	$('#scroll_to_top').click(function(){
		$(document).scrollTop(0);
	});
	
});

function toggleToolbar(scrollTop)
{
	if(scrollTop>=150)
		$('.toolbar_pc').removeClass('hide');
	else $('.toolbar_pc').addClass('hide');

	if(scrollHeight-scrollTop-clientHeight<=106)
		$('.toolbar_pc').css({'bottom':'106px'});
	else $('.toolbar_pc').css({'bottom':'0px'});
}
</script>