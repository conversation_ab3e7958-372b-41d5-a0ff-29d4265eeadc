<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                评审完毕的分组
            </h3>
            <div class="block-options">

            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <form action="" method="post" name="search" id="search">
                    <table width="100%" align="center">
                        <tbody id="show_search">
                            <tr>
                                <td><label>分组编号：
                                        <input name="subject" type="text" id="subject" value="<?=input::getInput("mix.subject")?>" class="form-control w-auto custom-control-inline" />
                                    </label><label>分组备注：
                                        <input name="note" type="text" id="note" value="<?=input::getInput("mix.note")?>" />
                                    </label><select name="group_year" id="group_year" class="required">
                                        <option value="">=评审年度=</option>
                                        <?=getYearList(input::getInput("mix.group_year"))?>
                                    </select>
                                    <?=btn('button','搜索','submit','find')?></td>
                            </tr>
                        </tbody>
                    </table>
                    </form>
                </div>
                <?php if($pager->getTotal()):?>
                    <div class="box">
                        <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th><?=getColumnStr('分组编号','group_subject')?></th>
                                <th><?=getColumnStr('项目数','project_count')?></th>
                                <th><?=getColumnStr('分组时间','updated_at')?></th>
                                <th><?=getColumnStr('备注','group_note')?></th>
                                <th style="width: 200px;">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($group = $pager->getObject()):?>
                                <tr>
                                    <td><?=link_to("review/group/show/id/".$group->getId(),$group->getGroupSubject())?></td>
                                    <td><?=$group->getProjectCount()?></td>
                                    <td><?=$group->getCreatedAt()?></td>
                                    <td><?=$group->getGroupNote()?></td>
                                    <td>
                                        <?=btn('link','查看',site_url("review/group/show/id/".$group->getId()))?>
                                        <?=btn('link','导出评审结果',site_url("review/group/export_result/id/".$group->getId()),'export')?>
                                        <!--
                                        <?=btn('link','评审汇总表',site_url("review/group/outSummary/id/".$group->getId()),'export')?>
                                        -->
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="5" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                <?php else:?>
                    <div class="box">
                        <p class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</p>
                    </div>
                <?php endif;?>
            </div>
        </div>
    </div>
</div>


