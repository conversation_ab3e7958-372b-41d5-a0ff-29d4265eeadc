<?php $grades = $project->selectAssess();?>
<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <?=btn('back')?>
    <!-- &nbsp;&nbsp; <a href="<?=site_url("declare/award/output/id/".$project->getUserId())?>" class="btn btn-info" role="button">导出结果</a> -->
    <p style="clear:both;"></p>
  </div>
  <h1 style="font-size:18px; font-weight:bold; font-family:'黑体'; text-align:center;">项目"
    <?=$project->getSubject()?>
    "的评审结果</h1>
    <h2 style="font-size:14px; font-weight:bold; font-family:'黑体'; text-align:center;">
      <?=$rule->getSubject()?>
    </h2>
    <div class="alert alert-success" role="alert">
      <b>温馨提示：</b>每项考核指标均分为<em><b> 0 </b></em>级到<em><b>
      <?=$rule->getAccuracy()?>
    </b></em>级（不是得分），等级越高，表示项目越好，请根据您的判断为该项目的每个考核指标选定适配的等级，系统最后会根据加权计算出该项目的最终得分。若您对该项目有其他的意见，请在"专家意见"栏目中详细列述。 
  </div>
  <div class="tabbable">
    <ul class="nav nav-tabs" id="myTab">
      <?php for($i=1,$n=$grades->getTotal();$i<=$n;$i++):?>
        <li<?php if($i == 1):?>  class="active"<?php endif;?>><a data-toggle="tab" href="#tab_<?=$i?>">第
        <?=$i?>
        位专家</a></li>
      <?php endfor;?>
    </ul>
    <div class="tab-content">
      <?php while($grade = $grades->getObject()):?>
        <div class="tab-pane<?php if($grades->getIndex() == 1):?> active<?php endif;?>" id="tab_<?=$grades->getIndex()?>">
         <table width="100%" border="0" cellspacing="1" cellpadding="3" class="tb_data table table-bordered">
          <caption>
            项目信息
          </caption>
          <tr>
            <th width="13%">姓名</th>
            <td width="60%"><?=$project->getSubject()?></td>
            <th width="12%">人才类别</th>
            <td width="15%"><?=$project->getTypeSubject()?></td>
          </tr>
          <tr>
            <th>工作单位</th>
            <td><?=$project->getCorporationName()?></td>
            <th>评审总分</th>
            <td><?=$project->getScore(true)?></td>
          </tr>
        </table>
        <table width="100%" border="0" cellspacing="1" cellpadding="3" class="tb_data table table-bordered">
          <caption>专家信息</caption>
          <tr>
            <th width="13%">专家姓名</th>
            <td width="60%"><?=$grade->getUser()->getUserName()?>（<?=$grade->getRoleName()?>）
              <?php if (in_array(input::getInput("session.userlevel"),['6'])): ?>
                <!-- <a href="<?=site_url("admin/super/rTalentGrade/id/".$grade->getId())?>" onclick="return confirm('您是否舍弃该专家的评审？')" class="btn btn-danger btn-xs" role="button">舍掉</a>  -->
                <a href="<?=site_url("admin/super/gradeback/id/".$grade->getId())?>" onclick="return confirm('是否退回该专家评审结果？')" class="btn btn-danger btn-xs" role="button">退回</a>
              <?php endif ?>
            </td>
            <th width="12%">专家职称</th>
            <td width="15%"><?=$grade->getUser()->getUserHonor()?></td>
          </tr>
          <tr>
            <th>从事专业</th>
            <td><?=$grade->getUser()->getUserWork()?></td>
            <th>评审得分</th>
            <td><?=$grade->getScore()?><?=$grade->getIsSubmitState()?></td>
          </tr>
          <tr>
            <th>综合评审意见</th>
            <td><?=showText($grade->getIdea())?></td>

          </tr>
        </table>
        <?=$rule->getShowHtml($grade->getMark(),$grade->getRole(),$grade->getIdeas());?>
      </div>
    <?php endwhile;?>
  </div>
</div>
</div>
<style>
  .rule_list { padding:0px 15px;}
  .rule_list li {list-style:decimal; padding:3px 3px;}
</style>