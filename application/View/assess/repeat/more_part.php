  <table width="100%" border="0" cellpadding="3" cellspacing="1" class="show_more">
    <caption class="caption_title">项目详细信息 &gt;&gt;&gt;</caption>
    <tr>
      <th width="17%">申报编号</th>
      <td width="33%"><?=$project->getAcceptId()?></td>
      <th width="17%">项目类别</th>
      <td width="33%"><?=$project->getTypeSubject()?></td>
    </tr>
    <tr>
      <th>计划年度</th>
      <td>&nbsp;
        <?=$project->getDeclareYear()?>
        (
        <?=$project->getTypeCurrentGroup()?>
      )&nbsp;</td>
      <th>所在科室</th>
      <td><?=$project->getOfficeSubject()?>&nbsp;</td>
    </tr>
    <tr>
      <th>申报经费</th>
      <td><?=$project->getDeclareMoney()?> 万元</td>
      <th>项目总经费</th>
      <td><?=$project->getTotalMoney()?> 万元</td>
    </tr>
     <tr>
      <th>承担单位</th>
      <td><?=link_to("admin/corporation/show/id/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <th>项目负责人</th>
      <td><?=link_to("admin/declarer/show/id/".$project->getUserId(),$project->getUserName())?>&nbsp;</td>
    </tr>
    <tr>
      <th>归口部门</th>
      <td><?=$project->getDepartmentName()?></td>
      <th>起止年限</th>
      <td><?=$project->getStartAt().' 至 '.$project->getEndAt()?></td>
    </tr>
    <tr>
      <th>评审得分</th>
      <td><?=$project->getBudgetScore()?></td>
      <th>预算书资料</th>
      <td><?=$project->getStateForBudget()?>
        <?php if($project->getBudgetOpen() > 0):?>
        [<a href="<?=site_url("declare/budget/show/id/".$project->getProjectId())?>">查看</a>]
    [<a href="<?=site_url("declare/budget/output/id/".$project->getProjectId())?>" target="_blank">导出</a>] [<a href="<?=site_url("declare/budget/download/id/".$project->getProjectId())?>">下载</a>]
    <?php endif;?></td>
    </tr>
    <tr>
      <th>任务书资料</th>
      <td><?=$project->getStateForTask()?>
        <?php if($project->getStateForPlanBook() > 0):?>
[<a href="<?=site_url("declare/task/show/id/".$project->getProjectId())?>">查看</a>]
    [<a href="<?=site_url("declare/task/output/id/".$project->getProjectId())?>" target="_blank">导出</a>] [<a href="<?=site_url("declare/task/download/id/".$project->getProjectId())?>">下载</a>]
    <?php endif;?></td>
	  <th>验收书资料</th>
      <td><?=$project->getStateForComplete()?>
        <?php if($project->getStateForCompleteBook() > 0):?>
[<a href="<?=site_url("declare/complete/show/id/".$project->getProjectId())?>">查看</a>]
    [<a href="<?=site_url("declare/complete/output/id/".$project->getProjectId())?>" target="_blank">导出</a>] [<a href="<?=site_url("declare/complete/download/id/".$project->getProjectId())?>">下载</a>]
    <?php endif;?></td>
    </tr>
    <tr>
      <th>操作记录</th>
      <td>[<a href="javascript:void(0);" onclick="return showWindow('查看操作记录','<?=site_url("admin/history/index/id/".$project->getProjectId())?>',450,300);return false;">查看记录</a>]</td>
      <th>历史版本</th>
      <td><?=$project->getVerHtml('budget')?></td>
    </tr>
  </table>
