<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include('search_part.php');?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>详</th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th width="100"><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('承担单位','corporation_id')?></th>
      <th><?=getColumnStr('所属年度','declare_year')?></th>
      <th width="130"><?=getColumnStr('预算申报书状态','state_for_budget_book')?></th>
	  <th>查看记录</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
      <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
      <td><?=$project->getMark()?><?=link_to("declare/budget/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">总经费：<?=$project->getTotalMoney()?> 万元，申报经费：<?=$project->getDeclareMoney()?> 万元，推荐经费：<?=$project->getCommendMoney()?> 万元。</p></td>
      <td align="center"><?=$project->getTypeSubject()?></td>
      <td><?=link_to("admin/corporation/show/id/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <td align="center"><?=$project->getDeclareYear()?>(<?=$project->getTypeCurrentGroup()?>)</td>
      <td align="center"><?=$project->getStateForBudget()?></td>
	  <td align="center"><a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("assess/history/index/type/budget/id/".$project->getProjectId())?>',450,350);return false;" class="message">查看记录</a></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
        <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="9" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>
