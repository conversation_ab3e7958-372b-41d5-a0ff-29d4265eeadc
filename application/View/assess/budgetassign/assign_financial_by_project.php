<div class="main">
<div class="btn-group btn-group-sm" role="group">
<a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a>
<a href="<?=site_url("assess/budgetassign/doAssignFinancialForProject/id/".$project_id)?>" class="btn btn-info btn-xs" role="button"><i class="glyphicon glyphicon-cog"></i>分配财务专家</a>
<a href="<?=site_url("assess/budgetassign/doAssignExpertForProject/id/".$project_id)?>" class="btn btn-info btn-xs" role="button"><i class="glyphicon glyphicon-cog"></i>分配技术专家</a>
<p style="clear:both"></p>
</div>
<div class="box">
<table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <form id="yesForm" name="yesForm" method="post" action="<?=site_url("assess/budgetassign/confirm")?>">
    <tr>
      <th>姓名</th>
      <th>性别</th>
      <th>学位</th>
      <th>职称</th>
      <th>工作单位</th>
      <th>研究领域</th>
      <th>可用操作</th>
    </tr>
    <?php while($grade = $grades->getObject()):?>
    <tr>
      <td align="center"><?=$grade->getUser(true)->getUserName()?></td>
      <td align="center"><?=$grade->getUser()->getUserSex()?></td>
      <td align="center"><?=$grade->getUser()->getUserDegree()?></td>
      <td align="center"><?=$grade->getUser()->getUserHonor()?></td>
      <td align="center"><?=$grade->getUser()->getWorkUnit()?></td>
      <td align="center"><?=implode(" / ",$grade->getUser()->getArrayWithSubjectName())?></td>
      <td align="center"><a href="<?=site_url("assess/budgetassign/doDeleteExpertForProject/id/".$grade->getId())?>" onclick="confirm('删除后不可恢复，确定要删除该专家？')" class="btn btn-sm btn-danger"><i class="ace-icon glyphicon glyphicon-remove-circle"></i> 删除</a></td>
    </tr>
    <?php endwhile; ?>
    <tr>
      <td colspan="7" align="left"><input type="submit" name="button2" id="button2" value="完成专家分配" onclick="return confirm('完成专家不能再次修改，你确定完成专家分配?');" />
        <input name="id" type="hidden" id="id" value="<?=$project_id?>" /></td>
      </tr>
  </form>
</table>
</div><br />
<div class="search">
<?php include("search_expert_part.php")?>
</div>
<div class="box">
<table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <form id="validateForm" name="validateForm" method="post" action="<?=site_url("assess/budgetassign/doAssignFinancialForProject")?>">
    <tr>
      <th><input name="selectAll" id="selectAll" type="checkbox" /></th>
      <th><?=getColumnStr('姓名','user_name')?></th>
      <th><?=getColumnStr('性别','user_sex')?></th>
      <th><?=getColumnStr('学位','user_level')?></th>
      <th><?=getColumnStr('职称','user_honor')?></th>
      <th><?=getColumnStr('工作单位','work_unit')?></th>
      <th><?=getColumnStr('研究领域','subject_names')?></th>
      <th>未评项目</th>
    </tr>
    <tr>
      <td colspan="8"><input type="submit" name="button3" id="button3" value="选取选中的专家" /></td>
      </tr>
    <?php while($user = $pager->getObject()):?>
    <tr>
      <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$user->getUserId()?>" /></td>
      <td align="center"><?=link_to("admin/financial/show/id/".$user->getUserId(),$user->getUserName())?></td>
      <td align="center"><?=$user->getUserSex()?></td>
      <td align="center"><?=$user->getUserlevel()?></td>
      <td align="center"><?=$user->getUserHonor()?></td>
      <td align="center"><?=$user->getWorkUnit()?></td>
      <td align="center"><?=$user->getSubjectName()?></td>
      <td align="center"><?=$user->hasUnAppraiseProjectString()?></td>
    </tr>
    <?php endwhile; ?>
    <tr>
      <td colspan="3" align="left"><label>
        <input type="submit" name="button" id="button" value="选取选中的专家" />
        <input name="id" type="hidden" value="<?=$project_id?>" />
      </label></td>
      <td colspan="5" align="right"><span class="pager_bar">
        <?=$pager->fromto().$pager->navbar(10)?>
      </span></td>
      </tr>
  </form>
</table>
</div>