<div class="main">
  <div class="tools"> 
    <!-- <a href="javascript:void(0);" onclick="selectAll();return false;" class="select">全部选中</a> -->
    <p style="clear:both;"></p>
  </div>
  <div class="box">
    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
        1、请选择分组的年度：
      </caption>
      <tr>
       <td><label>
         <select name="select" onchange="window.location.href='<?=site_url("assess/group/export/declare_year")?>'+'/'+this.value">
          <?=getYearList($year)?>
        </select>
      </label></td>
    </tr>
  </table>
  <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
   <form id="form1" name="form1" method="post" action="">
    <caption>2、请选择需要导出的分组：</caption>
    <?php if($pager->getTotal()):?>
      <tr>
        <td><div class="c_list"><?php while($group = $pager->getObject()):?><li><input name="group_id[]" type="checkbox" value="<?=$group->getId()?>" />【<?=$group->getGroupSubject()?>】<?=$group->getGroupNote()?></li><?php endwhile; ?></div></td>
      </tr>
      <tr>
        <td align="center"><input type="submit" name="Submit" class="btn btn-sm btn-success" value="导出数据" /></td>
      </tr>
    <?php else:?>
      <tr>
        <td class="note">暂时没有数据...</td>
      </tr>
    <?php endif;?>
  </form>
</table>
</div>
</div>
