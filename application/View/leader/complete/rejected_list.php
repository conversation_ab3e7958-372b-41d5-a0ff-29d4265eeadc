<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已退回的验收书
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                <!--
                <?=Button::setName('同意选中项')->setEvent('validateForm.action=\''.site_url("leader/complete/doSubmit").'\';$(\'#validateForm\').submit()')->setIcon('check')->button()?>
                -->
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default" >
                        <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/complete/doRejected")?>">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th><?=getColumnStr('项目编号','accept_id')?></th>
                                    <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                    <th><?=getColumnStr('立项年度','radicate_year')?></th>
                                    <th><?=getColumnStr('负责人','user_id')?></th>
                                    <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                                    <th width="200">状态</th>
                                    <th width="150" class="text-center">可用操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($project = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                        <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                        <td><?=$project->getAcceptId()?></td>
                                        <td><?=$project->getMark()?><?=link_to("declare/complete/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
                                        <td><?=$project->getRadicateYear()?></td>
                                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                        <td><?=$project->getStateForComplete()?></td>
                                        <td align="center">
                                            <?=Button::setName('审核')->setUrl(site_url("leader/complete/doAccept/id/".$project->getProjectId()))->setIcon('check')->setWidth('800px')->setHeight('600px')->window()?>
                                        </td>
                                    </tr>
                                    <tr class="fold_body">
                                        <td colspan="11">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="11" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

