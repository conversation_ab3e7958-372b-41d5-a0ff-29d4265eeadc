<div class="row">
  <div class="pull-left">
    <div class="tool"> <a href="<?=site_url("expert/register/download/userid/".$userid)?>" class="btn btn-primary btn-xs btn-submit"><i class="ace-icon fa fa-save"></i> 导出资料</a> </div>
  </div>
  <div style="clear: both"></div>
  <link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
  <script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script> 
  <script language="javascript" type="text/javascript">
        var debug=false;
        var is_submit=false;
        function delHtmlTag(str)
        {
            return $.trim(str.replace(/<[^>]+>/g,""));//去掉所有的html标记
        }

        function formSubmit()
        {
            var id = $('.tab-content .active').find('form').attr('id');
            if($('.tab-content .active').attr('id')=='tab1')
            {
                if(!flagInput(id))
                {
                    showAlert('请完整填写内容');
                    return false;
                }
            }
            is_submit = true;
            if(debug) return $('#'+id).submit();
            else return ajaxSubmit(id);
        }

        function flagInput(id)
        {

            var flag = true;
            $('#'+id).find('input[type=text],textarea').each(function(){
                var val = String($(this).val());
                if(val!='0' && val=='' && $(this).attr('class')!='tagInputField ui-autocomplete-input')
                {
                    $(this).closest('.form-group').addClass('has-error');
                    flag=false;
                }
                else $(this).closest('.form-group').removeClass('has-error');
            });

            var radio_names = ['skill_domain','innovation_type','fruit_level','standard','yield_study','fruits','patent'];

            for(index in radio_names)
            {
                if($('#'+id).find('input[name*="['+radio_names[index]+']"]').length>0){
                    if($('#'+id).find('input[name*="['+radio_names[index]+']"]:checked').length>0)
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').removeClass('has-error');
                    else
                    {
                        $('#'+id).find('input[name*="['+radio_names[index]+']"]').closest('.checkbox_list').addClass('has-error');
                        flag=false;

                    }
                }
            }

            return flag;
        }
    </script>
  <div class="header"> <i class="ace-icon fa fa-arrow-circle-down fa-2x green"></i> 请认真填写以下内容
    <div class="pull-right">
      <div class="tool"> <span class="red">（* 每页填写完都需要点击<span class="badge badge-info">保存</span>）</span> <a href="#" onclick="return formSubmit();" class="btn btn-primary btn-xs btn-submit"><i class="ace-icon fa fa-save"></i> 保存</a> </div>
    </div>
    <div class="clearfix"></div>
  </div>
  <div class="tabbable">
    <ul class="nav nav-tabs" id="myTab">
      <?php
            foreach($tab as $k=>$item):
                ?>
      <li class="<?=$pathinfo['filename']==pathinfo($item['url'])['filename'] ? 'active' : ''?>" > <a href="<?=$item['url']?>"> <i class="ace-icon fa fa-file-text-o bigger-120"></i>
        <?=$item['text']?>
        </a> </li>
      <?php
            endforeach;
            ?>
    </ul>
    <div class="tab-content">
      <div id="tab1" class="tab-pane fade  in active ">
        <form class="form-horizontal" id="form_1" action="" method="post">
          <script language="javascript" type="text/javascript" src="<?=site_path("js/DatePicker/WdatePicker.js")?>"></script>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">本人银行储蓄卡卡号</label>
            <div class="col-xs-12 col-sm-5 col-md-5">
              <input type="text" name="baseinfo[bank_card_number]" id="bank_card_number" class="form-control" value="<?=$value['bank_card_number']?>" placeholder="请输入本人的银行储蓄卡卡号" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">确认银行储蓄卡卡号</label>
            <div class="col-xs-12 col-sm-5 col-md-5">
              <input type="text" name="baseinfo[confirm_bank_card_number]" id="confirm_bank_card_number" class="form-control" value="<?=$value['bank_card_number']?>" placeholder="请再次输入本人的银行储蓄卡卡号，必须跟上一行输入的一致" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">开户行</label>
            <div class="col-xs-12 col-sm-5 col-md-2">
              <select name="baseinfo[bank_name]" id="bank_name">
                <option value="-1">请选择开户行</option>
                <?=getSelectFromArray(get_select_data('bank'),$value['bank_name'])?>
              </select>
            </div>
            <div class="col-xs-12 col-sm-5 col-md-4">
              <input type="text" name="baseinfo[bank_branch]" id="bank_branch" class="form-control" value="<?=$value['bank_branch']?>" placeholder="请准确输入开户行支行信息，否则会影响收款" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">开户行行号</label>
            <div class="col-xs-12 col-sm-5 col-md-5">
              <input type="text" name="baseinfo[bank_no]" id="bank_no" class="form-control" value="<?=$value['bank_no']?>" placeholder="请准确输入开户行行号" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">银行储蓄卡照片</label>
            <div class="col-xs-12 col-sm-5 col-md-5">
              <?php
                                if(empty($value['bank_card_pic'])):
                            ?>
              <div id="filemanager"> <span style="color:#0080FF">请上传银行储蓄卡正面照片，确保可以看清看号。</span>
                <div id="uploader" class="wu-example"> 
                  <!--用来存放文件信息-->
                  <div class="btns" style="position: relative">
                    <div id="fileList" class="uploader-list"> </div>
                  </div>
                  <div id="filePicker">上传附件</div>
                </div>
              </div>
              <?php
                                else:
                            ?>
              <a href="<?=site_path("up_files/".$value['bank_card_pic'])?>" target="_blank"><img src="<?=site_path("up_files/".$value['bank_card_pic'])?>" alt="" style="max-height: 100px"></a>
              <?php
                                endif;
                            ?>
              <input type="hidden" name="baseinfo[bank_card_pic]" id="bank_card_pic" value="<?=$value['bank_card_pic']?>">
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">专家类别</label>
            <div class="col-xs-12 col-sm-5 col-md-9">
              <?=get_checkbox(get_select_data('expert_type'),'baseinfo[user_type]',$value['user_type'],'',true,2)?>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">性别</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <select name="baseinfo[user_sex]" id="user_sex">
                <option value="男" <?php echo ($value['user_sex']=='男') ? 'selected' : '' ?>>男</option>
                <option value="女" <?php echo ($value['user_sex']=='女') ? 'selected' : '' ?>>女</option>
              </select>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">出生日期</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <input type="text" name="baseinfo[user_birthday]" id="user_birthday" class="form-control" onclick="WdatePicker({startDate:'1980-05-01',maxDate:'%y-%M-%d'})" value="<?=$value['user_birthday']?>" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">民族</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <select name="baseinfo[user_nation]" id="user_nation" class="required">
                <?=getSelectFromArray(get_select_data('nation'),$value['user_nation'])?>
              </select>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">学位</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <select name="baseinfo[user_degree]" id="user_degree" class="required">
                <?=getSelectFromArray(get_select_data('education'),$value['user_degree'])?>
              </select>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">学历</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <select name="baseinfo[user_level]" id="user_level" class="required">
                <?=getSelectFromArray(get_select_data('degree'),$value['user_level'])?>
              </select>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">毕业院校</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <input type="text" name="baseinfo[graduate_school]" id="graduate_school" class="form-control <?=$styles?>" <?=$events?> <?=$attributes?> value="<?=$value['graduate_school']?>" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">所学专业</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <input type="text" name="baseinfo[subject_name]" id="subject_name" class="form-control <?=$styles?>" <?=$events?> <?=$attributes?> value="<?=$value['subject_name']?>" />
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">职称</label>
            <div class="col-xs-12 col-sm-5 col-md-3">
              <select name="baseinfo[user_honor]" id="user_honor">
                <option value="0">无</option>
                <?=getSelectFromArray(get_select_data('work'),$value['user_honor'])?>
              </select>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">职业资格</label>
            <div class="col-xs-12 col-sm-5 col-md-9">
              <?=get_checkbox(get_select_data('job_qualification'),'baseinfo[job_qualification]',$value['job_qualification'],'',true,3)?>
            </div>
            <div class="clearfix"></div>
          </div>
          <div class="form-group col-xs-12 col-sm-12 col-md-12">
            <label class="control-label col-xs-12 col-sm-3 col-md-3">曾经参与的评审计划</label>
            <div class="col-xs-12 col-sm-5 col-md-9">
              <?=get_checkbox(get_select_data('old_plans'),'baseinfo[old_plans]',$value['old_plans'],'',true,3)?>
            </div>
            <div class="clearfix"></div>
          </div>
          <script language="javascript" type="text/javascript">


                        $(function(){
                            // 初始化Web Uploader
                            var uploader = WebUploader.create({
                                // 选完文件后，是否自动上传。
                                auto: true,

                                // swf文件路径
                                swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
                                // 文件接收服务端。
                                server: "<?=site_url('common/webupload')?>",
                                formData: {
                                    item_type: 'expert'
                                },
                                // 选择文件的按钮。可选。
                                // 内部根据当前运行是创建，可能是input元素，也可能是flash.
                                pick: '#filePicker',
                                fileVal:'file[]',
                                fileNumLimit:5,
                                resize: false,
                                // 只允许选择图片文件。
                                accept: {
                                    title: 'Images',
                                    extensions: 'gif,jpg,jpeg,bmp,png,txt,doc,docx,rar,zip',
                                    mimeTypes: '.gif,.jpg,.jpeg,.bmp,.png,.txt,.rar,.zip,.doc,.xls,.docx,.xlsx,.pdf'
                                }
                            }).on('ready', function () {
                                $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
                            });
                            // 当有文件添加进来的时候
                            uploader.on( 'fileQueued', function( file ) {
                                var $li = $(
                                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                                        '<img>' +
                                        '<div class="info">' + file.name + '</div>' +
                                        '</div>'
                                    ),
                                    $img = $li.find('img');


                                // $list为容器jQuery实例
//			$list.append( $li );
                                $("#fileList").html( $li );

                                // 创建缩略图
                                // 如果为非图片文件，可以不用调用此方法。
                                // thumbnailWidth x thumbnailHeight 为 100 x 100
                                uploader.makeThumb( file, function( error, src ) {
                                    if ( error ) {
                                        $img.replaceWith('<span>不能预览</span>');
                                        return;
                                    }

                                    $img.attr( 'src', src );
                                }, 300, 300 );
                            });
                            // 文件上传过程中创建进度条实时显示。
                            uploader.on( 'uploadProgress', function( file, percentage ) {
                                var $li = $( '#'+file.id ),
                                    $percent = $li.find('.progress span');

                                // 避免重复创建
                                if ( !$percent.length ) {
                                    $percent = $('<p class="progress"><span></span></p>')
                                        .appendTo( $li )
                                        .find('span');
                                }

                                $percent.css( 'width', percentage * 100 + '%' );
                            });

                            // 文件上传成功，给item添加成功class, 用样式标记上传成功。
                            uploader.on( 'uploadSuccess', function( file,response ) {
                                var json = eval(response);
                                var json = eval(json._raw);
                                $("#bank_card_pic").val(json[0].path);
                                $( '#'+file.id ).addClass('upload-state-done');
                                alert('上传成功');
                            });

                            // 文件上传失败，显示上传出错。
                            uploader.on( 'uploadError', function( file ) {
                                var $li = $( '#'+file.id ),
                                    $error = $li.find('div.error');

                                // 避免重复创建
                                if ( !$error.length ) {
                                    $error = $('<div class="error"></div>').appendTo( $li );
                                }

                                $error.text('上传失败');
                            });

                            // 完成上传完了，成功或者失败，先删除进度条。
                            uploader.on( 'uploadComplete', function( file ) {
                                $( '#'+file.id ).find('.progress').remove();
                            });

                            // 初始化技术职称证书扫描件
                            var uploader_title = WebUploader.create({

                                // 选完文件后，是否自动上传。
                                auto: true,

                                // swf文件路径
                                swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
                                // 文件接收服务端。
                                server: "<?=site_url('common/webupload')?>",

                                // 选择文件的按钮。可选。
                                // 内部根据当前运行是创建，可能是input元素，也可能是flash.
                                pick: '#titlePicker',
                                fileVal:'file[]',
                                fileNumLimit:5,
                                resize: false,
                                // 只允许选择图片文件。
                                accept: {
                                    title: 'Images',
                                    extensions: 'gif,jpg,jpeg,bmp,png',
                                    mimeTypes: 'image/*'
                                }
                            }).on('ready', function () {
                                $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
                            });
                            // 当有文件添加进来的时候
                            uploader_title.on( 'fileQueued', function( file ) {
                                var $li = $(
                                        '<div id="' + file.id + '" class="file-item thumbnail">' +
                                        '<img>' +
                                        '<div class="info">' + file.name + '</div>' +
                                        '</div>'
                                    ),
                                    $img = $li.find('img');


                                // $list为容器jQuery实例
//			$list.append( $li );
                                $("#titleList").html( $li );

                                // 创建缩略图
                                // 如果为非图片文件，可以不用调用此方法。
                                // thumbnailWidth x thumbnailHeight 为 100 x 100
                                uploader.makeThumb( file, function( error, src ) {
                                    if ( error ) {
                                        $img.replaceWith('<span>不能预览</span>');
                                        return;
                                    }

                                    $img.attr( 'src', src );
                                }, 300, 300 );
                            });
                            // 文件上传过程中创建进度条实时显示。
                            uploader_title.on( 'uploadProgress', function( file, percentage ) {
                                var $li = $( '#'+file.id ),
                                    $percent = $li.find('.progress span');

                                // 避免重复创建
                                if ( !$percent.length ) {
                                    $percent = $('<p class="progress"><span></span></p>')
                                        .appendTo( $li )
                                        .find('span');
                                }

                                $percent.css( 'width', percentage * 100 + '%' );
                            });

                            // 文件上传成功，给item添加成功class, 用样式标记上传成功。
                            uploader_title.on( 'uploadSuccess', function( file,response ) {
                                var json = eval(response);
                                var json = eval(json._raw);
                                $("#title_pic").val(json[0].path);
                                $( '#'+file.id ).addClass('upload-state-done');
                            });

                            // 文件上传失败，显示上传出错。
                            uploader_title.on( 'uploadError', function( file ) {
                                var $li = $( '#'+file.id ),
                                    $error = $li.find('div.error');

                                // 避免重复创建
                                if ( !$error.length ) {
                                    $error = $('<div class="error"></div>').appendTo( $li );
                                }

                                $error.text('上传失败');
                            });

                            // 完成上传完了，成功或者失败，先删除进度条。
                            uploader_title.on( 'uploadComplete', function( file ) {
                                $( '#'+file.id ).find('.progress').remove();
                            });

                        });
                    </script> 
          <script language="javascript" type="text/javascript">

                        function check(obj,type)
                        {
                            if($(obj).val() == ''){
                                $(obj).next("em").html('不能为空');
                                $(obj).css('border','1px red solid');
                                $(obj).focus();
                                return false;
                            }
                            $(obj).next("em").html('<span style="color:#000">验证中...</span>');
                            $.post('<?=site_url()?>ajax/check/userid/<?=$value['user_id']?>',{type:type,text:$(obj).val(),custom:1},function(json){
                                eval("json = "+json);
                                if(json.state){
                                    $(obj).val('').next("em").html(json.message);
                                    $(obj).css('border','1px red solid');
                                    $(obj).focus();
                                }else{
                                    $(obj).css('border','1px solid #d5d5d5');
                                    $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
                                }
                            });
                        }
                    </script>
          <div class="clearfix"></div>
        </form>
      </div>
    </div>
  </div>
</div>
