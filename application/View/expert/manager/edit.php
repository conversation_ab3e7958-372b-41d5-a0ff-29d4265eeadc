<script language="javascript" type="text/javascript">
function check(obj,type)
{
	if($(obj).val() == '') return false;
	
	if($("#card_type").val() != '身份证') custom = 0;
	else custom = 1;
	
	$.post('<?=site_url("ajax/check")?>',{type:type,text:$(obj).val(),custom:custom},function(json){
		eval("json = "+json);
		if(json.state){
			$(obj).val('').next("em").html(json.message);
		}else $(obj).next("em").html('<span style="color:#0C6">'+json.message+"</span>");
	});
}

</script>
<div class="col-xs-12 col-sm-12 col-md-12">
<div class="btn-group btn-group-sm" role="group">
<a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a>
<a href="javascript:$('#validateForm').submit();" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存资料</a>
<p style="clear:both"></p>
</div>
<div class="box">
  <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
    <caption class="caption_title">
    编辑专家信息
    </caption>
  <form action="" method="post" name="validateForm" id="validateForm">
    <tr>
      <th width="20%">专家姓名</th>
      <td width="40%"><input name="user_name" type="text" id="user_name" size="15" value="<?=$user->getUserName()?>" class="required" /></td>
      <th width="20%">出生日期</th>
      <td width="40%"><input name="user_birthday" type="text" id="user_birthday" size="12" value="<?=$user->getUserBirthday()?>" class="required date-pick" /></td>
    </tr>
    <tr>
      <th>证件类型</th>
      <td><input name="card_name" type="text" id="card_name" size="12" value="<?=$user->getCardName()?>" class="required" /></td>
      <th>证件号码</th>
      <td><input name="card_id" type="text" id="card_id" size="20" value="<?=$user->getCardId()?>" class="required" onblur="check(this,'Ecard');" />
        <em>*</em></td>
    </tr>
    <tr>
      <th>移动电话</th>
      <td><input name="user_mobile" type="text" id="user_mobile" size="15" value="<?=$user->getUserMobile()?>" class="required" /></td>
      <th>办公电话</th>
      <td><input name="unit_phone" type="text" id="unit_phone" size="15" value="<?=$user->getUnitPhone()?>" class="required" /></td>
    </tr>
    <tr>
      <th>评审情况</th>
      <td>已评（<?=$user->hasAppraiseProject()?>），未评（<?=$user->hasUnAppraiseProject()?>），放弃（<?=$user->hasGiveUpAppraiseProject()?>）。</td>
      <th>专家等级</th>
      <td><select name="user_grade" id="user_grade">
        <option value="A" <?php if($user->getUserGrade()=='A'){?> selected="selected"<?php }?>>A</option>
        <option value="B" <?php if($user->getUserGrade()=='B'){?> selected="selected"<?php }?>>B</option>
        <option value="C" <?php if($user->getUserGrade()=='C'){?> selected="selected"<?php }?>>C</option>
        <option value="D" <?php if($user->getUserGrade()=='D'){?> selected="selected"<?php }?>>D</option>
      </select></td>
    </tr>
    <tr>
      <td colspan="4" align="center"><label>
        <input type="submit" name="Submit" value=" &gt;&gt;保存资料&lt;&lt; " />
        <input name="userid" type="hidden" id="userid" value="<?=$user->getUserId()?>" />
        <input name="fromUrl2" type="hidden" id="fromUrl2" value="<?=getFromUrl()?>" />
      </label></td>
    </tr>
  </form>
</table>
</div>
</div>