<div class="main">
  <div class="btn-group btn-group-sm" role="group"> <a href="javascript:void(0);" onclick="javascript:history.go(-1);" class="glyphicon glyphicon-chevron-left">返回</a> <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存修改</a> <a href="<?=site_url("expert/budget/financial_download")?>" class="btn btn-info export" role="button">导出</a></div>
  <div class="box">
    <table  border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption class="caption_title">专家详细信息</caption>
        <form action="" method="post" name="validateForm" id="validateForm">
        <tr>
          <th width="139">姓名（汉字）</th>
          <td width="348"><label>
              <input name="user_username" type="text" id="user_name" value="<?=$expert->getUserName()?>" />
          </label></td>
          <th width="125">姓名（拼音）</th>
          <td width="366"><input name="user_name_en" type="text" id="user_name_en" value="<?=$expert->getUserNameEn()?>" class="required" /></td>
        </tr>
        <tr>
          <th>出生日期</th>
          <td><input name="user_birthday" type="text" id="user_birthday" value="<?=$expert->getUserBirthday()?>"  class="required date-pick"/></td>
          <th>性别</th>
          <td><label>
              <select name="user_sex" id="user_sex"  class="required">
                <option value="男" <?php if($expert->getUserSex() == '男') echo 'selected="selected"';?>>男</option>
                <option value="女" <?php if($expert->getUserSex() == '女') echo 'selected="selected"';?>>女</option>
              </select>
            </label></td>
        </tr>
        <tr>
          <th>身份证号</th>
          <td><input name="card_id" type="text" id="card_id" value="<?=$expert->getCardId()?>" class="required" /></td>
          <th>最高学历</th>
          <td><input name="user_level" type="text" id="user_level" value="<?=$expert->getUserLevel()?>" /></td>
        </tr>
        <tr>
          <th>毕业院校</th>
          <td><input name="graduate_school" type="text" id="graduate_school" value="<?=$expert->getGraduateSchool()?>" /></td>
          <th>所学专业</th>
          <td><input name="subject" type="text" id="subject" value="<?=$expert->getSubject()?>" /></td>
        </tr>
        <tr>
          <th>毕业时间</th>
          <td><input name="graduate_date" type="text" id="graduate_date" value="<?=$expert->getGraduateDate("Y-m-d")?>" class="date-pick" /></td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <th>工作单位</th>
          <td><input name="work_unit" type="text" id="work_unit" value="<?=$expert->getWorkUnit()?>" class="required" /></td>
          <th>所在省市</th>
          <td><input name="unit_area" type="text" id="unit_area" value="<?=$expert->getUnitArea()?>" class="required" /></td>
        </tr>
        <tr>
          <th>主管部门</th>
          <td><input name="department" type="text" id="department" value="<?=$expert->getDepartment()?>" class="required" /></td>
          <th>单位性质</th>
          <td><select name="unit_property" id="unit_property" class="required">
              <option value="高等院校" <?php if($expert->getUnitProperty() == '高等院校') echo 'selected="selected"';?>>高等院校</option>
              <option value="研究院所" <?php if($expert->getUnitProperty() == '研究院所') echo 'selected="selected"';?>>研究院所</option>
              <option value="政府机构" <?php if($expert->getUnitProperty() == '政府机构') echo 'selected="selected"';?>>政府机构</option>
              <option value="企业" <?php if($expert->getUnitProperty() == '企业') echo 'selected="selected"';?>>企业</option>
              <option value="其他" <?php if($expert->getUnitProperty() == '其他') echo 'selected="selected"';?>>其他</option>
            </select></td>
        </tr>
        <tr>
          <th>行政职务</th>
          <td><input name="user_duty" type="text" id="user_duty" value="<?=$expert->getUserDuty()?>" class="required" /></td>
          <th>技术职称</th>
          <td><select  name="user_honor" id="user_honor" class="required" style="width:200px;">
              <option value="高级实验师" <?php if($expert->getUserHonor() == '博士') echo 'selected="selected"';?>>高级实验师</option>
              <option value="高级编辑" <?php if($expert->getUserHonor() == '高级编辑') echo 'selected="selected"';?>>高级编辑</option>
              <option value="高级工程师" <?php if($expert->getUserHonor() == '高级工程师') echo 'selected="selected"';?>>高级工程师</option>
              <option value="研究馆员" <?php if($expert->getUserHonor() == '研究馆员') echo 'selected="selected"';?>>研究馆员</option>
              <option value="副研究馆员" <?php if($expert->getUserHonor() == '副研究馆员') echo 'selected="selected"';?>>副研究馆员</option>
              <option value="主任（药、护、技）师" <?php if($expert->getUserHonor() == '主任（药、护、技）师') echo 'selected="selected"';?>>主任（药、护、技）师</option>
              <option value="教授" <?php if($expert->getUserHonor() == '教授') echo 'selected="selected"';?>>教授</option>
              <option value="副教授" <?php if($expert->getUserHonor() == '副教授') echo 'selected="selected"';?>>副教授</option>
              <option value="研究员" <?php if($expert->getUserHonor() == '研究员') echo 'selected="selected"';?>>研究员</option>
              <option value="副研究员" <?php if($expert->getUserHonor() == '副研究员') echo 'selected="selected"';?>>副研究员</option>
              <option value="主治（主管）医（药、护、技）师" <?php if($expert->getUserHonor() == '主治（主管）医（药、护、技）师') echo 'selected="selected"';?>>主治（主管）医（药、护、技）师</option>
              <option value="高级记者" <?php if($expert->getUserHonor() == '高级记者') echo 'selected="selected"';?>>高级记者</option>
              <option value="高级统计师" <?php if($expert->getUserHonor() == '高级统计师') echo 'selected="selected"';?>>高级统计师</option>
              <option value="高级经济师" <?php if($expert->getUserHonor() == '高级经济师') echo 'selected="selected"';?>>高级经济师</option>
              <option value="经济师" <?php if($expert->getUserHonor() == '经济师') echo 'selected="selected"';?>>经济师</option>
              <option value="高级（审计）会计师" <?php if($expert->getUserHonor() == '高级（审计）会计师') echo 'selected="selected"';?>>高级（审计）会计师</option>
              <option value="（审计）会计师" <?php if($expert->getUserHonor() == '（审计）会计师') echo 'selected="selected"';?>>（审计）会计师</option>
              <option value="高级农艺师" <?php if($expert->getUserHonor() == '高级农艺师') echo 'selected="selected"';?>>高级农艺师</option>
              <option value="农艺师" <?php if($expert->getUserHonor() == '农艺师') echo 'selected="selected"';?>>农艺师</option>
              <option value="一级教练" <?php if($expert->getUserHonor() == '一级教练') echo 'selected="selected"';?>>一级教练</option>
              <option value="高级讲师" <?php if($expert->getUserHonor() == '高级讲师') echo 'selected="selected"';?>>高级讲师</option>
              <option value="高级教师" <?php if($expert->getUserHonor() == '高级教师') echo 'selected="selected"';?>>高级教师</option>
              <option value="高级畜牧师" <?php if($expert->getUserHonor() == '高级畜牧师') echo 'selected="selected"';?>>高级畜牧师</option>
              <option value="高级兽医师" <?php if($expert->getUserHonor() == '高级兽医师') echo 'selected="selected"';?>>高级兽医师</option>
              <option value="其它" <?php if($expert->getUserHonor() == '其它') echo 'selected="selected"';?>>其它</option>
            </select></td>
        </tr>
        <tr>
          <th>办公电话</th>
          <td><input name="unit_phone" type="text" id="unit_phone" value="<?=$expert->getUnitPhone()?>" class="required" /></td>
          <th>住址电话</th>
          <td><input name="user_home_phone" type="text" id="user_home_phone" value="<?=$expert->getUserHomePhone()?>" /></td>
        </tr>
        <tr>
          <th>移动电话</th>
          <td><input name="user_mobile" type="text" id="user_mobile" value="<?=$expert->getUserMobile()?>" class="required" /></td>
          <th>传真号码</th>
          <td><input name="user_fax" type="text" id="user_fax" value="<?=$expert->getUserFax()?>" /></td>
        </tr>
        <tr>
          <th>Email</th>
          <td><input name="user_email" type="text" id="user_email" value="<?=$expert->getUserEmail()?>" class="required email" /></td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <th>通讯地址</th>
          <td><input name="unit_address" type="text" id="unit_address" value="<?=$expert->getUnitAddress()?>" /></td>
          <th>邮政编码</th>
          <td><input name="unit_postal_code" type="text" id="unit_postal_code" value="<?=$expert->getUnitPostalCode()?>" /></td>
        </tr>
        <tr>
          <th>研究领域</th>
          <td><select name="subject_name" id="subject_name" class="required" >
              <option value="">请选择研究领域</option>
              <option value="" style="color:#F00;">----A.自然科学----</option>
              <option value="数学" <?php if($expert->getSubjectName() == '数学') echo 'selected="selected"';?>>数学</option>
              <option value="信息科学与系统科学" <?php if($expert->getSubjectName() == '信息科学与系统科学') echo 'selected="selected"';?>>信息科学与系统科学</option>
              <option value="力学" <?php if($expert->getSubjectName() == '力学') echo 'selected="selected"';?>>力学</option>
              <option value="物理学" <?php if($expert->getSubjectName() == '物理学') echo 'selected="selected"';?>>物理学</option>
              <option value="化学" <?php if($expert->getSubjectName() == '化学') echo 'selected="selected"';?>>化学</option>
              <option value="天文学" <?php if($expert->getSubjectName() == '天文学') echo 'selected="selected"';?>>天文学</option>
              <option value="地球科学" <?php if($expert->getSubjectName() == '地球科学') echo 'selected="selected"';?>>地球科学</option>
              <option value="生物学" <?php if($expert->getSubjectName() == '生物学') echo 'selected="selected"';?>>生物学</option>
              <option value="" style="color:#F00;">----B.农业科学----</option>
              <option value="农学" <?php if($expert->getSubjectName() == '农学') echo 'selected="selected"';?>>农学</option>
              <option value="林学" <?php if($expert->getSubjectName() == '林学') echo 'selected="selected"';?>>林学</option>
              <option value="畜牧、兽医科学" <?php if($expert->getSubjectName() == '畜牧、兽医科学') echo 'selected="selected"';?>>畜牧、兽医科学</option>
              <option value="水产学" <?php if($expert->getSubjectName() == '水产学') echo 'selected="selected"';?>>水产学</option>
              <option value="" style="color:#F00;">----C.医药科学----</option>
              <option value="基础医学" <?php if($expert->getSubjectName() == '基础医学') echo 'selected="selected"';?>>基础医学</option>
              <option value="临床医学" <?php if($expert->getSubjectName() == '临床医学') echo 'selected="selected"';?>>临床医学</option>
              <option value="预防医学与卫生学" <?php if($expert->getSubjectName() == '预防医学与卫生学') echo 'selected="selected"';?>>预防医学与卫生学</option>
              <option value="军事医学与特种医学" <?php if($expert->getSubjectName() == '军事医学与特种医学') echo 'selected="selected"';?>>军事医学与特种医学</option>
              <option value="药学" <?php if($expert->getSubjectName() == '药学') echo 'selected="selected"';?>>药学</option>
              <option value="中医学与中药学" <?php if($expert->getSubjectName() == '中医学与中药学') echo 'selected="selected"';?>>中医学与中药学</option>
              <option value="" style="color:#F00;">--D.工程与技术科学--</option>
              <option value="工程与技术科学基础学科" <?php if($expert->getSubjectName() == '工程与技术科学基础学科') echo 'selected="selected"';?>>工程与技术科学基础学科</option>
              <option value="测绘科学技术" <?php if($expert->getSubjectName() == '测绘科学技术') echo 'selected="selected"';?>>测绘科学技术</option>
              <option value="材料科学" <?php if($expert->getSubjectName() == '材料科学') echo 'selected="selected"';?>>材料科学</option>
              <option value="矿山工程技术" <?php if($expert->getSubjectName() == '矿山工程技术') echo 'selected="selected"';?>>矿山工程技术</option>
              <option value="冶金工程技术" <?php if($expert->getSubjectName() == '冶金工程技术') echo 'selected="selected"';?>>冶金工程技术</option>
              <option value="机械工程" <?php if($expert->getSubjectName() == '机械工程') echo 'selected="selected"';?>>机械工程</option>
              <option value="动力与电气工程" <?php if($expert->getSubjectName() == '动力与电气工程') echo 'selected="selected"';?>>动力与电气工程</option>
              <option value="能源科学技术" <?php if($expert->getSubjectName() == '能源科学技术') echo 'selected="selected"';?>>能源科学技术</option>
              <option value="核科学技术" <?php if($expert->getSubjectName() == '核科学技术') echo 'selected="selected"';?>>核科学技术</option>
              <option value="电子、通信与自动控制技术" <?php if($expert->getSubjectName() == '电子、通信与自动控制技术') echo 'selected="selected"';?>>电子、通信与自动控制技术</option>
              <option value="计算机科学技术" <?php if($expert->getSubjectName() == '计算机科学技术') echo 'selected="selected"';?>>计算机科学技术</option>
              <option value="化学工程" <?php if($expert->getSubjectName() == '化学工程') echo 'selected="selected"';?>>化学工程</option>
              <option value="纺织科学技术" <?php if($expert->getSubjectName() == '纺织科学技术') echo 'selected="selected"';?>>纺织科学技术</option>
              <option value="食品科学技术" <?php if($expert->getSubjectName() == '食品科学技术') echo 'selected="selected"';?>>食品科学技术</option>
              <option value="木建筑工程" <?php if($expert->getSubjectName() == '木建筑工程') echo 'selected="selected"';?>>木建筑工程</option>
              <option value="水利工程" <?php if($expert->getSubjectName() == '水利工程') echo 'selected="selected"';?>>水利工程</option>
              <option value="交通运输工程" <?php if($expert->getSubjectName() == '交通运输工程') echo 'selected="selected"';?>>交通运输工程</option>
              <option value="航空、航天科学技术" <?php if($expert->getSubjectName() == '航空、航天科学技术') echo 'selected="selected"';?>>航空、航天科学技术</option>
              <option value="环境科学技术" <?php if($expert->getSubjectName() == '环境科学技术') echo 'selected="selected"';?>>环境科学技术</option>
              <option value="安全科学技术" <?php if($expert->getSubjectName() == '安全科学技术') echo 'selected="selected"';?>>安全科学技术</option>
              <option value="管理学" <?php if($expert->getSubjectName() == '管理学') echo 'selected="selected"';?>>管理学</option>
              <option value="" style="color:#F00;">----E.人文与社会科学----</option>
              <option value="经济学" <?php if($expert->getSubjectName() == '经济学') echo 'selected="selected"';?>>经济学</option>
              <option value="政治学" <?php if($expert->getSubjectName() == '政治学') echo 'selected="selected"';?>>政治学</option>
              <option value="法学" <?php if($expert->getSubjectName() == '法学') echo 'selected="selected"';?>>法学</option>
              <option value="军事学" <?php if($expert->getSubjectName() == '军事学') echo 'selected="selected"';?>>军事学</option>
              <option value="社会学" <?php if($expert->getSubjectName() == '社会学') echo 'selected="selected"';?>>社会学</option>
              <option value="民族学" <?php if($expert->getSubjectName() == '民族学') echo 'selected="selected"';?>>民族学</option>
              <option value="新闻学与传播学" <?php if($expert->getSubjectName() == '新闻学与传播学') echo 'selected="selected"';?>>新闻学与传播学</option>
              <option value="教育学" <?php if($expert->getSubjectName() == '教育学') echo 'selected="selected"';?>>教育学</option>
              <option value="体育学" <?php if($expert->getSubjectName() == '体育学') echo 'selected="selected"';?>>体育学</option>
              <option value="统计学" <?php if($expert->getSubjectName() == '统计学') echo 'selected="selected"';?>>统计学</option>
            </select></td>
          <th>现在从事专业</th>
          <td><select name="user_work" id="user_work" class="required" >
              <option value="">请选择研究领域</option>
              <option value="" style="color:#F00;">----A.自然科学----</option>
              <option value="数学" <?php if($expert->getUserWork() == '数学') echo 'selected="selected"';?>>数学</option>
              <option value="信息科学与系统科学" <?php if($expert->getUserWork() == '信息科学与系统科学') echo 'selected="selected"';?>>信息科学与系统科学</option>
              <option value="力学" <?php if($expert->getUserWork() == '力学') echo 'selected="selected"';?>>力学</option>
              <option value="物理学" <?php if($expert->getUserWork() == '物理学') echo 'selected="selected"';?>>物理学</option>
              <option value="化学" <?php if($expert->getUserWork() == '化学') echo 'selected="selected"';?>>化学</option>
              <option value="天文学" <?php if($expert->getUserWork() == '天文学') echo 'selected="selected"';?>>天文学</option>
              <option value="地球科学" <?php if($expert->getUserWork() == '地球科学') echo 'selected="selected"';?>>地球科学</option>
              <option value="生物学" <?php if($expert->getUserWork() == '生物学') echo 'selected="selected"';?>>生物学</option>
              <option value="" style="color:#F00;">----B.农业科学----</option>
              <option value="农学" <?php if($expert->getUserWork() == '农学') echo 'selected="selected"';?>>农学</option>
              <option value="林学" <?php if($expert->getUserWork() == '林学') echo 'selected="selected"';?>>林学</option>
              <option value="畜牧、兽医科学" <?php if($expert->getUserWork() == '畜牧、兽医科学') echo 'selected="selected"';?>>畜牧、兽医科学</option>
              <option value="水产学" <?php if($expert->getUserWork() == '水产学') echo 'selected="selected"';?>>水产学</option>
              <option value="" style="color:#F00;">----C.医药科学----</option>
              <option value="基础医学" <?php if($expert->getUserWork() == '基础医学') echo 'selected="selected"';?>>基础医学</option>
              <option value="临床医学" <?php if($expert->getUserWork() == '临床医学') echo 'selected="selected"';?>>临床医学</option>
              <option value="预防医学与卫生学" <?php if($expert->getUserWork() == '预防医学与卫生学') echo 'selected="selected"';?>>预防医学与卫生学</option>
              <option value="军事医学与特种医学" <?php if($expert->getUserWork() == '军事医学与特种医学') echo 'selected="selected"';?>>军事医学与特种医学</option>
              <option value="药学" <?php if($expert->getUserWork() == '药学') echo 'selected="selected"';?>>药学</option>
              <option value="中医学与中药学" <?php if($expert->getUserWork() == '中医学与中药学') echo 'selected="selected"';?>>中医学与中药学</option>
              <option value="" style="color:#F00;">--D.工程与技术科学--</option>
              <option value="工程与技术科学基础学科" <?php if($expert->getUserWork() == '工程与技术科学基础学科') echo 'selected="selected"';?>>工程与技术科学基础学科</option>
              <option value="测绘科学技术" <?php if($expert->getUserWork() == '测绘科学技术') echo 'selected="selected"';?>>测绘科学技术</option>
              <option value="材料科学" <?php if($expert->getUserWork() == '材料科学') echo 'selected="selected"';?>>材料科学</option>
              <option value="矿山工程技术" <?php if($expert->getUserWork() == '矿山工程技术') echo 'selected="selected"';?>>矿山工程技术</option>
              <option value="冶金工程技术" <?php if($expert->getUserWork() == '冶金工程技术') echo 'selected="selected"';?>>冶金工程技术</option>
              <option value="机械工程" <?php if($expert->getUserWork() == '机械工程') echo 'selected="selected"';?>>机械工程</option>
              <option value="动力与电气工程" <?php if($expert->getUserWork() == '动力与电气工程') echo 'selected="selected"';?>>动力与电气工程</option>
              <option value="能源科学技术" <?php if($expert->getUserWork() == '能源科学技术') echo 'selected="selected"';?>>能源科学技术</option>
              <option value="核科学技术" <?php if($expert->getUserWork() == '核科学技术') echo 'selected="selected"';?>>核科学技术</option>
              <option value="电子、通信与自动控制技术" <?php if($expert->getUserWork() == '电子、通信与自动控制技术') echo 'selected="selected"';?>>电子、通信与自动控制技术</option>
              <option value="计算机科学技术" <?php if($expert->getUserWork() == '计算机科学技术') echo 'selected="selected"';?>>计算机科学技术</option>
              <option value="化学工程" <?php if($expert->getUserWork() == '化学工程') echo 'selected="selected"';?>>化学工程</option>
              <option value="纺织科学技术" <?php if($expert->getUserWork() == '纺织科学技术') echo 'selected="selected"';?>>纺织科学技术</option>
              <option value="食品科学技术" <?php if($expert->getUserWork() == '食品科学技术') echo 'selected="selected"';?>>食品科学技术</option>
              <option value="木建筑工程" <?php if($expert->getUserWork() == '木建筑工程') echo 'selected="selected"';?>>木建筑工程</option>
              <option value="水利工程" <?php if($expert->getUserWork() == '水利工程') echo 'selected="selected"';?>>水利工程</option>
              <option value="交通运输工程" <?php if($expert->getUserWork() == '交通运输工程') echo 'selected="selected"';?>>交通运输工程</option>
              <option value="航空、航天科学技术" <?php if($expert->getUserWork() == '航空、航天科学技术') echo 'selected="selected"';?>>航空、航天科学技术</option>
              <option value="环境科学技术" <?php if($expert->getUserWork() == '环境科学技术') echo 'selected="selected"';?>>环境科学技术</option>
              <option value="安全科学技术" <?php if($expert->getUserWork() == '安全科学技术') echo 'selected="selected"';?>>安全科学技术</option>
              <option value="管理学" <?php if($expert->getUserWork() == '管理学') echo 'selected="selected"';?>>管理学</option>
              <option value="" style="color:#F00;">----E.人文与社会科学----</option>
              <option value="经济学" <?php if($expert->getUserWork() == '经济学') echo 'selected="selected"';?>>经济学</option>
              <option value="政治学" <?php if($expert->getUserWork() == '政治学') echo 'selected="selected"';?>>政治学</option>
              <option value="法学" <?php if($expert->getUserWork() == '法学') echo 'selected="selected"';?>>法学</option>
              <option value="军事学" <?php if($expert->getUserWork() == '军事学') echo 'selected="selected"';?>>军事学</option>
              <option value="社会学" <?php if($expert->getUserWork() == '社会学') echo 'selected="selected"';?>>社会学</option>
              <option value="民族学" <?php if($expert->getUserWork() == '民族学') echo 'selected="selected"';?>>民族学</option>
              <option value="新闻学与传播学" <?php if($expert->getUserWork() == '新闻学与传播学') echo 'selected="selected"';?>>新闻学与传播学</option>
              <option value="教育学" <?php if($expert->getUserWork() == '教育学') echo 'selected="selected"';?>>教育学</option>
              <option value="体育学" <?php if($expert->getUserWork() == '体育学') echo 'selected="selected"';?>>体育学</option>
              <option value="统计学" <?php if($expert->getUserWork() == '统计学') echo 'selected="selected"';?>>统计学</option>
            </select></td>
        </tr>
        <tr>
          <th>熟悉外语</th>
          <td><input name="other_language" type="text" id="other_language" value="<?=$expert->getOtherLanguage()?>" /></td>
          <th>熟练程度</th>
          <td><select name="language_level" id="language_level">
              <option value="精通" <?php if($expert->getLanguageLevel() == '精通') echo 'selected="selected"';?>>精通</option>
              <option value="熟练" <?php if($expert->getLanguageLevel() == '熟练') echo 'selected="selected"';?>>熟练</option>
              <option value="良好" <?php if($expert->getLanguageLevel() == '良好') echo 'selected="selected"';?>>良好</option>
              <option value="一般" <?php if($expert->getLanguageLevel() == '一般') echo 'selected="selected"';?>>一般</option>
            </select></td>
        </tr>
        <tr>
          <th>备注</th>
          <td colspan="3"><textarea name="note" rows="10" id="note" style="width:98%;"><?=$expert->getNote()?>
  </textarea></td>
        </tr>
      </form>
    </table>
  </div>
  <div class="tools" style="text-align:center;"> <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存修改</a> </div>
</div>
