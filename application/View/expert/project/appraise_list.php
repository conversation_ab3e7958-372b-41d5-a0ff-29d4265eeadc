<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                等待上报的评审
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="alert alert-success" role="alert">
                    <b>尊敬的专家：</b>您好！您已经对以下项目进行了评审。评审完毕后请上报您所评审的项目，否则该次评审将视为无效处理。上报方法：选中您已评审完毕并确定上报的项目（在项目前面的方框中勾选），然后点击列表下面的“上报选中项”按钮完成项目评审的上报操作。
                </div>
                <div class="search">
                    <table width="100%" align="center">
                        <tbody id="show_search">
                        <form action="<?=site_url("expert/project/appraise_list")?>" method="post" name="search" id="search">
                            <tr>
                                <td>关键词：
                                    <input id="search" name="search" class="form-control w-auto custom-control-inline" />
                                    <label><input name="field" type="radio" value="subject" checked="checked" />项目名称</label>
                                    <label><input name="field" type="radio" value="type_subject" />研究对象</label>
                                    <?=btn('button','搜索','submit','find')?>
                                </td>
                            </tr>
                        </form>
                        </tbody>
                    </table>
                </div>
                <div class="box">
                    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("expert/project/doSubmit")?>">
                        <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
                            <tr>
                                <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th>项目名称</th>
                                <th>研究对象</th>
                                <th>评审日期</th>
                                <th class="text-center">评审得分</th>
                                <th width="120" class="text-center">操作</th>
                            </tr>
                            <?php while($grade = $pager->getObject()):?>
                                <tr>
                                    <td><input name="select_id[]" type="checkbox" value="<?=$grade->getId()?>" /></td>
                                    <td><a href="<?=site_url("apply/project/show/id/".$grade->getProject()->getProjectId())?>" target="_blank"><?=$grade->getProject()->getSubject()?></a></td>
                                    <td><?=$grade->getProject()->getTypeSubject()?></td>
                                    <td><?=$grade->getUpdatedAt("Y/m/d H:i")?></td>
                                    <td align="center"><?=$grade->getScore()?></td>
                                    <td align="center">
                                        <a href="<?=site_url("apply/assess/index/id/".$grade->getId())?>" class="btn btn-success btn-sm" role="button">重新评审</a>
                                        <!-- <a href="#" class="btn btn-danger btn-sm" role="button" onclick="return showWindow('放弃评审','<?=site_url("expert/project/doNonuser/id/".$grade->getId())?>',{area:['550px','360px']});">弃权</a> -->
                                        <!-- <a href="<?=site_url("expert/project/doNonuser/id/".$grade->getId())?>" class="btn btn-danger btn-sm" role="button" onclick="return confirm('放弃评审后将失去评审该项目的机会，你确定放弃评审吗？');">弃权</a> -->
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            <tr>
                                <td colspan="8">
                                    <?=Button::setType('submit')->setIcon('submit')->button('上报选中项')?>
                                    <?=Button::setType('button')->setIcon('submit')->setEvent("$('#validateForm').attr('action','".site_url("expert/project/doSubmitAll")."');$('#validateForm').submit();return false;")->setClass('btn-alt-danger')->button('上报所有项')?>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="8" align="right">
                                    <span class="pager_bar">
                                        <?=$pager->fromto().$pager->navbar(10)?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

