<div class="main">
  <div class="btn-group btn-group-sm" role="group"> <a href="javascript:void(0);" onclick="javascript:history.go(-1);" class="glyphicon glyphicon-chevron-left">返回</a> <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存修改</a> <a href="<?=site_url("expert/project/download")?>" class="btn btn-info export" role="button">导出</a></div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">专家详细信息</caption>
        <tr>
          <th width="80" align="right"> 专家姓名：</th>
          <td width="224" align="left"><input name="user_name" type="text" id="user_name" value="<?=$user->getUserName()?>" class="required"/></td>
          <th width="80" align="left">性别：</th>
          <td width="227" align="left"><select name="user_sex" id="user_sex" class="required">
            <option value="男" <?php if($user->getUserSex() == '男') echo 'selected="selected"';?> >男</option>
            <option value="女" <?php if($user->getUserSex() == '女') echo 'selected="selected"';?> >女</option>
          </select></td>
        </tr>
        <tr>
          <th width="80" align="right"> 出生日期：</th>
          <td align="left"><input type="text" name="user_birthday" id="user_birthday" class="date-pick required"  value="<?=$user->getUserBirthday()?>"/></td>
          <th width="80" align="left">民族：</th>
          <td align="left"><select name="user_nation" id="user_nation" class="required">
            <?=getNationList($user->getUserNation())?>
          </select></td>
        </tr>
        <tr>
          <th width="80" align="right">证件类别：</th>
          <td align="left"><label>
            <input name="card_name" type="radio" id="radio" value="身份证" checked="checked" />
            身份证</label></td>
          <th width="80" align="left">证件号码：</th>
          <td align="left"><input type="text" name="card_id" id="card_id"  value="<?=$user->getCardId()?>" class="required"/></td>
        </tr>
        <tr>
          <th width="80" align="right">身体状况：</th>
          <td align="left"><input type="text" name="user_health" id="user_health"  value="<?=$user->getUserHealth()?>"  class="required"/></td>
          <th width="80" align="left">学术荣誉：</th>
          <td align="left"><select name="user_academichonor" id="user_academichonor" class="required">
            <option value="中科院院士" <?php if($user->getUserAcademichonor() == '中科院院士') echo 'selected="selected"';?>>中科院院士</option>
            <option value="工程院院士" <?php if($user->getUserAcademichonor() == '工程院院士') echo 'selected="selected"';?>>工程院院士</option>
            <option value="省学科带头人" <?php if($user->getUserAcademichonor() == '省学科带头人') echo 'selected="selected"';?>>省学科带头人</option>
            <option value="长江学者" <?php if($user->getUserAcademichonor() == '长江学者') echo 'selected="selected"';?>>长江学者</option>
            <option value="国务院突出贡献专家" <?php if($user->getUserAcademichonor() == '国务院突出贡献专家') echo 'selected="selected"';?>>国务院突出贡献专家</option>
            <option value="其他" <?php if($user->getUserAcademichonor() == '其他') echo 'selected="selected"';?>>其他</option>
          </select></td>
        </tr>
        <tr>
          <th width="80" align="right">联系地址：</th>
          <td align="left"><input name="postal_address" type="text" class="required" id="postal_address" title="请填写你的联系地址" value="<?=$user->getPostalAddress()?>"  size="30"/></td>
          <th width="80" align="left">邮编</th>
          <td align="left"><input name="postal_code" type="text" class="required number" id="postal_code" value="<?=$user->getPostalCode()?>"  size="10"/></td>
        </tr>
        <tr>
          <th width="80" align="right">电子邮箱：</th>
          <td align="left"><input type="text" name="user_email" id="user_email" value="<?=$user->getUserEmail()?>" class="required" /></td>
          <th width="80" align="left">手机：</th>
          <td align="left"><input type="text" name="user_mobile" id="user_mobile"  value="<?=$user->getUserMobile()?>" class="required"/></td>
        </tr>
        <tr>
          <th width="80" align="right">专家类别：</th>
          <td align="left">
            <select name="user_type" id="user_type"  class="required">
              <option value="">请选择专家类别</option>
              <option value="经济专家" <?php if($user->getUserType() == '经济专家') echo 'selected="selected"';?> >经济专家</option>
              <option value="管理专家" <?php if($user->getUserType() == '管理专家') echo 'selected="selected"';?>>管理专家</option>
              <option value="投资专家" <?php if($user->getUserType() == '投资专家') echo 'selected="selected"';?>>投资专家</option>
              <option value="软科学专家" <?php if($user->getUserType() == '软科学专家') echo 'selected="selected"';?>>软科学专家</option>
              <option value="技术专家" <?php if($user->getUserType() == '技术专家') echo 'selected="selected"';?>>技术专家</option>
              <option value="其他" <?php if($user->getUserType() == '其他') echo 'selected="selected"';?>>其他</option>
            </select>
            <em>*</em></td>
          <th width="80" align="left">职称：</th>
          <td align="left"><select  name="user_honor" id="user_honor" class="required">
            <option value="高级实验师" <?php if($user->getUserHonor() == '博士') echo 'selected="selected"';?>>高级实验师</option>
            <option value="高级编辑" <?php if($user->getUserHonor() == '高级编辑') echo 'selected="selected"';?>>高级编辑</option>
            <option value="高级工程师" <?php if($user->getUserHonor() == '高级工程师') echo 'selected="selected"';?>>高级工程师</option>
            <option value="研究馆员" <?php if($user->getUserHonor() == '研究馆员') echo 'selected="selected"';?>>研究馆员</option>
            <option value="副研究馆员" <?php if($user->getUserHonor() == '副研究馆员') echo 'selected="selected"';?>>副研究馆员</option>
            <option value="主任（药、护、技）师" <?php if($user->getUserHonor() == '主任（药、护、技）师') echo 'selected="selected"';?>>主任（药、护、技）师</option>
            <option value="教授" <?php if($user->getUserHonor() == '教授') echo 'selected="selected"';?>>教授</option>
            <option value="副教授" <?php if($user->getUserHonor() == '副教授') echo 'selected="selected"';?>>副教授</option>
            <option value="研究员" <?php if($user->getUserHonor() == '研究员') echo 'selected="selected"';?>>研究员</option>
            <option value="副研究员" <?php if($user->getUserHonor() == '副研究员') echo 'selected="selected"';?>>副研究员</option>
            <option value="主治（主管）医（药、护、技）师" <?php if($user->getUserHonor() == '主治（主管）医（药、护、技）师') echo 'selected="selected"';?>>主治（主管）医（药、护、技）师</option>
            <option value="高级记者" <?php if($user->getUserHonor() == '高级记者') echo 'selected="selected"';?>>高级记者</option>
            <option value="高级统计师" <?php if($user->getUserHonor() == '高级统计师') echo 'selected="selected"';?>>高级统计师</option>
            <option value="高级经济师" <?php if($user->getUserHonor() == '高级经济师') echo 'selected="selected"';?>>高级经济师</option>
            <option value="经济师" <?php if($user->getUserHonor() == '经济师') echo 'selected="selected"';?>>经济师</option>
            <option value="高级（审计）会计师" <?php if($user->getUserHonor() == '高级（审计）会计师') echo 'selected="selected"';?>>高级（审计）会计师</option>
            <option value="（审计）会计师" <?php if($user->getUserHonor() == '（审计）会计师') echo 'selected="selected"';?>>（审计）会计师</option>
            <option value="高级农艺师" <?php if($user->getUserHonor() == '高级农艺师') echo 'selected="selected"';?>>高级农艺师</option>
            <option value="农艺师" <?php if($user->getUserHonor() == '农艺师') echo 'selected="selected"';?>>农艺师</option>
            <option value="一级教练" <?php if($user->getUserHonor() == '一级教练') echo 'selected="selected"';?>>一级教练</option>
            <option value="高级讲师" <?php if($user->getUserHonor() == '高级讲师') echo 'selected="selected"';?>>高级讲师</option>
            <option value="高级教师" <?php if($user->getUserHonor() == '高级教师') echo 'selected="selected"';?>>高级教师</option>
            <option value="高级畜牧师" <?php if($user->getUserHonor() == '高级畜牧师') echo 'selected="selected"';?>>高级畜牧师</option>
            <option value="高级兽医师" <?php if($user->getUserHonor() == '高级兽医师') echo 'selected="selected"';?>>高级兽医师</option>
            <option value="其它" <?php if($user->getUserHonor() == '其它') echo 'selected="selected"';?>>其它</option>
          </select></td>
        </tr>
        <tr>
          <th width="80" align="right"> 学位：</th>
          <td align="left"><select name="user_degree" id="user_degree" class="required" >
            <option value="博士" <?php if($user->getUserDegree() == '博士') echo 'selected="selected"';?>>博士</option>
            <option value="博士后" <?php if($user->getUserDegree() == '博士后') echo 'selected="selected"';?>>博士后</option>
            <option value="硕士" <?php if($user->getUserDegree() == '硕士') echo 'selected="selected"';?>>硕士</option>
            <option value="学士" <?php if($user->getUserDegree() == '学士') echo 'selected="selected"';?>>学士</option>
          </select></td>
          <th width="80" align="left">学历：</th>
          <td align="left"><select name="user_level" id="user_level" class="required" >
            <option value="">请选择学历</option>
            <option value="博士研究生"  <?php if($user->getUserLevel() == '博士研究生') echo 'selected="selected"';?> >博士研究生</option>
            <option value="硕士研究生"  <?php if($user->getUserLevel() == '硕士研究生') echo 'selected="selected"';?> >硕士研究生</option>
            <option value="本科"  <?php if($user->getUserLevel() == '本科') echo 'selected="selected"';?> >本科</option>
          </select></td>
        </tr>
        <tr>
          <th width="80" align="right">毕业院校：</th>
          <td align="left"><input type="text" name="graduate_school" id="graduate_school" value="<?=$user->getGraduateSchool()?>"  class="required"/></td>
          <th width="80" align="left">所学专业：</th>
          <td align="left"><input type="text" name="subject_name" id="subject_name" value="<?=$user->getSubjectName()?>" class="required" /></td>
        </tr>
        </table>
        <table border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">工作单位信息</caption>
        <tr>
          <th align="right" >工作单位：   </th>
          <td colspan="3" align="left" ><input name="work_unit" type="text"  class="required" id="work_unit" value="<?=$user->getWorkUnit()?>" size="60"/></td>
        </tr>
        <tr>
          <th align="right" >通信地址：</th>
          <td align="left" ><input type="text" name="unit_address" id="unit_address" value="<?=$user->getUnitAddress()?>" class="required" /></td>
          <th align="left" >邮编：</th>
          <td align="left" ><input name="unit_postal_code" type="text" class="required number" id="unit_postal_code" value="<?=$user->getUnitPostalCode()?>"  size="10" maxlength="20"/></td>
        </tr>
        <tr>
          <th align="right" >所属部门：</th>
          <td colspan="3" align="left" ><input name="work_department" type="text" class="required" id="work_department" value="<?=$user->getWorkDepartment()?>" size="60" /></td>
        </tr>
        <tr>
          <th align="right" > 职务：</th>
          <td align="left" ><input type="text" name="user_duty" id="user_duty" value="<?=$user->getUserDuty()?>" class="required" /></td>
          <th align="left" >从事专业：</th>
          <td align="left" ><input type="text" name="user_work" id="user_work" value="<?=$user->getUserWork()?>" class="required" /></td>
        </tr>
        <tr>
          <th align="right" >电话：</th>
          <td align="left" ><input type="text" name="unit_phone" id="unit_phone" value="<?=$user->getUnitPhone()?>"  class="required"/></td>
          <th align="left" >传真：</th>
          <td align="left" ><input name="user_fax" type="text" id="user_fax" value="<?=$user->getUserFax()?>" /></td>
        </tr>
        <tr>
          <th align="right" >研究领域1：</th>
          <td align="left" ><select name="subject_names[]" id="subject_names1" class="required" >
            <option value="">请选择研究领域</option>
            <option value="" style="color:#F00;">----A.自然科学----</option>
            <option value="数学" <?php if($user->getSubjectNames(1) == '数学') echo 'selected="selected"';?>>数学</option>
            <option value="信息科学与系统科学" <?php if($user->getSubjectNames(1) == '信息科学与系统科学') echo 'selected="selected"';?>>信息科学与系统科学</option>
            <option value="力学" <?php if($user->getSubjectNames(1) == '力学') echo 'selected="selected"';?>>力学</option>
            <option value="物理学" <?php if($user->getSubjectNames(1) == '物理学') echo 'selected="selected"';?>>物理学</option>
            <option value="化学" <?php if($user->getSubjectNames(1) == '化学') echo 'selected="selected"';?>>化学</option>
            <option value="天文学" <?php if($user->getSubjectNames(1) == '天文学') echo 'selected="selected"';?>>天文学</option>
            <option value="地球科学" <?php if($user->getSubjectNames(1) == '地球科学') echo 'selected="selected"';?>>地球科学</option>
            <option value="生物学" <?php if($user->getSubjectNames(1) == '生物学') echo 'selected="selected"';?>>生物学</option>
            <option value="" style="color:#F00;">----B.农业科学----</option>
            <option value="农学" <?php if($user->getSubjectNames(1) == '农学') echo 'selected="selected"';?>>农学</option>
            <option value="林学" <?php if($user->getSubjectNames(1) == '林学') echo 'selected="selected"';?>>林学</option>
            <option value="畜牧、兽医科学" <?php if($user->getSubjectNames(1) == '畜牧、兽医科学') echo 'selected="selected"';?>>畜牧、兽医科学</option>
            <option value="水产学" <?php if($user->getSubjectNames(1) == '水产学') echo 'selected="selected"';?>>水产学</option>
            <option value="" style="color:#F00;">----C.医药科学----</option>
            <option value="基础医学" <?php if($user->getSubjectNames(1) == '基础医学') echo 'selected="selected"';?>>基础医学</option>
            <option value="临床医学" <?php if($user->getSubjectNames(1) == '临床医学') echo 'selected="selected"';?>>临床医学</option>
            <option value="预防医学与卫生学" <?php if($user->getSubjectNames(1) == '预防医学与卫生学') echo 'selected="selected"';?>>预防医学与卫生学</option>
            <option value="军事医学与特种医学" <?php if($user->getSubjectNames(1) == '军事医学与特种医学') echo 'selected="selected"';?>>军事医学与特种医学</option>
            <option value="药学" <?php if($user->getSubjectNames(1) == '药学') echo 'selected="selected"';?>>药学</option>
            <option value="中医学与中药学" <?php if($user->getSubjectNames(1) == '中医学与中药学') echo 'selected="selected"';?>>中医学与中药学</option>
            <option value="" style="color:#F00;">--D.工程与技术科学--</option>
            <option value="工程与技术科学基础学科" <?php if($user->getSubjectNames(1) == '工程与技术科学基础学科') echo 'selected="selected"';?>>工程与技术科学基础学科</option>
            <option value="测绘科学技术" <?php if($user->getSubjectNames(1) == '测绘科学技术') echo 'selected="selected"';?>>测绘科学技术</option>
            <option value="材料科学" <?php if($user->getSubjectNames(1) == '材料科学') echo 'selected="selected"';?>>材料科学</option>
            <option value="矿山工程技术" <?php if($user->getSubjectNames(1) == '矿山工程技术') echo 'selected="selected"';?>>矿山工程技术</option>
            <option value="冶金工程技术" <?php if($user->getSubjectNames(1) == '冶金工程技术') echo 'selected="selected"';?>>冶金工程技术</option>
            <option value="机械工程" <?php if($user->getSubjectNames(1) == '机械工程') echo 'selected="selected"';?>>机械工程</option>
            <option value="动力与电气工程" <?php if($user->getSubjectNames(1) == '动力与电气工程') echo 'selected="selected"';?>>动力与电气工程</option>
            <option value="能源科学技术" <?php if($user->getSubjectNames(1) == '能源科学技术') echo 'selected="selected"';?>>能源科学技术</option>
            <option value="核科学技术" <?php if($user->getSubjectNames(1) == '核科学技术') echo 'selected="selected"';?>>核科学技术</option>
            <option value="电子、通信与自动控制技术" <?php if($user->getSubjectNames(1) == '电子、通信与自动控制技术') echo 'selected="selected"';?>>电子、通信与自动控制技术</option>
            <option value="计算机科学技术" <?php if($user->getSubjectNames(1) == '计算机科学技术') echo 'selected="selected"';?>>计算机科学技术</option>
            <option value="化学工程" <?php if($user->getSubjectNames(1) == '化学工程') echo 'selected="selected"';?>>化学工程</option>
            <option value="纺织科学技术" <?php if($user->getSubjectNames(1) == '纺织科学技术') echo 'selected="selected"';?>>纺织科学技术</option>
            <option value="食品科学技术" <?php if($user->getSubjectNames(1) == '食品科学技术') echo 'selected="selected"';?>>食品科学技术</option>
            <option value="木建筑工程" <?php if($user->getSubjectNames(1) == '木建筑工程') echo 'selected="selected"';?>>木建筑工程</option>
            <option value="水利工程" <?php if($user->getSubjectNames(1) == '水利工程') echo 'selected="selected"';?>>水利工程</option>
            <option value="交通运输工程" <?php if($user->getSubjectNames(1) == '交通运输工程') echo 'selected="selected"';?>>交通运输工程</option>
            <option value="航空、航天科学技术" <?php if($user->getSubjectNames(1) == '航空、航天科学技术') echo 'selected="selected"';?>>航空、航天科学技术</option>
            <option value="环境科学技术" <?php if($user->getSubjectNames(1) == '环境科学技术') echo 'selected="selected"';?>>环境科学技术</option>
            <option value="安全科学技术" <?php if($user->getSubjectNames(1) == '安全科学技术') echo 'selected="selected"';?>>安全科学技术</option>
            <option value="管理学" <?php if($user->getSubjectNames(1) == '管理学') echo 'selected="selected"';?>>管理学</option>
            <option value="" style="color:#F00;">----E.人文与社会科学----</option>
            <option value="经济学" <?php if($user->getSubjectNames(1) == '经济学') echo 'selected="selected"';?>>经济学</option>
            <option value="政治学" <?php if($user->getSubjectNames(1) == '政治学') echo 'selected="selected"';?>>政治学</option>
            <option value="法学" <?php if($user->getSubjectNames(1) == '法学') echo 'selected="selected"';?>>法学</option>
            <option value="军事学" <?php if($user->getSubjectNames(1) == '军事学') echo 'selected="selected"';?>>军事学</option>
            <option value="社会学" <?php if($user->getSubjectNames(1) == '社会学') echo 'selected="selected"';?>>社会学</option>
            <option value="民族学" <?php if($user->getSubjectNames(1) == '民族学') echo 'selected="selected"';?>>民族学</option>
            <option value="新闻学与传播学" <?php if($user->getSubjectNames(1) == '新闻学与传播学') echo 'selected="selected"';?>>新闻学与传播学</option>
            <option value="教育学" <?php if($user->getSubjectNames(1) == '教育学') echo 'selected="selected"';?>>教育学</option>
            <option value="体育学" <?php if($user->getSubjectNames(1) == '体育学') echo 'selected="selected"';?>>体育学</option>
            <option value="统计学" <?php if($user->getSubjectNames(1) == '统计学') echo 'selected="selected"';?>>统计学</option>
          </select></td>
          <th align="left" >研究领域2：</th>
          <td align="left" ><select name="subject_names[]" id="subject_names2">
            <option value="">请选择研究领域</option>
            <option value="" style="color:#F00;">----A.自然科学----</option>
            <option value="数学" <?php if($user->getSubjectNames(2) == '数学') echo 'selected="selected"';?>>数学</option>
            <option value="信息科学与系统科学" <?php if($user->getSubjectNames(2) == '信息科学与系统科学') echo 'selected="selected"';?>>信息科学与系统科学</option>
            <option value="力学" <?php if($user->getSubjectNames(2) == '力学') echo 'selected="selected"';?>>力学</option>
            <option value="物理学" <?php if($user->getSubjectNames(2) == '物理学') echo 'selected="selected"';?>>物理学</option>
            <option value="化学" <?php if($user->getSubjectNames(2) == '化学') echo 'selected="selected"';?>>化学</option>
            <option value="天文学" <?php if($user->getSubjectNames(2) == '天文学') echo 'selected="selected"';?>>天文学</option>
            <option value="地球科学" <?php if($user->getSubjectNames(2) == '地球科学') echo 'selected="selected"';?>>地球科学</option>
            <option value="生物学" <?php if($user->getSubjectNames(2) == '生物学') echo 'selected="selected"';?>>生物学</option>
            <option value="" style="color:#F00;">----B.农业科学----</option>
            <option value="农学" <?php if($user->getSubjectNames(2) == '农学') echo 'selected="selected"';?>>农学</option>
            <option value="林学" <?php if($user->getSubjectNames(2) == '林学') echo 'selected="selected"';?>>林学</option>
            <option value="畜牧、兽医科学" <?php if($user->getSubjectNames(2) == '畜牧、兽医科学') echo 'selected="selected"';?>>畜牧、兽医科学</option>
            <option value="水产学" <?php if($user->getSubjectNames(2) == '水产学') echo 'selected="selected"';?>>水产学</option>
            <option value="" style="color:#F00;">----C.医药科学----</option>
            <option value="基础医学" <?php if($user->getSubjectNames(2) == '基础医学') echo 'selected="selected"';?>>基础医学</option>
            <option value="临床医学" <?php if($user->getSubjectNames(2) == '临床医学') echo 'selected="selected"';?>>临床医学</option>
            <option value="预防医学与卫生学" <?php if($user->getSubjectNames(2) == '预防医学与卫生学') echo 'selected="selected"';?>>预防医学与卫生学</option>
            <option value="军事医学与特种医学" <?php if($user->getSubjectNames(2) == '军事医学与特种医学') echo 'selected="selected"';?>>军事医学与特种医学</option>
            <option value="药学" <?php if($user->getSubjectNames(2) == '药学') echo 'selected="selected"';?>>药学</option>
            <option value="中医学与中药学" <?php if($user->getSubjectNames(2) == '中医学与中药学') echo 'selected="selected"';?>>中医学与中药学</option>
            <option value="" style="color:#F00;">--D.工程与技术科学--</option>
            <option value="工程与技术科学基础学科" <?php if($user->getSubjectNames(2) == '工程与技术科学基础学科') echo 'selected="selected"';?>>工程与技术科学基础学科</option>
            <option value="测绘科学技术" <?php if($user->getSubjectNames(2) == '测绘科学技术') echo 'selected="selected"';?>>测绘科学技术</option>
            <option value="材料科学" <?php if($user->getSubjectNames(2) == '材料科学') echo 'selected="selected"';?>>材料科学</option>
            <option value="矿山工程技术" <?php if($user->getSubjectNames(2) == '矿山工程技术') echo 'selected="selected"';?>>矿山工程技术</option>
            <option value="冶金工程技术" <?php if($user->getSubjectNames(2) == '冶金工程技术') echo 'selected="selected"';?>>冶金工程技术</option>
            <option value="机械工程" <?php if($user->getSubjectNames(2) == '机械工程') echo 'selected="selected"';?>>机械工程</option>
            <option value="动力与电气工程" <?php if($user->getSubjectNames(2) == '动力与电气工程') echo 'selected="selected"';?>>动力与电气工程</option>
            <option value="能源科学技术" <?php if($user->getSubjectNames(2) == '能源科学技术') echo 'selected="selected"';?>>能源科学技术</option>
            <option value="核科学技术" <?php if($user->getSubjectNames(2) == '核科学技术') echo 'selected="selected"';?>>核科学技术</option>
            <option value="电子、通信与自动控制技术" <?php if($user->getSubjectNames(2) == '电子、通信与自动控制技术') echo 'selected="selected"';?>>电子、通信与自动控制技术</option>
            <option value="计算机科学技术" <?php if($user->getSubjectNames(2) == '计算机科学技术') echo 'selected="selected"';?>>计算机科学技术</option>
            <option value="化学工程" <?php if($user->getSubjectNames(2) == '化学工程') echo 'selected="selected"';?>>化学工程</option>
            <option value="纺织科学技术" <?php if($user->getSubjectNames(2) == '纺织科学技术') echo 'selected="selected"';?>>纺织科学技术</option>
            <option value="食品科学技术" <?php if($user->getSubjectNames(2) == '食品科学技术') echo 'selected="selected"';?>>食品科学技术</option>
            <option value="木建筑工程" <?php if($user->getSubjectNames(2) == '木建筑工程') echo 'selected="selected"';?>>木建筑工程</option>
            <option value="水利工程" <?php if($user->getSubjectNames(2) == '水利工程') echo 'selected="selected"';?>>水利工程</option>
            <option value="交通运输工程" <?php if($user->getSubjectNames(2) == '交通运输工程') echo 'selected="selected"';?>>交通运输工程</option>
            <option value="航空、航天科学技术" <?php if($user->getSubjectNames(2) == '航空、航天科学技术') echo 'selected="selected"';?>>航空、航天科学技术</option>
            <option value="环境科学技术" <?php if($user->getSubjectNames(2) == '环境科学技术') echo 'selected="selected"';?>>环境科学技术</option>
            <option value="安全科学技术" <?php if($user->getSubjectNames(2) == '安全科学技术') echo 'selected="selected"';?>>安全科学技术</option>
            <option value="管理学" <?php if($user->getSubjectNames(2) == '管理学') echo 'selected="selected"';?>>管理学</option>
            <option value="" style="color:#F00;">----E.人文与社会科学----</option>
            <option value="经济学" <?php if($user->getSubjectNames(2) == '经济学') echo 'selected="selected"';?>>经济学</option>
            <option value="政治学" <?php if($user->getSubjectNames(2) == '政治学') echo 'selected="selected"';?>>政治学</option>
            <option value="法学" <?php if($user->getSubjectNames(2) == '法学') echo 'selected="selected"';?>>法学</option>
            <option value="军事学" <?php if($user->getSubjectNames(2) == '军事学') echo 'selected="selected"';?>>军事学</option>
            <option value="社会学" <?php if($user->getSubjectNames(2) == '社会学') echo 'selected="selected"';?>>社会学</option>
            <option value="民族学" <?php if($user->getSubjectNames(2) == '民族学') echo 'selected="selected"';?>>民族学</option>
            <option value="新闻学与传播学" <?php if($user->getSubjectNames(2) == '新闻学与传播学') echo 'selected="selected"';?>>新闻学与传播学</option>
            <option value="教育学" <?php if($user->getSubjectNames(2) == '教育学') echo 'selected="selected"';?>>教育学</option>
            <option value="体育学" <?php if($user->getSubjectNames(2) == '体育学') echo 'selected="selected"';?>>体育学</option>
            <option value="统计学" <?php if($user->getSubjectNames(2) == '统计学') echo 'selected="selected"';?>>统计学</option>
          </select></td>
        </tr>
      </table>
      <table border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">
        专家详细信息
        </caption>
        <tr>
          <th width="15%" align="left" >主研或参与的科技计划简介：</th>
          <td width="85%" ><textarea name="user_technologyguides" id="user_technologyguides" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserTechnologyguides()?>
</textarea></td>
        </tr>
        <tr>
          <th align="left">参加的重要科技计划评估、评审活动简介：</th>
          <td ><textarea name="user_technologyevaluation" id="user_technologyevaluation" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserTechnologyevaluation()?>
</textarea></td>
        </tr>
        <tr>
          <th align="left" >获得的主要成果奖励情况：</th>
          <td ><textarea name="user_awardresults" id="user_awardresults" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserAwardresults()?>
</textarea></td>
        </tr>
        <tr>
          <th align="left" >社会兼、聘职信息：</th>
          <td ><textarea name="user_pinformationrecruits" id="user_pinformationrecruits" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserPinformationrecruits()?>
</textarea></td>
        </tr>
        <tr>
          <th align="left" >发表的主要专著、论文情况：</th>
          <td ><textarea name="user_monographspapers" id="user_monographspapers" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserMonographspapers()?>
</textarea></td>
        </tr>
        <tr>
          <th align="left" >获得的国家、省部级认定执业情况：</th>
          <td ><textarea name="user_findsjob" id="user_findsjob" cols="50" rows="5" class="required" style="width:100%;"><?=$user->getUserFindsjob()?>
</textarea></td>
        </tr>
      </table>
    </form>
  </div>
  <div class="tools" style="text-align:center;"> <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存修改</a> </div>
</div>
