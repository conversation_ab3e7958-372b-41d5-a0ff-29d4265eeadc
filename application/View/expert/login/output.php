<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>申报书打印</title>
<style type="text/css">
<!--
.STYLE4 {
	font-size: 36px
}
.STYLE5 {
	font-size: 24px;
	font-weight: bold;
}
.STYLE6 {
	font-size: 24px
}
-->
</style>
<style>
table {
	border-collapse:collapse;
	border:solid black;
}
td {
	border: 1px solid black
}
</style>
<style>
.gh {
	position:absolute;
	left:20px;
	right:20px;
	visibility: visible;
}
</style>
<style>
.p {
	text-indent:2em;
	font-size:17px;
	line-height:40px;
}
</style>
<style>
.p2 {
	text-indent:2em;
	font-size:16px;
	line-height:36px;
}
</style>
<style>
.p1 {
	line-height:16px;
	font-size:16px;
}
</style>
<style>
.middle-demo-2 {
	padding-top: 24px;
	padding-bottom: 24px;
}
</style>
<style>
.def {
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def1 {
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.abc {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-color:#000000;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
}
.abc1 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:0px;
	border-bottom-color:#CCCCCC;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #000000;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc2 {
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.abc3 {
	border-left-width:1px;
	border-top-width:0px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #000000;
	border-left-color: #000000;
}
.STYLE8 {
	color: #000000;
	font-weight: bold;
	font-size: 16px;
}
.bm {
	text-decoration: underline;
}
.STYLE10 {
	font-size: 18px
}
.STYLE11 {
	font-size: 16px
}
.abc4 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-color:#000000;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
}
.abc5 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-color:#000000;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
}
.btn {
	BORDER-RIGHT: #7b9ebd 1px solid;
	PADDING-RIGHT: 2px;
	BORDER-TOP: #7b9ebd 1px solid;
	PADDING-LEFT: 2px;
	FONT-SIZE: 12px;
FILTER: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr=#ffffff, EndColorStr=#cecfde);
	BORDER-LEFT: #7b9ebd 1px solid;
	CURSOR: hand;
	COLOR: black;
	PADDING-TOP: 2px;
	BORDER-BOTTOM: #7b9ebd 1px solid
}
.abc6 {
	border-left-width:1px;
	border-top-width:1px;
	border-right-width:1px;
	border-bottom-width:1px;
	border-color:#000000;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
}
.font_tishi {
	font-size: 15px;
	letter-spacing: 2px;
	font-family: "宋体";
	line-height: 1.8;
	color: #777777;
}
.def11 {
	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def2 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def3 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def4 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def5 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:1px;
	border-bottom-color:#000000;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
.def12 {	border-left-width:0px;
	border-top-width:0px;
	border-right-width:0px;
	border-bottom-width:0px;
	border-bottom-color:#FFFFFF;
	able-layout:fixed;
	word-break:break-all;
	ord-wrap: break-word;
	font-size: 14px;
	letter-spacing: 1px;
	font-family: "宋体";
	line-height: 1.8;
	border-top-color: #FFFFFF;
	border-right-color: #FFFFFF;
	border-left-color: #FFFFFF;
}
</style>
</head>
<body>
<p align="center">
<table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def1">
  <tr>
    <td height="5" align="right" valign="bottom"  width="17%"class="def1" >&nbsp;</td>
    <td class="def1"  align="left" width="21%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">&nbsp;</span></td>
    <td valign="bottom" class="def1" align="right" width="15%">&nbsp;</td>
    <td valign="bottom" class="def1" align="left" width="19%">&nbsp;</td>
    <td valign="bottom" class="def1" align="right" width="11%"><span class="def11"><span class="STYLE8">密级：</span></span></td>
    <td class="def"  align="left" width="17%" style="margin-bottom:0px;margin-top:20px" valign="bottom">&nbsp;</td>
  </tr>
</table>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
</p>
<p align="center"><span class="STYLE4"><strong>四川省能投集团科技信息综合管理平台</strong><br />
  <strong>网络评审专家申请书</strong></span></p>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
<table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def12">
  <tr>
    <td rowspan="7" align="right" class="def12"  width="6%" ></td>
    <td width="26%" align="right"  valign="bottom" class="def12" ><span class="STYLE8">申   请   人：</span></td>
    <td colspan="2"  align="left" valign="bottom" class="def5" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$expert->getUserName()?>
    </span></td>
    <td  width="15%" rowspan="6" align="right" class="def12" ></td>
  </tr>
  <tr>
    <td align="right" valign="bottom" class="def12" ><span class="STYLE8">职<span lang="EN-US" xml:lang="EN-US">  </span>称：</span></td>
    <td colspan="2" align="left"  valign="bottom" class="def5" style="margin-bottom:0px;margin-top:20px"><?=$expert->getUserHonor()?></td>
  </tr>
  <tr>
    <td align="right" valign="bottom" class="def12" ><span class="STYLE8">联系电话：</span></td>
    <td colspan="2" align="left"  valign="bottom" class="def5" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$expert->getUserMobile()?>
    </span></td>
  </tr>
  <tr>
    <td align="right" valign="bottom" class="def12" ><span class="STYLE8">申报单位：</span></td>
    <td colspan="2" align="left"  valign="bottom" class="def5" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=$expert->getWorkUnit()?>
      </span></td>
  </tr>
  <tr>
    <td align="right" valign="bottom" class="def12" ><span class="STYLE8">申请日期：</span></td>
    <td colspan="2" align="left" valign="bottom" class="def5" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11">
      <?=date("Y 年 m 月 d 日")?>
    </span></td>
  </tr>
</table>
<p>&nbsp;</p>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
<p>&nbsp;</p>
<p align="center"><span  class="STYLE5"><strong>中国民航局第二研究所制  </strong></span>

<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span>

<p align="center"><strong>中国民航局第二研究所科技计划项目评审专家库专家注册承诺书</strong></p>
<p>本人郑重承诺：<span lang="EN-US" xml:lang="EN-US"></span></p>
<p>一、本人已认真阅读了中国民航局第二研究所公布的项目评审专家的选聘原则和条件，自愿成为“中国民航局第二研究所科技计划项目评审专家库”的注册专家，并接受中国民航局第二研究所委托，承担并按期完成项目评审和专家咨询工作，并对上述工作成果的真实性、公正性负责；<span lang="EN-US" xml:lang="EN-US"></span></p>
<p>二、本人承诺在工作中将以严肃科学的态度，公平、公正的执行中国民航局第二研究所委托的项目评审和专家咨询工作；并接受中国民航局第二研究所的监督与考评；<span lang="EN-US" xml:lang="EN-US"></span></p>
<p>三、本人承诺自行调配工作时间，保证按时参加中国民航局第二研究所组织的项目评审和专家咨询工作；<span lang="EN-US" xml:lang="EN-US"></span></p>
<p>四、如发现待评项目或申报单位与本人存在利害关系，本人参加评审可能影响评审的公正性时，本人将及时向中国民航局第二研究所声明并提出书面的回避申请。如发现其他评审专家也存在相同情况时，本人也负有及时向中国民航局第二研究所反映情况的责任；</p>
<p>五、本人承诺严格遵守保密义务，不使用或披露，也不许可他人使用或披露在评审和专家咨询工作中获悉的项目资料、技术数据以及申报项目单位的商业秘密；<span lang="EN-US" xml:lang="EN-US"></span></p>
<p>六、本人承诺在成为“中国民航局第二研究所科技计划项目评审专家库”注册专家后，未经中国民航局第二研究所书面同意，不能以“中国民航局第二研究所科技计划项目评审专家库”注册专家的名义从事与中国民航局第二研究所所委托的项目评审和专家咨询工作无关的各类活动；<span lang="EN-US" xml:lang="EN-US"> </span></p>
<p>七、本人承诺在项目评审和专家咨询工作期间，遵守评审和咨询工作纪律和要求，不与被评项目的单位私下接触。</p>
<p>本承诺书一经接受即视为同意以上条款；如有违反，愿承担相应责任。<span lang="EN-US" xml:lang="EN-US"></span></p>
<p><span lang="EN-US" xml:lang="EN-US"> </span></p>
<p><strong></strong><strong><span lang="EN-US" xml:lang="EN-US">                                   </span></strong><strong>申请人（签字）：</strong><strong><span lang="EN-US" xml:lang="EN-US">  </span></strong></p>
<p align="right"><strong><span lang="EN-US" xml:lang="EN-US">          </span></strong><strong>年</strong><strong><span lang="EN-US" xml:lang="EN-US">   </span></strong><strong>月</strong><strong><span lang="EN-US" xml:lang="EN-US">   </span></strong><strong>日</strong></p>
<p>&nbsp;</p>

<span lang=EN-US style='font-size:10.5pt;mso-bidi-font-size:12.0pt;font-family:
"Times New Roman";mso-fareast-font-family:SimSun;mso-font-kerning:1.0pt;
mso-ansi-language:EN-US;mso-fareast-language:ZH-CN;mso-bidi-language:AR-SA'><br
clear=all style='mso-special-character:line-break;page-break-before:always'>
</span>

<p>&nbsp;</p>
<table border="1" align="center" cellpadding="0" cellspacing="0" width="680">
  <tbody>
    <tr>
      <td height="30" colspan="7"><p><strong>基本信息：</strong><strong><span lang="EN-US" xml:lang="EN-US"></span></strong></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">姓<span lang="EN-US" xml:lang="EN-US">    </span>名<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US"> 
        <?=$expert->getUserName()?>
      </span></p></td>
      <td width="98"><p align="center">性<span lang="EN-US" xml:lang="EN-US">    </span>别<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserSex()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">出生日期<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US"> 
        <?=$expert->getUserBirthday()?>
      </span></p></td>
      <td width="98"><p align="center">民<span lang="EN-US" xml:lang="EN-US">    </span>族<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserNation()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">证件类别<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US"> </span>
        <?=$expert->getCardName()?>
      </p></td>
      <td width="98"><p align="center">证件号码<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getCardId()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">身体状况<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6"><?=$expert->getUserHealth()?></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">通信地址<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="4" align="left"><?=$expert->getPostalAddress()?></td>
      <td width="98"><p align="center">邮政编码<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td width="104"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getPostalCode()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">电子邮箱<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserEmail()?>
       </span></p></td>
      <td width="98"><p align="center">手<span lang="EN-US" xml:lang="EN-US">    </span>机<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserMobile()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">专家类别<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6"><?=$expert->getUserType()?></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">职<span lang="EN-US" xml:lang="EN-US">    </span>称<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserHonor()?>
       </span></p></td>
      <td width="98"><p align="center">学术荣誉<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserAcademichonor()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">学<span lang="EN-US" xml:lang="EN-US">    </span>位<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US"> <?=$expert->getUserDegree()?></span> </p></td>
      <td width="98"><p align="center">学<span lang="EN-US" xml:lang="EN-US">    </span>历<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserLevel()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">毕业院校<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getGraduateSchool()?>
       </span></p></td>
      <td width="98"><p align="center">所学专业<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getSubjectName()?>
       </span></p></td>
    </tr>
    <tr>
      <td height="30" colspan="7"><p><strong>工作单位信息：</strong><strong><span lang="EN-US" xml:lang="EN-US"></span></strong></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">工作单位<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6"><?=$expert->getWorkUnit()?></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">单位通信地址<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="4"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUnitAddress()?>
       </span></p></td>
      <td width="98"><p align="center">邮政编码<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td width="104"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUnitPostalCode()?>
 </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">所在部门<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getWorkDepartment()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">职<span lang="EN-US" xml:lang="EN-US">    </span>务<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserDuty()?>
       </span></p></td>
      <td width="98"><p align="center">从事专业<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserWork()?>
       </span></p></td>
    </tr>
    <tr>
      <td width="97" height="30"><p align="center">办公电话<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="2"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUnitPhone()?>
       </span></p></td>
      <td width="98"><p align="center">传<span lang="EN-US" xml:lang="EN-US">    </span>真<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="3"><p align="center"><span lang="EN-US" xml:lang="EN-US">
        <?=$expert->getUserFax()?>
       </span></p></td>
    </tr>
    <tr>
      <td height="30" colspan="2"><p>主要研究领域<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="5"><?=implode(" 、 ",$expert->getArrayWithSubjectName())?></td>
    </tr>
    <tr>
      <td height="30" colspan="7"><p><strong>相关简介：</strong><span lang="EN-US" xml:lang="EN-US"></span></p></td>
    </tr>
    <tr>
      <td width="97"><p align="center">主研或参与的科技计划简介<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserTechnologyguides())?></td>
    </tr>
    <tr>
      <td width="97"><p align="center">参加的重要科技计划评估、评审活动简介<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserTechnologyevaluation())?></td>
    </tr>
    <tr>
      <td width="97"><p align="center">获得的主要成果奖励情况<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserAwardresults())?></td>
    </tr>
    <tr>
      <td width="97"><p align="center">社会兼、聘职信息<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserPinformationrecruits())?></td>
    </tr>
    <tr>
      <td width="97"><p align="center">发表的主要专著、论文情况<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserMonographspapers())?></td>
    </tr>
    <tr>
      <td width="97"><p align="center">获得的国家、省部级认定执业情况<span lang="EN-US" xml:lang="EN-US"></span></p></td>
      <td colspan="6" valign="top"><?=nl2br($expert->getUserFindsjob())?></td>
    </tr>
    <tr>
      <td height="30" colspan="7"><p align="center"><strong>申请人申报单位意见</strong><strong><span lang="EN-US" xml:lang="EN-US"></span></strong></p></td>
    </tr>
    <tr>
      <td colspan="7"><p align="center"><span lang="EN-US" xml:lang="EN-US"> </span></p>
        <p align="center"><span lang="EN-US" xml:lang="EN-US"> </span></p>
        <p align="center"><span lang="EN-US" xml:lang="EN-US"> </span></p>
        <p align="center"><span lang="EN-US" xml:lang="EN-US"> </span></p>
        <p>单位负责人（签字）：<span lang="EN-US" xml:lang="EN-US">                  </span>（单位公章）<span lang="EN-US" xml:lang="EN-US"></span></p>
        <p align="right">&nbsp;&nbsp;年&nbsp;&nbsp;月&nbsp;&nbsp;日&nbsp;&nbsp;</p></td>
    </tr>
  </tbody>
</table>
</body>
</html>
