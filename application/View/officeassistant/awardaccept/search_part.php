<form action="" method="post" name="search" id="search">
    <table width="100%" align="center" class="table table-hover table-striped">
        <tbody id="show_search">
        <tr>
            <td>关键词：
                <input class="form-control w-auto custom-control-inline" id="search" name="search" value="<?=input::getInput("post.search")?>" />
                <label><input name="field" type="radio" value="subject" checked="checked" />奖励名称</label>
                <label><input type="radio" name="field" value="corporation_name" />申报单位</label>
                <label><input type="radio" name="field" value="user_name" />奖励负责人</label>
                <?=btn('button','搜索','submit','find')?>
            </td>
        </tr>
        <tr>
            <td>
                <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                    <option value="">=申报年度=</option>
                    <?=getYearList(input::getInput("mix.declare_year"))?>
                </select>
                <select name="type_id" id="type_id" class="form-control w-auto custom-control-inline">
                    <option value="">=奖励类型=</option>
                    <?=getAwardType(input::getInput("mix.type_id"))?>
                </select>
                <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                    <option value="">=奖励状态=</option>
                    <?=awardStateSelect(input::getInput("mix.statement"))?>
                </select>
            </td>
        </tr>
        <tr>
            <td>
                <?php
                $companyId = input::getInput("mix.corporation_id")?:'9B581D83-B2BA-7337-0678-000000000000';
                ?>
                <label><span style="float: left;line-height: 35px">承担单位/部门：</span><div class="cxselect" data-selects="level1,level2,level3,level4" data-url="<?=site_url('ajax/companys') ?>" data-json-value="v" style="float: left">
                        <select class="form-control w-auto custom-control-inline parent_company level1" data-level=1 data-value="<?=getParentCompanyId($companyId,1,true)?>" ></select>
                        <select class="form-control w-auto custom-control-inline parent_company level2"  data-level=2 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,2,true)?>"></select>
                        <select class="form-control w-auto custom-control-inline parent_company level3"  data-level=3 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,3,true)?>"></select>
                        <select class="form-control w-auto custom-control-inline parent_company level4"  data-level=4 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,4,true)?>"></select>
                        <input type="hidden" name="corporation_id" id="corporation_id" value="<?=$companyId?>">
                    </div>
                </label>
            </td>
        </tr>
        </tbody>
    </table>
</form>

<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(".parent_company").change(function (){
        var level = $(this).data('level');
        if($(this).val()){
            $('#corporation_id').val($(this).val());
        }else{
            if(level>1){
                var pLevel = level-1;
                var pVal = $('.level'+pLevel).val();
                $('#corporation_id').val(pVal);
            }else{
                $('#corporation_id').val('');
            }
        }

    });
</script>