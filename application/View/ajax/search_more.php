<table class="table table-condensed table-more" >
 <tr> 
    <th>项目名称</th>
    <td><?=$search->subject?></td>
    <th>项目英文名称</th>
    <td><?=$search->project_enname?></td>
 <tr>
 <tr> 
    <th>委托单位</th>
    <td><?=$search->corporation_name?></td>
    <th>委托日期</th>
    <td><?=date('Y-m-d',strtotime($search->created_at))?></td>
 <tr>
    <th>查新编号</th>
    <td><?=$search->number?></td>
    <th>年 份</th>
    <td><?=$search->year?></td>
  </tr>
 <tr>
    <th>查新项目类型</th>
    <td><?=$search->type?></td>
    <th>紧急程度</th>
    <td><?=$search->urgency_degree?></td>
  </tr>
 <tr>
    <th>查新人员</th>
    <td><?=$search->searchman?></td>
    <th>审核人员</th>
    <td><?=$search->checkman?></td>
  </tr>  
  <tr>
    <th>项目负责人</th>
    <td><?=$search->project_leader?></td>
    <th>电 话</th>
    <td><?=$search->phone_leader?></td>
  </tr>
  <tr>
    <th>联系人</th>
    <td><?=$search->linkman?></td>
    <th>电 话</th>
    <td><?=$search->phone_linkman?></td>
  </tr> 
  <tr>
    <th>历史记录</th>
    <td><a href="javascript:void(0);" onclick="return showWindow('查看记录','<?=site_url("admin/history/index/type/searchs/id/".$search->search_id)?>');" class="btn btn-info btn-xs">查看记录</a></td>
    <th></th>
    <td></td>
  </tr>
  <?php if (count($files)>0): ?>
    <tr>
      <th>附件列表：</th>
      <td>文件名</td>
      <th>上传时间</th>
      <td></td>
    </tr> 
      <?php foreach ($files as $key => $value): ?>
    <tr>
      <th></th>
      <td><a href="<?=site_path('up_files/'.$value->file_path)?>" target='_blank'><?=$value->file_name?></a></td>
      <th><?=$value->created_at?></th>
      <td></td>
    </tr> 
  <?php endforeach ?>
  <?php else: ?>
  <tr>
    <th>附件列表：</th>
    <td>无</td>
    <th></th>
    <td></td>
  </tr> 
  <?php endif ?>
</table>

