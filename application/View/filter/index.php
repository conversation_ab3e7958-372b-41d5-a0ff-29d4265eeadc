<script type="text/javascript" language="javascript">
$(function(){
	$('#filter').submit(function(){
		if(!$('#subject').val())
		{
			alert('请填写筛选器名称');
			return false;
		}
	});
});
function checkLen()
{
	if($('#subject').val().length>10){$('#subject').val($('#subject').val().substr(0,20));$('.msg').html('<font color="$FF0000">&nbsp;&nbsp;(请输入20个字以内的名称)</font>');}
	else $('.msg').html('&nbsp;&nbsp;(请输入20个字以内的名称)');
}
</script>
<form action="" method="post" name="filter" id="filter" class="form-horizontal">
<div class="col-xs-12 col-sm-12 col-md-12 form-group">
	<label class="control-label col-xs-12 col-sm-3 col-md-3">筛选器名称</label>
	<div class="col-xs-12 col-sm-9 col-md-9">
		<input class="col-xs-12 col-sm-6 col-md-6" name="subject" id="subject" type="text" value="" onkeydown="checkLen()" />
		<span class="msg">&nbsp;&nbsp;(请输入20个字以内的名称)</span>
	</div>
</div>
<div class="col-xs-12 col-sm-12 col-md-12 form-group">
	<label class="control-label col-xs-12 col-sm-3 col-md-3">筛选器说明</label>
	<div class="col-xs-12 col-sm-9 col-md-9">
		<textarea class="col-xs-12 col-sm-12 col-md-12" name="description" rows="5"></textarea>
	</div>
</div>
<div class="col-xs-12 col-sm-12 col-md-12 form-group text-center">
	<input class="btn btn-sm btn-primary" type="submit" value="保存" />
</div>
</form>