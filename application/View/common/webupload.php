<link rel="stylesheet" type="text/css" href="<?=site_path('js/webuploader/webuploader.css')?>">
<script type="text/javascript" src="<?=site_path('js/webuploader/webuploader.nolog.min.js')?>"></script>
<script type="text/javascript" src="<?=site_path('js/jquery.cookie.min.js')?>"></script>
<div class="clearfix"></div>
<div class="center">
<div id="uploader" class="wu-example">
    <!--用来存放文件信息-->
    <div class="btns">
        <div id="picker">选择文件</div>
        <!-- <button id="ctlBtn" class="btn btn-default btn-sm">开始上传</button> -->
    </div>
    <div id="thelist" class="uploader-list">
        <div id="" class="item">
            <h4 class="info"></h4>
        </div>
    </div>
</div>
    </div>
<script language="javascript" type="text/javascript">
$(function(){
});
var data;
// $('#uploader').closest('form').submit(function(){return false;});
var uploader = WebUploader.create({
    swf: "<?=site_path('js/webuploader/Uploader.swf')?>",
    server: "<?=site_url('common/webupload')?>",
    pick: '#picker',
    auto:true,
    fileVal:'file[]',
    fileSingleSizeLimit:'<?=$upload_size?>',
    fileSizeLimit:'<?=$upload_size?>',
    formData:{table:'<?=$table?>',item_id:'<?=$item_id?>',item_type:'<?=$item_type?>'},
    accept:{
        title: 'file',
        extensions: '<?=$upload_type?>',
        mimeTypes: ''
    }
}).on('ready', function () {
    $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
});

function displayProp(obj){
        var names="";
        for(var name in obj){
           names+=name+": "+obj[name]+", ";
        }
        alert(names);
    }
uploader.on('fileQueued', function( file ) {
    $('#thelist').append( '<div id="' + file.id + '" class="item">' +
        '<h4 class="info">' + file.name + '</h4>' +
        '<p class="state">等待上传...</p>' +
    '</div>' );
});

uploader.on('uploadProgress', function( file, percentage ) {
    var $li = $( '#'+file.id ),
        $percent = $li.find('.progress .progress-bar');

    // 避免重复创建
    if ( !$percent.length ) {
        $percent = $('<div class="progress progress-striped active">' +
          '<div class="progress-bar" role="progressbar" style="width: 0%">' +
          '</div>' +
        '</div>').appendTo( $li ).find('.progress-bar');
    }

    $li.find('p.state').text('上传中');

    $percent.css( 'width', percentage * 100 + '%' );
});
uploader.on( 'uploadSuccess', function( file ) {
    $( '#'+file.id ).find('p.state').text('已上传');
});

uploader.on( 'uploadError', function( file ,reason) {
    console.log(reason);
    $( '#'+file.id ).find('p.state').text('上传出错');
});

uploader.on( 'uploadComplete', function( file ) {
    $( '#'+file.id ).find('.progress').fadeOut();
});
uploader.on( 'uploadAccept', function( file, response ) {
    if ( response.hasError ) {
        // 通过return false来告诉组件，此文件上传有错。
        return false;
    }
    data = response._raw;
    //data = eval("("+data+")");
    $.cookie('uploaddata', data,{path:'/'});
    // for(var i=0; i<data.length;i++)
    //     for(var key in data[i])
    //         alert(data[i][key]);
    return true;
});
uploader.on('error', function(handler) {
  console.log(handler);
});

</script>