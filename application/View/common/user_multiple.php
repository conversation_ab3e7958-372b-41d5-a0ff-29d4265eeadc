<div id="app" class="width:100%;padding:20px;">
    <div class="row">
        <div class="col-lg-12">
            <div style="width:100%;padding:5px 0;">已选择人员名单：</div>
            <el-card body-style="padding:15x;">
                <el-tag 
                type="info" 
                v-for="(item,index) in chose" 
                :key="index" closable 
                style="margin:0 5px;"
                @close="()=>handleClose(item)"
                >
                {{ item.split("?")[1] }}
                </el-tag>
            </el-card>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div style="width:100%;padding:15px 0;">选择单位/部门：</div>
            <el-tree
                style="height:590px;overflow-y:scroll;"
                :data="trees"
                :props="defaultProps"
                @node-click="handleNodeClick"
            />
        </div>
        <div class="col-lg-8">
            <div style="width:100%;padding:15px 0;">选择人员：</div>
            <div style="width:100%;">
                <el-form :model="formInline" size="small">
                    <el-row style="justify-content: space-between;">
                        <el-col :span=6 style="width: 30%;">
                            <el-form-item label="姓名">
                                <el-input v-model="formInline.personname" placeholder="搜索姓名" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span=6 style="width: 30%;">
                            <el-form-item label="手机号">
                                <el-input v-model="formInline.user_mobile" placeholder="搜索手机号" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col :span=6 style="width: 30%;">
                            <el-form-item label="单位/部门">
                                <el-input v-model="formInline.corporation_name" placeholder="搜索单位/部门" clearable />
                            </el-form-item>
                        </el-col>
                        <el-col span=6 style="width: 10%;">
                            <el-form-item>
                                <el-button type="primary" @click="onSearch">搜索</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    
                    
                    
                </el-form>
            </div>
            <el-table
                ref="multipleTableRef"
                :data="tableData"
                style="width: 100%;padding:15px 0;"
                @selection-change="handleSelectionChange"
                :cell-style="{ textAlign: 'center' }"
                :header-cell-style="{ 'text-align': 'center' }"
            >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column property="personname" label="姓名"></el-table-column>
                <el-table-column property="user_sex" label="性别" width="80"></el-table-column>
                <el-table-column property="user_birthday" label="出生年月" width="120"></el-table-column>
                <el-table-column property="user_mobile" label="手机号" width="160"></el-table-column>
                <el-table-column property="corporation_name" label="单位/部门" width="210"></el-table-column>
            </el-table>
            <div style="width:100%;height:50px;display:flex;justify-content:space-between;align-items:center;">
                <el-pagination
                    small
                    background
                    layout="prev, pager, next"
                    :total="total"
                    @current-change="handleCurrentChange"
                ></el-pagination>
                <el-button-group size="small">
                    <el-button type="primary" @click="onSubmit">提交</el-button>
                    <el-button type="info" @click="onClear">清空</el-button>
                </el-button-group>
            </div>
        </div>
    </div>
</div>




<script>
const {
    createApp,
    ref,
    reactive,
    onMounted
} = Vue
const {
    ElMessage,
    ElMessageBox
} = ElementPlus
createApp({
    mounted(){
        http('get', '<?=site_url('admin/article/company')?>', {}).then((res)=>{
            this.trees = res.data.data;
        });
        this.getTableData();
        const parentChose = parent.document.getElementById("chose").value;
        if(parentChose){
            this.chose = parentChose.split(",");
        }
    },
    data(){
        return {
            defaultProps :{
                children: 'children',
                label: 'label',
            },
            trees:[],
            tableData:[],
            total:0,
            page:1,
            cid:"",
            chose:[],//id=>title

            formInline:{
                personname:"",
                user_mobile:"",
                user_idcard:"",
            }
        }
    },
    methods:{
        getTableData(){
            http('get', '<?=site_url('admin/article/declare')?>', {id:this.cid,page:this.page, ...this.formInline}).then((res)=>{
                this.tableData = res.data.data;
                this.total = res.data.total;
                this.toggleSelection(this.tableData);
            });
        },
        handleNodeClick(node){
            this.cid=node.id;
            this.getTableData();
        },
        handleCurrentChange(num){
            this.page=num;
            this.getTableData();
        },
        handleSelectionChange(selections){
            if(selections.length>0){
                for(const selection of selections){
                    if(!JSON.stringify(this.chose).includes(selection.user_id))this.chose.push(selection.user_id+"?"+selection.personname);
                }
            }else{
                const tableDataStr=JSON.stringify(this.tableData);
                const temp = [];
                for(const ele of this.chose){
                    let flag = true;
                    for(const el of this.tableData){
                        if(ele.includes(el.user_id))flag=false;
                    }
                    if(flag)temp.push(ele);
                }
                this.chose = temp;
            }
        },
        toggleSelection(rows){
            if (rows) {
                rows.forEach((row) => {
                    if (JSON.stringify(this.chose).includes(row.user_id)) {
                        this.$nextTick(()=>{
                            this.$refs['multipleTableRef'].toggleRowSelection(row, true);
                        });
                    } else {
                        this.$nextTick(()=>{
                            this.$refs['multipleTableRef'].toggleRowSelection(row, false);
                        });
                    }
                });
            }
        },
        handleClose(e){
            const temp = [];
            for(const ele of this.chose){
                if(ele != e)temp.push(ele);
            }
            this.chose = temp;
            this.toggleSelection(this.tableData);
        },
        onSearch(){
            this.page=1;
            this.getTableData();
        },
        onClear(){
            this.chose=[];
            this.toggleSelection(this.tableData);
        },
        onSubmit(){
            parent.todo(this.chose);
        }
    }
}).use(ElementPlus).mount('#app')
</script>
