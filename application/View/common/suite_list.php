<div class="main">
  <div class="search">
      <link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
      <script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
      <script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
      <form action="" method="post" name="search" id="search">
          <input type="hidden" name="callback" value="<?=$callback?>">
          <table width="100%" align="center">
              <tbody id="show_search">
              <tr>
                  <td>关键词：
                      <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                      <label><input name="field" type="radio" value="subject" checked="checked" /> 产品名称</label>
                      <label>
                          <button type="button" class="btn" id="daterange-btn" style="border: 1px solid #d4dcec">
                              <i class="fa fa-calendar"></i>
                              <span>时间</span>
                              <i class="fa fa-caret-down"></i>
                          </button>
                          <input type="hidden" name="start_at" value="" id="start_at">
                          <input type="hidden" name="end_at" value="" id="end_at">
                      </label>
                      <?=btn('button','搜索','submit','find')?>
                  </td>
              </tr>
              <tr>
                  <td>
                      <select class="form-control w-auto custom-control-inline" name="company_id">
                          <option value="">=牵头单位=</option>
                          <?=getSelectFromArray(getSecondCompanys(),input::getInput("mix.company_id"),false)?>
                      </select>
                      <select name="type" id="type" class="form-control w-auto custom-control-inline">
                          <option value="">=类型=</option>
                          <?=getSelectFromArray(['牵头','参与'],input::getMix("type"))?>
                      </select>
                      <select name="level" id="level" class="form-control w-auto custom-control-inline">
                          <option value="">=级别=</option>
                          <?=getSelectFromArray(['国家级','省级'],input::getMix("level"))?>
                      </select>
                      <select name="statement" id="statement" class="form-control w-auto custom-control-inline">
                          <option value="">=审核状态=</option>
                          <?=patentSelect()?>
                      </select>
                  </td>
              </tr>
              </tbody>
          </table>
      </form>
  </div>
  <div class="box">
    <table class="table table-striped table-hover datatable col-md-12">
    <thead>
      <tr>
        <th><?=getColumnStr('产品名称','subject')?></th>
        <th><?=getColumnStr('牵头单位','company_id')?></th>
        <th><?=getColumnStr('类型','type')?></th>
        <th><?=getColumnStr('发证部门','cer_department')?></th>
        <th><?=getColumnStr('发证日期','cer_date')?></th>
        <th><?=getColumnStr('状态','statement')?></th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <?php while($suite = $pager->getObject()):?>
      <tr>
        <td><a href="<?=site_url("scientific/suite/user/show/id/".$suite->getSuiteId())?>"><?=$suite->getSubject()?></a></td>
        <td><?=$suite->getCompanyName()?></td>
        <td><?=$suite->getType()?></td>
        <td><?=$suite->getCerDepartment()?></td>
        <td><?=$suite->getCerDate()?></td>
        <td><?=$suite->getState()?></td>
        <td>
            <?=Button::setEvent("parent.{$callback}({suite_id:'".$suite->getSuiteId()."',itemid:'".$itemid."',subject:'".$suite->getSubject()."'});")->setIcon('check')->button('选取')?>
      </tr>
      <?php endwhile; ?>
    </tbody>
    <tfoot>
      <tr>
        <td colspan="9"><?=$pager->navbar(5)?></td>
      </tr>
    </tfoot>
    </table>
  </div>
</div>
<script>
    function init() {
        //定义locale汉化插件
        var locale = {
            "format": 'YYYY-MM-DD',
            "separator": " -222 ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间'",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        //初始化显示当前时间
        var start_at = "<?=input::getMix('start_at')?>";
        var end_at = "<?=input::getMix('end_at')?>";
        $("#start_at").val(start_at);
        $("#end_at").val(end_at);
        if(start_at && end_at){
            $('#daterange-btn span').html(start_at + ' - ' + end_at);
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    ranges: {
                        '本月': [moment().startOf('month'), moment().endOf('month')],
                        '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                        '本年': [moment().startOf('year'), moment().endOf('year')],
                        '上年': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                        '最近一月': [moment().subtract(1, 'month'), moment()],
                        '最近一年': [moment().subtract(1, 'year'), moment()],
                    },
                    startDate: start_at,
                    endDate: end_at
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }else{
            $('#daterange-btn span').html('发证日期');
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    ranges: {
                        '本月': [moment().startOf('month'), moment().endOf('month')],
                        '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                        '本年': [moment().startOf('year'), moment().endOf('year')],
                        '上年': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                        '最近一月': [moment().subtract(29, 'days'), moment()],
                        '最近一年': [moment().subtract(1, 'year'), moment()],
                    },
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }
    };
    $(document).ready(function() {
        init();
    });
</script>