<div class="row">
  <div class="col-xs-12 col-sm-12 col-md-12 page-header">
  <div class="btn-group btn-group-sm" role="group">
  <a href="<?=site_url("cms/auth/edit")?>" class="btn btn-sm btn-info" role="button"><i class="glyphicon glyphicon-plus"></i>新增权限</a>
  <p style="clear:both"></p>
  </div>
  </div>
</div>

<div class="row">
  <div class="col-xs-12 col-sm-12 col-md-12">
  <form id="validateForm" name="validateForm" class="form-horizontal" method="post" action="">
<div class="panel panel-default">
  <div class="panel-heading"><i class="fa fa-list"></i> 权限列表</div>
  <table width="100%" cellpadding="3" cellspacing="1" class="table table-striped table-bordered table-hover">
  <thead>
  <tr>
    <th width="5" class="text-center">/</th>
    <th width="" class="text-center">权限名</th>
    <th width="250" class="text-center">操作</th>
  </tr>
  </thead>
  <tbody>
  <?php ?>
  <tr>
    <td align="center"><input type="checkbox" name="select_ids[]" value=""></td>
    <td align="left"><a href="<?=site_url("cms/category/edit/id/")?>" class="" role="button"></a>
    </td>
    <td align="left">
      <?php if(Input::getInput('session.userlevel')==1): ?>
      <a href="<?=site_url("cms/category/edit/id/")?>" class="label label-primary label-lg" role="button"><i class="fa fa-edit"></i>修改</a>
      <?php endif; ?>
    </td>
  </tr>
  <?php ?>
  </tbody>
  <tfoot>
  <tr>
    <td colspan="5">
      <?php if(Input::getInput('session.userlevel')==1): ?>
      <button type="button" class="btn btn-danger" onclick="$('#validateForm').attr('action','<?=site_url("cms/category/delete")?>');loading();$('#validateForm').submit();">删除</button>
      <?php endif; ?>
    </td>
  </tr>
  </tfoot>
</table>
</div>
  </form>
  </div>
</div>

<div class="clearfix"></div>