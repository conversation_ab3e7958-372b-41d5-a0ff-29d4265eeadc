<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                任务书综合搜索
            </h3>
            <div class="block-options">
                <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
                <div class="search">
                    <?php include('search_part.php');?>
                </div>
                <div class="box">
                    <div class="no-padding no-margin panel panel-default" >
                        <form id="validateForm" name="validateForm" method="post" action="">
                            <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                                <thead>
                                <tr>
                                    <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th><?=getColumnStr('立项编号','radicate_id')?></th>
                                    <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                    <th><?=getColumnStr('负责人','user_id')?></th>
                                    <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                                    <th lass="text-center"><?=getColumnStr('总经费','total_money')?></th>
                                    <th class="text-center"><?=getColumnStr('立项年度','radicate_year')?></th>
                                    <th class="text-center"><?=getColumnStr('状态','state_for_plan_book')?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($project = $pager->getObject()):?>
                                    <tr>
                                        <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                        <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
                                        <td style="width:50px;"><?=$project->getRadicateId()?></td>
                                        <td><?=$project->getMark()?><?=link_to("declare/task/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
                                        <td><?=$project->getUserName()?></td>
                                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                        <td align="center"><?=$project->getTotalMoney()?> 万元</td>
                                        <td align="center"><?=$project->getRadicateYear()?></td>
                                        <td align="center"><?=$project->getStateForTask()?></td>
                                    </tr>
                                    <tr id="show_<?=$project->getId()?>" style="display:none;">
                                        <td colspan="14">
                                            <?php include('more_part.php') ?>
                                        </td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                <tr>
                                    <td colspan="14" align="right"><span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
                                </tr>
                                </tfoot>
                            </table>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

