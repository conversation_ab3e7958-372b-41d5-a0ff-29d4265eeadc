<div class="main">
  <?php include('navtabs.php'); ?>
  <div class="box">
    <h3 class="header">【专家咨询费】</h3>
    <div class="alert alert-info" role="alert">
      <h4>填写说明</h4>
      <p style="text-indent:2em;">专家咨询费是指在项目研究开发过程中支付给临时聘请的咨询专家的费用。专家咨询活动的组织形式主要有会议、通讯两种形式。以会议形式组织的咨询，是指通过召开专家参加的会议，或者查看实地、实物、原始业务资料等方式征询专家的意见和建议；以通讯形式组织的咨询，是指通过信函、邮件等方式征询专家的意见和建议。编制专家咨询费预算应注意：</p>
      <ul>
        <li>1. 咨询专家是指承担单位在项目实施过程中，临时聘请为项目研发活动提供咨询意见的专业人员。包括高级专业技术职称人员和其他专业人员。</li>
        <li>2. 专家咨询费的发放应当按照国家有关规定由单位代扣代缴个人所得税。编列专家咨询费预算时，可将代扣代缴的个人所得税编列在内。</li>
        <li>3. 访问学者和项目聘用的研究人员应在劳务费中编列，不应在本科目中编列。</li>
        <li>4. 专家咨询费不得支付给参与项目研究及其管理的相关人员。</li>
      </ul>
    </div>

    <?=$project->getBudgetBook()->getNavbar();?>
    <?php $consultancy = $project->getBudgetBook()->getConsultancy();?>
    <?php $items = $project->getBudgetBook()->getConsultancys();?>
    <form id="validateForm" name="validateForm" method="post" action="">
      <div class="alert alert-warning">
      合计：专项经费
        <b><?=$consultancy['zx']?:0.00?></b> 万元，自筹经费
        <b><?=$consultancy['zc']?:0.00?></b> 万元，总经费
        <b><?=$consultancy['hj']?:0.00?></b> 万元
      </div>

      <table class="table table-hover">
        <thead>
          <tr>
            <th class="text-center" width="11%">咨询形式</th>
            <th class="text-center" width="51%">与研究任务的相关性</th>
            <th class="text-center" width="8%">预计总天数</th>
            <th class="text-center" width="8%">预计金额</th>
            <th class="text-center" width="9%">资金来源</th>
            <th class="text-center" >/</th>
          </tr>
        </thead>
        <tbody>
          <?php if($items->getTotal()==0): ?>
            <tr>
              <td colspan="6" class="text-center">- 列表没有数据 -</td>
            </tr>
          <?php endif; ?>
          <?php while($item = $items->getObject()):?>
            <tr>
              <td align="center"><?=$item->getSubject()?></td>
              <td><?=$item->getContent()?></td>
              <td align="center"><?=$item->getDay()?></td>
              <td align="center" data-source="<?=$item->getSource()?>" data-money="<?=$item->getMoney()?>"><?=$item->getMoney()?> 万元 </td>
              <td align="center"><?=$item->getSource()?></td>
              <td align="center">
                <button type="button" class="btn btn-info btn-sm" onclick="return showWindow('编辑专家咨询费','<?=site_url('apply/outlay/consultancy/edit/id/'.$item->getId())?>',{area:['960px','650px']})">编辑</button>
                <a href="<?=site_url('apply/outlay/consultancy/delete/id/'.$item->getId())?>" class="btn btn-danger btn-sm" onclick="return confirm('确定要删除？');">删除</a>
              </td>
            </tr>
          <?php endwhile;?>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="6">
              <button type="button" class="btn btn-warning" onclick="return showWindow('新增专家咨询费','<?=site_url('apply/outlay/consultancy/edit')?>',{area:['960px','650px']})">增加记录</button>
            </td>
          </tr>
        </tfoot>
      </table>
    </form>
  </div>
</div>
