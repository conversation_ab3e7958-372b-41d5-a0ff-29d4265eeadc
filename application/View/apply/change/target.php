<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            变更申请向导
        </h2>
        <div class="content-options">
            <?php
                if(!$change->isNew()):
            ?>
            <?=Button::setUrl(site_url("user/change/doSubmit/id/".$change->getId()))->setIcon('submit')->link('上报')?>
            <?=Button::setUrl(site_url("apply/change/download/id/".$change->getId()))->setIcon('print')->link('打印')?>
            <?php endif;?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <li role="presentation" class="nav-item"><a class="nav-link" href="<?=site_url("apply/change/edit/id/".$change->getId())?>" aria-controls="base" role="tab">变更申请表</a></li>
                    <?php
                        if(in_array('total_money',$change->getChangeType())):
                    ?>
                    <li role="presentation" class="nav-item"><a class="nav-link" href="<?=site_url("apply/change/money/id/".$change->getId())?>" aria-controls="money" role="tab">经费变更明细</a></li>
                    <?php endif;?>
                    <?php
                    if(in_array('target',$change->getChangeType())):
                        ?>
                        <li role="presentation" class="nav-item"><a class="nav-link active" href="<?=site_url("apply/change/target/id/".$change->getId())?>" aria-controls="target" role="tab">阶段目标变更明细</a></li>
                    <?php endif;?>
                    <?php
                    if(in_array('member',$change->getChangeType())):
                        ?>
                        <li role="presentation" class="nav-item"><a class="nav-link" href="<?=site_url("apply/change/member/id/".$change->getId())?>" aria-controls="member" role="tab">项目成员变更明细</a></li>
                    <?php endif;?>
                    <li role="presentation" class="nav-item"><a class="nav-link" href="<?=site_url("apply/change/upload/id/".$change->getId())?>" aria-controls="upload" role="tab">相关附件</a></li>
                </ul>
                <div class="block-content">
                    <div class="page-body">
                        <div class="tab-content">
                            <div role="tabpanel" class="tab-pane active">
                                <form class="form-horizontal" id="form_1" action="" method="post">
                                    <table width="100%" class="table table-hover">
                                        <thead>
                                        <tr>
                                            <td width="15%" align="center">开始时间</td>
                                            <td width="15%" align="center">结束时间</td>
                                            <td width="60%" align="center">阶段目标</td>
                                            <?php if($writeModel=='open'): ?><td width="10%" align="center">操作</td> <?php endif;?>
                                        </tr>
                                        </thead>
                                        <tbody id="paper-content">
                                        <?php
                                        if($writeModel=='open'):
                                            $schedules = $schedules->toArray();
                                            $after = $change->getAfterTarget();
                                            $count = count($schedules)?:1;
                                            for($i=0,$n=$count;$i<$n;$i++):?>
                                                <tr>
                                                    <td>
                                                        <input type="hidden" name="before[start_at][]" value="<?=$schedules[$i]['start_at']?>" />
                                                        <input type="text" data-com="date" data-format="yyyy-mm-dd" data-icon="none" name="after[start_at][]" value="<?=$schedules[$i]['start_at']?>" class="required form-control" />

                                                    </td>
                                                    <td><input type="text" data-com="date" data-format="yyyy-mm-dd" data-icon="none" name="schedules[end_at][]" value="<?=$schedules[$i]['end_at']?>" class="required form-control" /></td>
                                                    <td><input name="schedules[content][]" type="text" class="required form-control" style="width:99%" value="<?=$schedules[$i]['content']?>" /></td>
                                                    <td class="text-center">
                                                        <span class="ace-icon badge badge-info addcolumn"><i title="加一行" class="ace-icon fa fa-plus"></i></span>
                                                        <span class="ace-icon badge badge-info delcolumn"><i title="减一行" class="ace-icon fa fa-minus"></i></span>
                                                    </td>
                                                </tr>
                                            <?php endfor;
                                        else:
                                            ?>
                                            <?php
                                            $after = $change->getAfterTarget();
                                            for($i=0,$n=count($schedules['start_at']);$i<$n;$i++):
                                            ?>
                                            <tr>
                                                <td>
                                                    <input class="form-control" type="text" name="after[start_at][]" value="<?=$after['start_at'][$i]?:$schedules['start_at'][$i]?>" readonly="readonly" class="required" />
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="after[end_at][]" value="<?=$after['end_at'][$i]?:$schedules['end_at'][$i]?>" readonly="readonly" class="required" />
                                                </td>
                                                <td>
                                                    <input class="form-control" name="after[content][]" type="text" class="required" style="width:99%" value="<?=$after['content'][$i]?:$schedules['content'][$i]?>" />
                                                </td>
                                            </tr>
                                        <?php endfor;?>
                                        <?php
                                        endif;
                                        ?>
                                        </tbody>
                                    </table>
                                    <?php
                                        while($scheduleBefore = $scheduleBefores->getObject()):
                                    ?>
                                        <input type="hidden" name="before[start_at][]" value="<?=$scheduleBefore->getStartAt();?>" />
                                        <input type="hidden" name="before[end_at][]" value="<?=$scheduleBefore->getEndAt();?>" />
                                        <input type="hidden" name="before[content][]" value="<?=$scheduleBefore->getContent();?>" />
                                    <?php
                                        endwhile;
                                    ?>
                                    <div class="form-group row">
                                        <label class="control-label text-right col-xs-12 col-sm-3 col-md-3"></label>
                                        <div class="col-xs-12 col-sm-6 col-md-9">
                                            <input name="id" type="hidden" value="<?=$change->getId()?>" />
                                            <?=btn('button','保存资料','submit','save')?>
                                        </div>
                                    </div>
                                </form>
                                <p style="clear:both"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="<?=site_url('js/jquery.tools.js')?>"></script>
<script src="<?=site_url('js/jquery.coms.js')?>"></script>
