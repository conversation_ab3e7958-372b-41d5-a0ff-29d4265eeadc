<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>无标题文档</title>
<style type="text/css">
@charset "utf-8";
*{ word-wrap:break-word; outline:none; margin:0px auto; padding:0px 0px; }
body { margin: 0px 0px; padding:0px 0px; font-size:12px;}
li { list-style:none;}
/*链接样式*/
a{text-decoration: none;}   /* 链接无下划线,有为underline */ 
a:link {color: #454545;}    /* 未访问的链接 */
a:visited {color: #454545;} /* 已访问的链接 */
a:hover{color: #ff0000;}   /* 鼠标在链接上 */ 
a:active {color: #454545;}  /* 点击激活链接 */
.header,.warp,.footer { width:1000px; }
.header {height:135px; background:url(../../../images/first/top.jpg) no-repeat;}
.left { width:220px; float:left;}
.left li { line-height:20px; background:url(../../../images/menu2.gif) no-repeat; padding:3px 3px 3px 5px; text-align:center;}
.right {}
.warp { background: url(file:///C|/Users/<USER>/Desktop/bg.jpg) repeat-y;}
.footer { background-color:#FFF; height:60px;}
h2 { border-bottom:solid #F00 2px; padding:3px;}
.new ()
.new li { float:left; padding:1px; margin:2px; width:24.9%;border:1px solid #CCC;}
.new img { width:200px; height:100px;}
.footer {clear:both; border-top: solid #0CF 5px;}
</style>
</head>

<body>
<div class="header"></div>

<div class="navbar">
<menu>
<li>xxx</li>
</menu></div>

<div class="warp">
  <div class="left">
    <ul>
      <h2>内容导航 </h2>
      <li>xxx</li>
      <li>xxx</li>
      <li>xxx</li>
      <li>xxx</li>
    </ul>
  </div>
  <div class="right">
    <?=$inc_body?>
  </div>
</div>

<div class="footer"></div>
</body>
</html>
