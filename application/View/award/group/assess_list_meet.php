<script language="javascript" type="text/javascript">
  var more = {
    toggle:function(id){
      $('#show_'+id).toggle();
      if(!$('#show_'+id).is(":hidden")){
        $('#show_'+id).children().html('数据加载中...');
        $('#show_'+id).children().load('<?=site_url("award/assigner/moreMeet/id/")?>'+'/'+id);
      }
    } 
  }
</script>
<div class="main">
  <div class="tools">
    <?=btn('back')?>
    <?=btn('link','项目分组',site_url("award/group/wait_list_meet"),'group')?>
    <p style="clear:both;"></p>
  </div>
  <div class="search">
    <table width="100%" align="center">
      <tbody id="show_search">
        <form action="" method="post" name="search" id="search">
          <tr>
            <td><label>分组编号：
              <input name="subject" type="text" id="subject" value="<?=input::getInput("mix.subject")?>" />
            </label>
            <label>分组备注：
              <input name="note" type="text" id="note" value="<?=input::getInput("mix.note")?>" />
            </label>
            <select name="declare_year" id="declare_year" class="required">
              <option value="">=评审年度=</option>
              <?=getYearList(input::getInput("mix.declare_year"))?>
            </select>
            <?=btn('button','搜索','submit','find')?></td>
          </tr>
        </form>
      </tbody>
      
    </table>
  </div>
  <?php if($pager->getTotal()):?>
    <div class="box">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
        <tr>
          <th width="30">详</th>
          <th><?=getColumnStr('分组编号','group_subject')?></th>
          <th><?=getColumnStr('项目个数','project_count')?></th>
          <th><?=getColumnStr('分组时间','updated_at')?></th>
          <th><?=getColumnStr('分组备注','group_note')?></th>
          <th width="300">操作</th>
        </tr>
        <?php while($group = $pager->getObject()):?>
          <tr>
            <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="more.toggle('<?=$group->getId()?>')">+</label></td>
            <td align="center"><?=link_to("award/group/show/id/".$group->getId(),$group->getGroupSubject())?></td>
            <td align="center"><?=$group->getProjectCount()?></td>
            <td align="center"><?=$group->getCreatedAt()?></td>
            <td align="center"><?=$group->getGroupNote()?></td>
            <td align="center">
              <?=btn('window','编辑',site_url("award/group/doEdit/id/".$group->getId()),'edit')?>
              <!-- <?=btn('window','通知',site_url("award/group/sendMessage/id/".$group->getId()),'send')?> -->
              <?php if(isSuper()):?> 
                <a class="btn btn-info btn-sm" href="<?=site_url('award/assign/doAssignExpertForGroup/id/'.$group->getId())?>"><i class="glyphicon glyphicon-plus"></i>分配</a>
                <?=btn('window','记录',site_url("admin/history/index/type/group/id/".$group->getId()),'message')?>
              <?php endif;?>
            </td>
          </tr>
          <tr id="show_<?=$group->getId()?>" style="display:none;">
            <td colspan="6"></td>
          </tr>
        <?php endwhile;?>
        <tr>
          <td colspan="6" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </div>
  <?php else:?>
    <div class="box">
      <p class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</p>
    </div>
  <?php endif;?>
</div>
