<div class="main">
  <div class="tools">
    <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info btn-sm select">全部选中</a>
    <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="find">高级搜索</a> -->
    <a href="<?=site_url("export/export/index/table/awards")?>" class="btn btn-danger btn-sm export" role="button">导出数据</a>
    <p style="clear:both;"></p>
  </div>
  <div class="search">
    <?php include_once('search_part.php') ?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/partition/doPartitionByProject")?>">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
        <tr>
          <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
          <th width="30">详</th>
          <th><?=getColumnStr('申报编号','accept_id')?></th>
          <th width="25%"><?=getColumnStr('项目名称/候选人','subject')?></th>
          <th><?=getColumnStr('奖励类别','type_name')?></th>
          <th><?=getColumnStr('项目负责人','user_name')?></th>
          <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
          <th><?=getColumnStr('评审组','group_name')?></th>
          <th><?=getColumnStr('评审得分','score')?></th>
        </tr>
        <?php if($pager->getTotal()):?>
          <?php while($project = $pager->getObject()):?>
            <tr>
              <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
              <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
              <td align="center"><?=$project->getAcceptId()?></td>
              <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
              <td align="center"><?=$project->getTypeName()?></td>
              <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
              <td align="center"><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
              <td align="center"><?=$project->getGroupName()?></td>
              <td align="center"><?=$project->getScore()?></td>
            </tr>
            <tr id="show_<?=$project->getId()?>" style="display:none;">
              <td colspan="16">
                <?php include('more_part.php') ?>
              </td>
            </tr>
          <?php endwhile;?>
          <tr>
            <td colspan="16" align="right">
              &nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
            </tr>
          <?php else:?>
            <tr>
              <td colspan="16" class="note">暂无符合条件的项目，请选择上面搜索条件重新搜索定位。</td>
            </tr>
          <?php endif;?>
        </table>
      </form>
    </div>
  </div>
