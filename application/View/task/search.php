<div class="">
  <?=view::part('task/navbar')?>
  <div class="search">
    <form action="" method="post" name="search" id="search">
      <table width="100%" align="center">
        <tbody id="show_search">
        <tr>
          <td><label>搜索关键词
              <input name="search" type="text" id="search" value="<?=$search?>">
            </label>
            <input type="submit" name="button" id="button" value="搜索"></td>
        </tr>
        </tbody>
      </table>
    </form>
    <ul class="nav nav-pills" style="margin: 0">
      <li><a href="<?=site_url('task/my_task')?>">我发布的</a></li>
      <li><a href="<?=site_url('task/no_finish')?>">我未完成</a></li>
      <li><a href="<?=site_url('task/dept')?>">部门任务</a></li>
      <li><a href="<?=site_url('task/finished')?>">我已完成</a></li>
      <li><a href="<?=site_url('task/my_assign')?>">我转交的</a></li>
    </ul>
  </div>
  <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
    <div class="no-padding no-margin panel panel-default" >
      <div class="panel-heading">
        <i class="ace-icon fa fa-list"></i> 任务综合查询列表
        <div class="pull-right hidden-480 tools">
          <div class="dropdown dropdown-hover inline">
            <a href="#" style="">排序<i class="ace-icon fa fa-caret-down"></i></a>
            <ul class="dropdown-menu dropdown-menu-right no-margin">
              <li><?=getColumnStr('默认排序','id')?></li>
              <li><?=getColumnStr('发起时间','created_at')?></li>
              <li><?=getColumnStr('完成时间','finished_at')?></li>
            </ul>
          </div>
          |
          <div class="inline fontchange" title="字号大小调节">
            <a class="lighter" href="#" size="16">大</a>
          </div>
          <div class="inline fontchange active" title="字号大小调节">
            <a class="lighter" href="#" size="13">小</a>
          </div>
        </div>
      </div>
      <table align="center" cellpadding="3" cellspacing="1" class="table">
        <thead>
        <tr>
          <th class="hidden-480 text-center"><input name="selectAll" type="checkbox" id="selectAll"/></th>
          <th class="text-center">编号</th>
          <th class="hidden-480 text-center">发起人</th>
          <th class="hidden-480 text-center">标题</th>
          <th class="hidden-480 text-center">指派给</th>
          <th class="hidden-480 text-center">发起时间</th>
          <th class="hidden-480 text-center">期望完成时间</th>
          <th class="hidden-480 text-center">完成时间</th>
          <th class="hidden-480 text-center">状态</th>
          <th class="hidden-480 text-center">操作</th>
        </tr>
        </thead>
        <tbody>
        <?php while($task = $tasks->getObject()):?>
          <tr>
            <td align="center" class="hidden-480"><input name="select_id[]" type="checkbox" value="<?=$task->getId()?>" /></td>
            <td align="center" class="hidden-480"><?=$task->getTaskNo()?></td>
            <td align="center" class="hidden-480"><?=$task->getUserName()?></td>
            <td align="center" class="hidden-480 text-left">
              <a href="<?=site_url('task/task_show/id/'.$task->getId())?>" ><?=$task->getSubject()?></a>&nbsp;
            </td>
            <td align="center" class="hidden-480"><?=$task->getExecutor(20)?></td>
            <td align="center" class="hidden-480"><?=$task->getCreatedAt()?></td>
            <td align="center" class="hidden-480"><?=$task->getExpectedAt('Y-m-d')?></td>
            <td align="center" class="hidden-480"><?=$task->getFinishedAt()?></td>
            <td align="center" class="hidden-480"><?=$task->getStatement()?></td>
            <td align="center" class="hidden-480">
                <a href="<?=site_url('task/task_show/id/'.$task->getId())?>" class="btn btn-info doit" role="button">查看</a>
            </td>
          </tr>
        <?php endwhile;?>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="8" align="right">&nbsp;<?=$tasks->fromTo().$tasks->navbar(10)?></td>
          </tr>
        </tfoot>
      </table>
    </div>
  </form>
</div>