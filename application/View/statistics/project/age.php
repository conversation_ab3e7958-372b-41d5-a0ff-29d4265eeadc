<script src="https://cdn.bootcss.com/echarts/3.8.5/echarts.min.js"></script>
<div class="main">
    <div class="btn-group btn-group-sm" role="group"> <a href="javascript:history.back();" class="btn btn-info" role="button"><i class="ace-icon fa fa-arrow-left"></i>返回</a><p style="clear:both"></p></div>
    <div class="search">
        <?php include("search_part.php");?>
    </div>
    <div class="box">
        <?php include('echart.php')?>
    <script type="text/javascript">
        // 显示标题，图例和空的坐标轴
        myChart.setOption({
            title: {
                text: '项目负责人年龄分布'
            },
            tooltip: {},
            legend: {
//                data:['20-29岁','30-39岁']
            },
            xAxis: {
                data: []
            },
            yAxis: {},
            series: [{
                name: '申报数量',
                type: 'bar',
                data: []
            }],
            toolbox: {
                show: true,
                feature: {
                    saveAsImage: {
                        show:true,
                        excludeComponents :['toolbox'],
                        pixelRatio: 2
                    }
                }
            }
        });
        function getDatas() {
            var year = $("#declare_year").val();
            var cat_id = $("#cat_id").val();
            // 异步加载数据
            $.getJSON('<?=site_url('statistics/project/age')?>/declare_year/'+year+'/cat_id/'+cat_id).done(function (data) {
                myChart.hideLoading();
                // 填入数据
                myChart.setOption({
                    xAxis: {
                        data: data.xAxis,
                        axisLabel: {
                            show: true
                        }
                    },
                    series: [{
                        // 根据名字对应到相应的系列
                        name: '申报数量',
                        data: data.series.data,
                        itemStyle:
                            {
                                normal: {
                                    label: {
                                        show: true,
                                        formatter: '{c} 项' // 这里是数据展示的时候显示的数据
                                    },
                                    //每个柱子的颜色即为colorList数组里的每一项，如果柱子数目多于colorList的长度，则柱子颜色循环使用该数组
                                    color: function (params) {
                                        var colorList = ['#c0232a', '#9bca62', '#e87c24', '#28727b'];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            }
                    }]
                });
            });
        }

    </script>
    </div>
</div>
