<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>德阳市专利奖申报书</title>
<style type="text/css">
.STYLE4 {font-size: 32px;
  font-family: "宋体";
  font-weight: 600;}
.STYLE5 {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 2px;
  font-family: "宋体";
}
.STYLE6 {font-size: 24px;font-family: "宋体";}
table{border-collapse:collapse;border:solid black;border:none;}   
td{border: 1px solid black}
.gh{
  position:absolute;
  left:20px;
  right:20px;
  visibility: visible;
}
.p{
text-indent:2em;
font-size:16px;
font-family: "宋体";
 line-height:40px;
 } 
.p2{
text-indent:2em;
font-size:16px;
 line-height:36px;
 }
.p1{
 line-height:16px;
 font-size:16px;
 }
.middle-demo-2{
padding-top: 24px;
padding-bottom: 24px;
}
.def{
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.def1{
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:0px;
  border-bottom-color:#FFFFFF;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 16px;
  letter-spacing: 2px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.abc{
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all; 
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.abc1{
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:0px;
  border-bottom-color:#CCCCCC;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #000000;
  border-right-color: #000000;
  border-left-color: #000000;
}
.abc2{
  border-left-width:1px;
  border-top-width:0px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #000000;
  border-left-color: #000000;
}
.abc3{
  border-left-width:1px;
  border-top-width:0px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #000000;
  border-left-color: #000000;
}
.STYLE8 {
  color: #000000;
  font-weight: bold;
  font-size: 19px;
}
.bm{
  text-decoration: underline;
}
.STYLE10 {font-size: 18px}
.STYLE11 {font-size: 17px}
.abc4 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 14px;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.abc5 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 14px;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.btn {
 BORDER-RIGHT: #7b9ebd 1px solid; PADDING-RIGHT: 2px; BORDER-TOP: #7b9ebd 1px solid; PADDING-LEFT: 2px; FONT-SIZE: 12px; FILTER: progid:DXImageTransform.Microsoft.Gradient(GradientType=0, StartColorStr=#ffffff, EndColorStr=#cecfde); BORDER-LEFT: #7b9ebd 1px solid; CURSOR: hand; COLOR: black; PADDING-TOP: 2px; BORDER-BOTTOM: #7b9ebd 1px solid
}
.abc6 {border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 14px;
letter-spacing: 1px;
font-family: "宋体";
line-height: 1.8; }
.font_tishi {
  font-size: 15px;
  letter-spacing: 2px;
  font-family: "宋体";
  line-height: 1.8;
  color: #777777;
}
#xmxxb p{
  font-size: 15px;
  font-family:"新宋体";
}
.forProSupport
{
  font-family:"Times New Roman";
}
.def11 {border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:0px;
  border-bottom-color:#FFFFFF;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.def11 {border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:0px;
  border-bottom-color:#FFFFFF;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.def2 {border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.space{margin: 10px 0 9px 0;}

/*大段文字*/
.div_table{border:1px solid #000;padding:5px;font-size: 16px;margin:5px 0;}
.div_table_title{padding:5px;min-height:35px;margin:0;}
.div_table_content{padding:5px;min-height:100px;margin:0;height:auto;overflow: hidden;}
.div_table  table,.div_table table td{border:1px solid #000;border-spacing:0;}

</style>
  </head>
<body>
  <?php $cpdw = $propertyright->getCpdw();?>
  <table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def11">
  <tr>
    <td height="5" align="right" valign="bottom"  width="17%" class="def11" >&nbsp;</td>
    <td class="def11"  align="left" width="21%" style="margin-bottom:0px;margin-top:20px" valign="bottom">&nbsp;</td>
    <td valign="bottom" class="def11" align="right" width="15%">&nbsp;</td>
    <td valign="bottom" class="def11" align="left" width="19%">&nbsp;</td>
    <td valign="bottom" class="def11" align="right" width="11%">&nbsp;</td>
    <td class="def11"  align="left" width="17%" style="margin-bottom:0px;margin-top:20px" valign="bottom">&nbsp;</td>
  </tr>
</table>
<p>&nbsp;</p>
<p>&nbsp;</p>
<p align="center"><span class="STYLE4"><strong>德阳市专利奖申报书</strong></span><br /></p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<table width="680" border="0" align="center" cellpadding="0" cellspacing="0" class="def1">
<tr>
  <td width="29%" height="72" align="right" valign="bottom" class="def1"><span class="STYLE8">专利名称：</span></td>
  <td width="50%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$propertyright->getSubject()?></span></td>
  <td rowspan="6" align="right" class="def1" width="10%"></td>
</tr>
<tr>
<td width="29%" height="65" align="right" valign="bottom" class="def1"><span class="STYLE8">专 利 号：</span></td>
<td class="def" align="left" width="50%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11"><?=$propertyright->getZlh()?></span></td>
</tr>
<tr>
  <td width="29%" height="64" align="right" valign="bottom" class="def1"><span class="STYLE8">参评单位：</span></td>
  <td width="50%" align="left" valign="bottom" class="def" style="margin-bottom:0px;margin-top:20px"><span class="STYLE11"><?=$cpdw['mc']?></span></td>
</tr>
<tr>
  <td width="29%" height="64" align="right" valign="bottom" class="def1">&nbsp;</td>
  <td width="50%" align="left" valign="bottom" class="def11" style="margin-bottom:0px;margin-top:20px">&nbsp;</td>
</tr>
<tr>
  <td width="29%" height="65" align="right" valign="bottom" class="def1"><span class="STYLE8">推荐单位（盖章）：</span></td>
  <td class="def" align="left" width="50%" style="margin-bottom:0px;margin-top:20px" valign="bottom"><span class="STYLE11">
    <?=$propertyright->getDepartmentName()?>
  </span></td>
</tr>
</table>
<div class="space"></div>
<h1 align="center">&nbsp;</h1>
<p align="center">&nbsp;</p>
<p align="center">&nbsp;</p>
<?php $firstcompany = $data['firstcompany'];?>
<?php $baseinfo = $data['baseinfo'];?>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<table border="0" align="center" cellpadding="0" cellspacing="0" class="def11">
  <tr>
    <td colspan="3" width="100%" align="center" class="def1">二〇一七年&nbsp;&nbsp;月&nbsp;&nbsp;日</td>
  </tr>
</table>
<h1 align="center">&nbsp;</h1>

<p style="text-align:center; font-size:30px;font-family:'黑体'">&nbsp;</p>
<pagebreak  resetpagenum="1"/>
<table width="680" align="center" cellpadding="5" cellspacing="0" class="table notes" border="1">
<tr>
    <td height="40" align="center" ><strong><h3>申报书填写说明</h3></strong></td>
  </tr>
<tr>
  <td height="800" align="left" valign="top" >
    <p>&nbsp;</p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;一、参评专利基本情况、参评专利技术(设计)状况、参评单位实施参评专利取得的直接经济效益、参评专利实施取得的社会效益、参评专利保护情况、参评专利获奖情况、其他情况说明等由参评单位负责填写。其他单位实施参评专利状况由其他实施单位填写。专利权人和参评单位声明表，由所有专利权人和产品单位签字和盖章。</h3></p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;二、参评专利基本情况表中：<br />&nbsp;&nbsp;&nbsp;&nbsp;发明人的地址指第一发明人的住址，专利权人的地址指第一专利权人的地址；<br />&nbsp;&nbsp;&nbsp;&nbsp;实施状况一栏，“实施单位”分栏中，应填写实施该专利的单位（专利权人、被许可实施单位等）；<br />&nbsp;&nbsp;&nbsp;&nbsp;许可种类”、“许可金额”“合同履行情况”分栏中，专利权人自行实施的，不用填写；经许可实施的，许可种类可填写独占许可、排他许可、普通许可、交叉许可、分许可等；</h3></p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;三、参评单位实施参评专利取得的直接经济效益，是指参评单位自己实施或许可转让该专利，直接取得的各类经济效益指标的累计或年度情况；其他单位实施专利状况，主要实施单位（参评专利基本情况表中所列实施单位）原则上都应分别填写，其中：<br />&nbsp;&nbsp;&nbsp;&nbsp;“实施”是指该专利技术应用于生产实践，包括专利授权之前的运用和授权之后的运用。<br />&nbsp;&nbsp;&nbsp;&nbsp;“新增销售额”是指实施该专利新增加的销售额，以实际出厂价格计算。<br />&nbsp;&nbsp;&nbsp;&nbsp;“新增利润”是指实施该项专利新增加的盈利。<br />&nbsp;&nbsp;&nbsp;&nbsp;“新增税收”是指实施该项专利新增加的上缴税金。<br /> &nbsp;&nbsp;&nbsp;&nbsp;“新增出口额”是指实施该项专利新增加的出口额，按人民币填报。</h3></p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;四、申报书中经济效益和社会效益的统计截止日均为2016年12月31日。经济效益方面的数字须以参评单位或实施单位财务部门核准的数额为基本依据，需加盖单位财务章，并提供原件；社会效益指已实际产生的效益。</h3></p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;五、效益数据的来源、计算方法、公式可另附材料说明。如有间接或潜在的经济效益，也可说明，供评审时参考。</h3></p>
    <p><h3>&nbsp;&nbsp;&nbsp;&nbsp;六、若纸张不够，请加页。</h3></p>
  </td>
</tr>
</table>
<pagebreak />
<p style="font-size:18px;font-family:'黑体'; text-align:center;"><strong>参评专利基本情况</strong></p>
<table width="680" align="center" cellpadding="5" cellspacing="0" class="table" border="1">
  <tr>
    <td width="18%" height="50" align="center">专利名称</td>
    <td colspan="3" align="left"><?=$propertyright->getSubject()?></td>
  </tr>
  <tr>
    <td width="20%" height="50" align="center">专利号</td>
    <td width="35%" align="center"><?=$propertyright->getZlh()?></td>
    <td width="15%" align="center">授权公告日</td>
    <td width="30%" align="center"><?=$propertyright->getSqggr()?></td>
  </tr>
  <tr>
    <td height="50" align="center"><p align="center">发明人（设计人）</p></td>
    <td colspan="3" align="left"><?=$propertyright->getFmr()?></td>
  </tr>
  <tr>
    <td height="50" align="center">专利权人</td>
    <td colspan="3" align="left"><?=$propertyright->getZlqr()?> &nbsp;&nbsp;&nbsp;&nbsp;（盖章）</td>
  </tr>
  <tr>
    <td height="50" align="center">通信地址</td>
    <td align="left"><?=$propertyright->getTxdz()?></td>
    <td align="center">邮编</td>
    <td align="center"><?=$propertyright->getYb()?></td>
  </tr>
  <tr>
    <td height="50" align="center">联系人</td>
    <td width="24%" align="center"><?=$propertyright->getLxr()?></td>
    <td colspan="2" align="left">电话：<?=$propertyright->getLxrdh()?>
    &nbsp;&nbsp;手机：<?=$propertyright->getLxrsj()?><br />
    传真：<?=$propertyright->getLxrcz()?></td>
  </tr>
  <tr>
    <td height="50" align="center">参评单位</td>
    <td colspan="3" align="left"><?=$cpdw['mc']?>&nbsp;&nbsp;&nbsp;&nbsp;（盖章）</td>
  </tr>
  <tr>
    <td height="50" align="center">通信地址</td>
    <td align="left"><?=$cpdw['txdz']?>&nbsp;</td>
    <td align="center">邮编</td>
    <td align="center"><?=$cpdw['yb']?></td>
  </tr>
  <tr>
    <td rowspan="2" align="center">参评单位账户信息</td>
    <td height="50" colspan="2" align="center">开户行名称</td>
    <td align="center">开户行帐号</td>
  </tr>
  <tr>
    <td height="50" colspan="2" align="center"><?=$cpdw['khhmc']?></td>
    <td align="center"><?=$cpdw['khhzh']?></td>
  </tr>
  <tr>
    <td height="50" align="center">负责人</td>
    <td align="center"><?=$cpdw['fzr']?></td>
    <td colspan="2" align="left">电话：<?=$cpdw['fzrdh']?>&nbsp;&nbsp;&nbsp;&nbsp;手机：<?=$cpdw['fzrsj']?><br />
    传真：<?=$cpdw['fzrcz']?></td>
  </tr>
  <tr>
    <td height="50" align="center">联系人</td>
    <td align="center"><?=$cpdw['lxr']?></td>
    <td colspan="2" align="left"><p>电话：<?=$cpdw['lxrdh']?>&nbsp;&nbsp;手机：<?=$cpdw['lxrsj']?><br />
    传真：<?=$cpdw['lxrcz']?>&nbsp;&nbsp;邮箱：<?=$cpdw['lxrdzyx']?></p>
</td>
  </tr>
  <tr>
    <td height="50" colspan="4" align="center"><p align="center">实施情况</p></td>
  </tr>
  <tr>
    <td height="50" align="center">实施单位</td>
    <td height="50" align="center">许可种类</td>
    <td height="50" align="center">许可金额</td>
    <td height="50" align="center">合同履行情况</td>
  </tr>
  <?php $ssqk = $propertyright->getSsqk();?>
  <?php for($i=0;$i<count($ssqk['ssdw']),$ssqk['ssdw'][$i];$i++):?>
  <tr>
    <td height="50" align="center"><?=$ssqk['ssdw'][$i]?></td>
    <td height="50" align="center"><?=$ssqk['xkzl'][$i]?></td>
    <td height="50" align="center"><?=$ssqk['xkje'][$i]?></td>
    <td height="50" align="center"><?=$ssqk['htlxqk'][$i]?></td>
  </tr>
  <?php endfor;?>
  <tr>
    <td height="50" align="center"></td>
    <td height="50" align="center"></td>
    <td height="50" align="center"></td>
    <td height="50" align="center"></td>
  </tr>  
  <tr>
    <td height="50" colspan="4" align="center">中国大陆以外国家（地区）申请专利情况</td>
  </tr>
  <tr>
    <td height="50" align="center">国家/地区</td>
    <td height="50" align="center">申请时间</td>
    <td height="50" colspan="2" align="center">法律状态</td>
  </tr>
  <?php $sqzlqk = $propertyright->getSqzlqk();?>
  <?php for($i=0;$i<count($sqzlqk['gjdq']),$sqzlqk['gjdq'][$i];$i++):?>
  <tr>
    <td height="50" align="center"><?=$sqzlqk['gjdq'][$i]?></td>
    <td height="50" align="center"><?=$sqzlqk['sqsj'][$i]?></td>
    <td height="50" colspan="2" align="center"><?=$sqzlqk['flzt'][$i]?></td>
  </tr>
  <?php endfor;?>
  <tr>
    <td height="50" align="center"></td>
    <td height="50" align="center"></td>
    <td height="50" colspan="2" align="center"></td>
  </tr>   
</table>
<pagebreak />
  <?php $jjxy = $propertyright->getJjxy();?>
  <h3 align="center">参评单位实施参评专利取得的直接经济效益</h3>
  <table width="680" align="center" cellpadding="5" cellspacing="0" class="table notes" border="1">
    <tr>
      <td width="39" rowspan="6" align="center">经济<br /><br />效益</td>
      <td width="140" height="50" align="center">项目/时间</td>
      <td width="140" height="50" align="center"><p>累  计</p>
      <p>（实施日至2016年底）</p></td>
      <td width="100" height="50" align="center">2016年</td>
      <td width="100" height="50" align="center">2015年</td>
      <td width="100" height="50" align="center">2014年</td>
    </tr>
    <tr>
      <td height="50" align="center">产量</td>
      <td height="50" align="center"><?=$jjxy['cl']['lj']?></td>
      <td height="50" align="center"><?=$jjxy['cl']['one']?></td>
      <td height="50" align="center"><?=$jjxy['cl']['two']?></td>
      <td height="50" align="center"><?=$jjxy['cl']['three']?></td>
    </tr>  
    <tr>
      <td height="50" align="center">新增销售额（万元）</td>
      <td height="50" align="center"><?=$jjxy['xse']['lj']?></td>
      <td height="50" align="center"><?=$jjxy['xse']['one']?></td>
      <td height="50" align="center"><?=$jjxy['xse']['two']?></td>
      <td height="50" align="center"><?=$jjxy['xse']['three']?></td>
    </tr> 
      <tr>
      <td height="50" align="center">新增利润（万元）</td>
      <td height="50" align="center"><?=$jjxy['lr']['lj']?></td>
      <td height="50" align="center"><?=$jjxy['lr']['one']?></td>
      <td height="50" align="center"><?=$jjxy['lr']['two']?></td>
      <td height="50" align="center"><?=$jjxy['lr']['three']?></td>
    </tr> 
      <tr>
      <td height="50" align="center">新增税收（万元）</td>
      <td height="50" align="center"><?=$jjxy['ss']['lj']?></td>
      <td height="50" align="center"><?=$jjxy['ss']['one']?></td>
      <td height="50" align="center"><?=$jjxy['ss']['two']?></td>
      <td height="50" align="center"><?=$jjxy['ss']['three']?></td>
    </tr> 
    <tr>
      <td height="50" align="center">新增出口额（万元）</td>
      <td height="50" align="center"><?=$jjxy['cke']['lj']?></td>
      <td height="50" align="center"><?=$jjxy['cke']['one']?></td>
      <td height="50" align="center"><?=$jjxy['cke']['two']?></td>
      <td height="50" align="center"><?=$jjxy['cke']['three']?></td>
    </tr>
  </table>
<div class="div_table">
  <p class="div_table_title"><strong>参评单位实施参评专利情况（包括：专利实施与产业化情况，以及许可转让他人实施情况）</strong></p>
  <p class="div_table_content"><?=$jjxy['zlqk']?></p>
</div>
<div class="div_table"> 
  <p class="div_table_title"><strong>各项经济效益核算说明（或列表）</strong></p>
  <p class="div_table_content"><?=$jjxy['hssm']?></p>
</div>
<p align="right">参评单位财务部门（盖章）&nbsp;&nbsp;&nbsp;&nbsp;</p>
<p align="right">年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
<pagebreak />
<h3 align="center">参评专利实施取得的社会效益</h3>
<div class="div_table">
  <p class="div_table_title"><strong>社会效益情况：（包括：对促进技术进步、提高科学管理水平、保护自然资源与生态环境、消除公害污染、安全生产、改善劳动条件、医疗保健、保障国家和公共安全、提高人民物质文化生活水平、引领消费习惯等方面所起的作用，应详细说明，如能采取定量方法说明的均需有具体数字等。）</strong></p>
  <p class="div_table_content"><?=$propertyright->getShxy()?></p>
</div>
  </table>
  </body>
  </html>