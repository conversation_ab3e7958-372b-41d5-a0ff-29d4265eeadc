<div class="main">
  <div class="tools"> <a href="javascript:history.go(-1);" class="back">返回</a> <a href="#" onclick="$('#validateForm').submit();return false;" class="save">保存申报资料</a> <a href="<?=site_url("declare/kjjbj/output/id/".$project->getProjectId())?>" class="print">导出打印</a>
  </div>
  <div class="title"> <a href="<?=site_url("declare/kjjbj/index")?>">填表说明</a> <a href="<?=site_url("declare/kjjbj/cover")?>">申请封面</a> <a href="<?=site_url("declare/kjjbj/info")?>">项目基本情况</a>  <a href="<?=site_url("declare/kjjbj/content1")?>">第一页</a> <a href="<?=site_url("declare/kjjbj/content2")?>">第二页</a> <a href="<?=site_url("declare/kjjbj/content3")?>">第三页</a> <a href="<?=site_url("declare/kjjbj/content4")?>">第四页</a> <a href="<?=site_url("declare/kjjbj/content5")?>" class="active">第五页</a> <a href="<?=site_url("declare/kjjbj/content6")?>">第六页</a> <a href="<?=site_url("declare/kjjbj/content7")?>">第七页</a> <a href="<?=site_url("declare/kjjbj/content8")?>">第八页</a></div>
   <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table  width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">六、本项目曾获科技奖励情况</caption>
          <tr><td colspan="7"><table id="show" width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
          <tr>
            <th width="30%">获奖项目名称</th>
            <th width="12%">获奖时间</th>
            <th width="21%">奖项名称</th>
            <th width="11%">奖励等级</th>
            <th width="15%">授奖部门</th>
            <th colspan="2">可用操作</th>
          </tr>
          <?php
		  $hdkjjl = $project->getDetails()->getHdkjjl();
		   $n=count($hdkjjl['xmmc']) ? count($hdkjjl['xmmc']) : 6;
		  for($i=0;$i<$n;$i++):?>
          <tr>
            <td align="center"><input name="hdkjjl[xmmc][<?=$i?>]" type="text" id="hdkjjl_xmmc_<?=$i?>" value="<?=$hdkjjl['xmmc'][$i]?>" style="width:99%;" /></td>
            <td align="center"><input name="hdkjjl[hjsj][<?=$i?>]" type="text" id="hdkjjl_hjsj_<?=$i?>" value="<?=$hdkjjl['hjsj'][$i]?>" size="10" /></td>
            <td align="center"><input name="hdkjjl[jxmc][<?=$i?>]" type="text" id="hdkjjl_jxmc_<?=$i?>" value="<?=$hdkjjl['jxmc'][$i]?>" style="width:99%;" /></td>
            <td align="center"><input name="hdkjjl[jldj][<?=$i?>]" type="text" id="hdkjjl_jldj_<?=$i?>" value="<?=$hdkjjl['jldj'][$i]?>" size="10" /></td>
            <td align="center"><input name="hdkjjl[jlbm][<?=$i?>]" type="text" id="hdkjjl_jlbm_<?=$i?>" value="<?=$hdkjjl['jlbm'][$i]?>" style="width:99%;" /></td>
            <td width="5%" align="center"><span onclick="add()">复制行</span></td>
            <td width="6%" align="center"><span onclick="del()">删除行</span></td>
          </tr>
          <?php endfor;?>
          </table></td></tr>
      
          <tr>
            <td colspan="7">
<pre>
本表所填科技奖励是指：
  1、市、州人民政府、地区行政公署设立的科技奖励；
  2、省、自治区、直辖市政府和国务院有关部门、中国人民解放军设立的科技奖励；
  3、经登记的社会力量设立的科技奖励。
</pre>
            </td>
          </tr>
        
      </table>
      <table  width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <caption class="caption_title">七、主要知识产权证明目录</caption>
       	  <tr><td><table id="shows" width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
          <tr>
            <th width="36%">授权项目名称</th>
            <th width="19%">知识产权类别</th>
            <th width="15%">国 别</th>
            <th width="18%">授权号</th>
            <th colspan="2">可用操作</th>
          </tr>
          <?php
		  $zscqzm = $project->getDetails()->getZscqzm();
		  $n=count($zscqzm['xmmc']) ? count($zscqzm['xmmc']) : 6;
		  for($i=0;$i<$n;$i++):?>
          <tr>
            <td><input name="zscqzm[xmmc][<?=$i?>]" type="text" id="zscqzm_xmmc_<?=$i?>" value="<?=$zscqzm['xmmc'][$i]?>" style="width:99%;" /></td>
            <td><input name="zscqzm[lb][<?=$i?>]" type="text" id="zscqzm_lb_<?=$i?>" value="<?=$zscqzm['lb'][$i]?>" style="width:99%;" /></td>
            <td><input name="zscqzm[gb][<?=$i?>]" type="text" id="zscqzm_gb_<?=$i?>" value="<?=$zscqzm['gb'][$i]?>" style="width:99%;" /></td>
            <td><input name="zscqzm[sqh][<?=$i?>]" type="text" id="zscqzm_sqh_<?=$i?>" value="<?=$zscqzm['sqh'][$i]?>" style="width:99%;" /></td>
            <td width="6%" align="center" ><span onclick="adds()">复制行</span></td>
            <td width="6%" align="center"><span onclick="dels()">删除行</span></td>
          </tr>
           <?php endfor;?>
           
       </table></td></tr>
      </table>
    </form>
  </div>
</div>
<script type="text/javascript">

function add() 
{ 
   
   var tableObject=new Object(); 
 
   
   tableObject=document.getElementById("show");
   
   var row=tableObject.rows.length-1;
  
 //document.write(row); 
    //添加一行   
    var newTR=tableObject.insertRow(-1); 
	
    var td0=newTR.insertCell(0); 
	td0.align="center"
    var td1=newTR.insertCell(1);
	td1.align="center"
    var td2=newTR.insertCell(2); 
	td2.align="center"
    var td3=newTR.insertCell(3);
	td3.align="center"
	var td4=newTR.insertCell(4);
	td4.align="center"
	var td5=newTR.insertCell(5);
	td5.align="center";
	td5.width="5%";
	var td6=newTR.insertCell(6); 
	td6.align="center";
	td6.width="6%";
	

	
    
    td0.innerHTML='<input name="hdkjjl[xmmc]['+row+']" type="text" id="hdkjjl_xmmc_'+row+'" value="<?=$hdkjjl['xmmc'][$i]?>" style="width:99%;" />'; 
    td1.innerHTML='<input name="hdkjjl[hjsj]['+row+']" type="text" id="hdkjjl_hjsj_'+row+'" value="<?=$hdkjjl['hjsj'][$i]?>" size="10" />'; 
	td2.innerHTML='<input name="hdkjjl[jxmc]['+row+']" type="text" id="hdkjjl_jxmc_'+row+'" value="<?=$hdkjjl['jxmc'][$i]?>" style="width:99%;" />';
    td3.innerHTML='<input name="hdkjjl[jldj]['+row+']" type="text" id="hdkjjl_jldj_'+row+'" value="<?=$hdkjjl['jldj'][$i]?>" size="10" />'; 
	td4.innerHTML='<input name="hdkjjl[jlbm]['+row+']" type="text" id="hdkjjl_jlbm_'+row+'" value="<?=$hdkjjl['jlbm'][$i]?>" style="width:99%;" />';
	td5.innerHTML='<span onclick="add()">复制行</span>';
	td6.innerHTML='<span onclick="del()">删除行</span>';
	
}

function del()
{	//直接删除一行
	var table =document.getElementById("show");
	var rows = table.rows.length;
	if(rows > 2)
	{
	GetRow=window.event.srcElement.parentElement.parentElement.rowIndex;
	show.deleteRow(GetRow);}
	//table的id是show
	else alert('至少保留一行！');
}

function adds() 
{ 
   
   var tableObject=new Object(); 
 
   
   tableObject=document.getElementById("shows");
   
   var row=tableObject.rows.length-1;
  
 //document.write(row); 
    //添加一行   
    var newTR=tableObject.insertRow(-1); 
	
    var td0=newTR.insertCell(0); 
    var td1=newTR.insertCell(1);
    var td2=newTR.insertCell(2); 
    var td3=newTR.insertCell(3);
	var td4=newTR.insertCell(4);
	td4.align="center";
	td4.width="6%";
	var td5=newTR.insertCell(5); 
	td5.align="center";
	td5.width="6%";
	

	
    
    td0.innerHTML='<input name="zscqzm[xmmc]['+row+']" type="text" id="zscqzm_xmmc_'+row+'" value="<?=$zscqzm['xmmc'][$i]?>" style="width:99%;" />'; 
    td1.innerHTML='<input name="zscqzm[lb]['+row+']" type="text" id="zscqzm_lb_'+row+'" value="<?=$zscqzm['lb'][$i]?>" style="width:99%;" />'; 
	td2.innerHTML='<input name="zscqzm[gb]['+row+']" type="text" id="zscqzm_gb_'+row+'" value="<?=$zscqzm['gb'][$i]?>" style="width:99%;" />';
    td3.innerHTML='<input name="zscqzm[sqh]['+row+']" type="text" id="zscqzm_sqh_'+row+'" value="<?=$zscqzm['sqh'][$i]?>" style="width:99%;" />'; 
	td4.innerHTML='<span onclick="adds()">复制行</span>';
	td5.innerHTML='<span onclick="dels()">删除行</span>';
	
}

function dels()
{	//直接删除一行
	var table =document.getElementById("shows");
	var rows = table.rows.length;
	if(rows > 2)
	{
	GetRow=window.event.srcElement.parentElement.parentElement.rowIndex;
	shows.deleteRow(GetRow);}
	//table的id是show
	else alert('至少保留一行！');
}
</script>