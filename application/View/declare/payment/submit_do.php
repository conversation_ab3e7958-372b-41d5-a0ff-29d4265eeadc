<div class="content">
    <div class="block">
        <div class="block-content">
            <div class="page-header mb-3">
                <div class="btn-group btn-group-sm" role="group">
                    <?=btn("back");?>&nbsp;
                    <?=btn("link","编辑",site_url("apply/payment/edit/id/".$payment->getPaymentId()),"edit")?></div>
            </div>
            <div class="page-body">
                <div class="alert alert-success" role="alert">
                    <h4>温馨提示：</h4>
                    <ul>
                        <li>1. 上报前请仔细校对您所填写的相关信息，一经上报将不可更改；</li>
                        <li>2. 按照要求每项经费支出必须上传相关凭证，请整理并上传；</li>
                        <li>3. 上报后还需<?=$payment->isResearcher()===false ? '项目负责人、' : ''?>单位/部门助理进行审核。</li>
                    </ul>
                </div>
                <?php if(count($msg)):?>
                    <div class="alert alert-danger" role="alert">
                        <h4>系统检查到错误，请检查并修改这些错误：</h4>
                        <ul>
                            <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
                                <li>
                                    （<?=($i+1)?>）<?=$msg[$i]?>
                                </li>
                            <?php endfor;?>
                        </ul>
                    </div>
                <?php endif;?>
                <form name="up" id="up" method="post" action="" class="mb-3">
                    <?php if(count($msg)): ?>
                        <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 经费支出有误，请返回修改后再上报！</button>
                    <?php else:?>
                        <?=Button::setType('submit')->setEvent("return confirm('一经上报将不可更改，您确定要上报吗？')")->setIcon('submit')->setSize('btn-lg')->button('上报资料')?>
                    <?php endif;?>
                    <input name="id" type="hidden" id="id" value="<?=$payment->getPaymentId();?>" />
                </form>
            </div>
        </div>
    </div>
</div>

