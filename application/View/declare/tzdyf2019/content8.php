<div class="main">
  <div class="btn-group btn-group-sm" role="group">
    <?=btn("back")?>
    <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存申报资料</a> <a href="<?=site_url("user/task/doSubmit/id/".$project->getProjectId())?>" class="btn btn-sm btn-warning"><i class="ace-icon glyphicon glyphicon-send"></i> 上报资料</a> <a href="<?=site_url("apply/task/output/id/".$project->getProjectId())?>" target="_blank" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-print"></i> 导出打印</a>
    <p style="clear:both;"></p>
</div>
<div class="nav nav-tabs">
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/index")?>">填写说明</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/cover")?>">第一页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content1")?>">第二页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content2")?>">第三页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content3")?>">第四页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content4")?>">第五页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content5")?>">第六页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content6")?>">第七页</a></li>
    <li role="presentation"><a href="<?=site_url("apply/tzdyf2019/content7/mark/task/id/".$project->getProjectId())?>">经费预算</a></li>
    <li role="presentation" class="active"><a href="<?=site_url("apply/tzdyf2019/content8/id/".$project->getProjectId())?>">附件上传</a></li>
</div>
<div class="box">
    <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <caption>
          请上传相关附件
      </caption>
      <tr>
        <td><div class="box">
            <form id="validateForm" name="validateForm" method="POST" action="<?=site_url('apply/tzdyf2019/upload')?>" enctype="multipart/form-data">
                <table class="table">
                    <tr>
                        <td width="287"><input type="file" name="Filedata[]" id="id-input-file"/></td>
                        <td width="116" align="right" valign="middle"><label class="control-label">文件说明：</label></td>
                        <td width="373"><input name="file_note" value="" class="form-control" placeholder="请填写文件的简要说明"/></td>
                        <td width="181" class="text-center">
                            <input name="item_id" value="<?=$project->getProjectId()?>" type="hidden"/>
                            <input name="item_type" value="task" type="hidden"/>
                            <input name="upload_type" value="jpg,gif,png" type="hidden"/>
                            <button type="submit" onClick="loading()" class="btn btn-primary btn-sm"><i class="fa fa-upload"></i> 上传</button>
                        </td>
                    </tr>
                    <tr>
                        <td class="orange" colspan="4">温馨提示：请选择文件后再点击上传按钮,文件格式为jpg,gif,png，单文件小于2M。</td>
                    </tr>
                </table>
            </form>
            <form class="form-horizontal" id="validateForm" name="validateForm" action="" method="post">
                <p><i class="fa fa-list"></i> 已上传附件</p>
                <table width="100%" class="table table-hover">
                    <thead>
                        <tr>
                            <td width="27%" align="center">文件名</td>
                            <td width="11%" align="center">文件大小</td>
                            <td width="31%" align="center">备注</td>
                            <td width="15%" align="center">上传时间</td>
                            <td width="16%" align="center">操作</td>
                        </tr>
                    </thead>
                    <tbody id="paper-content">
                        <?php
                        $attachments = $project->attachments('tasks');
                        while($attachment = $attachments->getObject()):
                            ?>
                        <tr id="list_<?=$attachment->getId()?>">
                            <td><a href="javascript:void();" class="btn btn-sm btn-danger" role="button" data-toggle="modal" data-target="#showpic_<?=$attachment->getId()?>"><?=$attachment->getFileName()?></a><div class="modal fade" id="showpic_<?=$attachment->getId()?>" tabindex="-1" role="dialog" aria-labelledby="附件预览">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                            <h4 class="modal-title" id="myModalLabel">附件预览</h4>
                                        </div>
                                        <div class="modal-body">
                                            <?php if($attachment->isImage()):?>
                                                <a href="#" class="thumbnail"><img src="<?=site_path("up_files/".$attachment->getFilePath())?>" alt="<?=$attachment->getFileName()?>"  onerror="this.src='<?=site_path("up_files/".$attachment->getFilePath(),'http://xmgl.scst.gov.cn/')?>'" /></a>
                                            <?php else:?>
                                                该文档不可预览。<a href="<?=site_path("up_files/".$attachment->getFilePath())?>">点击下载</a>
                                            <?php endif;?>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                                        </div>
                                    </div>
                                </div>
                            </div></td>
                            <td><?=$attachment->getFileSize()?></td>
                            <td><?=$attachment->getFileNote(100)?></td>
                            <td><?=$attachment->getCreatedAt("Y-m-d H:i:s")?></td>
                            <td align="center"> <a href="javascript:void(0);" onclick="del_file(<?=$attachment->getId()?>,this)" class="btn btn-sm btn-danger" role="button"><i class="glyphicon glyphicon-remove"></i> 删除</a></td>
                        </tr>
                    <?php endwhile;?>
                </tbody>
            </table>

        </form>
    </div></td>
</tr>
</table>
</div>
</div>
<script src="<?=site_path('assets/js/ace-elements.min.js')?>"></script>
<script language="javascript" type="text/javascript">
    function upload(json){
       $('<tr id="list_'+json[0].id+'"><td><a href="<?=site_url("up_files")?>'+'/'+json[0].path+'">'+json[0].file_name+'</a></td><td align="center">'+json[0].size+'B</td><td align="center">'+json[0].time+'</td><td align="center"><a href="javascript:row_del('+json[0].id+');" onclick="return confirm(\'确定删除？\');">删除</a></td></tr>').appendTo("#filemanager");
       closeWindow();
   }

   function del_file(id,me){
    if(confirm('确定要删除吗？')){
        var me = $(me);
        $.post("<?=site_url("common/removeAttachments1")?>",{file_id:id,table:'project'},function(json){
            eval("json = "+json);
            if(json.st == 'OK') {
                me.parent().parent().remove();
            }else{
                alert(json.msg);
            }
        });
    }
}

</script>