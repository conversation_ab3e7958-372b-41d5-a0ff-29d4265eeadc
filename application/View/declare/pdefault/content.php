<table width="680" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td height="5" align="right" valign="bottom"  width="17%" ><b>申报编号：</b></td>
    <td align="left" width="130" class="border-bottom" valign="bottom"><span>
      <?=$project->accept_id?>
      &nbsp;</span></td>
    <td valign="bottom" align="right" width="15%"><b>计划编号：</b></td>
    <td valign="bottom" align="left" width="130" class="border-bottom"><?=$project->radicate_id?>
      &nbsp;</td>
    <td valign="bottom" align="right" width="11%"><b>密级：</b></td>
    <td align="left" width="110" class="border-bottom" valign="bottom">&nbsp;</td>
  </tr>
</table>
  <div class="space"></div>
<h1 align="center">
<b><?=$project->type->subject?><br />
  申报书<?php if($project->is_shift == 4):?>（自筹）<?php endif;?></b>
</h1>
<table width="680" align="center" cellpadding="3" cellspacing="0" >
<tr>
<td rowspan="8" align="right"  width="6%" ></td>
<td width="80" height="65" align="right" valign="bottom">项目名称：</td>
<td align="left" width="350" valign="bottom" class="border-bottom"><?=$project->subject?></td>
<td rowspan="8" align="right"  width="15%" ></td>
</tr>
<tr>
<td align="right" height="53" valign="bottom" >研究领域：</td>
<td align="left" valign="bottom" class="border-bottom"><?=$project->subject_name?></td>
</tr>
<tr>
<td align="right" height="53" valign="bottom" >申报单位(盖章)：</td>
<td  valign="bottom" align="left" class="border-bottom"><?=$project->corporation_name?></td>
</tr>
<tr>
<td align="right" height="53" valign="bottom" >项目负责人：</td>
<td align="left" valign="bottom" class="border-bottom"><?=$project->user->getUser()->first()->personname?></td>
</tr>
<tr>
<td align="right" height="65" valign="bottom" >联系电话：</td>
<td align="left" valign="bottom" class="border-bottom"><?=$project->user->getUser()->first()->user_mobile?></td>
</tr>
<tr>
<td align="right" height="53" valign="bottom" >归口部门：</td>
<td align="left" valign="bottom" class="border-bottom"><?=$project->department_name?></td>
</tr>
<tr>
<td  height="53" align="right" valign="bottom" >项目起止年限：</td>
<td align="left" valign="bottom" class="border-bottom"><?=date('Y-m-d',strtotime($project->start_at))?> 至 <?=date('Y-m-d',strtotime($project->end_at))?></td>
</tr>
</table>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<div class="space"></div>
<table align="center" cellpadding="3" cellspacing="0" >
  <tr>
    <td colspan="4" align="center"><h2 align="center">中国民航局第二研究所制</h2></td>
  </tr>
  <tr>
    <td width="80" align="right">二0</td>
    <td width="40" align="right">年</td>
    <td width="40" align="right">月</td>
    <td width="40" ></td>
  </tr>
</table>

<pagebreak  resetpagenum="1"/>

<?php $baseinfo = $project->extra()->first()->getBaseinfo();?>
<table width="680" align="center" cellpadding="5" cellspacing="0" class="table" border="1">
<tr>
    <td colspan="5" height="40"><strong>项目信息表</strong></td>
  </tr>
  <tr>
  <td colspan="2" height="30" align="center">项目名称</td>
  <td colspan="3" align="left"><?=$project->subject?></td>
  </tr>
<tr>
  <td height="30" colspan="2" align="center">起始时间</td>
  <td width="225" ><?=$project->start_at?></td>
  <td width="83" align="center">终止时间</td>
  <td width="211" ><?=$project->end_at?></td>
</tr>
<tr>
  <td height="30" colspan="2" align="center">所属技术领域</td>
  <td colspan="3"><?=$baseinfo['skill_domain']?></td>
  </tr>
<tr>
  <td height="30" colspan="2" align="center">创新类型</td>
  <td colspan="3"><?=$baseinfo['innovation_type']?></td>
  </tr>
<tr>
  <td height="500" colspan="2" align="center">项目概述<br />
    (500字以内)</td>
  <td colspan="3" valign="top"><?=$baseinfo['brief']?></td>
  </tr>
<tr>
  <td width="23" rowspan="4" align="center">预期<br/>成果</td>
  <td width="106" height="30" align="center">成果水平</td>
  <td colspan="3"><?=$baseinfo['fruit_level']?></td>
  </tr>
<tr>
  <td height="30" align="center">成果形式</td>
  <td colspan="3"><?=implode(" ",$baseinfo['fruits'])?></td>
</tr>
<tr>
  <td height="30" align="center">知识产权</td>
  <td colspan="3">获得发明专利
    <?=$baseinfo['patent_country']?>
    项，实用新型专利
    <?=$baseinfo['patent_province']?>
    项，其他
    <?=$baseinfo['patent_other']?>
    项</td>
  </tr>
<tr>
  <td height="30" align="center">技术标准制定</td>
  <td colspan="3"><?=$baseinfo['standard']?></td>
  </tr>
<tr>
  <td height="18" colspan="2" align="center">产学研联合</td>
  <td colspan="3"><?=$baseinfo['yield_study']?></td>
  </tr>
<tr>
  <td height="18" colspan="2" align="center">知识产权状况</td>
  <td colspan="3"><?=$baseinfo['patent']?></td>
</tr>
<tr>
  <td height="30" colspan="2" align="center">经费预算</td>
  <td colspan="3"><u> <?=$project->total_money?> </u>万元，其中申请财政科技经费<u> <?=$project->declare_money?> </u>万元</td>
  </tr>
</table>

<pagebreak />

<?php $firstcompany = $project->extra()->first()->getFirstcompany();?>
<table width="680" border="1" align="center" cellpadding="5" cellspacing="0" class="table">
  <tr>
    <td height="40" colspan="8"><strong>项目申报单位、合作单位及主要研究人员情况</strong></td>
  </tr>
  <tr>
    <td colspan="3" align="center">项目名称</td>
    <td colspan="5"><?=$project->subject?></td>
  </tr>
  <tr>
    <td width="25" rowspan="9" align="center">申<br />
    报<br />
    单<br />
    位</td>
    <td colspan="2" align="center" >单位名称 </td>
    <td colspan="2" ><?=$firstcompany['company_name']?></td>
    <td colspan="2" align="center">组织机构代码</td>
    <td ><?=$firstcompany['code']?></td>
    </tr>
  <tr>
    <td colspan="2" align="center" >地址</td>
    <td colspan="2" ><?=$firstcompany['address']?></td>
    <td colspan="2" align="center" >邮编</td>
    <td ><?=$firstcompany['postcode']?></td>
  </tr>
  <tr>
    <td colspan="2" align="center" >单位类别</td>
    <td colspan="5"><?=$firstcompany['company_category']?></td>
    </tr>
  <tr>
    <td colspan="2" align="center" >单位主管部门</td>
    <td colspan="5"><?=$firstcompany['superior_department']?></td>
    </tr>
  <tr>
        <td colspan="2" align="center">负责人</td>
        <td colspan="2" ><?=$firstcompany['chairman']?></td>
        <td colspan="2">联系部门</td>
        <td >&nbsp;</td>
      </tr>
      <tr>
       <td colspan="2" align="center">联系人</td>
        <td colspan="2" ><?=$firstcompany['linkman']?></td>
        <td colspan="2">联系人手机</td>
        <td ><?=$firstcompany['linkman_mobile']?></td>
      </tr>
  <tr>
       <td colspan="2" align="center">联系人座机</td>
        <td colspan="5"><?=$firstcompany['linkman_phone']?></td>
      </tr>
  <tr>
    <td colspan="2" align="center" >职工人数 </td>
    <td colspan="2" ><?=$firstcompany['staff_number']?></td>
    <td colspan="2" align="center">单位性质</td>
    <td ><?=$firstcompany['company_property']?></td>
    </tr>
     <tr>
    <td colspan="4" align="center" >上级行政主管部门</td>
    <td colspan="3" >&nbsp;</td>
  </tr>

  <?php $cooperation = $project->extra()->first()->getCooperation();?>
	<?php $n = count($cooperation) > 4 ? count($cooperation) : 4;?>
  <tr>
    <td width="25"  rowspan="<?=($n+1)?>" align="center">合<br />
    作<br />
    单<br />
    位</td>
    <td colspan="3" align="center">名称</td>
    <td colspan="4" align="center">在本项目中分工</td>
  </tr>
   <?php for($i=0;$i<$n;$i++):?>
   <tr>
    <td colspan="3"><?=$cooperation['company_name'][$i]?></td>
    <td height="35" colspan="4"><?=$cooperation['works'][$i]?></td>
  </tr>
  <?php endfor;?>
  <?php $firstresearcher = $project->extra()->first()->getFirstresearcher();?>
  <tr>
    <td rowspan="3" align="center">项<br />
  目<br />
  负<br />
  责<br />
  人</td>
     <td align="center" >姓名</td>
     <td colspan="2" align="center" ><?=$firstresearcher['username']?></td>
     <td align="center" >性别</td>
     <td align="center" ><?=$firstresearcher['sex']?></td>
     <td align="center" >出生年月</td>
     <td align="center" ><?=$firstresearcher['birthday']?></td>
   </tr>
  <tr>
    <td align="center" >学历(学位)</td>
    <td colspan="2" align="center" ><?=$firstresearcher['degree']?></td>
    <td align="center" >职称</td>
    <td align="center" ><?=$firstresearcher['title']?></td>
    <td align="center" >电话</td>
    <td align="center" ><?=$firstresearcher['phone']?></td>
  </tr>
  <tr>
    <td align="center" >从事专业</td>
    <td colspan="6" align="left" ><?=$firstresearcher['major_in']?></td>
  </tr>
</table>

<pagebreak />

<?php $member = $project->extra()->first()->getMember();?>
<table width="680" border="1" align="center" cellpadding="5" cellspacing="0" class="table">
  <?php $n = count($member) > 20 ? count($member) : 20;?>
  <tr>
    <th height="40" colspan="7" align="left">主要研究人员</th>
  </tr>
  <tr>
    <td width="68" height="35" align="center" >姓名</td>
    <td width="47" align="center" >性别</td>
    <td width="49" align="center" >年龄</td>
    <td width="62" align="center" >学历</td>
    <td width="69" align="center" >职称</td>
    <td width="171" align="center">从事专业</td>
    <td width="128" align="center">所在单位</td>
  </tr>
  <?php for($i=0;$i<$n;$i++):?>
  <tr>
    <td height="30" align="center" ><?=$member['name'][$i]?></td>
    <td height="40" align="center" ><?=$member['sex'][$i]?></td>
    <td align="center" ><?=$member['age'][$i]?></td>
    <td align="center" ><?=$member['degree'][$i]?></td>
    <td align="center" ><?=$member['title'][$i]?></td>
    <td ><?=$member['subject_name'][$i]?></td>
    <td ><?=$member['company_name'][$i]?></td>
  </tr>
  <?php endfor;?>
</table>

<pagebreak />

<?php $money = $project->extra()->first()->getMoney();?>
<table width="680" border="1" align="center" cellpadding="5" cellspacing="0" class="table">
    <tr>
        <th height="40" colspan="3" align="left">经费来源<span class="pull-right">（单位：万元）</span></th>
    </tr>
    <tr>
        <td width="230" height="40" align="center">申请省级财政科技经费资助</td>
        <td width="200" align="center">自筹经费</td>
        <td width="200" align="center">合计</td>
    </tr>
    <tr>
        <td align="center" height="40"><?=$money['special']?></td>
        <td align="center" height="35"><?=$money['self']?></td>
        <td align="center" height="35"><?=$money['total']?></td>
    </tr>
</table>
<div class="space"></div>
<table width="680" border="1" align="center" cellpadding="5" cellspacing="0" class="table">
  <tr>
        <th height="40" colspan="3" align="left">经费支出<span class="pull-right">（单位：万元）</span></th>
  </tr>
    <tr>
        <td width="47" height="40" align="center">序号</td>
        <td width="492" align="center">概算科目名称</td>
        <td width="103" align="center">项目总经费</td>
    </tr>
    <tr>
        <td align="center" height="40">1</td>
        <td><p >设备费</p></td>
        <td align="center"><?=$money['device']?></td>
    </tr>
    <tr>
        <td align="center" height="40">2</td>
        <td><p >材料费</p></td>
        <td align="center"><?=$money['material']?></td>
    </tr>
    <tr>
        <td align="center" height="40">3</td>
        <td><p >测试化验加工费</p></td>
        <td align="center"><?=$money['test']?></td>
    </tr>
    <tr>
        <td align="center" height="40">4</td>
        <td><p >燃料动力费</p>
        </td>
        <td align="center"><?=$money['power']?></td>
    </tr>
    <tr>
        <td align="center" height="40">5</td>
        <td><p >差旅费</p></td>
        <td align="center"><?=$money['travel']?></td>
    </tr>
    <tr>
        <td align="center" height="40">6</td>
        <td><p >会议费</p></td>
        <td align="center"><?=$money['conference']?></td>
    </tr>
    <tr>
        <td align="center" height="40">7</td>
        <td><p >出版/文献/信息传播/知识产权事务费</p></td>
        <td align="center"><?=$money['reference']?></td>
    </tr>
    <tr>
        <td align="center" height="40">8</td>
        <td><p >劳务费</p></td>
        <td align="center"><?=$money['manpower']?></td>
    </tr>
    <tr>
        <td align="center" height="40">9</td>
        <td><p >专家咨询费</p></td>
        <td align="center"><?=$money['consultancy']?></td>
    </tr>
    <tr>
        <td align="center" height="40">10</td>
        <td><p >国际合作与交流费</p></td>
        <td align="center"><?=$money['cooperation']?></td>
    </tr>
    <tr>
        <td align="center" height="40">11</td>
        <td><p >其他费用</p></td>
        <td align="center"><?=$money['other']?></td>
    </tr>
</table>