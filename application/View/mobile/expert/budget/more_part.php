  <table border="0" cellpadding="3" cellspacing="1" class="show_more">
    <tr>
      <th colspan="4">项目详细信息 &gt;&gt;&gt;</th>
    </tr>
    <tr>
      <td>项目名称</td>
      <td><?=$grade->getProject()->getSubject()?>&nbsp;</td>
      <td>申报编号</td>
      <td><?=$grade->getProject()->getAcceptId()?>&nbsp;</td>
    </tr>
    <tr>
      <td>申报经费</td>
      <td><?=$grade->getProject()->getDeclareMoney()?> 万元</td>
      <td>计划年度</td>
      <td><?=$grade->getProject()->getDeclareYear()?>&nbsp;</td>
    </tr>
    <tr>
      <td>填报时间</td>
      <td><?=$grade->getProject()->getDeclareAt("Y年m月d日")?>&nbsp;</td>
      <td>起止年限</td>
      <td><?=$grade->getProject()->getStartAt().' 至 '.$grade->getProject()->getEndAt()?>&nbsp;</td>
    </tr>
    <tr>
      <td>评审得分</td>
      <td><?=$grade->getProject()->getScore()?>&nbsp;</td>
      <td>操作记录</td>
      <td>[<a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/history/index/id/".$grade->getProject()->getProjectId())?>',450,300);return false;">查看记录</a>]&nbsp;</td>
    </tr>
   <!-- <tr>
      <td>申报资料</td>
      <td colspan="3">[<a href="<?//=site_url("declare/project/show/id/".$grade->getProject()->getProjectId())?>">查看申报书</a>] [<a href="<?//=site_url("declare/project/output/id/".$grade->getProject()->getProjectId())?>" target="_blank">导出申报书</a>] [<a href="<?//=site_url("declare/project/download/id/".$grade->getProject()->getProjectId())?>" target="_blank">下载申报书</a>]</td>
    </tr>-->
    <?php if($grade->getProject()->getStatement() >= 29):?>
    <tr>
      <th colspan="4">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
    </tr>
    <tr>
      <td>立项编号</td>
      <td><?=$grade->getProject()->getRadicateId()?>&nbsp;</td>
      <td>立项经费</td>
      <td><?=$grade->getProject()->getRadicateMoney()?> 万元&nbsp;</td>
    </tr>
    <tr>
      <td>计划任务书</td>
      <td><?=$grade->getProject()->getStateForTask()?><?php if($grade->getProject()->getStateForPlanBook() > 1):?>
    [<a href="<?=site_url("declare/task/show/id/".$grade->getProject()->getProjectId())?>">查看</a>]
    [<a href="<?=site_url("declare/task/output/id/".$grade->getProject()->getProjectId())?>" target="_blank">导出</a>] </li>
    <?php endif;?></td>
      <td>验收书签署</td>
      <td><?=$grade->getProject()->getStateForComplete()?>
    <?php if($grade->getProject()->getStateForCompleteBook() > 1):?>
    [<a href="<?=site_url("declare/task/show/id/".$grade->getProject()->getProjectId())?>">查看</a>]
    [<a href="<?=site_url("declare/task/output/id/".$grade->getProject()->getProjectId())?>" target="_blank">导出</a>] </li>
    <?php endif;?></td>
    </tr>
    <?php endif;?>
  </table>