<div class="main">
  <div class="tools">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="select">全部选中</a>
  <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="submit">上报选中项</a>
  <a href="javascript:void(0);" onclick="$('#validateForm').attr('action','<?=site_url("expert/project/doSubmitAll")?>');$('#validateForm').submit();return false;" class="submit">上报所有项</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <em><h5>尊敬的专家：</h5>
　　您好，您已经对以下项目进行评审。<br />
　　评审完毕后请上报你的评审，否则该次评审将视为无效处理。<br />
　　上报方法：请选中你确定上报的项目的评审（在项目前面的方框中勾选），然后点击列表下面或者上面的“上报选中项目的评审”按钮完成项目评审的上报操作。
  </em>
</div>
<div class="search">
<table width="100%" align="center">
  <tbody id="show_search">
  <form action="<?=site_url("expert/project/appraise_list")?>" method="post" name="search" id="search">
    <tr>
      <td>搜索关键词:
        <input id="search" name="search" class="form-control w-auto custom-control-inline" />
        <label><input type="radio" name="field" value="accept_id" checked="checked" />申报编号</label>
        <label><input name="field" type="radio" value="subject" />项目名称</label>
        <?=btn('button','搜索','submit','find')?>
        </td>
    </tr>
  </form>
 </tbody> 
</table>
</div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("expert/project/doSubmit")?>">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>申报编号</th>
	  <th>项目名称</th>
      <th>项目类型</th>
      <th>是否推荐</th>
      <th>评审得分</th>
      <th>评审日期</th>
      <th width="120">可用操作</th>
      </tr>
      <?php while($grade = $pager->getObject()):?>
      <tr>
      <td align="center"><input name="select_id[]" type="checkbox" value="<?=$grade->getId()?>" /></td>
      <td><?=$grade->getProject()->getAcceptId()?></td>
	  <td><?=link_to("declare/project/show/id/".$grade->getProject()->getProjectId(),$grade->getProject()->getSubject())?></td>
      <td><?=$grade->getProject()->getTypeSubject()?></td>
      <td align="center"><?=$grade->getStatement()?></td>
      <td align="center"><?=$grade->getScore()?></td>
      <td align="center"><?=$grade->getUpdatedAt("Y/m/d H:i")?></td>
      <td align="center"><a href="<?=site_url("declare/assess/index/id/".$grade->getId())?>" class="set">评审</a> <a href="<?=site_url("expert/project/doNonuser/id/".$grade->getId())?>" class="delete" onclick="return confirm('放弃评审后将失去评审该项目的机会，你确定放弃评审吗？');">弃权</a></td>
      </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="8" align="left"><input type="submit" name="button" id="button" value=" >>上报选中项<< " /> <input type="button" name="buttonAll" id="buttonall" value=" >>上报所有项<< " onclick="$('#validateForm').attr('action','<?=site_url("expert/project/doSubmitAll")?>');$('#validateForm').submit();return false;" /><span class="pager_bar">
            <?=$pager->fromto().$pager->navbar(10)?>
          </span></td>
        </tr>
      </table>
    </form>
  </div>
</div>