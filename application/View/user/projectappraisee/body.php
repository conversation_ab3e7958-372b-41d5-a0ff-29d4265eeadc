<link rel="stylesheet" type="text/css" href="<?=site_path("js/webuploader/webuploader.css")?>">
<script type="text/javascript" src="<?=site_path("js/webuploader/webuploader.nolog.min.js")?>"></script>

<div class="page-header">
<?=view::part('user/projectappraisee/header')?>
</div>
<div class="page-body">
  <ul class="nav nav-tabs" role="tablist">
    <?php foreach($tabs as $tab):?>
    <li role="presentation"<?php if($tab['method'] == $method):?> class="active"<?php endif;?>><a href="<?=$tab['url']?>" aria-controls="<?=$tab['method']?>" role="tab">
      <?=$tab['text']?>
      </a></li>
    <?php endforeach;?>
  </ul>
  <div class="tab-content">
    <div class="tab-pane fade in active">
      <form class="form-horizontal" name="form1" id="activeForm" action="" method="post" enctype="multipart/form-data">
        <div class="header no-margin-top">正文</div>

        <div class="clearfix"></div>
        <div class="alert alert-info">
          <ul class="list-unstyled">
            <li><i class="ace-icon fa fa-caret-right blue"></i>第一步：下载正文模版;<a href="<?=site_path('up_files/2016/jds/zhengwen.doc')?>" class="badge badge-primary">点击下载</a></li>
            <li><i class="ace-icon fa fa-caret-right blue"></i>第二步：用office打开并填写<!-- ，完成填写后导出pdf;<a href="#" id="guide_export" class="badge badge-primary">查看导出方法</a>
            <div class="hide">
            1.点击office左上角<span class="badge badge-info">菜单</span><br/>
            2.选择<span class="badge badge-info">另存为(或导出)</span><br/>
            3.选择<span class="badge badge-info">pdf</span><br/>
            <span class="badge badge-danger">注:office版本为2007版或更高才能导出pdf</span>
            </div> -->
            </li>
            <li class="red"><em>注:建议使用office2007或更高版本进行填写</em><!-- ，<em>如果导出中没有pdf选项请“<a href="http://www.kjt.com:8091/download/officetopdf.exe" target="_blank">点击下载office导出pdf插件</a>”</em> --></li>
            <li><i class="ace-icon fa fa-caret-right blue"></i>第三步：上传正文文件（文件以.docx结尾）</li>
            <li class="red"><em>注:以最后一次上传的word文件为准</em></li>
            <li class="red"><em>注:如因网络原因造成上传失败，请选择其他时间重新上传</em></li>
          </ul>
        </div>
        <div class="form-group col-xs-12 col-sm-12 col-md-12">
          <label class="control-label col-xs-12 col-sm-3 col-md-3">正文上传</label>
          <div class="col-xs-12 col-sm-9 col-md-9">
            <div id="uploader" class="wu-example">
              <!--用来存放文件信息-->
              <div class="btns">
                <div id="picker">选择文件</div>
              </div>
              <div id="PDF_list" class="uploader-list">
                <?php
                  if(!empty($file->getFileName())):
                ?>
                <div id="PDF_WU_FILE_0" class="item">
                  <h4 class="info"><?=$file->getFileName()?></h4>
                  <p class="state">已上传</p>
                  <div class="progress progress-striped active" style="display: none;">
                    <div class="progress-bar" role="progressbar" style="width: 100%;"></div>
                  </div>
                </div>
                <?php
                  endif;
                ?>
              </div>
            </div>
          </div>
          <div class="clearfix"></div>
        </div>

        <div class="clearfix"></div>

        <script language="javascript" type="text/javascript">
          $(function(){
            $('#guide_export').mouseover(function(){
              var tips = $(this).next('div').html();
              return showTips(tips,this,{tips:2,time:0,area:['255px','100px']});
            }).mouseout(function(){
              closeWindow();
            });
          });
          // $('#uploader').closest('form').submit(function(){return false;});
          var uploader = WebUploader.create({
            swf: "<?=site_path("js/webuploader/Uploader.swf")?>",
            server: "<?=site_url("user/projectappraisee/webupload")?>",
            pick: '#picker',
            auto:true,
            fileVal:'file[]',
            fileNumLimit:5,
            fileSingleSizeLimit:'20MB',
            // 不压缩image, 默认如果是jpeg，文件上传前会压缩一把再上传！
            resize: false,
            formData:{table:'project_appraisee_attachments',item_id:'<?=$book->getId()?>'},
            accept:{
              title: 'PDF',
              extensions: 'doc,docx',
              mimeTypes: '.doc,.docx'
              // extensions: 'pdf,doc,docx',
              // mimeTypes: 'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }
          }).on('ready', function () {
            $('.webuploader-element-invisible').parent().css({'width':'80px','height':'40px'});
          });

          function displayProp(obj){
            var names="";
            for(var name in obj){
              names+=name+": "+obj[name]+", ";
            }
            alert(names);
          }
          uploader.on('fileQueued', function( file ) {
            $('#PDF_list').html( '<div id="PDF_' + file.id + '" class="item">' +
                '<h4 class="info">' + file.name + '</h4>' +
                '<p class="state">等待上传...</p>' +
                '</div>' );
          });

          uploader.on('uploadProgress', function( file, percentage ) {
            var $li = $( '#PDF_'+file.id ),
                $percent = $li.find('.progress .progress-bar');

            // 避免重复创建
            if ( !$percent.length ) {
              $percent = $('<div class="progress progress-striped active">' +
                  '<div class="progress-bar" role="progressbar" style="width: 0%">' +
                  '</div>' +
                  '</div>').appendTo( $li ).find('.progress-bar');
            }

            $li.find('p.state').text('上传中');

            $percent.css( 'width', percentage * 100 + '%' );
          });
          uploader.on( 'uploadSuccess', function( file ) {
            $( '#PDF_'+file.id ).find('p.state').text('已上传');
          });

          uploader.on( 'uploadError', function( file,reason) {
            console.log(reason);
            $( '#PDF_'+file.id ).find('p.state').text('上传出错');
          });

          uploader.on( 'uploadComplete', function( file ) {
            $( '#PDF_'+file.id ).find('.progress').fadeOut();
          });
          uploader.on( 'uploadAccept', function( file, response ) {
            //alert(response._raw);
            console.log(response._raw);
            if ( response.hasError ) {
              // 通过return false来告诉组件，此文件上传有错。
              return false;
            }
          });
        </script>                    <div class="clearfix"></div>
      </form>
    </div>
  </div>
</div>
<div class="page-footer">
  <!--右下角浮动-->
  <div class="position-fix-rb bg-grey border-primary padding-10-20"> <a href="javascript:void(0);" onclick="form1.submit();" class="btn btn-primary btn-sm btn-submit"><i class="ace-icon fa fa-save"></i> 保存资料</a> </div>
</div>