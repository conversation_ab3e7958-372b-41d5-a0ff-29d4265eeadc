<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                填写中的奖励
            </h3>
        </div>
        <div class="block-content">
            <div class="main">
            <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
              <div class="no-padding no-margin panel panel-default" >
                <table align="center" cellpadding="3" cellspacing="1" class="table table-hover" data-id="table1" data-tag="table" data-option="column|scrollx">
                  <thead>
                    <tr>
                      <th width="30" class="text-center">详</th>
                      <th width="30%"><?=getColumnStr('奖励名称','subject')?></th>
                      <th class="text-center" width="15%"><?=getColumnStr('奖励类别','project_type')?></th>
                      <th class="text-center"><?=getColumnStr('起始年月','start_at')?></th>
                      <th class="text-center"><?=getColumnStr('结束年月','end_at')?></th>
                      <th class="text-center"><?=getColumnStr('填报时间','declare_at')?></th>
                      <th class="text-center">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                      <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                        <td width="25%">
                          <?=$project->getMark()?><a target="_black" href="<?=site_url('apply/award/show/id/'.$project->getProjectId())?>" ><?=$project->getSubject()?></a>
              </td>
              <td align="center"><?=$project->getProjectTypeSubject()?></td>
              <td align="center"><?=$project->getStartAt('Y-m')?></td>
              <td align="center"><?=$project->getEndAt('Y-m')?></td>
              <td align="center"><?=$project->getDeclareAt('Y-m-d')?></td>
              <td align="center" width="80" style="position:relative;">
                  <?php
                  $urls = [
                      [
                          'name'=>'编辑',
                          'url'=>site_url("apply/award/edit/id/".$project->getProjectId()),
                          'icon'=>'edit',
                      ],
                      [
                          'name'=>'查看',
                          'url'=>site_url("apply/award/show/id/".$project->getProjectId()),
                          'icon'=>'show',
                      ],
                      [
                          'name'=>'上报',
                          'url'=>site_url("user/award/doSubmit/id/".$project->getProjectId()),
                          'icon'=>'submit',
                      ],
                      [
                          'name'=>'分割线',
                          'url'=>'-',
                      ],
                      [
                          'name'=>'删除',
                          'url'=>site_url("user/award/dodelete/id/".$project->getProjectId()),
                          'icon'=>'trash',
                          'class'=>'text-danger',
                          'event'=>'return showConfirm(\'删除后将不可恢复，确定要删除吗？\',this);',
                      ]
                  ];
                  echo Button::setClass('btn-primary')->group('操作',$urls)?>
              </td>
            </tr>
            <tr class="fold_body">
              <td colspan="10">
               <?php include('more_part.php');?>
             </td>
            </tr>
            <?php endwhile;?>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
              </tr>
            </tfoot>
            </table>

            </div>
            </form>
            </div>
        </div>
    </div>
</div>