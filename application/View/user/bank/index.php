<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            <?=$user->getUserUsername()?>的银行账户信息
        </h2>
    </div>
    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <!-- Block Tabs Default Style -->
            <div class="block block-rounded">
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="space-10"></div>
                        <table class="table table-hover datatable col-md-12">
                            <thead>
                            <tr>
                                <th class="text-center" style="width: 80px">序号</th>
                                <th width="15%">开户行</th>
                                <th >具体支行</th>
                                <th >开户行行号</th>
                                <th >储蓄卡卡号</th>
                                <th >默认</th>
                                <th class="text-center" style="width:300px;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="member-list">
                            <?php $banks = $user->selectBanks(); ?>
                            <?php while ($bank = $banks->getObject()) :?>
                                <tr>
                                    <td class="text-center">
                                        <input type="hidden" name="sort[<?=$bank->getId()?>]" class="sort" value="<?=$bank->getSort()?>">
                                        <span class="sort_text"><?=$banks->getIndex()?></span>
                                    </td>
                                    <td>
                                        <?=$bank->getName()?>
                                    </td>
                                    <td>
                                        <?=$bank->getBranch()?>
                                    </td>
                                    <td>
                                        <?=$bank->getNo()?>
                                    </td>
                                    <td>
                                        <?=$bank->getCard()?>
                                    </td>
                                    <td>
                                        <?=$bank->getIsDefault()?"是":"否"?>
                                    </td>
                                    <td class="text-center">
                                        <?=Button::setUrl(site_url('user/bank/edit/userid/'.$user->getUserId().'/id/'.$bank->getId()))->setIcon('edit')->window('编辑')?>
                                        <?php
                                        if($bank->getIsDefault()==0):
                                            ?>
                                            <?=Button::setUrl(site_url('user/bank/default/userid/'.$user->getUserId().'/id/'.$bank->getId()))->setClass('btn-alt-primary')->setIcon('check')->link('设为默认')?>
                                        <?php endif;?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                            </tbody>
                        </table>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 150px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('user/bank/edit/userid/'.$user->getUserId()))->setIcon('add')->window('添加银行账户')?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script>
    var filetable = document.getElementById('member-list');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#member-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
            })
            updateSort();
        },

    });

    function updateSort() {
        var url = $("#validateForm").attr('action');
        $.post(url,$("#validateForm").serialize(),function(data){
            if (data.code==0) {
                // showSuccess('排序更新成功');
            } else {
                // showError('排序更新失败');
            }
        },'json');
    }
</script>