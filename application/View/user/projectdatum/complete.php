<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
  <p style="clear:both;"></p>
  </div>
  <div class="alert alert-warning"><b>尊敬的项目负责人你好:</b><br />
  以下项目为已经验收通过的项目，你可以点击<b>"打印"</b>按钮在线打印验收证书。</div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("user/complete/doSubmit")?>">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>详</th>
      <th><?=getColumnStr('立项编号','radicate_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('立项经费','radicate_money')?></th>
      <th>归口部门</th>
      <th width="150">操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr>
      <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
      <td align="center"><?=$project->getRadicateId()?></td>
      <td>
	  <?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p>
	  </td>
      <td><?=$project->getTypeSubject()?></td>
      <td align="center"><?=$project->getRadicateMoney()?> 万元</td>
      <td><?=$project->getDepartmentName()?></td>
      <td align="center">
        <a href="<?=site_url('user/complete/print_certificate/id/'.$project->getProjectId())?>" target="_blank" class="btn btn-sm btn-primary" role="button"><i class="glyphicon glyphicon-print"></i> 打印验收证书</a>
	  </td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
         <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="9" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
        </tr>
      </table>
    </form>
  </div>
</div>
