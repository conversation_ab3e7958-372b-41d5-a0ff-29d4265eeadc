<div class="main">
  <div class="btn-group btn-group-sm" role="group"><a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info"><i class="ace-icon fa fa-save"></i>保存资料</a></div>
    <div class="box">
        <form id="validateForm" name="validateForm" method="post" action="">
	  <table width="100%" border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
  <caption>必传附件</caption>
        <tr>
          <th width="28%">任务书附件</th>
          <td width="72%"><label>
            <input name="task_book" type="text" id="task_book" value="<?=$project->getConfigs("file.task")?>"  readonly="readonly"/>
            <input type="button" name="button3" id="button3" value=" &gt;&gt;上传&lt;&lt; " onclick="loadbox.load('<?=site_url("common/upload/item_id/".$project->getProjectId()."/upload_type/doc,docx,pdf")?>',function(json){$('#declare_book').val(json[0].path)},350,280)" />
          </label></td>
        </tr>
		<tr>
          <th width="28%">验收书附件</th>
          <td width="72%"><label>
            <input name="complete_book" type="text" id="complete_book" value="<?=$project->getConfigs("file.complete")?>"  readonly="readonly" />
            <input type="button" name="button3" id="button3" value=" &gt;&gt;上传&lt;&lt; " onclick="loadbox.load('<?=site_url("common/upload/item_id/".$project->getProjectId()."/upload_type/doc,docx,pdf")?>',function(json){$('#declare_book').val(json[0].path)},350,280)" />
          </label></td>
        </tr> 
		<tr>
		<td colspan="2" align="center"><input type="submit" name="button2" id="button2" value="保存资料" /></td>
		</tr>  
      </table>
    </form>
  </div>
</div>