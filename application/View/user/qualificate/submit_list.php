<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        已上报的资质列表
                    </h3>
                </div>
                <div class="block-content">
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th width="30">详</th>
                            <th><?=getColumnStr('资质名称','subject')?></th>
                            <th><?=getColumnStr('批准机关','department')?></th>
                            <th><?=getColumnStr('批准日期','date')?></th>
                            <th><?=getColumnStr('有效期','end_at')?></th>
                            <th><?=getColumnStr('状态','statement')?></th>
                            <th class="text-center" style="width: 140px;">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($qualificate = $pager->getObject()):?>
                            <tr>
                                <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                <td class="font-w600">
                                    <a href="<?=site_url('user/qualificate/show/id/'.$qualificate->getQualId())?>"><?=$qualificate->getSubject()?>
                                </td>
                                <td><?=$qualificate->getDepartment()?></td>
                                <td><?=$qualificate->getDate()?></td>
                                <td><?=$qualificate->getEndAt()?></td>
                                <td><?=$qualificate->getState()?></td>
                                <td class="text-center">
                                    <?=Button::setUrl(site_url('user/qualificate/show/id/'.$qualificate->getQualId()))->link('查看')?>
                                    <?php
                                    if(in_array($qualificate->getStatement(),[4,20])):
                                        ?>
                                        <?=Button::setClass('btn-alt-danger')->setUrl(site_url('user/qualificate/reBack/id/'.$qualificate->getQualId()))->window('撤回')?>
                                    <?php endif;?>
                                </td>
                            </tr>
                            <tr class="fold_body">
                                <td colspan="10">
                                    <?php include('more_part.php');?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        <?php if($pager->getTotal()==0):?>
                            <tr>
                                <td colspan="10" class="text-center">无记录</td>
                            </tr>
                        <?php endif;?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
