<table width="100%" border="0" cellpadding="3" cellspacing="1">
  <caption>详细信息</caption>
  <tr>
    <th width="20%">联系电话</th>
    <td width="40%"><?=$user->getUserPhone()?></td>
    <th width="20%">联系手机</th>
    <td width="40%"><?=$user->getUserMobile()?></td>
  </tr>
  <tr>
    <th>电子邮件</th>
    <td><?=$user->getUserEmail()?></td>
    <th>毕业学校</th>
    <td><?=$user->getUserGraduate()?></td>
  </tr>
  <tr>
    <th>从事工作</th>
    <td><?=$user->getUserWork()?></td>
    <th>职业</th>
    <td><?=$user->getUserOccupation()?></td>
  </tr>
  <tr>
    <th>家庭地址</th>
    <td><?=$user->getUserHomeAddress()?></td>
    <th>邮政编码</th>
    <td><?=$user->getPostalCode()?></td>
  </tr>
  <tr>
  	<th>注册时间</th>
    <td><?=$user->getCreatedAt("Y/m/d")?></td>
    <th>历史记录</th>
    <td> [<a href="javascript:void(0);" onclick="return showWindow('查看记录','<?=site_url("admin/history/index/id/".$user->getUserId())?>',450,300);return false;">查看记录</a>]</td>
  </tr>
</table>
  <?php
		$historys = $user->selectProject();
		if($historys->getTotal()):
	?>
<table width="100%" border="0" cellpadding="3" cellspacing="1">
  <caption>项目一览表</caption>
  <?php while($history = $historys->getObject()):?>
  <tr>
    <th width="20%">项目名称</th>
    <td width="40%"><?=link_to("apply/project/show/id/".$history->getProjectId(),$history->getSubject())?></td>
    <th width="20%">起止年限</th>
    <td width="40%"><?=$history->getStartAt().' 至 '.$history->getEndAt()?></td>
  </tr>
  <?php endwhile;?>
</table>
<?php endif;?>