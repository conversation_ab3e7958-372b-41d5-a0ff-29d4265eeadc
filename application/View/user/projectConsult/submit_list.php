<div class="main"> 
  <div class="btn-group btn-group-sm" role="group"> 
    <a href="javascript:void(0);" onclick="return showWindow('添加项目咨询方案','<?=site_url("user/projectConsult/add/id/")?>',{maxmin:false,end:function(){top.location.reload()}});" class="btn btn-danger btn-xs" role="button">添加方案</a>
    <p style="clear:both;"></p>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
     <div class="no-padding no-margin panel panel-default" >
      <div class="panel-heading">
        <i class="ace-icon fa fa-list"></i> 已上报的咨询方案
      </div>
      <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
        <tr>
          <th width="30">详</th>
          <th width="30%"><?=getColumnStr('项目名称','project_id')?></th>
          <th width="15%"><?=getColumnStr('承担单位','corporation_id')?></th>
          <th>项目类别</th>
          <th><?=getColumnStr('负责人','user_name')?></th>
          <th><?=getColumnStr('咨询时间','consult_at')?></th>
          <th><?=getColumnStr('咨询形式','type')?></th>
          <th><?=getColumnStr('状 态','statement')?></th>
          <th>操 作</th>
        </tr>
        <?php while($pro_consult = $pager->getObject()):?>
          <?php $project = $pro_consult->getProject() ?>
          <tr>
            <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
            <td width="25%"><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
            <td align="center"><?=$project->getCorporationName()?></td>
            <td align="center"><?=$project->getResearchTyp()?></td>
            <td align="center"><?=$pro_consult->getUserName()?></td>
            <td align="center"><?=$pro_consult->getConsultAt()?></td>
            <td align="center"><?=$pro_consult->getType()?></td>
            <td align="center"><?=$pro_consult->getState()?></td>
            <td align="center"><a target="_blank" class="btn btn-xs btn-success" href="<?=site_url('user/projectConsult/show/id/'.$pro_consult->getId())?>">查 看</a></td>
          </tr>
          <tr id="show_<?=$project->getId()?>" style="display:none;">
            <td colspan="10">
             <?php include('more_part.php') ?>
           </td>
         </tr>
       <?php endwhile;?>
       <tr>
        <td colspan="10" align="right">
          &nbsp;<?=$pager->fromto().$pager->navbar(10)?>
        </td>
      </tr>
    </table>
  </div>
</form>
</div>
</div>