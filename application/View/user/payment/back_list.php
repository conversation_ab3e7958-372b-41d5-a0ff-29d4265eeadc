<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        被退回的支出
                    </h3>
                </div>
                <div class="block-content">
                    <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                        <thead>
                        <tr>
                            <th width="30">详</th>
                            <th width="30%"><?=getColumnStr('项目名称','subject')?></th>
                            <th width="15%"><?=getColumnStr('支出科目','mark_subject')?></th>
                            <th><?=getColumnStr('支出金额','money')?></th>
                            <th><?=getColumnStr('添加时间','updated_at')?></th>
                            <th>状态</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($pay = $pager->getObject()):?>
                            <tr>
                                <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                <td>
                                    <a target="_black" href="<?=site_url('apply/project/show/id/'.$pay->getProjectId())?>" ><?=$pay->getSubject()?></a>
                                </td>
                                <td>
                                    <a href="<?=site_url("apply/payment/show/id/".$pay->getPaymentId())?>" ><?=$pay->getMarkSubject()?></a>
                                </td>
                                <td><?=$pay->getMoney()?> 万元</td>
                                <td><?=$pay->getUpdatedAt("Y-m-d")?></td>
                                <td><?=$pay->getState()?></td>
                            </tr>
                            <tr class="fold_body">
                                <td colspan="10">
                                    <?php include('more_pay_part.php') ?>
                                </td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="10" align="right">&nbsp;<span class="pager_bar">
              <?=$pager->fromto().$pager->navbar(10)?>
            </span></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
