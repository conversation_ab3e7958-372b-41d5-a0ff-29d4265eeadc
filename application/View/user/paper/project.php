<style>
    .handle {
        cursor: move !important;
        font-weight: 900;
        line-height: 35px;
    }
    .drag-hover{
        background-color:#f9f5d1 !important;
        border:1px dashed #919191;
        margin-bottom:15px
    }
</style>
<script type="text/javascript" src="<?=site_url('assets/js/Sortable.min.js')?>"></script>
<script type="text/javascript">
    function todo(json)
    {
        $.post('<?=site_url("user/paper/addProject")?>',{id:"<?=$paper->getPaperId()?>",project_id:json.project_id},function(json){
            if(json.code==1){
                layer.closeAll();
                showError(json.msg);
            }else{
                top.location.href=top.location.href+'/_save/yes/_msg/'+json.msg;
            }
        },'json');
    }
</script>
<div class="content">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center">
        <h2 class="content-heading">
            新增/编辑科技论文
        </h2>
        <?php
        if(!$paper->isNew()):
            ?>
            <div>
                <?=Button::setSize('btn-xs')->back();?>
<?=Button::setSize('btn-xs')->setUrl(site_url('user/paper/show/id/'.$paper->getPaperId()))->setClass('btn-alt-info my-2')->setIcon('far fa-sticky-note')->link('查看');?>
                <?=Button::setSize('btn-xs')->setUrl(site_url('user/paper/submit/id/'.$paper->getPaperId()))->setClass('btn-alt-warning my-2')->setIcon('send')->link('上报');?>
            </div>
        <?php endif;?>
    </div>
    <form name="form1" class="form-horizontal" id="validateForm" action="" method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-12">
            <!-- Block Tabs Default Style -->
            <div class="block block-rounded">
                <ul class="nav nav-tabs nav-tabs-block">
                    <?php foreach($tabs as $tab):
                        ?>
                        <li class="nav-item"><a class="nav-link <?php if($tab['method'] == $method):?>active<?php endif;?>" href="<?=$tab['url']?>">
                                <?=$tab['text']?>
                            </a></li>
                    <?php endforeach;?>
                    
                </ul>
                <div class="block-content tab-content">
                    <div class="tab-pane active" id="tab1" role="tabpanel">
                        <div class="space-10"></div>
                        <table class="table table-hover datatable col-md-12">
                            <thead>
                            <tr>
                                <th class="text-center" style="width: 80px">排序</th>
                                <th class="text-center" style="width: 80px">序号</th>
                                <th width="15%">项目编号</th>
                                <th>项目名称</th>
                                <th>承担单位/部门</th>
                                <th>项目负责人</th>
                                <th style="width:100px;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="project-list">
                            <?php $projects = $paper->selectProjects(); ?>
                            <?php while ($project = $projects->getObject()) :
                                $_project = $project->getProject(true);
                                ?>
                                <tr>
                                    <td class="text-center"><i class="fa fa-arrows-alt handle"></i></td>
                                    <td class="text-center">
                                        <input type="hidden" name="sort[<?=$project->getId()?>]" class="sort" value="<?=$project->getSort()?>">
                                        <span class="sort_text"><?=$project->getSort()?></span>
                                    </td>
                                    <td>
                                        <?=$_project->getRadicateId()?>
                                    </td>
                                    <td>
                                        <?=$_project->getSubject()?>
                                    </td>
                                    <td>
                                        <?=$_project->getCorporationName()?>
                                    </td>
                                    <td>
                                        <?=$_project->getUserName()?>
                                    </td>
                                    <td>
                                        <?=Button::setUrl(site_url('user/paper/delProject/id/'.$project->getItemId().'/pid/'.$project->getId()))->setClass('btn-alt-danger')->setIcon('delete')->delete('删除')?>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                            </tbody>
                        </table>
                        <div class="clearfix"></div>
                    </div>
                    <div class="form-group row">
                        <div class="col-lg-12">
                            <div style="width: 120px;margin: 0 auto;">
                                <?=Button::setUrl(site_url('common/project_search'))->setIcon('add')->window('添加项目')?>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- END Block Tabs Default Style -->
        </div>
    </div><!-- END Block Tabs -->
    </form>
</div>
<script>
    var filetable = document.getElementById('project-list');
    var sortable = new Sortable(filetable, {
        handle: '.handle',
        animation: 150,
        ghostClass: 'drag-hover',
        onEnd: function (evt) {
            var index = 0;
            $("#project-list tr").each(function (){
                var sort = ++index;
                $(this).find("input[name^=sort]").val(sort);
                $(this).find(".sort_text").text(sort);
            })
            updateSort();
        },

    });

    function updateSort() {
        var url = $("#validateForm").attr('action');
        $.post(url,$("#validateForm").serialize(),function(data){
            if (data.code==0) {
                // showSuccess('排序更新成功');
            } else {
                // showError('排序更新失败');
            }
        },'json');
    }
</script>