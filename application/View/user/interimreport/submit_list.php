<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:selectAll();" class="btn btn-info"><i class="ace-icon glyphicon glyphicon-ok-circle"></i> 全部选中</a>
  <p style="clear:both;"></p>
  </div>

  <div class="panel panel-default">
    <div class="panel-heading">中期检查报告列表</div>
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("user/complete/doSubmit")?>">
    <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
      <tr>
      <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th>详</th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th width="150">操作</th>
      <th width="150">整改意见</th>
      <th width="150">状态</th>
      </tr>
      <?php foreach($projects as $project):?>
      <tr>
      <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
      <td>
    <?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p>
    </td>
      <td align="center"><?=$project->getTypeSubject()?></td>
      <td align="center"><a href="<?=site_url('user/interimreport/output/id/'.$project->project_id)?>" class="btn btn-sm btn-primary" target="_blank">导出报告</a></td>
      <td align="center"><a href="#" class="btn btn-sm btn-info" onClick="return showWindow('整改意见','<?=site_url('user/interimreport/showNote/id/'.$project->project_id)?>',{area:['800px','400px']})">整改意见</a></td>
      <td align="center"><?=$project->getStateForInterimreport(false)?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
         <?php include('more_part.php');?>
        </td>
        </tr>
    <?php endforeach;?>
      <tr>
        <td colspan="9" align="right">&nbsp;<?=$projects->render()?></td>
        </tr>
      </table>
    </form>
  </div>
  </div>
</div>