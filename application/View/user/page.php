<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="X-UA-Compatible" content="edge" />
<meta charset="utf-8" />
<title><?=config::get("site_name")?></title>
<meta name="description" content="<?=config::get("site_name")?>" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
<!-- bootstrap & fontawesome -->
<link rel="stylesheet" href="<?=site_path('assets/css/bootstrap.min.css')?>" />
<link rel="stylesheet" href="<?=site_path('assets/css/bootstrap-extend.css')?>" />
<link rel="stylesheet" href="<?=site_path('css/style.css')?>" />
<link rel="stylesheet" href="<?=site_path('css/css.css')?>" />
<link rel="stylesheet" href="<?=site_path('assets/css/font-awesome.min.css')?>" />
<link rel="stylesheet" href="<?=site_path('assets/css/jquery-ui.min.css')?>" />
<!--[if lte IE 9]>
<link rel="stylesheet" href="<?=site_path('assets/css/ace-ie.min.css')?>" />
<![endif]-->
<!-- ace settings handler -->
<script src="<?=site_path('assets/js/ace-extra.min.js')?>"></script>
<!-- HTML5shiv and Respond.js for IE8 to support HTML5 elements and media queries -->
<!--[if lte IE 8]>
<script src="<?=site_path('assets/js/html5shiv.min.js')?>"></script>
<script src="<?=site_path('assets/js/respond.min.js')?>"></script>
<![endif]-->
<!-- basic scripts -->
<!--[if !IE]> -->
  <script type="text/javascript">window.jQuery || document.write("<script src='<?=site_path('assets/js/jquery.min.js')?>'>"+"<"+"/script>");</script>
<!-- <![endif]-->

<!--[if IE]>
<script type="text/javascript">
 window.jQuery || document.write("<script src='<?=site_path('assets/js/jquery1x.min.js')?>'>"+"<"+"/script>");')?>
</script>
<![endif]-->
<script type="text/javascript">
  if('ontouchstart' in document.documentElement) document.write("<script src='<?=site_path('assets/js/jquery.mobile.custom.min.js')?>'>"+"<"+"/script>");
</script>
<script src="<?=site_path('assets/js/ace.min.js')?>"></script>
<script src="<?=site_path('js/ueditor/ueditor.config.js')?>"></script>
<script src="<?=site_path('js/ueditor/ueditor.all.js')?>"></script>
<script src="<?=site_path('assets/js/jquery.validate.min.js')?>"></script>
<script type="text/jscript">
$(function(){
	$("#validateForm").validate({errorElement: "small"});
});
</script>
<style type="text/css">
	small.error{color: #F00;}
</style>
</head>

<body class="">
<input type="hidden" id="baseUrl" value="<?=site_url('/')?>" />

<div class="container-fluid">
  <div class="row">

  <?=view::part('header')?>

  <div class="space-10"></div>

  <?=$inc_body?>

  <?=view::part('footer')?>

<script src="<?=site_path('assets/js/bootstrap.min.js') ?>"></script>
<script src="<?=site_path('assets/js/jquery-ui.min.js')?>"></script>
<script src="<?=site_path('js/jquery.form.min.js') ?>"></script>
<script src="<?=site_path('js/jquery.cookie.min.js') ?>"></script>
<script src="<?=site_path('js/lay/layer/layer.js') ?>"></script>
<script src=<?=site_path('js/common.js') ?>?v=<?=config::get('js.common_version')?>"></script>
<script src="<?=site_path('js/service.js') ?>"></script>
</body>
</html>