<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        可外协的项目列表
                    </h3>
                </div>
                <div class="block-content">
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table align="center" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="30">详</th>
                                <th><?=getColumnStr('立项编号','radicate_id')?></th>
                                <th width="30%"><?=getColumnStr('项目名称','subject')?></th>
                                <th><?=getColumnStr('总经费','total_money')?></th>
                                <th><?=getColumnStr('立项年度','radicate_year')?></th>
                                <th class="text-center">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($project = $pager->getObject()):?>
                                <tr>
                                    <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
                                    <td><?=$project->getRadicateId()?>&nbsp;</td>
                                    <td width="25%"><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?></td>
                                    <td><?=$project->getTotalMoney()?>万元</td>
                                    <td><?=$project->getRadicateYear()?></td>
                                    <td align="center">
                                        <?=Button::setUrl(site_url('user/project/about/id/'.$project->getProjectId()))->link('申请')?>
                                    </td>
                                </tr>
                                <tr id="show_<?=$project->getId()?>" style="display:none;">
                                    <td colspan="11">
                                        <?php include('more_part.php');?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="11" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>