
<div class="panel panel-primary">
  <div class="panel-heading center">科 技 查 新 列 表</div>
  <div class="panel-body">
    <div class="search">
      <form class="form-inline" method="post" action="<?=site_url("user/search/listSearch")?>">
        <div class="form-group ">
          搜索关键词：
          <input type="text" class="form-control" name='search' value="<?=$_SESSION['condition']['search']?>">&nbsp;&nbsp;
        </div>
          <?=get_radio(['subject'=>'项目名称','type'=>'查新类型','number'=>'查新编号','corporation_name'=>'委托单位','linkman'=>'联系人'],'type',$_SESSION['condition']['type'],'',false)?><br><br>
        <div class="form-group">
           紧&nbsp;急&nbsp;程&nbsp;度：
          <select name="urgency_degree" class="form-control">
            <option value="">全部</option>
            <?=getSelectFromArray(['普通件','加急件'],$_SESSION['condition']['urgency_degree'])?>
          </select>
        </div>
        <div class="form-group ">
           &nbsp;&nbsp;&nbsp;&nbsp;委托日期：
          <input type="date" name="date_from" class="form-control" value="<?=$_SESSION['condition']['date_from']?>">
           &nbsp;&nbsp;到&nbsp;&nbsp;
          <input type="date" name="date_to" class="form-control" value="<?=$_SESSION['condition']['date_to']?>">
        </div>
        &nbsp;&nbsp;&nbsp;<button type="submit" class="btn btn-primary btn-sm">搜索</button>
      </form>
    </div>
    <form action="">
      <div class="panel panel-default">
        <div class="panel-heading">
            <i class="ace-icon fa fa-list"></i>&nbsp;查新项目列表 
             &nbsp;&nbsp;(&nbsp;共 <strong><?= $s->lastPage()?></strong>&nbsp;页，<strong><?=$s->total()?></strong>&nbsp;条记录 )
        </div>
      <table class="table table-hover center tb_list">
          <tr>
            <!-- <th width="3%">详</th> -->
            <th width="20%"><?=getColumnStr('项目名称','subject')?></th>
            <th width="10%"><?=getColumnStr('查新类型','type')?></th>
            <th width="10%"><?=getColumnStr('查新编号','number')?></th>
            <th width="10%"><?=getColumnStr('委托单位','corporation_name')?></th>
            <th width="8%"><?=getColumnStr('紧急程度','urgency_degree')?></th>
            <!-- <th width="7%"><?=getColumnStr('联系人','linkman')?></th> -->
            <th width="8%"><?=getColumnStr('委托日期','created_at')?></th>
            <th width="8%"><?=getColumnStr('状态','state')?></th>
            <th width="6%">查看</th>
            <th width="8%">查新报告</th>
          </tr>
          <?php foreach($s as $v) :?>
            <tr>
              <!-- <td><a target="_blank" href="<?=site_url('user/search/show/search_id/'.$v->search_id)?>">    <i class="ace-icon fa fa-plus"></i></a></td> -->
              <td><a target="_blank" href="<?=site_url('user/search/show/search_id/'.$v->search_id)?>">
                  <?= $v->subject;?></a></td>
              <td><?= $v->type;?></td>
              <td><?= $v->number;?></td>
              <td><?= $v->corporation_name;?></td>
              <td><?= $v->urgency_degree;?></td>
              <!-- <td><?= $v->linkman;?></td> -->
              <td><?= date('Y-m-d',strtotime($v->created_at));?></td>
              <td class="text-success"><?= $v->getSearchState();?></td>
              <td>
                <?php if (in_array($v->state, ['2','6','8','11'])): ?>
                  <!-- <input type="text" id="reject_explain".<?=$v->id?>  value="<?=$v->reject_explain?>" > -->
                  <div class="hidden">
                    <div id="reject_explain<?=$v->id?>"><?=$v->reject_explain?></div>
                  </div>
                  <a href="" onclick="return showWindow('\'<?=$v->getSearchState();?>\'说明','<br />&nbsp;&nbsp;&nbsp;'+$('#reject_explain'+'<?=$v->id?>').html(),{'type':1,'area':['30%','40%']})" >驳回说明</a>
                <?php endif ?>
              </td>
              <td>
                <?php if($v->state >=7): ?>
                  <a target="_blank" class="btn btn-primary btn-sm" href="<?=site_url('admin/search/report/search_id/'.$v->search_id)?>">查看</a>
                <?php endif ?>
              </td>
            </tr>
          <?php endforeach;?>
      </table>
      </div>
      <div align="center">
        <?= $s->render() ?>
      </div>
    </form>       
  </div>
</div>
