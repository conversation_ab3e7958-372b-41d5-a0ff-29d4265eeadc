<div class="page-header">
  <div class="btn-group btn-group-sm" role="group"> 
    <a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a>
  </div>
</div>
<div class="page-body">
  <?php if(count($msg)):?>
    <div class="alert alert-danger" role="alert">
      <h4>系统检查到您上传的证明材料版本（格式）有错误，导致无法合并，请重新扫描并上传以下文件：</h4>
      <ul>
        <?php for($i=0,$n=count($msg);$i<$n;$i++):?>
          <li>
            （<?=($i+1)?>）<?=$msg[$i]?>；
          </li>
        <?php endfor;?>
      </ul>
    </div>
  <?php endif;?>
  <div class="alert alert-success" role="alert">
    <form name="up" id="up" method="post" action="">
      <?php if(count($msg)): ?>
        <button type="button" class="btn btn-danger" disabled="disabled" ><i class="ace-icon glyphicon glyphicon-ban-circle"></i> 请返回更正后再导出！</button>
      <?php endif;?>
    </form>
  </div>
</div>