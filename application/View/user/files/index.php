
<div class="main">
  <div class="btn-group btn-group-sm" role="group"><a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a><p style="clear:both"></p></div>
  <div class="search">
	<table border="0" cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped"  >
      <tr>
      <form id="searchfrom" name="searchfrom" method="post" action="<?=site_url("user/files/index")?>">
      	<td>搜索关键词：
        	<input type="text" name="search" id="search"  />
            <input type="radio" name="filed" id="filed" value="file_name" checked="checked" />文件名
            <input type="radio" name="filed" id="filed1" value="file_note" />备注
            <input type="submit" name="button" id="button" value="搜索" />
        </td>
         </form>
         <td></td>
      </tr>
    </table>  
    </div>
    <div class="box">
    <table cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <form id="form1" name="form1" method="post" action="">
      
        <tr >
          <th><input name="selectAll" id="selectAll" type="checkbox" /></th>
          <th>ID</th>
          <th>文件名</th>
          <th>文件大小</th>
          <th>备注</th>
          <th>上传时间</th>
          <th>操作</th>
        </tr>
        <tr id="filemanager">
          <td colspan="7" class="note"><span style="color:#0080FF">你已经上传的附件：<span style="color:#F00"><?=$num->getSumSize()?></span>,你可以<a href="javascript:void(0);" onclick="return showWindow('上传附件','<?=site_url("common/upload/item_id/upload_type/rar,doc,docx,zip,jpg,gif,pdf")?>',{area:['60%','100%'],shadeClose:false})"

          style="color:#F00;">点击这里</a>上传其他文件。</span></td>
        </tr>
        <?php while($file = $pager->getObject()):?>
        <tr>
          <td align="center"><input name="select_id[]"  type="checkbox" value="<?=$file->getId()?>" /></td>
          <td align="center"><?=$file->getId()?></td>
          <td width="200"><a href="<?=site_url("user/files/show/id/".$file->getId());?>">
            <?=$file->getFileName()?>
            </a></td>
          <td align="center"><?=$file->getFileSize()?></td>
          <td><?=$file->getFileNote(20)?></td>
          <td align="center"><?=$file->getCreatedAt("Y-m-d H:i:s")?></td>
          <td align="center"><a href="javascript:void(0);" onclick="return showWindow('文件备注','<?=site_url("user/files/edit/id/".$file->getId())?>',400,300);" class="btn btn-sm btn-info" role="button"><i class="ace-icon glyphicon glyphicon-pencil"></i>编辑</a></td>
        </tr>
        <?php endwhile; ?>
        <tr>
          <td colspan="9"><span class="pager_bar">
            <?=$pager->fromto().$pager->navbar(5)?>
            </span>
            </td>
        </tr>
      </form>
    </table>
  </div>
</div>
<script language="javascript" type="text/javascript">
function upload(json){
	$('<tr id="list_'+json[0].id+'"><td align="center"><input name="select_id[]"  type="checkbox" value="'+json[0].id+'"  /></td><td align="center">'+json[0].id+'</td><td><a href="<?=site_url("up_files")?>'+'/'+json[0].path+'">'+json[0].file_name+'</a></td><td align="center">'+json[0].size+'B</td><td align="center"></td><td align="center">'+json[0].time+'</td><td align="center"><a href="javascript:row_del('+json[0].id+');" onclick="return confirm(\'确定删除？\');">删除</a></td></tr>').insertAfter("#filemanager"); 
	closeWindow();	
}

function row_del(id){
	$.post("<?=site_url("common/removeAttachments")?>",{file_id:id},function(json){
		eval("json = "+json);
		if(json.st == 'OK') $("#list_"+id).empty();
		else alert(json.msg);
	});
}
</script>