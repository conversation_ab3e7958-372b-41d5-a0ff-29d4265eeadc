<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                已上报的验收书
            </h3>
            <div class="block-options">
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="box">
                <div class="no-padding no-margin panel panel-default" >
                  <form id="validateForm" name="validateForm" method="post" action="<?=site_url("user/complete/doSubmit")?>">
                    <table cellpadding="3" cellspacing="1" class="tb_data table table-hover table-striped">
                        <thead>
                      <tr>
                        <th width="30">详</th>
                        <th width="120px"><?=getColumnStr('项目编号','radicate_id')?></th>
                        <th width="30%"><?=getColumnStr('项目名称','subject')?></th>
                        <th width="15%"><?=getColumnStr('项目类别','cat_id')?></th>
                        <th><?=getColumnStr('项目经费','total_money')?></th>
                        <th><?=getColumnStr('立项年度','radicate_year')?></th>
                        <th>状态</th>
                      </tr>
                        </thead>
                        <tbody>
                      <?php while($project = $pager->getObject()):?>
                        <tr>
                          <td><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle()">+</label></td>
                          <td><?=$project->getAcceptId()?></td>
                          <td><?=$project->getMark()?><?=link_to("declare/complete/show/id/".$project->getProjectId(),$project->getSubject())?></td>
                          <td><?=$project->getCatSubject()?></td>
                          <td><?=$project->getTotalMoney()?> 万元</td>
                          <td><?=$project->getRadicateYear()?></td>
                          <td><?=$project->getStateForComplete()?></td>
                        </tr>
                        <tr id="show_<?=$project->getId()?>" style="display:none;">
                          <td colspan="11">
                           <?php include('more_part.php') ?>
                         </td>
                       </tr>
                     <?php endwhile;?>
                        </tbody>
                        <tfoot>
                     <tr>
                      <td colspan="11" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                    </tr>
                        </tfoot>
                  </table>
                </form>
              </div>
            </div>
            </div>
        </div>
    </div>
</div>
