<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        外协申请综合查询
                    </h3>
                    <div class="block-options">
                        <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part_out.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
                            <tr>
                                <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                <th width="30" class="tmore">详</th>
                                <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
                                <th><?=getColumnStr('项目负责人','user_id')?></th>
                                <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                                <th><?=getColumnStr('立项年度','radicate_year')?></th>
                                <th><?=getColumnStr('总经费','total_money')?></th>
                                <th><?=getColumnStr('外协经费','out_money')?></th>
                                <th><?=getColumnStr('状态','state_for_out')?></th>
                                <th>申请表</th>
                            </tr>
                            <?php while($project = $pager->getObject()):?>
                                <tr>
                                    <td><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
                                    <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                    <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
                                    <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                                    <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                                    <td><?=$project->getRadicateYear()?></td>
                                    <td><?=$project->getTotalMoney()?>万元</td>
                                    <td><?=$project->getOutMoney()?>万元</td>
                                    <td><?=$project->getStateForOut()?></td>
                                    <td>
                                        <?=Button::setUrl(site_url('user/project/showOut/id/'.$project->getProjectId()))->link('查看')?>
                                    </td>
                                </tr>
                                <tr class="fold_body">
                                    <td colspan="12">
                                        <?php include('more_part.php') ?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            <tr>
                                <td colspan="12" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
