<table width="100%" border="0" cellpadding="3" cellspacing="1" class="show_more table table-striped table-sm">
  <tr>
    <th colspan="4">项目详细信息 &gt;&gt;&gt;</th>
  </tr>
    <tr>
        <th width="15%">项目名称</th>
        <td width="35%"><?=$project->getSubject()?>&nbsp;</td>
        <th width="15%">申报编号</th>
        <td width="35%"><?=$project->getAcceptId()?>&nbsp;</td>
    </tr>
  <tr>
    <th>研究对象</th>
    <td><?=$project->getTypeSubject()?></td>
    <th>项目类别</th>
    <td><?=$project->getProjectTypeSubject()?></td>
  </tr>
  <tr>
    <th>研究课题</th>
    <td><?=$project->getResearchTyp()?></td>
    <th>项目总经费</th>
    <td><?=$project->getTotalMoney()?> 万元</td>
  </tr>
  <tr>
    <th>申报单位</th>
    <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
    <th>项目负责人</th>
    <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
  </tr>
  <tr>
    <th>申报年度</th>
    <td><?=$project->getDeclareYear()?></td>
    <th>起止年限</th>
    <td><?=$project->getStartAt().' 至 '.$project->getEndAt()?> 【申报时间：<?=$project->getDeclareAt("Y-m-d")?>】&nbsp;</td>
  </tr>
  <tr>
    <th>评审得分</th>
    <td><?=$project->getScore()?></td>
    <th>申报书资料</th>
    <td><?=$project->getState()?>
        <?php
            if($project->getIsImport()==0):
        ?>
      【<a href="<?=site_url("apply/award/show/id/".$project->getProjectId())?>">查看</a>】
        <?php endif;?>
    </td>
  </tr>
  <tr>
    <th>评审分组</th>
    <td colspan="3"><?=$project->getTypeGroup()?></td>
  </tr>
  <tr>
    <th>操作记录</th>
    <td colspan="3">
        <?=Button::setName('查看记录')->setUrl(site_url("admin/history/index/id/".$project->getProjectId()))->setWidth('550px')->setHeight('80%')->window()?>
    </td>
  </tr>
  <tr>
    <th>参研单位</th>
    <td colspan="3"><?=$project->getCooperation(true)?></td>
  </tr>
  <tr></tr>
  <?php if($project->getStatement() >= 29):?>
    <tr>
      <th colspan="4">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
    </tr>
    <tr>
      <th>立项编号</th>
      <td><?=$project->getRadicateId()?>&nbsp;
        <?php if(in_array(input::getInput("session.userlevel"),array('1'))):?><a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/super/changeRadicate/id/".$project->getProjectId())?>',450,330);" class="btn btn-danger btn-xs" role="button">编辑</a>
        <?php endif;?>
      </td>
      <th>立项经费</th>
      <td><?=$project->getRadicateMoney()?> 万元&nbsp;
        <!-- <a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("office/payfor/index/id/".$project->getProjectId())?>',450,400);" class="btn btn-info btn-xs" role="button">拨款记录</a> -->
      </td>
    </tr>
    
  <?php endif;?>
  <?php if ($project->getUserId()): ?>
    <?php
    $historys = $project->getHistoryProjectForUser();
    if($historys->getTotal()):?>
    <tr>
      <th colspan="4">负责人历史项目一览表 &gt;&gt;&gt;</th>
    </tr>
    <?php if ($project->getUserId()): ?>
      <?php while($history = $historys->getObject()):?>
        <tr>
          <th>项目名称</th>
          <td><?=link_to("apply/award/show/id/".$history->getProjectId(),$history->getSubject())?></td>
          <th>起止年限</th>
          <td><?=$history->getStartAt().' 至 '.$history->getEndAt()?></td>
        </tr>
      <?php endwhile;?>
    <?php endif;?>   
  <?php endif;?>   
<?php endif ?>
<tr></tr>
</table>