<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<style>
    table.snote td{
        padding: 0.25rem;
        border:none;
        border-bottom: 1px solid #e2e8f2;
    }
</style>
<script language="javascript" type="text/javascript">
  $(document).on("change","#money_year",function(){
    var me = $(this);
    var year = me.val();
    <?php $url = Config::get('base_url');$url=rtrim($url,'/');?>
    var url = '<?=$url?>';
    url = url+'/common/YearTotalMoney';
    var data = {year:year};
    $.getJSON(url,data,function(data){
      $("#total_money").html('总经费：'+data+' 万元');
    });
  });
  $(document).ready(function(){
    <?php $url = Config::get('base_url');   $url=rtrim($url,'/');?>
    var url = '<?=$url?>';
    url = url+'/common/YearTotalMoney';
    var date=new Date;
    var year=date.getFullYear()
    var data = {year:year};
    $.getJSON(url,data,function(data){
      $("#total_money").html('总经费：'+data+' 万元');
    });
  });
</script>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                奖励综合搜索
            </h3>
            <div class="block-options">
                <?=btn('link','导出数据',site_url('export/export/index/table/awards'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="search">
                  <form action="" method="post" name="search" id="search">
                <table width="100%">
                  <tbody id="show_search">
                      <tr>
                        <td>关键词：
                          <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                          <label><input name="field" type="radio" value="subject" checked="checked" />奖励名称&nbsp;</label>
                          <label><input type="radio" name="field" value="radicate_id" />奖励编号&nbsp;</label>
                          <label><input type="radio" name="field" value="corporation_name" />申报单位&nbsp;</label>
                          <label><input type="radio" name="field" value="user_name" />奖励负责人&nbsp;</label>
                          <label><input type="radio" name="field" value="declare_year" />申报年度&nbsp;</label>
                          <?=btn('button','搜索','submit','find')?>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                            <option value="">=申报年度=</option>
                            <?=getYearList(input::getInput("mix.declare_year"))?>
                          </select>
                          <select name="type_id" id="type_id" class="form-control w-auto custom-control-inline">
                            <option value="">=奖励类型=</option>
                            <?=getAwardType(input::getInput("mix.type_id"))?>
                          </select>
                          <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                            <option value="">=奖励状态=</option>
                            <?=awardStateSelect(input::getInput("mix.statement"))?>
                          </select>
                        </td>
                      </tr>
                      <tr>
                          <td>
                              <?php
                              $companyId = input::getInput("mix.corporation_id")?:'9B581D83-B2BA-7337-0678-000000000000';
                              ?>
                              <label><span style="float: left;line-height: 35px">承担单位/部门：</span><div class="cxselect" data-selects="level1,level2,level3,level4" data-url="<?=site_url('ajax/companys') ?>" data-json-value="v" style="float: left">
                                      <select class="form-control w-auto custom-control-inline parent_company level1" data-level=1 data-value="<?=getParentCompanyId($companyId,1,true)?>" ></select>
                                      <select class="form-control w-auto custom-control-inline parent_company level2"  data-level=2 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,2,true)?>"></select>
                                      <select class="form-control w-auto custom-control-inline parent_company level3"  data-level=3 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,3,true)?>"></select>
                                      <select class="form-control w-auto custom-control-inline parent_company level4"  data-level=4 data-first-title="请选择" data-value="<?=getParentCompanyId($companyId,4,true)?>"></select>
                                      <input type="hidden" name="corporation_id" id="corporation_id" value="<?=$companyId?>">
                                  </div>
                              </label>
                          </td>
                      </tr>
                  </tbody>
                </table>
                  </form>
              </div>
              <div class="box">
                <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                  <thead>
                    <tr>
                      <th>详</th>
                      <th width="20%"><?=getColumnStr('奖励名称','subject')?></th>
                      <th width="20%"><?=getColumnStr('奖励类型','type_id')?></th>
                      <th><?=getColumnStr('负责人','user_name')?></th>
                      <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                      <th><?=getColumnStr('申报年度','declare_year')?></th>
                      <th><?=getColumnStr('奖励状态','statement')?></th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                          <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                          <td><?=$project->getMark()?><?=link_to("apply/award/show/id/".$project->getProjectId(),$project->getSubject(),array('target'=>"_blank"))?></td>
                          <td><?=$project->getProjectTypeSubject()?></td>
                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                        <td><?=$project->getDeclareYear()?></td>
                        <td><?=$project->getState()?></td>
                      </tr>
                        <tr class="fold_body">
                        <td colspan="14">
                         <?php include('more_part.php') ?>
                       </td>
                     </tr>
                   <?php endwhile;?>
                 </tbody>
                    <tfoot>
                 <tr>
                  <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?></span></td>
                </tr>
                    </tfoot>
              </table>

            </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    $(".parent_company").change(function (){
        var level = $(this).data('level');
        if($(this).val()){
            $('#corporation_id').val($(this).val());
        }else{
            if(level>1){
                var pLevel = level-1;
                var pVal = $('.level'+pLevel).val();
                $('#corporation_id').val(pVal);
            }else{
                $('#corporation_id').val('');
            }
        }

    });
</script>

