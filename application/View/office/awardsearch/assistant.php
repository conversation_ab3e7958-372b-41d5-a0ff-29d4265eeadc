<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<style>
    table.snote td{
        padding: 0.25rem;
        border:none;
        border-bottom: 1px solid #e2e8f2;
    }
</style>
<script language="javascript" type="text/javascript">
    function todo(json)
    {
        $.post('<?=site_url("admin/super/changeLinkman")?>',{id:json.itemid,username:json.username,userid:json.userid},function(m){alert(m);location.reload(true)});
    }

</script>
<div class="content">
    <div class="block">
        <div class="block-header">
            <h3 class="block-title">
                奖励科研助理查询
            </h3>
            <div class="block-options">
                <?=btn('link','导出数据',site_url('export/export/index'),'export')?>
            </div>
        </div>
        <div class="block-content">
            <div class="main">
              <div class="search">
                  <form action="" method="post" name="search" id="search">
                <table width="100%">
                  <tbody id="show_search">
                      <tr>
                        <td>关键词：
                          <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
                          <label><input name="field" type="radio" value="subject" checked="checked" />奖励名称&nbsp;</label>
                          <label><input type="radio" name="field" value="radicate_id" />奖励编号&nbsp;</label>
                          <label><input type="radio" name="field" value="corporation_name" />申报单位&nbsp;</label>
                          <label><input type="radio" name="field" value="user_name" />奖励负责人&nbsp;</label>
                          <label><input type="radio" name="field" value="declare_year" />申报年度&nbsp;</label>
                          <?=btn('button','搜索','submit','find')?>
                        </td>
                      </tr>
                      <tr>
                        <td>
                          <select name="declare_year" id="declare_year" class="form-control w-auto custom-control-inline">
                            <option value="">=申报年度=</option>
                            <?=getYearList(input::getInput("mix.declare_year"))?>
                          </select>
                          <select name="cat_id" id="cat_id" class="form-control w-auto custom-control-inline">
                            <option value="">=奖励来源=</option>
                            <?=getProjectType(input::getInput("mix.cat_id"))?>
                          </select>
                          <select name="project_type" id="project_type" class="form-control w-auto custom-control-inline">
                            <option value="">==奖励类别==</option>
                            <?=getProjectTypes(input::getInput("mix.project_type"))?>
                          </select>
                          <select name="type_id" id="type_id" class="form-control w-auto custom-control-inline">
                            <option value="">==研究对象==</option>
                            <?=getResearchObject(input::getInput("mix.type_id"))?>
                          </select>
                          <select name="statement" id="statement"  class="form-control w-auto custom-control-inline">
                            <option value="">=奖励状态=</option>
                            <?=projectStateSelect(input::getInput("mix.statement"))?>
                          </select>
                        </td>
                      </tr>
                      <tr>
                          <td>
                              <label><span style="float: left;line-height: 35px">承担单位：</span><div class="cxselect" data-selects="first_id,second_id,third_id" data-url="<?=site_url('ajax/companys') ?>" data-json-value="v" style="float: left">
                                      <select class="form-control w-auto custom-control-inline parent_company first_id" data-value="9B581D83-B2BA-7337-0678-000000000000" name="first_id"></select>
                                      <select class="form-control w-auto custom-control-inline parent_company second_id" name="second_id" data-first-title="请选择" data-value="<?=input::getMix('second_id')?>"></select>
                                      <select class="form-control w-auto custom-control-inline parent_company third_id" name="third_id" data-first-title="请选择" data-value="<?=input::getMix('third_id')?>"></select>
                                  </div>
                              </label>
                          </td>
                      </tr>
                  </tbody>
                </table>
                  </form>
              </div>
              <div class="box">
                <table width="100%" cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                  <thead>
                    <tr>
                      <th>详</th>
                      <th width="20%"><?=getColumnStr('奖励名称','subject')?></th>
                        <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
                        <th><?=getColumnStr('负责人','user_name')?></th>
                        <th><?=getColumnStr('科研助理','assistant_name')?></th>
                      <th><?=getColumnStr('申报年度','declare_year')?></th>
                      <th><?=getColumnStr('奖励经费','total_money')?></th>
                      <th><?=getColumnStr('奖励状态','statement')?></th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php while($project = $pager->getObject()):?>
                      <tr>
                          <td><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">立项编号：<?=$project->getRadicateId()?><br>奖励周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?><br>
<?php if($project->getCatId()==15):?>奖励类别：<?=$project->getProjectTypeSubject()?><br>研究对象：<?=$project->getTypeSubject()?><br><?php endif;?>奖励来源：<?=$project->getCatSubject()?></p></td>
                          <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
                        <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
                        <td><?=$project->getAssistantId()?link_to("user/profile/show/userid/".$project->getAssistantId(),$project->getAssistantName()):'无'?></td>
                        <td><?=$project->getDeclareYear()?></td>
                        <td><?=$project->getTotalMoney()?>万元</td>
                        <td><?=$project->getState()?></td>
                        <td>
                            <?=$project->getAssistantId()?Button::setUrl(site_url('common/user_search/itemid/'.$project->getProjectId().'/companyid/'.$project->getCorporationId()))->setClass('btn-alt-danger')->window('更换助理'):Button::setUrl(site_url('common/user_search/itemid/'.$project->getProjectId().'/companyid/'.$project->getCorporationId()))->window('设置助理')?>
                        </td>

                      </tr>
                        <tr class="fold_body">
                        <td colspan="14">
                         <?php include('more_part.php') ?>
                       </td>
                     </tr>
                   <?php endwhile;?>
                 </tbody>
                    <tfoot>
                 <tr>
                  <td colspan="14" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?></span></td>
                </tr>
                    </tfoot>
              </table>

            </div>
            </div>
        </div>
    </div>
</div>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
</script>

