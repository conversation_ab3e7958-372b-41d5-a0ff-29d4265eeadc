<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="javascript:void(0);" onclick="validateForm.action='<?=site_url("office/check/doRejected")?>';$('#validateForm').submit()" class="btn btn-danger undoit" role="button">退回选中项</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button"><i class="ace-icon fa fa-download"></i>导出数据</a>
   <!-- <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a> -->
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php') ?>
</div>
  <div class="box">
      <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/check/doAccept")?>">
      <tr>
        <th><input name="selectAll" type="checkbox" id="selectAll"/></th>
        <th>详</th>
        <th><?=getColumnStr('申报编号','accept_id')?></th>
        <th width="25%"><?=getColumnStr('项目名称','subject')?></th>
        <th><?=getColumnStr('项目负责人','user_id')?></th>
        <th><?=getColumnStr('研究对象','type_id')?></th>
        <th><?=getColumnStr('项目类别','flow_type')?></th>
        <th width="15%"><?=getColumnStr('申报单位','corporation_id')?></th>
        <th><?=getColumnStr('年度','declare_year')?></th>
        <th><?=getColumnStr('总经费','total_money')?></th>
        <th>操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> 
        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
        <td align="center"><?=$project->getAcceptId()?></td>
        <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=date('Y-m',strtotime($project->getStartAt()))?> 到 <?=date('Y-m',strtotime($project->getEndAt()))?></p></td>
        <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
        <td align="center"><?=$project->getTypeSubject()?></td>
        <td align="center"><?=$project->getProjectTypeSubject()?></td>
        <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
        <td align="center"><?=$project->getDeclareYear()?></td>
        <td align="center"><?=$project->getTotalMoney()?>万元</td>
        <td align="center">
          <!-- <a href="<?=site_url("office/check/doBackWait/id/".$project->getProjectId())?>" onclick="return confirm('您确定将此项目返回至待受理吗？');" class="btn btn-info btn-xs" role="button"><i class="glyphicon glyphicon-chevron-left"></i>返回待受理</a>  -->
          <a href="javascript:void(0);" onclick="return showWindow('退回项目','<?=site_url("office/check/doRejectedOnlyOne/id/".$project->getProjectId())?>',400,250);" class="btn btn-danger btn-xs" role="button"><i class="glyphicon glyphicon-remove-circle"></i> 退 回</a>
        </td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="16">
        <?php include('more_part.php') ?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="16" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
        </form>
      </table>
  </div>
</div>