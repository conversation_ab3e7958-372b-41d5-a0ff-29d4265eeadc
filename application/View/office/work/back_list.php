<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        被退回的著作列表
                    </h3>
                </div>
                <div class="block-content">
                    <!-- <div class="btn-group btn-group-sm" role="group"> -->
                        <!-- <a href="javascript:selectAll();" class="btn btn-info">全部选中</a> -->
                        <!-- <a href="<?=site_url("export/export/index/table/works")?>" class="btn btn-info export" role="button">导出数据</a> -->
                        <!-- <p style="clear:both;"></p> -->
                    <!-- </div> -->
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
                        <div class="no-padding no-margin panel panel-default" >
                            <table align="center" cellpadding="3" cellspacing="1" class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <th class="text-center" width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                                    <th width="30">详</th>
                                    <th><?=getColumnStr('著作名称','subject')?></th>
                                    <th><?=getColumnStr('出版社','publication')?></th>
                                    <th><?=getColumnStr('出版号','publication_no')?></th>
                                    <th><?=getColumnStr('单位/部门','corporation_id')?></th>
                                    <th><?=getColumnStr('出版类型','type')?></th>
                                    <th><?=getColumnStr('出版日期','date')?></th>
                                    <th><?=getColumnStr('状态','statement')?></th>
                                    <th class="text-center">操作</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php while($work = $pager->getObject()):?>
                                    <tr>
                                        <td align="center"><input name="select_id[]" type="checkbox" value="<?=$work->getWorkId()?>" /></td>
                                        <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$work->getId()?>').toggle();">+</label></td>
                                        <td width="30%"><a href="<?=site_url('user/work/show/id/'.$work->getWorkId())?>"><?=$work->getSubject()?></a></td>
                                        <td><?=$work->getPublication()?></td>
                                        <td><?=$work->getPublicationNo()?></td>
                                        <td><?=$work->getCorporation(true)->getAllSubject()?></td>
                                        <td><?=$work->getType()?></td>
                                        <td><?=$work->getDate()?></td>
                                        <td><?=$work->getState()?></td>
                                        <td align="center" width="150" >
                                            <a href="javascript:void(0);"  onclick="return showWindow('审核科技著作','<?=site_url("office/work/doSubmitOne/id/".$work->getWorkId())?>',400,300);" title="审核" class='btn btn-xs btn-info'><i class="glyphicon glyphicon-ok"></i>重新审核</a>
                                        </td>
                                    </tr>
                                    <tr id="show_<?=$work->getId()?>" style="display:none;">
                                        <td colspan="16"><?php include('more_part.php') ?></td>
                                    </tr>
                                <?php endwhile;?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="10" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>