  <table border="0" cellpadding="3" cellspacing="1" class="show_more">
    <tr>
      <th colspan="4">项目详细信息 &gt;&gt;&gt;</th>
    </tr>
    <tr>
      <th width="15%">项目名称</th>
      <td width="35%"><?=$project->getSubject()?>&nbsp;</td>
      <th width="15%">申报编号</th>
      <td><?=$project->getAcceptId()?>&nbsp;</td>
    </tr>
    <tr>
      <th>项目类别</th>
      <td><?=$project->getTypeSubject()?>&nbsp;</td>
      <th>所在部门</th>
      <td><?=$project->getOfficeSubject()?>&nbsp;</td>
    </tr>
    <tr>
      <th>申报经费</th>
      <td><?=$project->getDeclareMoney()?> 万元</td>
      <th>项目总经费</th>
      <td><?=$project->getTotalMoney()?> 万元</td>
    </tr>
    <tr>
      <th>承担单位</th>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <th>项目负责人</th>
      <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?>&nbsp;</td>
    </tr>
    <tr>
      <th>申报年度</th>
      <td><?=$project->getDeclareYear()?>（<?=$project->getTypeCurrentGroup()?>）&nbsp;</td>
      <th>立项年度</th>
      <td><?=$project->getRadicateYear(true)?>&nbsp;【评审分组：<?=$project->getTypeGroup()?>】</td>
    </tr>
    <tr>
      <th>起止年限</th>
      <td><?=$project->getStartAt().' 至 '.$project->getEndAt()?> 【项目申报时间：<?=$project->getDeclareAt("Y-m-d")?>】
        &nbsp;</td>
        <th>评审得分</th>
        <td><?=$project->getScore()?>&nbsp;</td>
      </tr>    
      <tr>
        <th>操作记录</th>
        <td>【<a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/history/index/id/".$project->getProjectId())?>',450,300);return false;">查看记录</a>】&nbsp;</td>
        <th>推荐记录</th>
        <td>【<a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/commend/index/id/".$project->getProjectId())?>',450,300);return false;">推荐记录</a>】</td>
      </tr>
      <tr>
        <th>申报资料</th>
        <td>
          【<a href="<?=site_url("apply/project/show/id/".$project->getProjectId())?>">查看申报书</a>】
          <!-- 【<a href="<?=site_url("apply/project/output/id/".$project->getProjectId())?>" target="_blank">导出申报书</a>】  -->
          <!-- 【<a href="<?=site_url("apply/project/download/id/".$project->getProjectId())?>" target="_blank">下载申报书</a>】 -->
        </td>
      </tr>
      <?php if($project->getStatement() >= 29 || $project->getTaskOpen()):?>
        <tr>
          <th colspan="4">以下信息仅立项项目可见 &gt;&gt;&gt;</th>
        </tr>
        <tr>
          <th>立项编号</th>
          <td><?=$project->getRadicateId()?>&nbsp;
            <?php if(in_array(input::getInput("session.userlevel"),array('1','5','6'))):?>
              【<a href="javascript:void(0);" onclick="return showWindow('系统信息','<?=site_url("admin/super/changeRadicate/id/".$project->getProjectId())?>',450,260);">编辑</a>】
            <?php endif;?>
          </td>
          <th>立项经费</th>
          <td><?=$project->getRadicateMoney()?> 万元&nbsp;
            <!-- 【<a href="javascript:void(0);" onclick="return showWindow('分年度拨款记录','<?=site_url("office/payfor/index/id/".$project->getProjectId())?>',450,400);">分年度拨款记录</a>】 -->
          </td>
        </tr>
        <tr>
          <th>计划任务书</th>
          <td><?=$project->getStateForTask()?>
            <?php if($project->getStateForPlanBook() > 0):?>
              【<a href="<?=site_url("declare/task/show/id/".$project->getProjectId())?>">查 看</a>】
              <!-- 【<a href="<?=site_url("declare/task/output/id/".$project->getProjectId())?>" target="_blank">导出</a>】 -->
            <?php endif;?>
          </td>
          <th>验收书</th>
          <td><?=$project->getStateForComplete()?>
            <?php if($project->getStateForCompleteBook() > 0):?>
              【<a href="<?=site_url("declare/complete/show/id/".$project->getProjectId())?>">查 看</a>】
              <!-- 【<a href="<?=site_url("declare/complete/output/id/".$project->getProjectId())?>" target="_blank">导出</a>】 -->
            <?php endif;?>
          </td>
        </tr>
      <?php endif;?>
      <tr></tr>
      <?php if ($project->getUserId()): ?>
        <?php
        $historys = $project->getHistoryProjectForUser();
        if($historys->getTotal()):?>
        <tr>
          <th colspan="4">负责人历史项目一览表</th>
        </tr>
        <?php if ($project->getUserId()): ?>
          <?php while($history = $historys->getObject()):?>
            <tr>
              <th>项目名称</th>
              <td><?=link_to("apply/project/show/id/".$history->getProjectId(),$history->getSubject())?></td>
              <th>起止年限</th>
              <td><?=$history->getStartAt().' 至 '.$history->getEndAt()?></td>
            </tr>
          <?php endwhile;?>
        <?php endif;?>      
      <?php endif;?>      
    <?php endif ?>
  </table>