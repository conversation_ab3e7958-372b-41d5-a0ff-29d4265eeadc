<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        成果鉴定及评价综合列表
                    </h3>
                    <div class="block-options">
                        <?=btn('link','导出数据',site_url('export/export/index/table/fruits'),'export')?>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include_once('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="">
                        <table cellpadding="3" cellspacing="1"  class="tb_data table table-hover">
                            <thead>
                            <tr>
                                <th width="30">详</th>
                                <th width="25%"><?=getColumnStr('成果名称','subject')?></th>
                                <th><?=getColumnStr('任务来源','source')?></th>
                                <th width="12%"><?=getColumnStr('成果归属单位','unit')?></th>
                                <th><?=getColumnStr('鉴定或<br>评价日期','date')?></th>
                                <th><?=getColumnStr('成果水平','level')?></th>
                                <th width="10%"><?=getColumnStr('所属产业类别','industry')?></th>
                                <th><?=getColumnStr('申报人','user_name')?></th>
                                <th><?=getColumnStr('审核状态','statement')?></th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php while($fruit = $pager->getObject()):?>
                                <tr>
                                    <td align="center"><label class="fold_bar" onclick="fold.toggle('<?=$pager->getIndex()?>')">+</label></td>
                                    <td><a href="<?=site_url('user/fruit/show/id/'.$fruit->getFruitId())?>"><?=$fruit->getSubject()?></a></td>
                                    <td><?=$fruit->getSource()?></td>
                                    <td><?=$fruit->getUnit()?></td>
                                    <td><?=$fruit->getDate()?></td>
                                    <td><?=$fruit->getLevel()?></td>
                                    <td><?=$fruit->getIndustry()?></td>
                                    <td><?=$fruit->getUserName()?></td>
                                    <td><?=$fruit->getState()?></td>
                                </tr>
                                <tr class="fold_body">
                                    <td colspan="12">
                                        <?php include('more_part.php') ?>
                                    </td>
                                </tr>
                            <?php endwhile;?>
                            </tbody>
                            <tfoot>
                            <tr>
                                <td colspan="12" align="right">&nbsp;<span class="pager_bar"><?=$pager->total().$pager->navbar(10)?></span></td>
                            </tr>
                            </tfoot>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
