<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:history.back();" class="btn btn-info"><i class="ace-icon fa fa-arrow-left"></i> 返回</a>
  <a href="javascript:void(0);" onclick="$('#validateForm').submit();return false;" class="btn btn-info export" role="button">汇总数据</a>
  <a href="<?=site_url("office/collect/appraise")?>" class="btn btn-info find" role="button">按百分比查看</a>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <caption>数据汇总向导</caption>
     <tr>
      <td><p>1、请你选择汇总年度：
          <select name="year" id="year" class="form-control w-auto custom-control-inline">
            <?=getYearList(input::getInput("mix.year"))?>
          </select>
          (年度必须选择，该年度是指分组的年度)</p></td>
      </tr>
      <tr>
      <td>2、请你选择汇总类型：<select name="guide_id" id="guide_id">
          <option value="">=所属指南=</option>
          <?=getGuidesList(input::getInput("mix.guide_id"))?>
        </select>（不选为所有类型）</td>
      </tr>
      <tr>
      <td>3、请你选择汇总科室：<select name="office_id">
          <option value="">=所属科室=</option>
          <?=getOfficeList(input::getInput("mix.office_id"))?>
        </select>(不选为所有科室,针对已经分流的项目)</td>
      </tr>
        <td>4、请你选择汇总归口部门：
            <select name="department_id" id="department_id">
          <option value="">==所属归口部门==</option>
          <?=getDepartmentList(input::getInput("mix.department_id"))?>
        </select>（不选为所有归口部门）</td>
      </tr>  <tr>
          <td><span style="float:left">5、请你选择时间段：</span><input name="start_at" type="text" class="date-pick" size="10" value="<?=input::getInput("mix.start_at")?>" /><span style="float:left">到</span><input name="end_at" type="text" class="date-pick" size="10" value="<?=input::getInput("mix.end_at")?>" />。</td>
        </tr>
     <tr>
       <td align="center"><input type="submit" name="button2" id="button2" value=" >汇总数据< " /></td>
     </tr>
      </table>
    </form>
    <table width="100%" align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <caption>按分数段统计情况</caption>
     <?php for($i=0,$n=count($data);$i<$n;$i++):?>
     <tr>
      <th width="30%"><?=$data[$i]['LEV']?></th>
      <td width="76%"><?=$data[$i]['NUM']?></td>
      </tr>
     <?php endfor;?>
      </table>
  </div>
</div>