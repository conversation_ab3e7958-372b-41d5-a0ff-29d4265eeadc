<link href="<?=site_path('assets/js/daterangepicker/daterangepicker.css')?>" rel="stylesheet">
<script src="<?=site_path('assets/js/daterangepicker/moment.min.js')?>"></script>
<script src="<?=site_path('assets/js/daterangepicker/daterangepicker.js')?>"></script>
<?php
$postUrl = \Sofast\Core\router::getController().'/'.\Sofast\Core\router::getMethod();
?>
<form action="<?=site_url($postUrl)?>" method="post" name="search" id="search">
<table width="100%" align="center">
  <tbody id="show_search">
    <tr>
      <td>关键词：
        <input id="search" name="search" value="<?=input::getInput("post.search")?>" class="form-control w-auto custom-control-inline"  />
        <label><input name="field" type="radio" value="subject" checked="checked" /> 文章标题</label>
        <label><input name="field" type="radio" value="publication" /> 刊物名称</label>
        <label><input name="field" type="radio" value="author" /> 撰稿人</label>
        <label><input name="field" type="radio" value="user_name" /> 填报人</label>
        <select name="statement" id="statement" class="form-control w-auto custom-control-inline">
          <option value="">==状态==</option>
            <?=paperSelect(input::getMix('statement'))?>
        </select>
          <label>
              <button type="button" class="btn" id="daterange-btn" style="border: 1px solid #d4dcec">
                  <i class="fa fa-calendar"></i>
                  <span>发表日期</span>
                  <i class="fa fa-caret-down"></i>
              </button>
              <input type="hidden" name="start_at" value="" id="start_at">
              <input type="hidden" name="end_at" value="" id="end_at">
          </label>
        <?=btn('button','搜索','submit','find')?>
      </td>
    </tr>
    <tr>
        <td>
            <label><span style="float: left;line-height: 35px">单位/部门：</span><div class="cxselect" data-selects="first_id,second_id,third_id" data-url="<?= site_url('ajax/companys') ?>" data-json-value="v" style="float: left">
                    <select class="form-control w-auto custom-control-inline parent_company first_id" data-value="9B581D83-B2BA-7337-0678-000000000000" name="first_id"></select>
                    <select class="form-control w-auto custom-control-inline parent_company second_id" name="second_id" data-first-title="请选择" data-value="<?=input::getMix('second_id')?>"></select>
                    <select class="form-control w-auto custom-control-inline parent_company third_id" name="third_id" data-first-title="请选择" data-value="<?=input::getMix('third_id')?>"></select>
                </div>
            </label>
        </td>
    </tr>
 </tbody>
</table>
</form>
<script src="<?= site_path('assets/js/jquery.cxselect.js') ?>"></script>
<script>
    $('.cxselect').cxSelect();
    function init() {
        //定义locale汉化插件
        var locale = {
            "format": 'YYYY-MM-DD',
            "separator": " -222 ",
            "applyLabel": "确定",
            "cancelLabel": "取消",
            "fromLabel": "起始时间",
            "toLabel": "结束时间'",
            "customRangeLabel": "自定义",
            "weekLabel": "W",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
            "firstDay": 1
        };
        //初始化显示当前时间
        var start_at = "<?=input::getMix('start_at')?>";
        var end_at = "<?=input::getMix('end_at')?>";
        $("#start_at").val(start_at);
        $("#end_at").val(end_at);
        if(start_at && end_at){
            $('#daterange-btn span').html(start_at + ' - ' + end_at);
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    ranges: {
                        '本月': [moment().startOf('month'), moment().endOf('month')],
                        '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                        '本年': [moment().startOf('year'), moment().endOf('year')],
                        '上年': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                        '最近一月': [moment().subtract(1, 'month'), moment()],
                        '最近一年': [moment().subtract(1, 'year'), moment()],
                    },
                    startDate: start_at,
                    endDate: end_at
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }else{
            $('#daterange-btn span').html('发表日期');
            //日期控件初始化
            $('#daterange-btn').daterangepicker(
                {
                    'locale': locale,
                    //汉化按钮部分
                    ranges: {
                        '本月': [moment().startOf('month'), moment().endOf('month')],
                        '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                        '本年': [moment().startOf('year'), moment().endOf('year')],
                        '上年': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
                        '最近一月': [moment().subtract(29, 'days'), moment()],
                        '最近一年': [moment().subtract(1, 'year'), moment()],
                    },
                },
                function (start, end) {
                    $('#daterange-btn span').html(start.format('YYYY-MM-DD') + ' - ' + end.format('YYYY-MM-DD'));
                    $("#start_at").val(start.format('YYYY-MM-DD'));
                    $("#end_at").val(end.format('YYYY-MM-DD'));
                }
            );
        }
    };
    $(document).ready(function() {
        init();
    });
</script>