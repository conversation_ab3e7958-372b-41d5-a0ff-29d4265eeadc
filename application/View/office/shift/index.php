<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
   <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php')?>
</div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th width="80"><?=getColumnStr('立项编号','radicate_id')?></th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('项目类型','type_id')?></th>
      <th><?=getColumnStr('项目负责人','user_name')?></th>
	  <th><?=getColumnStr('承担单位','corporation_id')?></th>
	  <th><?=getColumnStr('所属归口部门','department_id')?></th>
      <th><?=getColumnStr('项目状态','statement')?></th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
      <td align="center"><?=$project->getRadicateId()?></td>
      <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">项目周期：<?=$project->getStartAt()?> 到 <?=$project->getEndAt()?><br>项目类别：<?=$project->getProjectTypeSubject()?></p></td>
      <td><?=$project->getTypeSubject()?></td>
      <td align="center"><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
	   <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
	    <td><?=$project->getDepartmentName()?></td>
      <td align="center"><?=$project->getState()?></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="9">
        <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="9" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>