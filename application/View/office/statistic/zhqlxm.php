<div class="content">
  <div class="row">
    <div class="col-lg-12">
      <div class="block block-rounded">
        <div class="block-header block-header-default">
          <h3 class="block-title">
            转化潜力项目列表
          </h3>
          <div class="block-options">
            <a href="javascript:void(0);" onclick="return showWindow('新增信息','<?=site_url("office/statistic/addZhqlxm")?>');" class="btn btn-danger btn-sm pull-right" role="button"><span class="glyphicon glyphicon-plus"></span> 添 加</a>
          </div>
        </div>
        <div class="block-content">
          <div class="search">
            <table width="100%">
              <tbody id="show_search">
                <form action="" method="post" name="search" id="search">
                  <tr>
                    <td>关键词：
                      <input id="search" style="width:300px;" name="search" />
                      <label><input type="radio" name="field" value="subject" checked="checked" /> 名称</label>
                      <label><input type="radio" name="field" value="unit" /> 公司</label>
                      <select name="type" id="type">
                        <option value="">==类别==</option>
                        <?=getSelectFromArray(['专利类','工法类','科技奖励类','技术引进类'],'',true)?>
                      </select>
                      <input type="submit" name="Submit4" value=" 搜 索 " class="btn btn-info btn-sm" /></td>
                    </tr>
                  </form>
                </tbody>
              </table>
            </div>
            <form id="validateForm" name="validateForm" method="post" action="" class="form-horizontal">
              <div class="no-padding no-margin panel panel-default" >
                <table cellpadding="3" cellspacing="1" class="table table-hover table-bordered">
                  <thead>
                   <tr>
                    <th class="text-center" width="30">详</th>
                    <th class="text-center" width="30%"><?=getColumnStr('名 称','subject')?></th>
                    <th class="text-center"><?=getColumnStr('公 司','unit')?></th>
                    <th class="text-center"><?=getColumnStr('推荐人','person')?></th>
                    <th class="text-center"><?=getColumnStr('类 别','type')?></th>
                    <th class="text-center">操 作</th>
                  </tr>
                </thead>
                <?php while($zhqlxm = $pager->getObject()):?>
                  <tr> 
                    <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$zhqlxm->getId()?>').toggle()">+</label></td>
                    <td align="center"><?=$zhqlxm->getSubject()?></td>
                    <td align="center"><?=$zhqlxm->getUnit()?></td>
                    <td align="center"><?=$zhqlxm->getPerson()?></td>
                    <td align="center"><?=$zhqlxm->getType()?></td>
                    <td align="center">               
                      <a href="javascript:void(0);" onclick="return showWindow('新增信息','<?=site_url("office/statistic/addZhqlxm/id/".$zhqlxm->getId())?>');" class="btn btn-info btn-sm">编 辑</a>
                      <a href="<?=site_url("office/statistic/delZhqlxm/id/".$zhqlxm->getId())?>" class="btn btn-danger btn-sm" onclick="return showConfirm('删除后将不可恢复，确定要删除吗？',this);">删 除</a>
                    </td>
                  </tr>
                <?php endwhile;?>
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="12" align="right">&nbsp;<?=$pager->fromto().$pager->navbar(10)?></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
</div>