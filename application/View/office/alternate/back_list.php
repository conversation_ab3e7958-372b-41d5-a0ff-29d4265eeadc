<div class="main">
  <div class="btn-group btn-group-sm" role="group">
  <a href="javascript:void(0);" onclick="selectAll();return false;" class="btn btn-info select" role="button">全部选中</a>
  <a href="<?=site_url("export/export/index")?>" class="btn btn-info export" role="button">导出数据</a>
   <a href="javascript:void(0);" onclick="return showWindow('高级搜索','<?=site_url("office/search/doSearch/hash/".$hash)?>',730,500);" class="btn btn-info find" role="button">高级搜索</a>
  <p style="clear:both;"></p>
  </div>
  <div class="search">
  <?php include_once('search_part.php')?>
  </div>
  <div class="box">
    <form id="validateForm" name="validateForm" method="post" action="<?=site_url("office/alternate/doAccept")?>">
      <table align="center" cellpadding="3" cellspacing="1"  class="tb_data table table-hover table-striped">
      <tr>
      <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
      <th width="30">详</th>
      <th><?=getColumnStr('项目名称','subject')?></th>
      <th><?=getColumnStr('申报单位','corporation_id')?></th>
      <th><?=getColumnStr('所属部门','department_id')?></th>
      <th width="60"><?=getColumnStr('负责人','user_name')?></th>
      <th width="40"><?=getColumnStr('得分','score')?></th>
	  <th>未结项目</th>
      <th>推荐比例</th>
      <th width="60">可用操作</th>
      </tr>
      <?php while($project = $pager->getObject()):?>
      <tr> <td align="center"><input name="select_id[]" type="checkbox" value="<?=$project->getProjectId()?>" /></td>
      <td align="center"><label style="cursor:pointer; font-size:14px; font-weight:900;" onclick="$('#show_<?=$project->getId()?>').toggle();">+</label></td>
      <td><?=$project->getMark()?><?=link_to("apply/project/show/id/".$project->getProjectId(),$project->getSubject())?><p class="snote">该项目申报经费 <?=$project->getDeclareMoney()?> 万元，为 <?=$project->getDeclareYear()?> 年申报。</p></td>
      <td><?=link_to("unit/profile/show/userid/".$project->getCorporationId(),$project->getCorporationName())?></td>
      <td><?=$project->getDepartmentName()?></td>
      <td><?=link_to("user/profile/show/userid/".$project->getUserId(),$project->getUserName())?></td>
      <td align="center"><?=$project->getScore()?></td>
	  <td align="center"><?=$project->hasUnCompleteProjectString()?></td>
      <td align="center"><?=$project->getElect()?></td>
       <td align="center"><a href="javascript:void(0);" onclick="return showWindow('移入备选库','<?=site_url("office/alternate/doSubmit/id/".$project->getProjectId())?>',400,250);" class="btn btn-info btn-sm" role="button"><i class="ace-icon fa fa-star"></i>入选</a></td>
      </tr>
      <tr id="show_<?=$project->getId()?>" style="display:none;">
        <td colspan="10">
        <?php include('more_part.php')?>
        </td>
        </tr>
	  <?php endwhile;?>
      <tr>
        <td colspan="10" align="right">&nbsp;<span class="pager_bar"><?=$pager->fromto().$pager->navbar(10)?></span></td>
        </tr>
      </table>
    </form>
  </div>
</div>