<div class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="block block-rounded">
                <div class="block-header block-header-default">
                    <h3 class="block-title">
                        OA通知消息综合搜索
                    </h3>
                    <div class="block-options">
                        <?=Button::setName('全部选中')->setEvent('selectAll();return false;')->setClass('btn-alt-info select')->link()?>
                        <?=Button::setName('阅读选中项')->setEvent('validateForm.action=\''.site_url("message/oa/doRead").'\';$(\'#validateForm\').submit()')->setIcon('check')->setClass('btn-alt-warning')->button()?>
                       <a href="javascript:void(0);" onclick="return showWindow('发送OA通知','<?=site_url("message/oa/edit/item_type/custom")?>',500,300)" class="btn btn-sm btn-info" role="button"><i class="ace-icon glyphicon glyphicon-pencil"></i>发送OA通知</a>
                    </div>
                </div>
                <div class="block-content">
                    <div class="search">
                        <?php include('search_part.php') ?>
                    </div>
                    <form id="validateForm" name="validateForm" method="post" action="">
                    <table width="100%" cellpadding="3" cellspacing="1" class="tb_data table table-hover">
                        <thead>
                        <tr>
                            <th width="30"><input name="selectAll" type="checkbox" id="selectAll"/></th>
                            <th><?=getColumnStr('接收人OA账号','user_name')?></th>
                            <th><?=getColumnStr('消息标题','title')?></th>
                            <th width="120"><?=getColumnStr('发送人','user_name')?></th>
                            <th><?=getColumnStr('创建时间','created_at')?></th>
                            <th width="120" class="text-center"><?=getColumnStr('是否发送','is_send')?></th>
                            <th width="120" class="text-center"><?=getColumnStr('是否阅读','is_read')?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php while($oa = $pager->getObject()):?>
                            <tr>
                                <td><input name="select_id[]" type="checkbox" value="<?=$oa->getMsgId()?>" /></td>
                                <td align="left"><?=$oa->getUserName()?></td>
                                <td align="left"><?=$oa->getTitle()?></td>
                                <td><?=$oa->getSenderName()?></td>
                                <td><?=$oa->getCreatedAt('Y/m/d H:i:s')?></td>
                                <td align="center"><?=$oa->getSendState()?></td>
                                <td align="center"><?=$oa->getReadState()?></td>
                            </tr>
                        <?php endwhile;?>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="8" align="right"><div class="pager_bar">
                                    <?=$pager->fromto().$pager->navbar(6).$pager->pagejump()?>
                                </div>
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>