<?php
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
?>
<div class="main">
    <div class="box">
        <table width="100%" cellpadding="3" cellspacing="1" class="table no-margin">
            <form id="validateForm" name="validateForm" method="post" action="<?=site_url("message/email/edit")?>">
                <tr>
                    <th class="center width-20">收件人地址<em class="red">*</em></th>
                    <td><input name="email" type="text" class="required number form-control" id="email" value="<?=$email->getEmail()?$email->getEmail():input::getInput("mix.emmail")?>" style="width:50%;"/></td>
                </tr>
                <tr>
                    <th class="center width-20">邮件标题<em class="red">*</em></th>
                    <td><input name="title" type="text" class="required form-control" id="title" value=""/></td>
                </tr>
                <tr>
                    <th class="center width-20">邮件内容<em class="red">*</em></th>
                    <td><textarea class="form-control" name="message" cols="60" rows="2"><?=$email->getMessage()?>
</textarea></td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <button type="submit" name="button" id="button" class="btn btn-sm btn-primary" >
                            发送消息 <i class='ace-icon fa fa-paper-plane-o'></i>
                        </button>
                        <input name="id" type="hidden" id="id" value="<?=$email->getId()?>" />
                        <input name="item_id" type="hidden" id="item_id" value="<?=$email->getItemId()?$email->getItemId():input::getInput("mix.item_id")?>" />
                        <input name="item_type" type="hidden" id="item_type" value="<?=$email->getItemType()?$email->getItemType():input::getInput("mix.item_type")?>" />
                        <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" />
                    </td>
                </tr>
            </form>
        </table>
    </div>
</div>
