<?php 
use Sofast\Core\Sf;
use Sofast\Core\Config;
use Sofast\Core\Input;
?>
<div class="main">
  <div class="box">
    <table width="100%" cellpadding="3" cellspacing="1" class="table no-margin">
      <form id="validateForm" name="validateForm" method="post" action="<?=site_url("message/short/edit")?>">
        <tr>
          <th class="center width-20">手机号码<em class="red">*</em></th>
          <td><input name="mobile" type="text" class="required number form-control" id="mobile" value="<?=$short->getMobile()?$short->getMobile():input::getInput("mix.mobile")?>" style="width:50%;"/></td>
        </tr>
        <tr>
          <th class="center width-20">发送时间<em class="red">*</em></th>
          <td><input name="send_at" type="text" class="required form-control" id="send_at" value="<?=date("Y-m-d H:i:s")?>" size="12" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:50%;"/></td>
        </tr>
        <tr>
          <th class="center width-20">发送内容<em class="red">*</em></th>
          <td><textarea class="form-control" name="message" cols="60" rows="2" style="width:99%;"><?=$short->getMessage()?>
</textarea></td>
        </tr>
        <tr>
          <td colspan="4" align="center">
            <button type="submit" name="button" id="button" class="btn btn-sm btn-primary" >
              发送消息 <i class='ace-icon fa fa-paper-plane-o'></i>
            </button>
            <input name="id" type="hidden" id="id" value="<?=$short->getId()?>" />
            <input name="item_id" type="hidden" id="item_id" value="<?=$short->getItemId()?$short->getItemId():input::getInput("mix.item_id")?>" />
            <input name="item_type" type="hidden" id="item_type" value="<?=$short->getItemType()?$short->getItemType():input::getInput("mix.item_type")?>" />
            <input name="fromUrl" type="hidden" id="fromUrl" value="<?=getFromUrl()?>" />
          </td>
        </tr>
      </form>
    </table>
  </div>
</div>
