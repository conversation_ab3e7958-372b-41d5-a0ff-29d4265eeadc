<?php
namespace App\Lib;

class Http
{
    private $url = '';
    private $url_file = '';
    private $callback_url = '';
    private $headers = [];
    private $timeout = 60;
    private $fields = [];
    private $request_type = 'default' ;

    function __construct($url, $callback_url=''){
        $this->url = $url;
        $this->callback_url = $callback_url;
    }

    function setHeaders(array $value=[]){
        $this->headers = $value;
        return $this;
    }

    function setTimeout(int $value=5){
        $this->timeout = $value;
        return $this;
    }

    function setData($value=[]){
        $this->fields = $value;
        return $this;
    }

    function setRequestType($value){
        $this->request_type = $value;
        return $this;
    }

    private function getPostFields(){
        if(!$this->callback_url) return $this->fields;

        $item = [];
        $item['callback'] = $this->callback_url;

        if(is_array($this->fields)) if(count($this->fields)) $item = array_merge($item, $this->fields);
        else $item['data'] = $this->fields;
        return $item;
    }

    function post(){
        $fields = $this->getPostFields();
        if($this->request_type == 'json'){
            $fields = json_encode($fields, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
            $this->headers[] = 'Content-Type:application/json';
            $this->headers[] = 'Content-Length:'.strlen($fields);
        }else{
            if(is_array($fields)) $fields = http_build_query($fields);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout); //设置cURL允许执行的最长秒数
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return json_decode($data, true);
    }

    function get(){
        $fields = $this->getPostFields();

        if($this->request_type == 'json'){
            $fields_str = json_encode($fields, JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
            $this->headers[] = 'Content-Type:application/json';
            $this->headers[] = 'Content-Length:'.strlen($fields_str);
        }

        if(preg_match_all('/{(.*?)}/', $this->url, $marks)){
            foreach($marks[0] as $index=>$mark){
                $value = $fields[$marks[1][$index]]?:'';
                $this->url = str_replace($mark, urlencode($value), $this->url);
            }
                
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->timeout); //设置cURL允许执行的最长秒数
        if (!empty($options)) {
            curl_setopt_array($ch, $options);
        }
        //https请求 不验证证书和host
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $data = curl_exec($ch);
        curl_close($ch);
        return json_decode($data, true);
    }
}