@charset "utf-8";

.no-margin {margin: 0!important}
.no-padding {padding: 0!important}

h1{margin:30px 0 50px 0;font-size: 35px;}
h2{text-align: center;margin:15px 0;font-size: 20px;}
.table{ border-collapse:collapse;}
.table tr td{}
.grey{color:#DCDCDC;}
.grey:hover{color:#CCC;}
div.space{height:50px;margin:0;padding:0;width:750px;}
div{border:1px soild #FFF;}
.part{width:100%;position: relative;}
u{text-align: center;}
.border-bottom{border-bottom: 1px solid #000;}
.def,.def2{border-bottom: 1px solid #000;}
.whiteborder{border:1px solid #FFF;}

.div[data-widget-id]{}

/*大段文字*/
.div_table{border:1px solid #000;padding:0;font-size: 18px;}
.div_table_title{padding:5px;min-height:35px;margin:0;}
.div_table_content{padding:5px;min-height:100px;margin:0;}

/*填写说明*/
.notes tr td{line-height: 35px;text-indent: 10px;}
img{border:1px solid #FFF;}

/*预览页面样式*/
.project_review{background: #CCCCCC;position: relative;}
.project_review .main{width:800px; background: #FFF;}

.project_review .header{padding:10px;position: fixed;top:0;width:100%;-webkit-box-shadow:0px 1px 10px #A0A0A0;  
  -moz-box-shadow:0px 1px 10px #A0A0A0;  
  box-shadow:0px 1px 10px #A0A0A0;z-index: 99999;}
.project_review .header span{font-size: 16px;}

.project_review table:not(.table){
border-left-width:1px;
border-top-width:1px;
border-right-width:1px;
border-bottom-width:1px;
border-color:#000000;
border-bottom-color:#000000;
able-layout:fixed;
word-break:break-all; 
ord-wrap: break-word;
font-size: 14px;
letter-spacing: 1px;
font-family: "宋体";
line-height: 2.0;}
.project_review table tr td{padding:2px;}
.project_review .table:not(.table) tr td{border:1px solid #000;vertical-align:center;}
.project_review .pagebreak{height:15px;background: #f0f3f8;margin:50px -60px 50px -60px;-webkit-box-shadow:0px 0px 6px #A5A5A5 inset;
  -moz-box-shadow:0px 0px 6px #A5A5A5 inset;
  box-shadow:0px 0px 6px #A5A5A5 inset;}

#wrapContent{font-size: 18px; line-height: 50px;}

.loading-text{font-size:18px;height:100%;text-align:center;color:#222;padding-top:50px;z-index: 888;text-align:center;}

.loading{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(222,222,222,0.8);
    z-index: 666;
}

.STYLE4 {
  font-size: 32px;
  font-family: "黑体";
  font-weight: 600;
}
.STYLE5 {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 2px;
  font-family: "宋体";
}

table.abc{border:1px solid black;border-spacing:0;border-collapse:collapse;}
table.abc td{border:1px solid black;}

table:not(.table) {
  border-collapse:collapse;
  /*border:solid black;*/
  overflow:wrap;
}
td {
  /*border: 1px solid black;*/
  word-wrap:break-word;
}
.def {
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.def1 {
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:0px;
  border-bottom-color:#FFFFFF;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 16px;
  letter-spacing: 2px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.abc {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.STYLE8 {
  color: #000000;
  font-weight: bold;
  font-size: 16px;
}
.STYLE11 {
  font-size: 16px
}
.def11 {
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:0px;
  border-bottom-color:#FFFFFF;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.def2 {
  border-left-width:0px;
  border-top-width:0px;
  border-right-width:0px;
  border-bottom-width:1px;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 14px;
  letter-spacing: 1px;
  font-family: "宋体";
  line-height: 1.8;
  border-top-color: #FFFFFF;
  border-right-color: #FFFFFF;
  border-left-color: #FFFFFF;
}
.pagebox {
  border:solid 1px #000;
  width:680px;
}
.pagebox .pagebox_head {
  padding:5px 2px;
  border-bottom:1px solid #000;
}
.pagebox .pagebox_body {
  padding:2px;
  min-height:820px;
}
.abc1 {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.abc2 {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.abc21 {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.abc211 {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}
.abc22 {
  border-left-width:1px;
  border-top-width:1px;
  border-right-width:1px;
  border-bottom-width:1px;
  border-color:#000000;
  border-bottom-color:#000000;
  able-layout:fixed;
  word-break:break-all;
  ord-wrap: break-word;
  font-size: 10.5pt;
  font-family: "宋体";
  line-height: 1.5;
}

caption { text-align:left; font-size:16px; font-weight:bold; padding-top:10px;}