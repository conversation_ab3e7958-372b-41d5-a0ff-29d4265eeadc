@charset "utf-8";
ul{overflow: hidden;}
ul li{list-style:none;}
a{color:#333;}
body{background: #EDF4F9;}
body div{background: #FFF;}
.space-4{background: none;}
.top_links{height:30px;line-height: 30px;padding-bottom:0;}
.top_links a{padding:0px 25px 0px 25px;display:block;font-size:12px;font-family:Verdana,Geneva,sans-serif;background-repeat:no-repeat;text-decoration:none;float:right;}
.top_links a:hover{color:#06C;background-color:#f4f4f4}
.top_links a:active{color:#06C;background-color:#f4f4f4}

.header-title{background-image: url(../images/name.png);background-repeat: no-repeat;height:100px;background-position: -10px -20px;}
.header-title .btn-group{font-size: 14px;letter-spacing: 0px;margin:30px 25px 0 0;}

.header-menu{border-top:3px solid #2080CA;border-bottom:1px solid #E1E1E1;background: #F5F5F5;padding:0; box-shadow:0px 0px 5px #E0E0E0;}
.header-menu ul li{height:50px; line-height: 55px; font-size: 16px;font-weight: 500;cursor: pointer;}
.header-menu ul li a{color:#666;margin-left:1px;font-weight: bolder;}
.header-menu ul li i{color:#CCC;border:1px solid #D7D7D7;border-radius: 30px;-moz-border-radius: 30px;-webkit-border-radius: 30px; height:30px;width:30px;text-align: center;margin-right: 5px;line-height: 28px;}

.header-menu ul li.menu_mouseover{border-bottom: 2px solid #F8A835;}
.header-menu ul li.menu_mouseover i{color:#F8A835;border:1px solid #F8A835;}
.header-menu ul li.menu_mouseover a{color:#1F7EC9;text-decoration: none;/*text-shadow: 0px 0px 2px #F0F0F0;*/}

.dropdown-menu.login-menu{width:250px;}
.dropdown-menu.login-menu li{padding:15px 15px 0 15px;}
.dropdown-menu.login-menu li .content-wrap div{margin-bottom: 10px;}
.dropdown-menu.login-menu li i{width: 15px;}

.banner{overflow: hidden;}


.notice{height:30px;overflow: hidden;line-height: 16px;font-size: 16px;}

/*列表内容*/
.nav-tabs li{background: #FCFCFC;}
.nav-index li.active a{background: #1A75C4;color: #FFF;border:1px solid #FFF;}
.nav-index li.active a:hover{background: #1A75C4;color: #FFF;border:1px solid #FFF;}
.nav-content div{display: none;border-radius: 0 0 3px 3px;padding:0 5px;min-height:320px;background: #FDFDFD;}
.nav-content div.active{display: block;}
.nav-content div.active div{display: block;}
.nav-content div ul li{ height:39px;line-height: 39px; border-bottom: 1px #ADADAD dotted;}
.nav-content div ul li:last-child{border:none;}
.nav-content div ul li span{overflow: hidden;;}
.nav-content div ul li .author{font-size: 13px;color:#959496;}
.nav-content div ul li .time{font-size: 13px;color:#959496;}
.nav-content div ul li i{margin-right: 5px;}
.nav-content div ul li:last-child a{color:#1A75C4;}
.nav-content div ul li:not(:last-child):hover{background:#F0F0F0;}
.nav-content div ul li:not(:last-child):hover a{color:#1A75C4;}
.nav-content div ul li:not(:last-child):hover span{color:#000;}

.login-cat a{margin-bottom: 10px;}


/*快捷入口*/
.quick{}
.quick li{transition:all 1s ease-out;padding-top:8px;border:1px solid #FFF;overflow: hidden;}
.quick li a{color: #75B7EE;display: block;}
.quick li div{transition:all 1s ease-out;margin:0 auto;border:2px solid #75B7EE;width:70px; height:70px;line-height: 85px; box-shadow:0px 0px 11px #FCFCFC;}
.quick li p{padding:5px 0 0 0; height: 51px;}
.quick li small{color:#999;}

.quick li.active{transition:all 0.3s ease-in;border:1px solid #FFF; background:#FFF;border-radius: 2px;}
.quick li.active div{transition:all 0.3s ease-in;color:#F8A835;border:2px solid #FEB902;}

/*常用提示*/
.common-tips{border-bottom: 1px solid #F0F0F0;border-top: 1px solid #F0F0F0;margin:6px 0;}
.common-tips li{border:1px solid #FFF;background: #FFF;height:40px;line-height:40px; font-size: 15px;text-align: center;}
.common-tips li a{color: #555;display: block;}
.common-tips li.head{font-size: 16px;color:#FFF;background:#2E63A7;border:none;text-align: left;padding:0;padding-left:25px;}
.common-tips li.active{background: #3595CC;border:1px solid #B1D7F5;}
.common-tips li.active a{text-decoration: none;color:#FFF;}

#tipdiv.divdown{width:100%;background-color:#FFF;margin:0;padding:0;border:1px solid #FFF;overflow:hidden;position:relative;height:auto;padding-top:5px;z-index:100;}
#tipdiv.divdown i{margin:0;padding:0;position:absolute;top:-5px;background-color:#FFF;}
#tipdiv.divdown p{border:1px dotted #0B9BE2;margin:0;font-size: 13px; line-height: 18px;padding:8px;color:#666;text-align: left;}

/*可申报项目列表*/
.list-style{min-height:200px;}
.list-style li{height:37px;line-height: 37px; border-bottom: 1px #DCDCDC dotted;overflow: hidden}
.list-style li:last-child{border:none;}
.list-style li.head{font-weight: bolder;border-bottom: 1px solid #DCDCDC;color:#666;}
.list-style.list-style-replace li:nth-child(odd):not(.head){background: #F8F8F8;}

.footer{text-align: center;padding:15px 0;border-top:1px solid #D8D8D8;}
.footer a{text-decoration: none;color: #666;font-size: 14px;}
.footer .menu a{padding:0px 15px;border-right:1px solid #999;}
.footer p{font-size: 14px;color: #666;margin-top:5px;}
.footer .menu a:hover{text-decoration:underline;color: #5FA1DC;}
.footer .menu a:last-child{border:none;}

.contact_us{padding:10px 20px;}
.contact_us h3{font-size:16px;font-weight: bolder;}
.contact_us p{line-height: 16px;}

.declare_guide{margin:15px 10px;}
.declare_guide li{ line-height: 30px;}

@media (min-width: 768px) {
	.container{width:1200px;}
	.header-title{padding:20px 0 20px 0px;}
	.quick{margin-left:40px;}
	.common-tips li{float: left;}
	.common-tips li.head{width:110px;}
	.margin-right-40{margin-right: 40px!important}
	.margin-right-10{margin-right: 10px!important}
	.margin-left-10{margin-left: 10px!important}
	.margin-left-40{margin-left: 40px!important}
	.padding-left-25{padding-left:25px!important}
	.padding-right-25{padding-right:25px!important}
	.right-div-index{width: 360px!important}
}
@media (max-width: 1200px) {
	.container{width:1000px;}
	.banner{width:100%;}
	.banner img{width:100%;}
}
@media (max-width: 1119px) {
	.container{width:1000px;}
}
@media (max-width: 767px) {
	.container{width:100%;}
	.header-title{overflow: hidden;height:auto;}
	.area-login{width:100%;margin-top: 100px;margin-bottom:10px;text-align: right;}
	.area-login .btn-group{margin:0;}
	.margin-top-20{margin-top: 20px!important}
}
@media (max-width: 480px) {
	.header-title{background-size: 100% ;background-position: 0 0 ;}
	.area-login{margin-top: 80px;}
}

@media (max-width: 320px) {
}"}"