<template>
    <div class="i-table-fields-filter">
        <el-popover
        :trigger="trigger"
        :placement="placement">
            <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAll">全选</el-checkbox> -->
            <el-checkbox-group v-model="displayColumns" @change="checkboxChange">
                <el-checkbox v-for="field in filterColumns" :label="field.name" :key="field.name" :disabled="field.lock">
                    <!-- <el-tag size="mini" v-if="field.display=='main'">主</el-tag>
                    <el-tag size="mini" type="info" v-else>副</el-tag> -->
                    {{field.label}}
                </el-checkbox>
            </el-checkbox-group>
            
            <!-- <div style="text-align: center; margin: 0">
                <el-button type="primary" size="mini" @click="haddle">确定</el-button>
            </div> -->
            <template #reference>
              <i class="fa fa-list" slot="reference"></i>
            </template>
        </el-popover>
    </div>
</template>
 
<script>
export default {
  props: {
    columns: {
      type: Array,
      default: []
    },
    trigger:{
      type: String,
      default: 'hover'
    },
    placement:{
      type: String,
      default: 'bottom-end'
    }
  },
  setup(prop,context){
    const data = Vue.reactive({})
    const checkboxChange = value=>{
      context.emit("refreshCols",value)
    }

    return {
        ...Vue.toRefs(data),
        checkboxChange
    }
  },
  data(){
    return {
      show:true,
      displayColumns:[],
      checkAll:false
    }
  },
  created() {},
  watch:{
    columns:{
      handler(val){
        val.forEach(item=>{
          // if(['main','expand'].indexOf(item.display) >= 0 && item.hide!==true && item.lock!==true)
          if(item.hide!==true && item.lock!==true){
            this.displayColumns.push(item.name)
          }
        })
      },
      immediate:true,
    }
  },
  computed:{
    isIndeterminate:function(){
        let checkedCount = this.displayColumns.length;
        return checkedCount > 0 && checkedCount < Object.keys(this.columns).length || false;
    },
    filterColumns(){
      return this.columns.filter(item=>{
        return item.lock!==true
      })
    },
  },
  methods:{
    handleCheckAll(){
      if(this.checkAll){
        this.columns.forEach(item=>{
          this.displayColumns.push(item.name)
        })
      }else this.displayColumns = []
    },
    haddle(){
      let items = []
      this.columns.map(item=>{
          let _item = JSON.parse(JSON.stringify(item))
          if(this.displayColumns.indexOf(_item.name) >= 0) _item.hide = false
          else if(_item.lock!==true){
              _item.hide = true
          }
          items.push(_item)
      })
      this.show = false
      this.$emit('change',items)
    }
  }
}
</script>
 
<style scoped>
.table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.el-checkbox{height: 20px!important;}
</style>