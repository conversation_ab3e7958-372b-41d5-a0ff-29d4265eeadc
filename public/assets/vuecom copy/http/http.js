//使用create方法创建axios实例
const Service = axios.create({
    timeout: 6000, // 请求超时时间
    responseType: 'json',
    withCredentials: true,
    headers: {
        // 'Accept': 'application/json',
        // 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        'Content-Type': 'application/json;charset=UTF-8'
        // 'Content-Type': 'multipart/form-data;charset=UTF-8;boundary = ' + new Date().getTime()
    },
    validateStatus: status => ((status >= 200 && status < 300) || status === 401 || status === 403 || status === 404)
})

// 添加请求拦截器
Service.interceptors.request.use(config => {

    if(config.headers.sign && config.headers.sign.length > 1024) config.headers.sign = ''
    return config
})

// 添加响应拦截器
Service.interceptors.response.use(async response => {

  if (response.status >= 200 && response.status < 300) {
      return Promise.resolve(response);
  } else {       
      return Promise.reject(response);        
  }
}, error => {

  if (error.response.status) {
      return Promise.reject(error.response);
  }else{
    const msg = error.Message !== undefined ? error.Message : ''
    ElMessage({
      message: '网络错误' + msg,
      type: 'error',
      duration: 3 * 1000
    })
    return Promise.reject(error)
  }
  
})

const http = (method,url,param,headers)=>{
    return new Promise((resolve,reject)=>{
        let options = {}

        switch(method){
          case 'get':
          case 'delete':
            options = {
                method:method,
                url,
                params:param,
                headers
            }
            break;
          case 'post':
          case 'put':
          case 'patch':
            options = {
                method:method,
                url,
                data:param,
                // data:qs.stringify(param),
                headers
            }
          break;
        }
        Service(options).then(res=>{
            resolve(res)
        }).catch(res=>{
            reject(res)
        })

    })
}