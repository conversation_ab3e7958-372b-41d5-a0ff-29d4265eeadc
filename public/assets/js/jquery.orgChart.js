/**
$("#org").orgChart(data,options)
data：
{
    content:'',  //节点内容
    children:[],  //子节点内容
    ...  //其他自定义参数
}
options：
{
    btns:[
        {
            text:'',  //按钮名称
            class:'',  //class名称
            click:function(data){
                //点击事件具体实现，data为当前节点数据
            }
        }
    ]
}
**/
(function($) {
    let loadCss = _=>{
      $('head').append('<style>.orgChart{display:flex;justify-content:center;}.orgChart table{border-collapse: inherit}.orgChart .line{height:20px;width:4px;}.orgChart .down{background-color:rgba(137,137,137,.4);margin:0px auto;}.orgChart .top{border-top:3px solid rgba(137,137,137,.4);}.orgChart .left{border-right:2px solid rgba(137,137,137,.4);}.orgChart .right{border-left:2px solid rgba(137,137,137,.4);}.org<PERSON>hart td{text-align:center;vertical-align:top;padding:0;}.orgChart .node{background-color:#35363B;display:inline-block;width:96px;height:60px;z-index:10;margin:02px;}.drag-active{border-style:dotted!important;}.drop-hover{border-style:solid!important;border-color:#E05E00!important;}.orgChart .node-shadow{position:absolute;top:0;left:0;height:100%;width:100%;background:rgba(55,55,55,.3);-webkit-filter: blur(15px);-moz-filter: blur(15px);-o-filter: blur(15px);-ms-filter: blur(15px);filter: blur(15px);}.orgChart .node{width:120px;height:160px;border-radius:5px;padding:3px;box-sizing:border-box;transition:opacity .25s ease-out;color:#FFF;border:1px solid #FFF;background:#2a8bf9}.orgChart .nodeadd{font-size:40px}.orgChart .toolbtn{cursor:pointer;color:#fff;background-color:#ffb119;height:auto;padding:0 .75rem;height:2rem;line-height:2rem;z-index:5;font-weight:600;border-radius:0;border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}</style>');
    }

    loadCss();

    let defaults = {
        chartElement : 'body',
        depth      : -1,
        chartClass : "orgChart",
        dragAndDrop: false
    };

    $.fn.orgChart = function(data,options) {
        var opts = $.extend({}, defaults, options);

        $this = $(this);
        var $container = $("<div class='" + opts.chartClass + "'/>");

        if(typeof data == undefined || !data || data.length==0){
            buildNodeNone($container, opts)
        }else buildNode(data, $container, 0, opts);

        $(this).html($container);

        // add drag and drop if enabled
        if(opts.dragAndDrop){
            $('div.node').draggable({
                cursor      : 'move',
                distance    : 40,
                helper      : 'clone',
                opacity     : 0.8,
                revert      : 'invalid',
                revertDuration : 100,
                snap        : 'div.node.expanded',
                snapMode    : 'inner',
                stack       : 'div.node'
            });
            
            $('div.node').droppable({
                accept      : '.node',          
                activeClass : 'drag-active',
                hoverClass  : 'drop-hover'
            });
            
          // Drag start event handler for nodes
          $('div.node').bind("dragstart", function handleDragStart( event, ui ){
            
            var sourceNode = $(this);
            sourceNode.parentsUntil('.node-container')
                       .find('*')
                       .filter('.node')
                       .droppable('disable');
          });

          // Drag stop event handler for nodes
          $('div.node').bind("dragstop", function handleDragStop( event, ui ){

            /* reload the plugin */
            $(opts.chartElement).children().remove();
            $this.orgChart(opts);      
          });
        
          // Drop event handler for nodes
          $('div.node').bind("drop", function handleDropEvent( event, ui ) {    
    	  
            var targetID = $(this).data("tree-node");
            var targetLi = $this.find("li").filter(function() { return $(this).data("tree-node") === targetID; } );
            var targetUl = targetLi.children('ul');
    		
            var sourceID = ui.draggable.data("tree-node");		
            var sourceLi = $this.find("li").filter(function() { return $(this).data("tree-node") === sourceID; } );		
            var sourceUl = sourceLi.parent('ul');

            if (targetUl.length > 0){
      		    targetUl.append(sourceLi);
            } else {
      		    targetLi.append("<ul></ul>");
      		    targetLi.children('ul').append(sourceLi);
            }
            
            //Removes any empty lists
            if (sourceUl.children().length === 0){
              sourceUl.remove();
            }
    		
          }); // handleDropEvent
            
        }
        return this
  };

  function buildNodeNone($appendTo, opts){
    $nodeDiv = $("<div>").addClass("node nodeadd")
                .data("tree-node", 0).attr('style','display:flex;align-items:center;justify-content:center;cursor:pointer;').html('+');
    if(opts.class){
        $nodeDiv.addClass(opts.class)
    }
    $appendTo.append($nodeDiv);
  }
	
  var nodeCount = 0;
  // Method that recursively builds the tree
  function buildNode(data, $appendTo, level, opts) {
    var $table = $("<table cellpadding='0' cellspacing='0' border='0'/>");
    var $tbody = $("<tbody/>");

    // Construct the node container(s)
    var $nodeRow = $("<tr/>").addClass("node-cells");
    var $nodeCell = $("<td/>").addClass("node-cell").attr("colspan", 2);
    var $childNodes = data.children || [];
    var $nodeDiv;
    
    if($childNodes.length > 1) {
      $nodeCell.attr("colspan", $childNodes.length * 2);
    }

    var $nodeContent = data.content;
	
      //Increaments the node count which is used to link the source list and the org chart
  	nodeCount++;
  	// $node.data("tree-node", nodeCount);
  	$nodeDiv = $("<div>").addClass("node")
                .attr('style','position:relative;')
                .data("tree-node", nodeCount)
                .append($nodeContent);

    if(opts.class){
        $nodeDiv.addClass(opts.class)
    }

    // Expand and contract nodes
    if ($childNodes.length > 0) {
      $nodeDiv.click(function() {
          var $this = $(this);
          var $tr = $this.closest("tr");

          if($tr.hasClass('contracted')){
            $this.css('cursor','n-resize');
            $tr.removeClass('contracted').addClass('expanded');
            $tr.nextAll("tr").css('visibility', '');

            // Update the <li> appropriately so that if the tree redraws collapsed/non-collapsed nodes
            // maintain their appearance
            // $node.removeClass('collapsed');
          }else{
            $this.css('cursor','s-resize');
            $tr.removeClass('expanded').addClass('contracted');
            $tr.nextAll("tr").css('visibility', 'hidden');

            // $node.addClass('collapsed');
          }
        });
    }

    if(opts.btns && opts.btns.length>0){
        let btnContent = [];
        for(let i in opts.btns){
            let btn = opts.btns[i]

            $btn = $("<div>").addClass('toolbtn').addClass(btn['class']).html(btn['text']);

            if(typeof btn['click'] == 'function'){
                $btn.on('click',function(e){
                    btn['click'](data)
                    e.stopPropagation();
                })
            }

            btnContent.push($btn);
        }
        let nodeBtns = $("<div>").addClass("node-btns").attr('style','display:none;position:absolute;left:0;top:0;font-size:10px;color:#FFF;flex-direction:column;justify-content:space-around;height:100%;').append(btnContent);
        $nodeDiv.append(nodeBtns);

        $nodeDiv.on('mouseenter',function(){
            $(this).append('<div class="node-shadow"></div>');
            $(this).find('.node-btns').show().css('display','flex');
        }).on('mouseleave',function(){
            $(this).find('.node-btns').hide();
            $(this).find('.node-shadow').remove();
        })
    }
    
    $nodeCell.append($nodeDiv);
    $nodeRow.append($nodeCell);
    $tbody.append($nodeRow);

    if($childNodes.length > 0) {
      $nodeDiv.css('cursor','n-resize').attr('title','点击展开/收起');
    
      // recurse until leaves found (-1) or to the level specified
      if(opts.depth == -1 || (level+1 < opts.depth)) { 
        var $downLineRow = $("<tr/>");
        var $downLineCell = $("<td/>").attr("colspan", $childNodes.length*2);
        $downLineRow.append($downLineCell);
        
        // draw the connecting line from the parent node to the horizontal line 
        $downLine = $("<div></div>").addClass("line down");
        $downLineCell.append($downLine);
        $tbody.append($downLineRow);

        var $linesRow = $("<tr/>");
        
        for(let i in $childNodes){
            var $left = $("<td>&nbsp;</td>").addClass("line left top");
            var $right = $("<td>&nbsp;</td>").addClass("line right top");
            $linesRow.append($left).append($right);
        }

        $linesRow.find("td:first")
                .removeClass("top")
                .end()
                .find("td:last")
                .removeClass("top");

        $tbody.append($linesRow);
        var $childNodesRow = $("<tr/>");

        for(let i in $childNodes){
           var $td = $("<td class='node-container'/>");
           $td.attr("colspan", 2);
           
           buildNode($childNodes[i], $td, level+1, opts);
           $childNodesRow.append($td);
        };

      }
      $tbody.append($childNodesRow);
    }

    if (data.class != undefined) {
        var classList = $node.attr('class').split(/\s+/);
        $.each(classList, function(index,item) {
            if (item == 'collapsed') {
                $nodeRow.nextAll('tr').css('visibility', 'hidden');
                    $nodeRow.removeClass('expanded');
                    $nodeRow.addClass('contracted');
                    $nodeDiv.css('cursor','s-resize');
            } else {
                $nodeDiv.addClass(item);
            }
        });
    }

    $table.append($tbody);
    $appendTo.append($table);
    
    $nodeDiv.children('a').click(function(e){
        e.stopPropagation();
    });
  };

})(jQuery);
