/*!
 * dashmix - v3.1.0
 * <AUTHOR> - https://pixelcave.com
 * Copyright (c) 2020
 */
!function(){function a(a,r){for(var e=0;e<r.length;e++){var t=r[e];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(a,t.key,t)}}var r=function(){function r(){!function(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}(this,r)}var e,t;return e=r,t=[{key:"initChartsChartJS",value:function(){Chart.defaults.global.defaultFontColor="#495057",Chart.defaults.scale.gridLines.color="rgba(0,0,0,.04)",Chart.defaults.scale.gridLines.zeroLineColor="rgba(0,0,0,.1)",Chart.defaults.scale.ticks.beginAtZero=!0,Chart.defaults.global.elements.line.borderWidth=2,Chart.defaults.global.elements.point.radius=5,Chart.defaults.global.elements.point.hoverRadius=7,Chart.defaults.global.tooltips.cornerRadius=3,Chart.defaults.global.legend.labels.boxWidth=12;var a,r,e=jQuery(".js-chartjs-lines"),t=jQuery(".js-chartjs-bars"),o=jQuery(".js-chartjs-radar"),n=jQuery(".js-chartjs-polar"),l=jQuery(".js-chartjs-pie"),i=jQuery(".js-chartjs-donut");a={labels:["MON","TUE","WED","THU","FRI","SAT","SUN"],datasets:[{label:"This Week",fill:!0,backgroundColor:"rgba(6, 101, 208, .75)",borderColor:"rgba(6, 101, 208, 1)",pointBackgroundColor:"rgba(6, 101, 208, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(6, 101, 208, 1)",data:[34,42,62,78,39,83,98]},{label:"Last Week",fill:!0,backgroundColor:"rgba(108, 117, 125, .25)",borderColor:"rgba(108, 117, 125, .75)",pointBackgroundColor:"rgba(108, 117, 125, 1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(108, 117, 125, 1)",data:[130,95,125,160,187,110,143]}]},r={labels:["Earnings","Sales","Tickets"],datasets:[{data:[65,15,20],backgroundColor:["rgba(141, 196, 81, 1)","rgba(255, 177, 25, 1)","rgba(224, 79, 26, 1)"],hoverBackgroundColor:["rgba(141, 196, 81, .5)","rgba(255, 177, 25, .5)","rgba(224, 79, 26, .5)"]}]},e.length&&new Chart(e,{type:"line",data:a}),t.length&&new Chart(t,{type:"bar",data:a}),o.length&&new Chart(o,{type:"radar",data:a}),n.length&&new Chart(n,{type:"polarArea",data:r}),l.length&&new Chart(l,{type:"pie",data:r}),i.length&&new Chart(i,{type:"doughnut",data:r})}},{key:"initRandomEasyPieChart",value:function(){jQuery(".js-pie-randomize").on("click",(function(a){jQuery(a.currentTarget).parents(".block").find(".pie-chart").each((function(a,r){return jQuery(r).data("easyPieChart").update(Math.floor(100*Math.random()+1))}))}))}},{key:"init",value:function(){this.initChartsChartJS(),this.initRandomEasyPieChart()}}],null&&a(e.prototype,null),t&&a(e,t),r}();jQuery((function(){r.init()}))}();